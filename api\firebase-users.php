<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once 'config/database.php';

try {
    $database = new Database();
    $db = $database->getConnection();

    // Créer la table users si elle n'existe pas
    createUsersTableIfNotExists($db);

    $method = $_SERVER['REQUEST_METHOD'];
    $action = isset($_GET['action']) ? $_GET['action'] : '';

    // Simple auth check
    $headers = function_exists('getallheaders') ? getallheaders() : [];
    if (empty($headers) && isset($_SERVER['HTTP_AUTHORIZATION'])) {
        $headers['Authorization'] = $_SERVER['HTTP_AUTHORIZATION'];
    }

    $token = isset($headers['Authorization']) ? str_replace('Bearer ', '', $headers['Authorization']) : null;

    switch ($method) {
        case 'GET':
            if ($action === 'all') {
                getAllUsers($db);
            } elseif ($action === 'sync') {
                syncFirebaseUsers($db);
            } else {
                http_response_code(404);
                echo json_encode(['error' => 'Action non supportée']);
            }
            break;

        case 'POST':
            $input = json_decode(file_get_contents('php://input'), true);
            if ($action === 'create') {
                createUser($db, $input);
            } elseif ($action === 'sync-user') {
                syncSingleUser($db, $input);
            } else {
                http_response_code(404);
                echo json_encode(['error' => 'Action non supportée']);
            }
            break;

        case 'PUT':
            $input = json_decode(file_get_contents('php://input'), true);
            $id = isset($_GET['id']) ? $_GET['id'] : null;
            if ($action === 'update' && $id) {
                updateUser($db, $id, $input);
            } else {
                http_response_code(404);
                echo json_encode(['error' => 'Action non supportée']);
            }
            break;

        default:
            http_response_code(405);
            echo json_encode(['error' => 'Méthode non supportée']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Erreur serveur: ' . $e->getMessage()]);
}

/**
 * Create users table if it doesn't exist
 */
function createUsersTableIfNotExists($db)
{
    try {
        $createTableQuery = "
            CREATE TABLE IF NOT EXISTS users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                firebase_uid VARCHAR(255) UNIQUE NULL,
                email VARCHAR(255) UNIQUE NOT NULL,
                display_name VARCHAR(255) NULL,
                photo_url TEXT NULL,
                email_verified TINYINT(1) DEFAULT 0,
                role ENUM('admin', 'merchant', 'user') DEFAULT 'merchant',
                status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
                last_login TIMESTAMP NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_firebase_uid (firebase_uid),
                INDEX idx_email (email),
                INDEX idx_role (role),
                INDEX idx_status (status)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";

        $db->exec($createTableQuery);

        // Créer quelques utilisateurs de test s'ils n'existent pas
        $testUsers = [
            [
                'firebase_uid_1',
                '<EMAIL>',
                'User Id 1',
                'admin',
                'inactive'
            ],
            [
                'firebase_uid_2',
                '<EMAIL>',
                'User Id 2',
                'merchant',
                'inactive'
            ]
        ];

        $insertQuery = "
            INSERT IGNORE INTO users (firebase_uid, email, display_name, role, status, created_at)
            VALUES (?, ?, ?, ?, ?, NOW())
        ";

        $stmt = $db->prepare($insertQuery);
        foreach ($testUsers as $user) {
            $stmt->execute($user);
        }
    } catch (Exception $e) {
        error_log("Erreur création table users: " . $e->getMessage());
    }
}

/**
 * Get all users from local database
 */
function getAllUsers($db)
{
    try {
        $query = "
            SELECT
                u.*,
                s.name as subscription_name,
                us.status as subscription_status
            FROM users u
            LEFT JOIN user_subscriptions us ON u.id = us.user_id AND us.status = 'active'
            LEFT JOIN subscriptions s ON us.subscription_id = s.id
            ORDER BY u.created_at DESC
        ";

        $stmt = $db->query($query);
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Compter par rôle
        $stats = [
            'total' => count($users),
            'active' => 0,
            'admins' => 0,
            'merchants' => 0
        ];

        foreach ($users as $user) {
            if ($user['status'] === 'active') {
                $stats['active']++;
            }
            if ($user['role'] === 'admin') {
                $stats['admins']++;
            }
            if ($user['role'] === 'merchant') {
                $stats['merchants']++;
            }
        }

        echo json_encode([
            'success' => true,
            'data' => [
                'users' => $users,
                'stats' => $stats
            ]
        ]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la récupération des utilisateurs: ' . $e->getMessage()]);
    }
}

/**
 * Create or update user from Firebase data
 */
function syncSingleUser($db, $firebaseUser)
{
    try {
        // Vérifier si l'utilisateur existe déjà
        $checkQuery = "SELECT id, role FROM users WHERE firebase_uid = ? OR email = ?";
        $stmt = $db->prepare($checkQuery);
        $stmt->execute([$firebaseUser['uid'], $firebaseUser['email']]);
        $existingUser = $stmt->fetch();

        if ($existingUser) {
            // Mettre à jour l'utilisateur existant
            $updateQuery = "
                UPDATE users SET
                    firebase_uid = ?,
                    email = ?,
                    display_name = ?,
                    photo_url = ?,
                    email_verified = ?,
                    last_login = ?,
                    updated_at = NOW()
                WHERE id = ?
            ";

            $stmt = $db->prepare($updateQuery);
            $stmt->execute([
                $firebaseUser['uid'],
                $firebaseUser['email'],
                $firebaseUser['displayName'] ?? $firebaseUser['email'],
                $firebaseUser['photoURL'] ?? null,
                $firebaseUser['emailVerified'] ? 1 : 0,
                isset($firebaseUser['lastLoginAt']) ? date('Y-m-d H:i:s', $firebaseUser['lastLoginAt'] / 1000) : null,
                $existingUser['id']
            ]);

            $userId = $existingUser['id'];
            $action = 'updated';
        } else {
            // Créer un nouvel utilisateur
            $insertQuery = "
                INSERT INTO users (
                    firebase_uid, email, display_name, photo_url,
                    email_verified, role, status, last_login, created_at
                ) VALUES (
                    ?, ?, ?, ?, ?, 'merchant', 'active', ?, NOW()
                )
            ";

            $stmt = $db->prepare($insertQuery);
            $stmt->execute([
                $firebaseUser['uid'],
                $firebaseUser['email'],
                $firebaseUser['displayName'] ?? $firebaseUser['email'],
                $firebaseUser['photoURL'] ?? null,
                $firebaseUser['emailVerified'] ? 1 : 0,
                isset($firebaseUser['lastLoginAt']) ? date('Y-m-d H:i:s', $firebaseUser['lastLoginAt'] / 1000) : null
            ]);

            $userId = $db->lastInsertId();
            $action = 'created';

            // Assigner un abonnement gratuit par défaut
            $freeSubscriptionQuery = "SELECT id FROM subscriptions WHERE name = 'Gratuit' OR price = 0 ORDER BY id LIMIT 1";
            $stmt = $db->query($freeSubscriptionQuery);
            $freeSubscription = $stmt->fetch();

            if ($freeSubscription) {
                $subscriptionQuery = "
                    INSERT INTO user_subscriptions (
                        user_id, subscription_id, status, started_at, created_at
                    ) VALUES (
                        ?, ?, 'active', NOW(), NOW()
                    )
                ";
                $stmt = $db->prepare($subscriptionQuery);
                $stmt->execute([$userId, $freeSubscription['id']]);
            }
        }

        echo json_encode([
            'success' => true,
            'data' => [
                'user_id' => $userId,
                'action' => $action,
                'message' => "Utilisateur $action avec succès"
            ]
        ]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la synchronisation: ' . $e->getMessage()]);
    }
}

/**
 * Create new user
 */
function createUser($db, $data)
{
    try {
        $query = "
            INSERT INTO users (
                firebase_uid, email, display_name, role, status, created_at
            ) VALUES (
                ?, ?, ?, ?, 'active', NOW()
            )
        ";

        $stmt = $db->prepare($query);
        $stmt->execute([
            $data['firebase_uid'] ?? null,
            $data['email'],
            $data['display_name'] ?? $data['email'],
            $data['role'] ?? 'merchant'
        ]);

        $userId = $db->lastInsertId();

        echo json_encode([
            'success' => true,
            'data' => [
                'id' => $userId,
                'message' => 'Utilisateur créé avec succès'
            ]
        ]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la création: ' . $e->getMessage()]);
    }
}

/**
 * Update user
 */
function updateUser($db, $id, $data)
{
    try {
        $query = "
            UPDATE users SET
                email = ?,
                display_name = ?,
                role = ?,
                status = ?,
                updated_at = NOW()
            WHERE id = ?
        ";

        $stmt = $db->prepare($query);
        $stmt->execute([
            $data['email'],
            $data['display_name'],
            $data['role'],
            $data['status'],
            $id
        ]);

        if ($stmt->rowCount() === 0) {
            http_response_code(404);
            echo json_encode(['error' => 'Utilisateur non trouvé']);
            return;
        }

        echo json_encode([
            'success' => true,
            'data' => [
                'message' => 'Utilisateur mis à jour avec succès'
            ]
        ]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la mise à jour: ' . $e->getMessage()]);
    }
}

/**
 * Sync multiple Firebase users
 */
function syncFirebaseUsers($db)
{
    try {
        // Cette fonction pourrait être appelée avec une liste d'utilisateurs Firebase
        // Pour l'instant, on retourne un message de succès
        echo json_encode([
            'success' => true,
            'data' => [
                'message' => 'Synchronisation des utilisateurs Firebase disponible via l\'action sync-user'
            ]
        ]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la synchronisation: ' . $e->getMessage()]);
    }
}
