<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once 'config/database.php';

try {
    $database = new Database();
    $db = $database->getConnection();
    createUsersTableIfNotExists($db);

    $method = $_SERVER['REQUEST_METHOD'];
    $action = isset($_GET['action']) ? $_GET['action'] : '';

    // Simple auth check
    $headers = function_exists('getallheaders') ? getallheaders() : [];
    if (empty($headers) && isset($_SERVER['HTTP_AUTHORIZATION'])) {
        $headers['Authorization'] = $_SERVER['HTTP_AUTHORIZATION'];
    }

    $token = isset($headers['Authorization']) ? str_replace('Bearer ', '', $headers['Authorization']) : null;

    switch ($method) {
        case 'GET':
            if ($action === 'all') {
                getAllUsers($db);
            } else {
                http_response_code(404);
                echo json_encode(['error' => 'Action non supportée']);
            }
            break;

        case 'POST':
            $input = json_decode(file_get_contents('php://input'), true);
            if ($action === 'create') {
                createUser($db, $input);
            } elseif ($action === 'sync-user') {
                syncSingleUser($db, $input);
            } else {
                http_response_code(404);
                echo json_encode(['error' => 'Action non supportée']);
            }
            break;

        case 'PUT':
            $input = json_decode(file_get_contents('php://input'), true);
            $id = isset($_GET['id']) ? $_GET['id'] : null;
            if ($action === 'update' && $id) {
                updateUser($db, $id, $input);
            } else {
                http_response_code(404);
                echo json_encode(['error' => 'Action non supportée']);
            }
            break;

        default:
            http_response_code(405);
            echo json_encode(['error' => 'Méthode non supportée']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Erreur serveur: ' . $e->getMessage()]);
}

/**
 * Create users table if it doesn't exist
 */
function createUsersTableIfNotExists($db)
{
    try {
        // Create subscription_plans table if not exists
        $createPlansQuery = "
            CREATE TABLE IF NOT EXISTS subscription_plans (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                features TEXT NOT NULL,
                price DECIMAL(10,2) NOT NULL,
                duration INT NOT NULL, -- in days
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        ";
        $db->exec($createPlansQuery);

        // Create subscriptions table if not exists
        $createSubsQuery = "
            CREATE TABLE IF NOT EXISTS subscriptions (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                plan_id INT NOT NULL,
                start_date TIMESTAMP NOT NULL,
                end_date TIMESTAMP NOT NULL,
                status ENUM('active', 'cancelled') DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (plan_id) REFERENCES subscription_plans(id) ON DELETE RESTRICT
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        ";
        $db->exec($createSubsQuery);

        $createTableQuery = "
            CREATE TABLE IF NOT EXISTS users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                firebase_uid VARCHAR(255) UNIQUE NULL,
                email VARCHAR(255) UNIQUE NOT NULL,
                display_name VARCHAR(255) NULL,
                photo_url TEXT NULL,
                email_verified TINYINT(1) DEFAULT 0,
                role ENUM('admin', 'merchant', 'user') DEFAULT 'merchant',
                status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
                subscription_id INT NULL,
                last_login TIMESTAMP NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_firebase_uid (firebase_uid),
                INDEX idx_email (email),
                INDEX idx_role (role),
                INDEX idx_status (status),
                FOREIGN KEY (subscription_id) REFERENCES subscriptions(id) ON DELETE SET NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";

        $db->exec($createTableQuery);

        // Créer quelques utilisateurs de test s'ils n'existent pas
        $testUsers = [
            [
                'firebase_uid_1',
                '<EMAIL>',
                'User Id 1',
                'admin',
                'inactive'
            ],
            [
                'firebase_uid_2',
                '<EMAIL>',
                'User Id 2',
                'merchant',
                'inactive'
            ]
        ];

        $insertQuery = "
            INSERT IGNORE INTO users (firebase_uid, email, display_name, role, status, created_at)
            VALUES (?, ?, ?, ?, ?, NOW())
        ";

        $stmt = $db->prepare($insertQuery);
        foreach ($testUsers as $user) {
            $stmt->execute($user);
        }
    } catch (Exception $e) {
        error_log("Erreur création table users: " . $e->getMessage());
    }
}

/**
 * Get all users from local database
 */
function getAllUsers($db)
{
    try {
        $query = "
            SELECT
                u.id,
                u.firebase_uid,
                u.email,
                u.name,
                u.phone,
                u.status,
                u.created_at,
                u.updated_at,
                COALESCE(r.name, 'user') as role,
                r.display_name as role_display_name,
                s.id as subscription_id,
                sp.name as subscription_name,
                sp.features as subscription_features
            FROM users u
            LEFT JOIN user_roles ur ON u.id = ur.user_id
            LEFT JOIN roles r ON ur.role_id = r.id
            LEFT JOIN subscriptions s ON u.id = s.user_id AND s.status = 'active'
            LEFT JOIN subscription_plans sp ON s.plan_id = sp.id
            ORDER BY u.created_at DESC
        ";

        $stmt = $db->query($query);
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Compter par rôle
        $stats = [
            'total' => count($users),
            'active' => 0,
            'admins' => 0,
            'merchants' => 0,
            'users' => 0
        ];

        foreach ($users as $user) {
            if ($user['status'] === 'active') {
                $stats['active']++;
            }
            if ($user['role'] === 'admin') {
                $stats['admins']++;
            }
            if ($user['role'] === 'merchant') {
                $stats['merchants']++;
            }
            if ($user['role'] === 'user') {
                $stats['users']++;
            }
        }

        echo json_encode([
            'success' => true,
            'data' => [
                'users' => $users,
                'stats' => $stats
            ]
        ]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la récupération des utilisateurs: ' . $e->getMessage()]);
    }
}

/**
 * Create or update user from Firebase data
 */
function syncSingleUser($db, $firebaseUser)
{
    try {
        // Vérifier si l'utilisateur existe déjà
        $checkQuery = "SELECT id, role FROM users WHERE firebase_uid = ? OR email = ?";
        $stmt = $db->prepare($checkQuery);
        $stmt->execute([$firebaseUser['uid'], $firebaseUser['email']]);
        $existingUser = $stmt->fetch();

        if ($existingUser) {
            // Mettre à jour l'utilisateur existant
            $updateQuery = "
                UPDATE users SET
                firebase_uid = COALESCE(firebase_uid, ?),
                email = ?,
                display_name = ?,
                photo_url = ?,
                email_verified = ?,
                updated_at = NOW()
                WHERE id = ?
            ";

            $stmt = $db->prepare($updateQuery);
            $stmt->execute([
                $firebaseUser['uid'],
                $firebaseUser['email'],
                $firebaseUser['displayName'] ?? null,
                $firebaseUser['photoURL'] ?? null,
                $firebaseUser['emailVerified'] ? 1 : 0,
                $existingUser['id']
            ]);

            return $existingUser;
        } else {
            // Créer un nouvel utilisateur
            $insertQuery = "
                INSERT INTO users (
                    firebase_uid,
                    email,
                    display_name,
                    photo_url,
                    email_verified,
                    role,
                    status,
                    created_at
                ) VALUES (?, ?, ?, ?, ?, 'merchant', 'active', NOW())
            ";

            $stmt = $db->prepare($insertQuery);
            $stmt->execute([
                $firebaseUser['uid'],
                $firebaseUser['email'],
                $firebaseUser['displayName'] ?? null,
                $firebaseUser['photoURL'] ?? null,
                $firebaseUser['emailVerified'] ? 1 : 0
            ]);

            return [
                'id' => $db->lastInsertId(),
                'role' => 'merchant'
            ];
        }
    } catch (Exception $e) {
        throw new Exception('Erreur lors de la synchronisation de l'utilisateur: ' . $e->getMessage());
    }
}
