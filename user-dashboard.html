<!DOCTYPE html>
<html lang="fr" dir="ltr">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Mon Espace - صفحات هبوط للجميع</title>

    <!-- Bootstrap CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <!-- Font Awesome -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />

    <style>
      .sidebar {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        color: white;
      }

      .sidebar .nav-link {
        color: rgba(255, 255, 255, 0.8);
        border-radius: 8px;
        margin: 2px 0;
        transition: all 0.3s ease;
      }

      .sidebar .nav-link:hover,
      .sidebar .nav-link.active {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        transform: translateX(5px);
      }

      .main-content {
        background-color: #f8f9fa;
        min-height: 100vh;
      }

      .stat-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease;
      }

      .stat-card:hover {
        transform: translateY(-5px);
      }

      .stat-card.primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
      }
      .stat-card.success {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
      }
      .stat-card.warning {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
      }
      .stat-card.info {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        color: white;
      }

      .user-avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        object-fit: cover;
      }

      .welcome-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
      }
    </style>
  </head>

  <body>
    <!-- Loading Overlay -->
    <div
      id="loadingOverlay"
      class="position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center bg-white"
      style="z-index: 9999"
    >
      <div class="text-center">
        <div
          class="spinner-border text-primary"
          style="width: 3rem; height: 3rem"
          role="status"
        >
          <span class="visually-hidden">Chargement...</span>
        </div>
        <p class="mt-3 text-muted">Chargement de votre espace...</p>
      </div>
    </div>

    <!-- Auth Required -->
    <div
      id="authRequired"
      class="container-fluid vh-100 d-flex align-items-center justify-content-center"
      style="display: none"
    >
      <div class="row justify-content-center w-100">
        <div class="col-md-6 col-lg-4">
          <div class="card shadow-lg border-0">
            <div class="card-body text-center p-5">
              <i class="fas fa-lock fa-3x text-muted mb-3"></i>
              <h3>Connexion Requise</h3>
              <p class="text-muted mb-4">
                Connectez-vous pour accéder à votre espace personnel.
              </p>
              <div class="d-grid gap-2">
                <a href="login.html" class="btn btn-primary">
                  <i class="fas fa-sign-in-alt me-2"></i>Se connecter
                </a>
                <a href="register.html" class="btn btn-outline-primary">
                  <i class="fas fa-user-plus me-2"></i>Créer un compte
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main User Dashboard -->
    <div id="mainDashboard" style="display: none">
      <div class="container-fluid">
        <div class="row">
          <!-- Sidebar -->
          <div class="col-md-3 col-lg-2 sidebar p-3">
            <div class="text-center mb-4">
              <h4><i class="fas fa-rocket"></i> Mon Espace</h4>
              <small>صفحات هبوط للجميع</small>
            </div>

            <!-- User Info -->
            <div
              id="userInfo"
              class="text-center mb-4 p-3 rounded"
              style="background: rgba(255, 255, 255, 0.1)"
            >
              <img
                id="userAvatar"
                src=""
                alt="Avatar"
                class="user-avatar mb-2"
                style="display: none"
              />
              <div
                id="userInitials"
                class="user-avatar mb-2 d-flex align-items-center justify-content-center bg-white text-primary fw-bold fs-4 mx-auto"
              ></div>
              <h6 id="userName" class="mb-1"></h6>
              <small id="userEmail" class="text-light"></small>
              <div class="mt-2">
                <span id="userPlan" class="badge bg-warning">Plan Gratuit</span>
              </div>
            </div>

            <!-- Navigation -->
            <nav class="nav flex-column">
              <a
                class="nav-link active"
                href="#overview"
                onclick="showSection('overview')"
              >
                <i class="fas fa-home me-2"></i> Accueil
              </a>
              <a class="nav-link" href="#pages" onclick="showSection('pages')">
                <i class="fas fa-file-alt me-2"></i> Mes Pages
              </a>
              <a
                class="nav-link"
                href="#products"
                onclick="showSection('products')"
              >
                <i class="fas fa-box me-2"></i> Mes Produits
              </a>
              <a
                class="nav-link"
                href="#categories"
                onclick="showSection('categories')"
              >
                <i class="fas fa-tags me-2"></i> Catégories
              </a>
              <a
                class="nav-link"
                href="#analytics"
                onclick="showSection('analytics')"
              >
                <i class="fas fa-chart-bar me-2"></i> Statistiques
              </a>
              <a
                class="nav-link"
                href="#subscription"
                onclick="showSection('subscription')"
              >
                <i class="fas fa-crown me-2"></i> Mon Abonnement
              </a>
              <a
                class="nav-link"
                href="#profile"
                onclick="showSection('profile')"
              >
                <i class="fas fa-user me-2"></i> Mon Profil
              </a>
              <hr class="my-3" style="border-color: rgba(255, 255, 255, 0.3)" />
              <a class="nav-link" href="#" onclick="handleLogout()">
                <i class="fas fa-sign-out-alt me-2"></i> Déconnexion
              </a>
            </nav>
          </div>

          <!-- Main Content -->
          <div class="col-md-9 col-lg-10 main-content p-4">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
              <h2 id="pageTitle">Bienvenue dans votre espace</h2>
              <div class="d-flex align-items-center">
                <button class="btn btn-primary me-2" onclick="createNewPage()">
                  <i class="fas fa-plus me-1"></i>Nouvelle Page
                </button>
                <div class="dropdown">
                  <button
                    class="btn btn-outline-secondary dropdown-toggle"
                    type="button"
                    data-bs-toggle="dropdown"
                  >
                    <i class="fas fa-user-circle me-1"></i>
                  </button>
                  <ul class="dropdown-menu">
                    <li>
                      <a
                        class="dropdown-item"
                        href="#profile"
                        onclick="showSection('profile')"
                        >Mon Profil</a
                      >
                    </li>
                    <li>
                      <a
                        class="dropdown-item"
                        href="#subscription"
                        onclick="showSection('subscription')"
                        >Mon Abonnement</a
                      >
                    </li>
                    <li><hr class="dropdown-divider" /></li>
                    <li>
                      <a class="dropdown-item" href="#" onclick="handleLogout()"
                        >Déconnexion</a
                      >
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <!-- Overview Section (Default) -->
            <div id="overview-section" class="section-content">
              <!-- Welcome Card -->
              <div class="welcome-card p-4 mb-4">
                <div class="row align-items-center">
                  <div class="col-md-8">
                    <h3 class="mb-2">
                      Bienvenue <span id="welcomeUserName"></span> ! 👋
                    </h3>
                    <p class="mb-3">
                      Créez des pages de destination professionnelles en
                      quelques minutes.
                    </p>
                    <button class="btn btn-light" onclick="createNewPage()">
                      <i class="fas fa-plus me-1"></i>Créer ma première page
                    </button>
                  </div>
                  <div class="col-md-4 text-center">
                    <i class="fas fa-rocket fa-4x opacity-50"></i>
                  </div>
                </div>
              </div>

              <!-- Stats Cards -->
              <div class="row mb-4">
                <div class="col-md-3 mb-3">
                  <div class="card stat-card primary">
                    <div class="card-body text-center">
                      <i class="fas fa-file-alt fa-2x mb-2"></i>
                      <h3 id="userPagesCount">0</h3>
                      <p class="mb-0">Pages créées</p>
                    </div>
                  </div>
                </div>
                <div class="col-md-3 mb-3">
                  <div class="card stat-card success">
                    <div class="card-body text-center">
                      <i class="fas fa-box fa-2x mb-2"></i>
                      <h3 id="userProductsCount">0</h3>
                      <p class="mb-0">Produits</p>
                    </div>
                  </div>
                </div>
                <div class="col-md-3 mb-3">
                  <div class="card stat-card warning">
                    <div class="card-body text-center">
                      <i class="fas fa-eye fa-2x mb-2"></i>
                      <h3 id="userViewsCount">0</h3>
                      <p class="mb-0">Vues totales</p>
                    </div>
                  </div>
                </div>
                <div class="col-md-3 mb-3">
                  <div class="card stat-card info">
                    <div class="card-body text-center">
                      <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                      <h3 id="userOrdersCount">0</h3>
                      <p class="mb-0">Commandes</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Quick Actions -->
              <div class="row">
                <div class="col-md-6 mb-4">
                  <div class="card h-100">
                    <div class="card-header">
                      <h5><i class="fas fa-rocket me-2"></i>Actions Rapides</h5>
                    </div>
                    <div class="card-body">
                      <div class="d-grid gap-2">
                        <button
                          class="btn btn-outline-primary"
                          onclick="createNewPage()"
                        >
                          <i class="fas fa-plus me-2"></i>Créer une nouvelle
                          page
                        </button>
                        <button
                          class="btn btn-outline-success"
                          onclick="showSection('products')"
                        >
                          <i class="fas fa-box me-2"></i>Ajouter un produit
                        </button>
                        <button
                          class="btn btn-outline-info"
                          onclick="showSection('analytics')"
                        >
                          <i class="fas fa-chart-bar me-2"></i>Voir mes
                          statistiques
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-md-6 mb-4">
                  <div class="card h-100">
                    <div class="card-header">
                      <h5><i class="fas fa-crown me-2"></i>Mon Plan</h5>
                    </div>
                    <div class="card-body">
                      <div id="subscriptionInfo">
                        <p class="text-muted">Chargement des informations...</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Other sections will be loaded dynamically -->
            <div
              id="pages-section"
              class="section-content"
              style="display: none"
            >
              <h3>Mes Pages</h3>
              <p>Section en cours de développement...</p>
            </div>

            <div
              id="products-section"
              class="section-content"
              style="display: none"
            >
              <h3>Mes Produits</h3>
              <p>Section en cours de développement...</p>
            </div>

            <div
              id="categories-section"
              class="section-content"
              style="display: none"
            >
              <h3>Mes Catégories</h3>
              <p>Section en cours de développement...</p>
            </div>

            <div
              id="analytics-section"
              class="section-content"
              style="display: none"
            >
              <h3>Statistiques</h3>
              <p>Section en cours de développement...</p>
            </div>

            <div
              id="subscription-section"
              class="section-content"
              style="display: none"
            >
              <h3>Mon Abonnement</h3>
              <p>Section en cours de développement...</p>
            </div>

            <div
              id="profile-section"
              class="section-content"
              style="display: none"
            >
              <h3>Mon Profil</h3>
              <p>Section en cours de développement...</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-auth-compat.js"></script>
    <script src="/js/firebase-config.js"></script>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
      let currentUser = null;

      // Navigation
      function showSection(section) {
        // Hide all sections
        document
          .querySelectorAll(".section-content")
          .forEach((s) => (s.style.display = "none"));

        // Remove active class from all nav links
        document
          .querySelectorAll(".nav-link")
          .forEach((link) => link.classList.remove("active"));

        // Show selected section
        const sectionElement = document.getElementById(section + "-section");
        if (sectionElement) {
          sectionElement.style.display = "block";
        }

        // Add active class to current nav link
        const activeLink = document.querySelector(`[href="#${section}"]`);
        if (activeLink) {
          activeLink.classList.add("active");
        }

        // Update page title
        const titles = {
          overview: "Bienvenue dans votre espace",
          pages: "Mes Pages",
          products: "Mes Produits",
          categories: "Mes Catégories",
          analytics: "Statistiques",
          subscription: "Mon Abonnement",
          profile: "Mon Profil",
        };

        document.getElementById("pageTitle").textContent =
          titles[section] || "Mon Espace";
      }

      // Initialize page
      async function initializePage() {
        console.log("🚀 Initialisation de l'espace utilisateur");

        // Check if Firebase is available
        if (typeof firebase !== "undefined" && window.LandingPageFirebase) {
          const auth = window.LandingPageFirebase.getAuth();
          if (auth) {
            auth.onAuthStateChanged((user) => {
              handleAuthStateChange(user);
            });
          }
        } else {
          // For development, simulate a user
          setTimeout(() => {
            const mockUser = {
              uid: "demo_user_123",
              email: "<EMAIL>",
              displayName: "Utilisateur Demo",
              photoURL: null,
            };
            handleAuthStateChange(mockUser);
          }, 1000);
        }
      }

      // Handle auth state change
      async function handleAuthStateChange(user) {
        console.log("👤 État d'authentification changé:", user);

        const loadingOverlay = document.getElementById("loadingOverlay");
        const authRequired = document.getElementById("authRequired");
        const mainDashboard = document.getElementById("mainDashboard");

        if (user) {
          currentUser = user;

          // Sync user with database
          await syncUserWithDatabase(user);

          // Show user info
          showUserInfo(user);

          // Load user data
          await loadUserData();

          // Show dashboard
          loadingOverlay.style.display = "none";
          authRequired.style.display = "none";
          mainDashboard.style.display = "block";
        } else {
          currentUser = null;

          // Show auth required
          loadingOverlay.style.display = "none";
          authRequired.style.display = "block";
          mainDashboard.style.display = "none";
        }
      }

      // Sync user with database
      async function syncUserWithDatabase(user) {
        try {
          const response = await fetch(
            "/api/firebase-users.php?action=sync-user",
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                Authorization: "Bearer demo_token",
              },
              body: JSON.stringify({
                uid: user.uid,
                email: user.email,
                displayName: user.displayName,
                photoURL: user.photoURL,
                emailVerified: user.emailVerified,
                lastLoginAt: Date.now(),
              }),
            }
          );

          if (response.ok) {
            const data = await response.json();
            console.log("✅ Utilisateur synchronisé:", data);
          } else {
            console.error(
              "❌ Erreur lors de la synchronisation:",
              response.status
            );
          }
        } catch (error) {
          console.error("❌ Erreur lors de la synchronisation:", error);
        }
      }

      // Show user info
      function showUserInfo(user) {
        const userName = user.displayName || user.email.split("@")[0];
        const userEmail = user.email;

        document.getElementById("userName").textContent = userName;
        document.getElementById("userEmail").textContent = userEmail;
        document.getElementById("welcomeUserName").textContent = userName;

        // Vérifier si c'est un admin et ajouter un lien vers le dashboard admin
        const adminEmails = [
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
        ];

        if (adminEmails.includes(userEmail)) {
          // Ajouter un lien vers le dashboard admin
          const adminLink = document.createElement("div");
          adminLink.className = "mt-2";
          adminLink.innerHTML = `
                    <a href="/dashboard.html" class="btn btn-sm btn-outline-light">
                        <i class="fas fa-cog me-1"></i>Dashboard Admin
                    </a>
                `;
          document.getElementById("userInfo").appendChild(adminLink);
        }

        // Handle avatar
        if (user.photoURL) {
          document.getElementById("userAvatar").src = user.photoURL;
          document.getElementById("userAvatar").style.display = "block";
          document.getElementById("userInitials").style.display = "none";
        } else {
          const initials = userName
            .split(" ")
            .map((n) => n[0])
            .join("")
            .toUpperCase()
            .substring(0, 2);
          document.getElementById("userInitials").textContent = initials;
          document.getElementById("userInitials").style.display = "flex";
          document.getElementById("userAvatar").style.display = "none";
        }
      }

      // Load user data
      async function loadUserData() {
        try {
          // Load user stats (placeholder for now)
          document.getElementById("userPagesCount").textContent = "0";
          document.getElementById("userProductsCount").textContent = "0";
          document.getElementById("userViewsCount").textContent = "0";
          document.getElementById("userOrdersCount").textContent = "0";

          // Load subscription info
          document.getElementById("subscriptionInfo").innerHTML = `
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">Plan Gratuit</h6>
                            <small class="text-muted">5 pages • 10 produits</small>
                        </div>
                        <button class="btn btn-sm btn-outline-primary" onclick="showSection('subscription')">
                            Améliorer
                        </button>
                    </div>
                `;
        } catch (error) {
          console.error("❌ Erreur lors du chargement des données:", error);
        }
      }

      // Placeholder functions
      function createNewPage() {
        alert("Fonctionnalité de création de page en cours de développement");
      }

      function handleLogout() {
        if (typeof firebase !== "undefined" && window.LandingPageFirebase) {
          const auth = window.LandingPageFirebase.getAuth();
          auth.signOut().then(() => {
            window.location.href = "/";
          });
        } else {
          window.location.href = "/";
        }
      }

      // Initialize when page loads
      document.addEventListener("DOMContentLoaded", initializePage);
    </script>
  </body>
</html>
