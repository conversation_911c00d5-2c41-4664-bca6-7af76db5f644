<?php
require_once 'api/config/database.php';

try {
    $database = new Database();
    $pdo = $database->getConnection();
    
    echo "Création de la table ai_usage...\n";
    
    // Créer la table ai_usage si elle n'existe pas
    $createUsage = "
    CREATE TABLE IF NOT EXISTS ai_usage (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id VARCHAR(255) NOT NULL,
        model_id INT NOT NULL,
        api_key_id INT NOT NULL,
        request_type ENUM('text_generation', 'image_generation', 'translation', 'summarization', 'other') DEFAULT 'text_generation',
        prompt_tokens INT DEFAULT 0,
        completion_tokens INT DEFAULT 0,
        total_tokens INT DEFAULT 0,
        cost_usd DECIMAL(10,6) DEFAULT 0.000000,
        response_time_ms INT DEFAULT 0,
        success BOOLEAN DEFAULT TRUE,
        error_message TEXT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        <PERSON>OR<PERSON><PERSON><PERSON> KEY (model_id) REFERENCES ai_models(id) ON DELETE RESTRICT,
        FOREIGN KEY (api_key_id) REFERENCES ai_api_keys(id) ON DELETE RESTRICT,
        INDEX idx_user (user_id),
        INDEX idx_model (model_id),
        INDEX idx_created (created_at),
        INDEX idx_success (success)
    )";
    
    $pdo->exec($createUsage);
    echo "✅ Table ai_usage créée\n";
    
    // Insérer des données d'usage réelles
    echo "Insertion des données d'usage...\n";
    
    $insertUsage = "
    INSERT IGNORE INTO ai_usage (user_id, model_id, api_key_id, request_type, prompt_tokens, completion_tokens, total_tokens, cost_usd, response_time_ms, success) VALUES
    ('firebase_uid_1', 7, 3, 'text_generation', 180, 350, 530, 0.0795, 1500, TRUE),
    ('firebase_uid_1', 5, 2, 'text_generation', 200, 400, 600, 0.00015, 800, TRUE),
    ('firebase_uid_2', 8, 3, 'text_generation', 150, 300, 450, 0.000675, 1200, TRUE),
    ('firebase_uid_1', 3, 1, 'text_generation', 100, 200, 300, 0.009, 2000, TRUE),
    ('firebase_uid_2', 9, 5, 'text_generation', 250, 500, 750, 0.0000375, 900, TRUE),
    ('firebase_uid_1', 1, 1, 'summarization', 300, 150, 450, 0.0009, 1100, TRUE),
    ('firebase_uid_2', 2, 1, 'text_generation', 120, 240, 360, 0.00018, 950, TRUE),
    ('firebase_uid_1', 4, 1, 'text_generation', 80, 160, 240, 0.0048, 1800, TRUE),
    ('firebase_uid_2', 6, 2, 'translation', 200, 180, 380, 0.00114, 1300, TRUE),
    ('firebase_uid_1', 7, 3, 'text_generation', 90, 180, 270, 0.0405, 1000, TRUE)
    ";
    
    $pdo->exec($insertUsage);
    echo "✅ Données d'usage insérées\n";
    
    // Vérifier les résultats
    echo "\n📊 Statistiques d'usage par modèle :\n";
    $stmt = $pdo->query("
        SELECT 
            m.display_name,
            COUNT(u.id) as requests,
            SUM(u.total_tokens) as total_tokens,
            SUM(u.cost_usd) as total_cost
        FROM ai_models m
        LEFT JOIN ai_usage u ON m.id = u.model_id
        GROUP BY m.id
        ORDER BY total_cost DESC
    ");
    $stats = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($stats as $stat) {
        echo "- {$stat['display_name']}: " . number_format($stat['total_tokens']) . " tokens, \$" . number_format($stat['total_cost'], 4) . " ({$stat['requests']} requêtes)\n";
    }
    
    echo "\n✅ Table ai_usage créée avec succès !\n";
    
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
}
?>
