# Guide du Système de Quotas et Abonnements

## 📋 Vue d'ensemble

Ce système complet de gestion des quotas et abonnements permet de contrôler l'utilisation des ressources par les utilisateurs selon leur plan d'abonnement. Il inclut :

- **Gestion des rôles** : admin, seller, agent
- **Plans d'abonnement** avec limites configurables
- **Vérification automatique des quotas**
- **Interface utilisateur multilingue** (AR/FR/EN)
- **Tableau de bord administrateur**
- **API REST complète**

---

## 🗂️ Structure des fichiers

```
├── sql/
│   └── quota-system-schema.sql          # Schéma de base de données
├── php/
│   └── QuotaManager.php                 # Gestionnaire principal des quotas
├── api/
│   └── quota-api.php                    # API REST pour les quotas
├── components/
│   └── quota-widget.php                 # Widget d'affichage des quotas
├── admin/
│   └── subscriptions-manager.php        # Interface admin
├── examples/
│   └── create-product-with-quota.php    # Exemple d'intégration
└── docs/
    └── quota-system-guide.md             # Ce guide
```

---

## 🚀 Installation

### 1. Base de données

```sql
-- Exécuter le fichier SQL
source sql/quota-system-schema.sql;
```

### 2. Configuration

```php
// Dans votre config/database.php
define('DB_HOST', 'localhost');
define('DB_PORT', '3307');
define('DB_USERNAME', 'root');
define('DB_PASSWORD', '');
define('DB_DATABASE', 'landingpage_new');
define('DB_CHARSET', 'utf8mb4');
```

### 3. Inclusion des fichiers

```php
require_once 'php/QuotaManager.php';
require_once 'components/quota-widget.php';
```

---

## 🎯 Utilisation de base

### Vérifier les quotas avant création

```php
<?php
require_once 'php/QuotaManager.php';

$pdo = new PDO(/* vos paramètres de connexion */);
$quotaManager = new QuotaManager($pdo);

// Vérifier si l'utilisateur peut créer un produit
$userId = 1;
$canCreate = $quotaManager->canCreate($userId, 'products');

if ($canCreate['allowed']) {
    // Créer le produit
    echo "Création autorisée";
} else {
    // Afficher le message d'erreur
    echo "Erreur: " . $canCreate['message'];
}
?>
```

### Afficher le widget des quotas

```php
<?php
require_once 'components/quota-widget.php';

// Afficher le widget pour l'utilisateur connecté
$userId = $_SESSION['user_id'];
$language = 'ar'; // ou 'fr', 'en'
$showUpgradeButton = true;

renderQuotaWidget($userId, $language, $showUpgradeButton);
?>
```

### Utiliser l'API REST

```javascript
// Récupérer les quotas via JavaScript
async function loadUserQuotas(userId) {
    try {
        const response = await fetch(`/api/quota-api.php?action=get_quota_usage&user_id=${userId}`);
        const data = await response.json();
        
        if (data.success) {
            console.log('Quotas:', data.data);
            return data.data;
        }
    } catch (error) {
        console.error('Erreur:', error);
    }
}

// Vérifier avant création
async function checkQuotaBeforeCreate(userId, type) {
    try {
        const response = await fetch('/api/quota-api.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                action: 'check_creation',
                user_id: userId,
                type: type
            })
        });
        
        const data = await response.json();
        return data;
    } catch (error) {
        console.error('Erreur:', error);
        return { success: false, message: 'Erreur réseau' };
    }
}
```

---

## 📊 Gestion des abonnements

### Plans d'abonnement par défaut

| Plan | Prix | Produits | Landing Pages | Catégories |
|------|------|----------|---------------|------------|
| Gratuit | 0€ | 5 | 2 | 3 |
| Starter | 29€ | 50 | 10 | 10 |
| Pro | 99€ | 200 | 50 | 25 |
| Enterprise | 299€ | Illimité | Illimité | Illimité |

### Créer un nouveau plan

```sql
INSERT INTO subscriptions (name, price, max_products, max_landing_pages, max_categories, description)
VALUES ('Custom Plan', 149.99, 100, 25, 15, 'Plan personnalisé pour entreprises moyennes');
```

### Assigner un abonnement à un utilisateur

```php
$quotaManager->assignSubscription($userId, $subscriptionId);
```

---

## 🔧 API Endpoints

### Endpoints utilisateur

#### GET `/api/quota-api.php?action=get_quota_usage&user_id={id}`
Récupère l'utilisation des quotas pour un utilisateur.

**Réponse :**
```json
{
    "success": true,
    "data": {
        "subscription": {
            "name": "Pro",
            "max_products": 200,
            "max_landing_pages": 50,
            "max_categories": 25
        },
        "usage": {
            "products": { "current": 15, "max": 200, "percentage": 7.5 },
            "landing_pages": { "current": 3, "max": 50, "percentage": 6 },
            "categories": { "current": 8, "max": 25, "percentage": 32 }
        }
    }
}
```

#### POST `/api/quota-api.php` (action: check_creation)
Vérifie si un utilisateur peut créer un élément.

**Requête :**
```json
{
    "action": "check_creation",
    "user_id": 1,
    "type": "products"
}
```

**Réponse :**
```json
{
    "success": true,
    "allowed": true,
    "message": "Création autorisée",
    "remaining": 185
}
```

### Endpoints administrateur

#### GET `/api/quota-api.php?action=admin_stats`
Statistiques globales pour les administrateurs.

#### GET `/api/quota-api.php?action=admin_users&page=1&limit=20`
Liste paginée des utilisateurs avec leur utilisation.

#### POST `/api/quota-api.php` (action: force_upgrade)
Forcer la mise à niveau d'un utilisateur.

---

## 🎨 Interface utilisateur

### Widget des quotas

Le widget affiche automatiquement :
- Barres de progression pour chaque quota
- Pourcentages d'utilisation
- Alertes visuelles (vert/orange/rouge)
- Bouton de mise à niveau si nécessaire

### Intégration dans les formulaires

```php
// Avant d'afficher un formulaire de création
$canCreate = $quotaManager->canCreate($userId, 'products');

if (!$canCreate['allowed']) {
    // Afficher un message d'erreur
    echo '<div class="alert alert-danger">' . $canCreate['message'] . '</div>';
    
    // Désactiver le formulaire
    echo '<fieldset disabled>';
}
```

### Vérification JavaScript en temps réel

```javascript
// Vérifier les quotas lors de la saisie
document.getElementById('productForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const quotaCheck = await checkQuotaBeforeCreate(userId, 'products');
    
    if (quotaCheck.success && quotaCheck.allowed) {
        this.submit();
    } else {
        showQuotaError(quotaCheck.message);
    }
});
```

---

## 🌐 Support multilingue

### Langues supportées
- **Arabe (ar)** : Direction RTL, police Cairo
- **Français (fr)** : Direction LTR, police Inter
- **Anglais (en)** : Direction LTR, police Inter

### Traductions

Les traductions sont gérées dans chaque fichier :

```php
$translations = [
    'ar' => [
        'quota_exceeded' => 'تم تجاوز حد الاستخدام المسموح',
        'upgrade_required' => 'يتطلب ترقية الاشتراك'
    ],
    'fr' => [
        'quota_exceeded' => 'Quota dépassé',
        'upgrade_required' => 'Mise à niveau requise'
    ],
    'en' => [
        'quota_exceeded' => 'Quota exceeded',
        'upgrade_required' => 'Upgrade required'
    ]
];
```

---

## 🔒 Sécurité

### Validation des permissions

```php
// Vérifier le rôle avant les actions admin
if ($_SESSION['user_role'] !== 'admin') {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Accès refusé']);
    exit();
}
```

### Protection CSRF

```php
// Générer un token CSRF
$_SESSION['csrf_token'] = bin2hex(random_bytes(32));

// Vérifier le token
if (!hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'])) {
    throw new Exception('Token CSRF invalide');
}
```

### Validation des données

```php
// Valider l'ID utilisateur
$userId = filter_var($_POST['user_id'], FILTER_VALIDATE_INT);
if (!$userId) {
    throw new Exception('ID utilisateur invalide');
}

// Échapper les données pour SQL
$name = htmlspecialchars(trim($_POST['name']), ENT_QUOTES, 'UTF-8');
```

---

## 📈 Performance

### Cache des quotas

Le système utilise un cache pour éviter les requêtes répétées :

```php
// Mise à jour du cache après création
$quotaManager->updateQuotaCache($userId);

// Le cache est automatiquement utilisé lors des vérifications
$canCreate = $quotaManager->canCreate($userId, 'products');
```

### Optimisations SQL

- Index sur `user_id` dans toutes les tables
- Requêtes préparées pour éviter les injections
- Pagination pour les listes d'utilisateurs
- Agrégation des statistiques

---

## 🐛 Débogage

### Logs d'activité

Toutes les actions sont enregistrées :

```sql
SELECT * FROM activity_logs 
WHERE user_id = 1 
ORDER BY created_at DESC 
LIMIT 10;
```

### Mode debug

```php
// Activer le mode debug
define('QUOTA_DEBUG', true);

// Les erreurs détaillées seront affichées
$quotaManager = new QuotaManager($pdo, true);
```

### Vérification de l'intégrité

```php
// Vérifier la cohérence des quotas
$quotaManager->validateQuotaIntegrity($userId);
```

---

## 🔄 Migration et mise à jour

### Mise à jour des quotas existants

```sql
-- Recalculer tous les quotas
CALL RecalculateAllQuotas();

-- Mettre à jour un abonnement
UPDATE subscriptions 
SET max_products = 100 
WHERE name = 'Starter';
```

### Migration des données

```php
// Script de migration pour les utilisateurs existants
$users = $pdo->query("SELECT id FROM users WHERE subscription_id IS NULL");

foreach ($users as $user) {
    // Assigner l'abonnement gratuit par défaut
    $quotaManager->assignSubscription($user['id'], 1);
}
```

---

## 📞 Support

### Problèmes courants

1. **Quotas incorrects** : Vider le cache avec `updateQuotaCache()`
2. **Permissions refusées** : Vérifier le rôle utilisateur
3. **Erreurs de base de données** : Vérifier la connexion et les tables

### Contact

Pour toute question technique :
- Documentation : `/docs/`
- Exemples : `/examples/`
- API : `/api/quota-api.php?action=help`

---

## 📝 Changelog

### Version 1.0.0
- Système de quotas complet
- Support multilingue (AR/FR/EN)
- Interface administrateur
- API REST
- Widget utilisateur
- Documentation complète

---

*Ce guide couvre l'utilisation complète du système de quotas et abonnements. Pour des cas d'usage spécifiques, consultez les exemples dans le dossier `/examples/`.*