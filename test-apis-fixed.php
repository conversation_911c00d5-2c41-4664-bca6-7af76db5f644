<?php
/**
 * Test des APIs corrigées
 */

// Simuler les variables d'environnement
$_GET['store_id'] = '3';
$_SERVER['REQUEST_METHOD'] = 'GET';
$_SERVER['HTTP_AUTHORIZATION'] = 'Bearer demo_token';

echo "=== Test des APIs corrigées ===\n\n";

// Test 1: API Categories
echo "1. Test API Categories\n";
echo "---\n";

ob_start();
try {
    include 'api/categories.php';
    $output = ob_get_contents();
    ob_end_clean();
    
    echo "Sortie: " . substr($output, 0, 200) . "...\n";
    
    $data = json_decode($output, true);
    if ($data) {
        echo "✅ JSON valide\n";
        echo "Success: " . ($data['success'] ? 'true' : 'false') . "\n";
        if (isset($data['data'])) {
            echo "Nombre de catégories: " . count($data['data']) . "\n";
        }
    } else {
        echo "❌ JSON invalide: " . json_last_error_msg() . "\n";
        echo "Sortie complète: $output\n";
    }
} catch (Exception $e) {
    ob_end_clean();
    echo "❌ Exception: " . $e->getMessage() . "\n";
}

echo "\n" . str_repeat("-", 50) . "\n\n";

// Test 2: API Landing Pages
echo "2. Test API Landing Pages\n";
echo "---\n";

// Reset variables
$_GET = ['store_id' => '3'];
$_SERVER['REQUEST_METHOD'] = 'GET';
$_SERVER['HTTP_AUTHORIZATION'] = 'Bearer demo_token';

ob_start();
try {
    include 'api/landing-pages.php';
    $output = ob_get_contents();
    ob_end_clean();
    
    echo "Sortie: " . substr($output, 0, 200) . "...\n";
    
    $data = json_decode($output, true);
    if ($data) {
        echo "✅ JSON valide\n";
        echo "Success: " . ($data['success'] ? 'true' : 'false') . "\n";
        if (isset($data['data'])) {
            echo "Nombre de landing pages: " . count($data['data']) . "\n";
        }
    } else {
        echo "❌ JSON invalide: " . json_last_error_msg() . "\n";
        echo "Sortie complète: $output\n";
    }
} catch (Exception $e) {
    ob_end_clean();
    echo "❌ Exception: " . $e->getMessage() . "\n";
}

echo "\n" . str_repeat("-", 50) . "\n\n";

// Test 3: API Products (déjà fonctionnelle)
echo "3. Test API Products\n";
echo "---\n";

// Reset variables
$_GET = ['store_id' => '3'];
$_SERVER['REQUEST_METHOD'] = 'GET';

ob_start();
try {
    include 'api/products-simple.php';
    $output = ob_get_contents();
    ob_end_clean();
    
    echo "Sortie: " . substr($output, 0, 200) . "...\n";
    
    $data = json_decode($output, true);
    if ($data) {
        echo "✅ JSON valide\n";
        echo "Success: " . ($data['success'] ? 'true' : 'false') . "\n";
        if (isset($data['data'])) {
            echo "Nombre de produits: " . count($data['data']) . "\n";
        }
    } else {
        echo "❌ JSON invalide: " . json_last_error_msg() . "\n";
        echo "Sortie complète: $output\n";
    }
} catch (Exception $e) {
    ob_end_clean();
    echo "❌ Exception: " . $e->getMessage() . "\n";
}

echo "\n=== Fin des tests ===\n";
?>
