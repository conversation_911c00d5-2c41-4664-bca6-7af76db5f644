<?php
require_once 'php/config/database.php';

header('Content-Type: text/html; charset=UTF-8');

echo "<h1>🔧 Correction de l'encodage UTF-8</h1>";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .success{color:green;} .error{color:red;} .warning{color:orange;}</style>";

try {
    // 1. Vérifier l'encodage actuel de la base de données
    echo "<h2>📊 Vérification de l'encodage actuel</h2>";
    
    $stmt = $pdo->query("SELECT @@character_set_database, @@collation_database");
    $encoding = $stmt->fetch();
    echo "<p>Encodage de la base: " . $encoding['@@character_set_database'] . "</p>";
    echo "<p>Collation de la base: " . $encoding['@@collation_database'] . "</p>";
    
    // 2. Vérifier l'encodage des tables stores
    $stmt = $pdo->query("SHOW TABLE STATUS LIKE 'stores'");
    $table_info = $stmt->fetch();
    echo "<p>Collation de la table stores: " . $table_info['Collation'] . "</p>";
    
    // 3. Corriger l'encodage si nécessaire
    echo "<h2>🔧 Correction de l'encodage</h2>";
    
    // Modifier la base de données pour utiliser utf8mb4
    $pdo->exec("ALTER DATABASE landingpage_new CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "<p class='success'>✅ Base de données convertie en utf8mb4</p>";
    
    // Modifier la table stores
    $pdo->exec("ALTER TABLE stores CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "<p class='success'>✅ Table stores convertie en utf8mb4</p>";
    
    // Modifier la table products
    $pdo->exec("ALTER TABLE products CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "<p class='success'>✅ Table products convertie en utf8mb4</p>";
    
    // 4. Insérer des données de test en arabe correctement encodées
    echo "<h2>📝 Mise à jour des données de test</h2>";
    
    // Mettre à jour les noms en arabe avec un encodage correct
    $stmt = $pdo->prepare("UPDATE stores SET store_name_ar = ? WHERE id = 1");
    $stmt->execute(['متجر تجريبي']);
    
    $stmt = $pdo->prepare("UPDATE stores SET store_name_ar = ? WHERE id = 2");
    $stmt->execute(['متجر تجريبي 2']);
    
    echo "<p class='success'>✅ Noms des stores mis à jour avec un encodage correct</p>";
    
    // Mettre à jour les noms des produits en arabe
    $stmt = $pdo->prepare("UPDATE products SET name_ar = ? WHERE id = 1");
    $stmt->execute(['هاتف ذكي تجريبي']);
    
    $stmt = $pdo->prepare("UPDATE products SET name_ar = ? WHERE id = 2");
    $stmt->execute(['منتج تجريبي']);
    
    echo "<p class='success'>✅ Noms des produits mis à jour avec un encodage correct</p>";
    
    // 5. Vérifier le résultat
    echo "<h2>✅ Vérification du résultat</h2>";
    
    $stmt = $pdo->prepare("SELECT id, store_name, store_name_ar FROM stores");
    $stmt->execute();
    $stores = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse:collapse;'>";
    echo "<tr><th>ID</th><th>Store Name</th><th>Store Name AR</th></tr>";
    foreach ($stores as $store) {
        echo "<tr>";
        echo "<td>" . $store['id'] . "</td>";
        echo "<td>" . htmlspecialchars($store['store_name']) . "</td>";
        echo "<td dir='rtl'>" . htmlspecialchars($store['store_name_ar']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    $stmt = $pdo->prepare("SELECT id, name, name_ar FROM products");
    $stmt->execute();
    $products = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse:collapse;margin-top:20px;'>";
    echo "<tr><th>ID</th><th>Product Name</th><th>Product Name AR</th></tr>";
    foreach ($products as $product) {
        echo "<tr>";
        echo "<td>" . $product['id'] . "</td>";
        echo "<td>" . htmlspecialchars($product['name']) . "</td>";
        echo "<td dir='rtl'>" . htmlspecialchars($product['name_ar']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Erreur: " . $e->getMessage() . "</p>";
}
?>
