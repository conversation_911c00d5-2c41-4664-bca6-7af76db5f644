<?php
require_once 'config/database.php';

header('Content-Type: application/json; charset=UTF-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    // Connexion à la base de données
    $database = new Database();
    $pdo = $database->getConnection();

    $method = $_SERVER['REQUEST_METHOD'];
    $request_uri = $_SERVER['REQUEST_URI'];
    $path_parts = explode('/', trim(parse_url($request_uri, PHP_URL_PATH), '/'));

    switch ($method) {
        case 'GET':
            if (isset($path_parts[2]) && is_numeric($path_parts[2])) {
                // GET /api/roles/{id} - Obtenir un rôle spécifique
                getRole($pdo, $path_parts[2]);
            } else {
                // GET /api/roles - Obtenir tous les rôles
                getRoles($pdo);
            }
            break;

        case 'POST':
            // POST /api/roles - Créer un nouveau rôle
            createRole($pdo);
            break;

        case 'PUT':
            if (isset($path_parts[2]) && is_numeric($path_parts[2])) {
                // PUT /api/roles/{id} - Mettre à jour un rôle
                updateRole($pdo, $path_parts[2]);
            } else {
                ApiResponse::error('ID du rôle requis pour la mise à jour', 400);
            }
            break;

        case 'DELETE':
            if (isset($path_parts[2]) && is_numeric($path_parts[2])) {
                // DELETE /api/roles/{id} - Supprimer un rôle
                deleteRole($pdo, $path_parts[2]);
            } else {
                ApiResponse::error('ID du rôle requis pour la suppression', 400);
            }
            break;

        default:
            ApiResponse::error('Méthode non supportée', 405);
    }
} catch (Exception $e) {
    error_log("Error in roles API: " . $e->getMessage());
    ApiResponse::error('Erreur interne du serveur', 500);
}

/**
 * Obtenir tous les rôles
 */
function getRoles($pdo)
{
    try {
        $sql = "
            SELECT
                r.*,
                COUNT(ur.id) as user_count
            FROM roles r
            LEFT JOIN user_roles ur ON r.id = ur.role_id
            GROUP BY r.id
            ORDER BY r.is_system DESC, r.name
        ";

        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        $roles = $stmt->fetchAll();

        // Décoder les permissions JSON
        foreach ($roles as &$role) {
            $role['permissions'] = json_decode($role['permissions'], true);
            $role['user_count'] = intval($role['user_count']);
            $role['is_system'] = intval($role['is_system']);
        }

        ApiResponse::success([
            'roles' => $roles,
            'total' => count($roles)
        ]);
    } catch (Exception $e) {
        error_log("Error getting roles: " . $e->getMessage());
        ApiResponse::error('Erreur lors de la récupération des rôles', 500);
    }
}

/**
 * Obtenir un rôle spécifique
 */
function getRole($pdo, $role_id)
{
    try {
        $sql = "
            SELECT
                r.*,
                COUNT(ur.id) as user_count
            FROM roles r
            LEFT JOIN user_roles ur ON r.id = ur.role_id
            WHERE r.id = ?
            GROUP BY r.id
        ";

        $stmt = $pdo->prepare($sql);
        $stmt->execute([$role_id]);
        $role = $stmt->fetch();

        if (!$role) {
            ApiResponse::error('Rôle non trouvé', 404);
        }

        $role['permissions'] = json_decode($role['permissions'], true);
        $role['user_count'] = intval($role['user_count']);
        $role['is_system'] = intval($role['is_system']);

        ApiResponse::success($role);
    } catch (Exception $e) {
        error_log("Error getting role: " . $e->getMessage());
        ApiResponse::error('Erreur lors de la récupération du rôle', 500);
    }
}

/**
 * Créer un nouveau rôle
 */
function createRole($pdo)
{
    try {
        $input = json_decode(file_get_contents('php://input'), true);

        if (!$input || !isset($input['name']) || !isset($input['display_name']) || !isset($input['permissions'])) {
            ApiResponse::error('Données manquantes (name, display_name, permissions requis)', 400);
        }

        $sql = "
            INSERT INTO roles (name, display_name, description, permissions, is_system,
                             max_products, max_landing_pages, max_categories, max_subcategories)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ";

        $stmt = $pdo->prepare($sql);
        $stmt->execute([
            $input['name'],
            $input['display_name'],
            $input['description'] ?? '',
            json_encode($input['permissions']),
            $input['is_system'] ?? 0,
            $input['max_products'] ?? 10,
            $input['max_landing_pages'] ?? 1,
            $input['max_categories'] ?? 5,
            $input['max_subcategories'] ?? 10
        ]);

        $role_id = $pdo->lastInsertId();

        // Récupérer le rôle créé avec toutes ses données
        $stmt = $pdo->prepare("SELECT * FROM roles WHERE id = ?");
        $stmt->execute([$role_id]);
        $created_role = $stmt->fetch(PDO::FETCH_ASSOC);

        ApiResponse::success($created_role, 'Rôle créé avec succès');
    } catch (Exception $e) {
        error_log("Error creating role: " . $e->getMessage());
        ApiResponse::error('Erreur lors de la création du rôle: ' . $e->getMessage(), 500);
    }
}

/**
 * Mettre à jour un rôle
 */
function updateRole($pdo, $role_id)
{
    try {
        $input = json_decode(file_get_contents('php://input'), true);

        if (!$input) {
            ApiResponse::error('Données JSON invalides', 400);
        }

        // Vérifier si le rôle existe
        $stmt = $pdo->prepare("SELECT * FROM roles WHERE id = ?");
        $stmt->execute([$role_id]);
        $role = $stmt->fetch();

        if (!$role) {
            ApiResponse::error('Rôle non trouvé', 404);
        }

        // Empêcher la modification des rôles système critiques
        if ($role['is_system'] && in_array($role['name'], ['admin', 'merchant', 'customer'])) {
            ApiResponse::error('Les rôles système ne peuvent pas être modifiés', 403);
        }

        $sql = "
            UPDATE roles
            SET display_name = ?, description = ?, permissions = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        ";

        $stmt = $pdo->prepare($sql);
        $stmt->execute([
            $input['display_name'] ?? $role['display_name'],
            $input['description'] ?? $role['description'],
            json_encode($input['permissions'] ?? json_decode($role['permissions'], true)),
            $role_id
        ]);

        ApiResponse::success(null, 'Rôle mis à jour avec succès');
    } catch (Exception $e) {
        error_log("Error updating role: " . $e->getMessage());
        ApiResponse::error('Erreur lors de la mise à jour du rôle', 500);
    }
}

/**
 * Supprimer un rôle
 */
function deleteRole($pdo, $role_id)
{
    try {
        // Vérifier si le rôle existe
        $stmt = $pdo->prepare("SELECT * FROM roles WHERE id = ?");
        $stmt->execute([$role_id]);
        $role = $stmt->fetch();

        if (!$role) {
            ApiResponse::error('Rôle non trouvé', 404);
        }

        // Empêcher la suppression des rôles système
        if ($role['is_system']) {
            ApiResponse::error('Les rôles système ne peuvent pas être supprimés', 403);
        }

        // Vérifier s'il y a des utilisateurs avec ce rôle
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM user_roles WHERE role_id = ?");
        $stmt->execute([$role_id]);
        $count = $stmt->fetch()['count'];

        if ($count > 0) {
            ApiResponse::error('Impossible de supprimer un rôle assigné à des utilisateurs', 409);
        }

        $stmt = $pdo->prepare("DELETE FROM roles WHERE id = ?");
        $stmt->execute([$role_id]);

        ApiResponse::success(null, 'Rôle supprimé avec succès');
    } catch (Exception $e) {
        error_log("Error deleting role: " . $e->getMessage());
        ApiResponse::error('Erreur lors de la suppression du rôle', 500);
    }
}
