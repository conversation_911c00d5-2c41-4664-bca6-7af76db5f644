<?php

/**
 * Stores API Endpoint
 * Handles store management and settings
 */

require_once 'config/database.php';

// Set headers
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    $database = new Database();
    $db = $database->getConnection();
    $auth = new Auth($db);

    // Get request method and path
    $method = $_SERVER['REQUEST_METHOD'];
    $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
    $pathParts = explode('/', trim($path, '/'));

    // Extract action from path (e.g., /api/stores, /api/stores/settings)
    $action = isset($pathParts[2]) ? $pathParts[2] : '';
    $id = isset($pathParts[3]) ? intval($pathParts[3]) : null;

    // Authenticate user
    $headers = function_exists('getallheaders') ? getallheaders() : [];

    // Fallback for CLI and some servers
    if (empty($headers) && isset($_SERVER['HTTP_AUTHORIZATION'])) {
        $headers['Authorization'] = $_SERVER['HTTP_AUTHORIZATION'];
    }

    $token = isset($headers['Authorization']) ? str_replace('Bearer ', '', $headers['Authorization']) : null;
    $user_id = $auth->verifyFirebaseToken($token);
    $store = $auth->getUserStore($user_id);

    switch ($method) {
        case 'GET':
            if ($action === 'settings' || $action === '') {
                getUserStore($db, $auth, $user_id);
            } elseif ($action === 'all' && $store['role'] === 'admin') {
                getAllStores($db, $_GET);
            } else {
                ApiResponse::error('Invalid endpoint', 404);
            }
            break;

        case 'POST':
            $input = json_decode(file_get_contents('php://input'), true);
            createStore($db, $user_id, $input);
            break;

        case 'PUT':
            $input = json_decode(file_get_contents('php://input'), true);
            if ($action === 'settings' || $action === '') {
                updateStore($db, $auth, $user_id, $input);
            } else {
                ApiResponse::error('Invalid endpoint', 404);
            }
            break;

        case 'DELETE':
            if ($store['role'] === 'admin' && $id) {
                deleteStore($db, $id);
            } else {
                ApiResponse::error('Admin permission required', 403);
            }
            break;

        default:
            ApiResponse::error('Method not allowed', 405);
    }
} catch (Exception $e) {
    error_log('Stores API Error: ' . $e->getMessage());
    ApiResponse::error('Internal server error', 500);
}

/**
 * Get user's store information
 */
function getUserStore($db, $auth, $user_id)
{
    try {
        $store = $auth->getUserStore($user_id);

        if (!$store) {
            ApiResponse::error('Store not found', 404);
        }

        // Decode JSON fields
        $store['social_media'] = json_decode($store['social_media'], true);
        $store['verification_documents'] = json_decode($store['verification_documents'], true);
        $store['settings'] = json_decode($store['settings'], true);

        // Get store statistics
        $stats_query = "
            SELECT
                COUNT(DISTINCT p.id) as total_products,
                COUNT(DISTINCT o.id) as total_orders,
                COALESCE(SUM(o.total_amount), 0) as total_revenue,
                COUNT(DISTINCT o.customer_email) as total_customers
            FROM stores s
            LEFT JOIN products p ON s.id = p.store_id
            LEFT JOIN orders o ON s.id = o.store_id
            WHERE s.id = ?
        ";

        $stats_stmt = $db->prepare($stats_query);
        $stats_stmt->execute([$store['id']]);
        $stats = $stats_stmt->fetch(PDO::FETCH_ASSOC);

        $store['statistics'] = $stats;

        ApiResponse::success($store);
    } catch (Exception $e) {
        error_log('Get user store error: ' . $e->getMessage());
        ApiResponse::error('Failed to retrieve store information');
    }
}

/**
 * Get all stores (admin only)
 */
function getAllStores($db, $params)
{
    try {
        $page = isset($params['page']) ? max(1, intval($params['page'])) : 1;
        $limit = isset($params['limit']) ? min(100, max(1, intval($params['limit']))) : 20;
        $offset = ($page - 1) * $limit;

        $where_conditions = [];
        $where_params = [];

        // Add filters
        if (!empty($params['status'])) {
            $where_conditions[] = 'status = ?';
            $where_params[] = $params['status'];
        }

        if (!empty($params['verification_status'])) {
            $where_conditions[] = 'verification_status = ?';
            $where_params[] = $params['verification_status'];
        }

        if (!empty($params['search'])) {
            $where_conditions[] = '(store_name LIKE ? OR store_name_ar LIKE ? OR email LIKE ?)';
            $search_term = '%' . $params['search'] . '%';
            $where_params[] = $search_term;
            $where_params[] = $search_term;
            $where_params[] = $search_term;
        }

        $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

        // Get total count
        $count_query = "SELECT COUNT(*) as total FROM stores $where_clause";
        $count_stmt = $db->prepare($count_query);
        $count_stmt->execute($where_params);
        $total = $count_stmt->fetch(PDO::FETCH_ASSOC)['total'];

        // Get stores with statistics
        $query = "
            SELECT
                s.*,
                COUNT(DISTINCT p.id) as total_products,
                COUNT(DISTINCT o.id) as total_orders,
                COALESCE(SUM(o.total_amount), 0) as total_revenue
            FROM stores s
            LEFT JOIN products p ON s.id = p.store_id
            LEFT JOIN orders o ON s.id = o.store_id
            $where_clause
            GROUP BY s.id
            ORDER BY s.created_at DESC
            LIMIT ? OFFSET ?
        ";

        $stmt = $db->prepare($query);
        $stmt->execute(array_merge($where_params, [$limit, $offset]));
        $stores = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Decode JSON fields for each store
        foreach ($stores as &$store) {
            $store['social_media'] = json_decode($store['social_media'], true);
            $store['verification_documents'] = json_decode($store['verification_documents'], true);
            $store['settings'] = json_decode($store['settings'], true);
        }

        ApiResponse::success([
            'stores' => $stores,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => $total,
                'pages' => ceil($total / $limit)
            ]
        ]);
    } catch (Exception $e) {
        error_log('Get all stores error: ' . $e->getMessage());
        ApiResponse::error('Failed to retrieve stores');
    }
}

/**
 * Create a new store
 */
function createStore($db, $user_id, $data)
{
    try {
        // Check if user already has a store
        $existing_query = "SELECT id FROM stores WHERE user_id = ?";
        $existing_stmt = $db->prepare($existing_query);
        $existing_stmt->execute([$user_id]);

        if ($existing_stmt->fetch()) {
            ApiResponse::error('User already has a store', 400);
        }

        // Validate required fields
        if (empty($data['store_name_ar']) && empty($data['store_name_en'])) {
            ApiResponse::error('Store name is required in Arabic or English', 400);
        }

        $db->beginTransaction();

        try {
            // Create store
            $query = "
                INSERT INTO stores (
                    user_id, store_name, store_name_ar, store_name_fr, store_name_en,
                    description, description_ar, description_fr, description_en,
                    phone, email, address, address_ar, address_fr, address_en,
                    logo_url, banner_url, website_url, social_media,
                    business_type, category, status, verification_status,
                    verification_documents, settings
                ) VALUES (
                    ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
                )
            ";

            $stmt = $db->prepare($query);
            $stmt->execute([
                $user_id,
                $data['store_name'],
                isset($data['store_name_ar']) ? $data['store_name_ar'] : null,
                isset($data['store_name_fr']) ? $data['store_name_fr'] : null,
                isset($data['store_name_en']) ? $data['store_name_en'] : null,
                isset($data['description']) ? $data['description'] : null,
                isset($data['description_ar']) ? $data['description_ar'] : null,
                isset($data['description_fr']) ? $data['description_fr'] : null,
                isset($data['description_en']) ? $data['description_en'] : null,
                isset($data['phone']) ? $data['phone'] : null,
                isset($data['email']) ? $data['email'] : null,
                isset($data['address']) ? $data['address'] : null,
                isset($data['address_ar']) ? $data['address_ar'] : null,
                isset($data['address_fr']) ? $data['address_fr'] : null,
                isset($data['address_en']) ? $data['address_en'] : null,
                isset($data['logo_url']) ? $data['logo_url'] : null,
                isset($data['banner_url']) ? $data['banner_url'] : null,
                isset($data['website_url']) ? $data['website_url'] : null,
                isset($data['social_media']) ? json_encode($data['social_media']) : null,
                isset($data['business_type']) ? $data['business_type'] : null,
                isset($data['category']) ? $data['category'] : null,
                'pending',
                'unverified',
                null,
                isset($data['settings']) ? json_encode($data['settings']) : json_encode([])
            ]);

            $store_id = $db->lastInsertId();

            // Create user role
            $role_query = "INSERT INTO user_roles (user_id, role, store_id, is_active) VALUES (?, 'seller', ?, 1)";
            $role_stmt = $db->prepare($role_query);
            $role_stmt->execute([$user_id, $store_id]);

            // Create default payment methods
            $default_methods = [
                ['baridimob', 'بريدي موب', '{"number": "", "name": "", "instructions": "قم بالتحويل إلى رقم بريدي موب وأرسل إثبات الدفع"}'],
                ['ccp', 'حساب جاري بريدي', '{"account_number": "", "account_name": "", "instructions": "قم بالتحويل إلى الحساب الجاري وأرسل إثبات الدفع"}'],
                ['bank_transfer', 'تحويل مصرفي', '{"bank_name": "", "account_number": "", "account_name": "", "instructions": "قم بالتحويل المصرفي وأرسل إثبات الدفع"}'],
                ['cod', 'دفع عند التسليم', '{"additional_fee": 0, "available_areas": [], "instructions": "سيتم الدفع عند استلام الطلب"}']
            ];

            $payment_query = "INSERT INTO payment_methods (store_id, method_type, method_name, configuration, is_active, display_order) VALUES (?, ?, ?, ?, 0, ?)";
            $payment_stmt = $db->prepare($payment_query);

            foreach ($default_methods as $index => $method) {
                $payment_stmt->execute([$store_id, $method[0], $method[1], $method[2], $index + 1]);
            }

            $db->commit();

            // Get the created store
            $get_query = "SELECT * FROM stores WHERE id = ?";
            $get_stmt = $db->prepare($get_query);
            $get_stmt->execute([$store_id]);
            $store = $get_stmt->fetch(PDO::FETCH_ASSOC);

            $store['social_media'] = json_decode($store['social_media'], true);
            $store['verification_documents'] = json_decode($store['verification_documents'], true);
            $store['settings'] = json_decode($store['settings'], true);

            ApiResponse::success($store, 'Store created successfully', 201);
        } catch (Exception $e) {
            $db->rollBack();
            throw $e;
        }
    } catch (Exception $e) {
        error_log('Create store error: ' . $e->getMessage());
        ApiResponse::error('Failed to create store');
    }
}

/**
 * Update store information
 */
function updateStore($db, $auth, $user_id, $data)
{
    try {
        $store = $auth->getUserStore($user_id);

        if (!$store) {
            ApiResponse::error('Store not found', 404);
        }

        $update_fields = [];
        $update_params = [];

        // Define allowed fields for update
        $allowed_fields = [
            'store_name',
            'store_name_ar',
            'store_name_fr',
            'store_name_en',
            'description',
            'description_ar',
            'description_fr',
            'description_en',
            'phone',
            'email',
            'address',
            'address_ar',
            'address_fr',
            'address_en',
            'logo_url',
            'banner_url',
            'website_url',
            'business_type',
            'category'
        ];

        foreach ($allowed_fields as $field) {
            if (isset($data[$field])) {
                $update_fields[] = "$field = ?";
                $update_params[] = $data[$field];
            }
        }

        // Handle JSON fields
        if (isset($data['social_media'])) {
            $update_fields[] = 'social_media = ?';
            $update_params[] = json_encode($data['social_media']);
        }

        if (isset($data['settings'])) {
            $update_fields[] = 'settings = ?';
            $update_params[] = json_encode($data['settings']);
        }

        if (isset($data['verification_documents'])) {
            $update_fields[] = 'verification_documents = ?';
            $update_params[] = json_encode($data['verification_documents']);
        }

        if (empty($update_fields)) {
            ApiResponse::error('No fields to update', 400);
        }

        $update_fields[] = 'updated_at = NOW()';
        $update_params[] = $store['id'];

        $query = "UPDATE stores SET " . implode(', ', $update_fields) . " WHERE id = ?";
        $stmt = $db->prepare($query);
        $stmt->execute($update_params);

        // Get updated store
        $get_query = "SELECT * FROM stores WHERE id = ?";
        $get_stmt = $db->prepare($get_query);
        $get_stmt->execute([$store['id']]);
        $updated_store = $get_stmt->fetch(PDO::FETCH_ASSOC);

        $updated_store['social_media'] = json_decode($updated_store['social_media'], true);
        $updated_store['verification_documents'] = json_decode($updated_store['verification_documents'], true);
        $updated_store['settings'] = json_decode($updated_store['settings'], true);

        ApiResponse::success($updated_store, 'Store updated successfully');
    } catch (Exception $e) {
        error_log('Update store error: ' . $e->getMessage());
        ApiResponse::error('Failed to update store');
    }
}

/**
 * Delete a store (admin only)
 */
function deleteStore($db, $store_id)
{
    try {
        // Check if store exists
        $check_query = "SELECT id FROM stores WHERE id = ?";
        $check_stmt = $db->prepare($check_query);
        $check_stmt->execute([$store_id]);

        if (!$check_stmt->fetch()) {
            ApiResponse::error('Store not found', 404);
        }

        // Check if store has orders
        $orders_query = "SELECT COUNT(*) as count FROM orders WHERE store_id = ?";
        $orders_stmt = $db->prepare($orders_query);
        $orders_stmt->execute([$store_id]);
        $orders_count = $orders_stmt->fetch(PDO::FETCH_ASSOC)['count'];

        if ($orders_count > 0) {
            ApiResponse::error('Cannot delete store with existing orders', 400);
        }

        $db->beginTransaction();

        try {
            // Delete related data
            $tables = ['payment_methods', 'ai_keys', 'products', 'store_analytics', 'user_roles'];

            foreach ($tables as $table) {
                $delete_query = "DELETE FROM $table WHERE store_id = ?";
                $delete_stmt = $db->prepare($delete_query);
                $delete_stmt->execute([$store_id]);
            }

            // Delete store
            $store_query = "DELETE FROM stores WHERE id = ?";
            $store_stmt = $db->prepare($store_query);
            $store_stmt->execute([$store_id]);

            $db->commit();

            ApiResponse::success(null, 'Store deleted successfully');
        } catch (Exception $e) {
            $db->rollBack();
            throw $e;
        }
    } catch (Exception $e) {
        error_log('Delete store error: ' . $e->getMessage());
        ApiResponse::error('Failed to delete store');
    }
}
