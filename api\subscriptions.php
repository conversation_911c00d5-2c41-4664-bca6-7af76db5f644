<?php

/**
 * Subscriptions API Endpoint
 * Handles subscription management
 */

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/error.log');

require_once __DIR__ . '/../config/database.php';

// Set headers
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// <PERSON>le preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    $database = new Database();
    $db = $database->getConnection();

    // Get request method and path
    $method = $_SERVER['REQUEST_METHOD'];
    $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
    $pathParts = explode('/', trim($path, '/'));

    // Extract action from path or query parameters
    $action = isset($pathParts[2]) ? $pathParts[2] : (isset($_GET['action']) ? $_GET['action'] : '');
    $id = isset($pathParts[3]) ? $pathParts[3] : (isset($_GET['id']) ? $_GET['id'] : null);

    switch ($method) {
        case 'GET':
            if ($action === 'plans' || $action === 'all' || $action === '') {
                getAllSubscriptionPlans($db);
            } else {
                http_response_code(404);
                echo json_encode(['error' => 'Action non supportée']);
            }
            break;

        default:
            http_response_code(405);
            echo json_encode(['error' => 'Méthode non supportée']);
    }
} catch (Exception $e) {
    error_log('Subscriptions API Error: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ]);
}

/**
 * Get all subscription plans
 */
function getAllSubscriptionPlans($db)
{
    try {
        $query = "SELECT 
                    id,
                    name,
                    name_fr,
                    name_en,
                    description,
                    description_fr,
                    description_en,
                    price,
                    currency,
                    billing_cycle,
                    features,
                    max_products,
                    max_categories,
                    max_landing_pages,
                    max_storage_mb,
                    status,
                    created_at,
                    updated_at
                FROM subscription_plans
                WHERE status = 'active'
                ORDER BY price ASC";

        $stmt = $db->prepare($query);
        $stmt->execute();
        $plans = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Process the data
        foreach ($plans as &$plan) {
            $plan['features'] = json_decode($plan['features'], true);
            $plan['max_products'] = (int)$plan['max_products'];
            $plan['max_categories'] = (int)$plan['max_categories'];
            $plan['max_landing_pages'] = (int)$plan['max_landing_pages'];
            $plan['max_storage_mb'] = (int)$plan['max_storage_mb'];
        }

        echo json_encode([
            'success' => true,
            'data' => [
                'plans' => $plans,
                'total' => count($plans)
            ]
        ]);
    } catch (PDOException $e) {
        error_log("Error getting subscription plans: " . $e->getMessage());
        http_response_code(500);
        throw new Exception('Erreur lors de la récupération des plans');
    }
}
