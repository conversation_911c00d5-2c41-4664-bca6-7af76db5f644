<?php

/**
 * Subscriptions API Endpoint
 * Handles subscription management
 */

require_once 'config/database.php';

// Set headers
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    $database = new Database();
    $db = $database->getConnection();

    // Get request method and path
    $method = $_SERVER['REQUEST_METHOD'];
    $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
    $pathParts = explode('/', trim($path, '/'));

    // Extract action from path or query parameters
    $action = isset($pathParts[2]) ? $pathParts[2] : (isset($_GET['action']) ? $_GET['action'] : '');
    $id = isset($pathParts[3]) ? $pathParts[3] : (isset($_GET['id']) ? $_GET['id'] : null);

    // Simple authentication check
    $headers = function_exists('getallheaders') ? getallheaders() : [];
    if (empty($headers) && isset($_SERVER['HTTP_AUTHORIZATION'])) {
        $headers['Authorization'] = $_SERVER['HTTP_AUTHORIZATION'];
    }
    $token = isset($headers['Authorization']) ? str_replace('Bearer ', '', $headers['Authorization']) : null;

    // For demo purposes, accept demo_token or skip auth for some actions
    if ($token !== 'demo_token' && !in_array($action, ['plans', 'all', 'details'])) {
        http_response_code(401);
        echo json_encode(['error' => 'Token d\'authentification requis']);
        exit;
    }

    switch ($method) {
        case 'GET':
            if ($action === 'plans' || $action === 'all' || $action === '') {
                getAllSubscriptionPlans($db);
            } elseif ($action === 'details' && $id) {
                getSubscriptionDetails($db, $id);
            } elseif ($action === 'stats') {
                // Get subscription statistics
                $stats = [];

                // Total active subscriptions
                $query = "SELECT COUNT(*) as total FROM subscriptions WHERE status = 'active'";
                $stmt = $db->prepare($query);
                $stmt->execute();
                $stats['total_active'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

                // Monthly revenue
                $query = "
                    SELECT COALESCE(SUM(p.price), 0) as monthly_revenue
                    FROM subscriptions s
                    JOIN subscription_plans p ON s.plan_id = p.id
                    WHERE s.status = 'active'
                ";
                $stmt = $db->prepare($query);
                $stmt->execute();
                $stats['monthly_revenue'] = $stmt->fetch(PDO::FETCH_ASSOC)['monthly_revenue'];

                // New subscriptions this month
                $query = "
                    SELECT COUNT(*) as new_this_month
                    FROM subscriptions
                    WHERE DATE_FORMAT(created_at, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-%m')
                ";
                $stmt = $db->prepare($query);
                $stmt->execute();
                $stats['new_this_month'] = $stmt->fetch(PDO::FETCH_ASSOC)['new_this_month'];

                echo json_encode([
                    'success' => true,
                    'data' => $stats
                ]);
            } else {
                http_response_code(404);
                echo json_encode(['error' => 'Action non supportée']);
            }
            break;

        case 'POST':
            $input = json_decode(file_get_contents('php://input'), true);
            if ($action === 'create') {
                createSubscriptionPlan($db, $input);
            } else {
                http_response_code(404);
                echo json_encode(['error' => 'Action non supportée']);
            }
            break;

        case 'PUT':
            $input = json_decode(file_get_contents('php://input'), true);
            if ($action === 'update' && $id) {
                updateSubscriptionPlan($db, $id, $input);
            } else {
                http_response_code(404);
                echo json_encode(['error' => 'Action non supportée']);
            }
            break;

        case 'DELETE':
            if ($action === 'delete' && $id) {
                deleteSubscriptionPlan($db, $id);
            } else {
                http_response_code(404);
                echo json_encode(['error' => 'Action non supportée']);
            }
            break;

        default:
            http_response_code(405);
            echo json_encode(['error' => 'Méthode non supportée']);
    }
} catch (Exception $e) {
    error_log('Subscriptions API Error: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
}

/**
 * Get all subscription plans
 */
function getAllSubscriptionPlans($db)
{
    try {
        $query = "
            SELECT
                s.*,
                COUNT(us.id) as active_subscribers
            FROM subscriptions s
            LEFT JOIN user_subscriptions us ON s.id = us.subscription_id AND us.status = 'active'
            WHERE s.is_active = 1
            GROUP BY s.id
            ORDER BY s.sort_order, s.price
        ";

        $stmt = $db->prepare($query);
        $stmt->execute();
        $plans = $stmt->fetchAll();

        // Process the data
        foreach ($plans as &$plan) {
            $plan['features'] = json_decode($plan['features'], true);
            $plan['ai_models'] = json_decode($plan['ai_models'], true);
            $plan['active_subscribers'] = (int)$plan['active_subscribers'];
            $plan['is_active'] = (bool)$plan['is_active'];
            $plan['is_featured'] = (bool)$plan['is_featured'];
            $plan['ai_enabled'] = (bool)$plan['ai_enabled'];
            $plan['custom_domain'] = (bool)$plan['custom_domain'];
            $plan['analytics_advanced'] = (bool)$plan['analytics_advanced'];
            $plan['white_label'] = (bool)$plan['white_label'];
            $plan['api_access'] = (bool)$plan['api_access'];
        }

        echo json_encode([
            'success' => true,
            'data' => [
                'plans' => $plans,
                'total' => count($plans)
            ]
        ]);
    } catch (Exception $e) {
        error_log("Error getting subscription plans: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la récupération des plans']);
    }
}

/**
 * Get user subscription
 */
function getUserSubscription($db, $user_id)
{
    try {
        $query = "
            SELECT
                us.*,
                s.name as plan_name,
                s.display_name as plan_display_name,
                s.price as plan_price,
                s.max_products,
                s.max_landing_pages,
                s.max_categories,
                s.ai_enabled,
                s.ai_monthly_tokens
            FROM user_subscriptions us
            JOIN subscriptions s ON us.subscription_id = s.id
            WHERE us.user_id = ? AND us.status = 'active'
            ORDER BY us.created_at DESC
            LIMIT 1
        ";

        $stmt = $db->prepare($query);
        $stmt->execute([$user_id]);
        $subscription = $stmt->fetch();

        if (!$subscription) {
            http_response_code(404);
            echo json_encode(['error' => 'Aucun abonnement actif trouvé']);
            return;
        }

        echo json_encode(['success' => true, 'data' => $subscription]);
    } catch (Exception $e) {
        error_log("Error getting user subscription: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la récupération de l\'abonnement']);
    }
}

/**
 * Subscribe user to a plan
 */
function subscribeUser($db, $data)
{
    try {
        // Validate required fields
        if (!isset($data['user_id']) || !isset($data['subscription_id'])) {
            http_response_code(400);
            echo json_encode(['error' => 'user_id et subscription_id requis']);
            return;
        }

        // Check if plan exists
        $stmt = $db->prepare("SELECT * FROM subscriptions WHERE id = ? AND is_active = 1");
        $stmt->execute([$data['subscription_id']]);
        $plan = $stmt->fetch();

        if (!$plan) {
            http_response_code(404);
            echo json_encode(['error' => 'Plan d\'abonnement non trouvé']);
            return;
        }

        // Cancel existing active subscription
        $db->prepare("UPDATE user_subscriptions SET status = 'cancelled', cancelled_at = NOW() WHERE user_id = ? AND status = 'active'")->execute([$data['user_id']]);

        // Create new subscription
        $expires_at = date('Y-m-d H:i:s', strtotime('+1 month'));
        $next_payment = date('Y-m-d H:i:s', strtotime('+1 month'));

        $query = "
            INSERT INTO user_subscriptions
            (user_id, subscription_id, status, expires_at, next_payment_at, payment_method)
            VALUES (?, ?, 'active', ?, ?, ?)
        ";

        $stmt = $db->prepare($query);
        $stmt->execute([
            $data['user_id'],
            $data['subscription_id'],
            $expires_at,
            $next_payment,
            $data['payment_method'] ?? 'manual'
        ]);

        $subscription_id = $db->lastInsertId();

        // Get the created subscription
        $stmt = $db->prepare("
            SELECT us.*, s.display_name as plan_name
            FROM user_subscriptions us
            JOIN subscriptions s ON us.subscription_id = s.id
            WHERE us.id = ?
        ");
        $stmt->execute([$subscription_id]);
        $created_subscription = $stmt->fetch();

        echo json_encode(['success' => true, 'data' => $created_subscription, 'message' => 'Abonnement créé avec succès']);
    } catch (Exception $e) {
        error_log("Error subscribing user: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la création de l\'abonnement: ' . $e->getMessage()]);
    }
}

/**
 * Get subscription details
 */
function getSubscriptionDetails($db, $id)
{
    try {
        $query = "
            SELECT
                s.*,
                COUNT(us.id) as active_users
            FROM subscriptions s
            LEFT JOIN user_subscriptions us ON s.id = us.subscription_id AND us.status = 'active'
            WHERE s.id = ?
            GROUP BY s.id
        ";

        $stmt = $db->prepare($query);
        $stmt->execute([$id]);
        $subscription = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$subscription) {
            http_response_code(404);
            echo json_encode(['error' => 'Abonnement non trouvé']);
            return;
        }

        echo json_encode([
            'success' => true,
            'data' => $subscription
        ]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la récupération des détails: ' . $e->getMessage()]);
    }
}

/**
 * Create new subscription plan
 */
function createSubscriptionPlan($db, $data)
{
    try {
        $query = "
            INSERT INTO subscriptions (
                name, description, price, max_products, max_pages,
                max_categories, max_subcategories, max_ai_tokens,
                features, is_active, created_at
            ) VALUES (
                ?, ?, ?, ?, ?, ?, ?, ?, ?, 1, NOW()
            )
        ";

        $features = json_encode([
            'custom_domain' => false,
            'analytics' => true,
            'support' => 'email',
            'api_access' => false
        ]);

        $stmt = $db->prepare($query);
        $stmt->execute([
            $data['name'],
            $data['description'] ?? '',
            $data['price'],
            $data['max_products'] ?? 10,
            $data['max_pages'] ?? 5,
            $data['max_categories'] ?? 5,
            $data['max_subcategories'] ?? 10,
            $data['max_ai_tokens'] ?? 1000,
            $features
        ]);

        $subscriptionId = $db->lastInsertId();

        echo json_encode([
            'success' => true,
            'data' => [
                'id' => $subscriptionId,
                'message' => 'Abonnement créé avec succès'
            ]
        ]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la création: ' . $e->getMessage()]);
    }
}

/**
 * Update subscription plan
 */
function updateSubscriptionPlan($db, $id, $data)
{
    try {
        $query = "
            UPDATE subscriptions SET
                name = ?,
                description = ?,
                price = ?,
                max_products = ?,
                max_pages = ?,
                max_categories = ?,
                max_subcategories = ?,
                max_ai_tokens = ?,
                updated_at = NOW()
            WHERE id = ?
        ";

        $stmt = $db->prepare($query);
        $stmt->execute([
            $data['name'],
            $data['description'] ?? '',
            $data['price'],
            $data['max_products'] ?? 10,
            $data['max_pages'] ?? 5,
            $data['max_categories'] ?? 5,
            $data['max_subcategories'] ?? 10,
            $data['max_ai_tokens'] ?? 1000,
            $id
        ]);

        if ($stmt->rowCount() === 0) {
            http_response_code(404);
            echo json_encode(['error' => 'Abonnement non trouvé']);
            return;
        }

        echo json_encode([
            'success' => true,
            'data' => [
                'message' => 'Abonnement mis à jour avec succès'
            ]
        ]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la mise à jour: ' . $e->getMessage()]);
    }
}

/**
 * Delete subscription plan
 */
function deleteSubscriptionPlan($db, $id)
{
    try {
        // Check if subscription has active users
        $checkQuery = "SELECT COUNT(*) as count FROM user_subscriptions WHERE subscription_id = ? AND status = 'active'";
        $stmt = $db->prepare($checkQuery);
        $stmt->execute([$id]);
        $activeUsers = $stmt->fetch()['count'];

        if ($activeUsers > 0) {
            http_response_code(400);
            echo json_encode(['error' => 'Impossible de supprimer un abonnement avec des utilisateurs actifs']);
            return;
        }

        $query = "DELETE FROM subscriptions WHERE id = ?";
        $stmt = $db->prepare($query);
        $stmt->execute([$id]);

        if ($stmt->rowCount() === 0) {
            http_response_code(404);
            echo json_encode(['error' => 'Abonnement non trouvé']);
            return;
        }

        echo json_encode([
            'success' => true,
            'data' => [
                'message' => 'Abonnement supprimé avec succès'
            ]
        ]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la suppression: ' . $e->getMessage()]);
    }
}
