<?php

/**
 * Subscriptions API Endpoint
 * Handles subscription management
 */

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/error.log');

require_once __DIR__ . '/../config/database.php';

// Set headers
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// <PERSON>le preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    $database = new Database();
    $db = $database->getConnection();

    // Get request method and path
    $method = $_SERVER['REQUEST_METHOD'];
    $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
    $pathParts = explode('/', trim($path, '/'));

    // Extract action from path or query parameters
    $action = isset($pathParts[2]) ? $pathParts[2] : (isset($_GET['action']) ? $_GET['action'] : '');
    $id = isset($pathParts[3]) ? $pathParts[3] : (isset($_GET['id']) ? $_GET['id'] : null);

    switch ($method) {
        case 'GET':
            if ($action === 'plans' || $action === 'all' || $action === '') {
                getAllSubscriptionPlans($db);
            } elseif ($action === 'details' && $id) {
                getSubscriptionDetails($db, $id);
            } elseif ($action === 'stats') {
                getSubscriptionStats($db);
            } else {
                http_response_code(404);
                echo json_encode(['error' => 'Action non supportée']);
            }
            break;

        default:
            http_response_code(405);
            echo json_encode(['error' => 'Méthode non supportée']);
    }
} catch (Exception $e) {
    error_log('Subscriptions API Error: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ]);
}

/**
 * Get all subscription plans
 */
function getAllSubscriptionPlans($db)
{
    try {
        $query = "SELECT
                    id,
                    name,
                    name_fr,
                    name_en,
                    description,
                    description_fr,
                    description_en,
                    price,
                    currency,
                    billing_cycle,
                    features,
                    max_products,
                    max_categories,
                    max_landing_pages,
                    max_storage_mb,
                    status,
                    created_at,
                    updated_at
                FROM subscription_plans
                WHERE status = 'active'
                ORDER BY price ASC";

        $stmt = $db->prepare($query);
        $stmt->execute();
        $plans = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Process the data
        foreach ($plans as &$plan) {
            $plan['features'] = json_decode($plan['features'], true);
            $plan['max_products'] = (int)$plan['max_products'];
            $plan['max_categories'] = (int)$plan['max_categories'];
            $plan['max_landing_pages'] = (int)$plan['max_landing_pages'];
            $plan['max_storage_mb'] = (int)$plan['max_storage_mb'];
        }

        echo json_encode([
            'success' => true,
            'data' => [
                'plans' => $plans,
                'total' => count($plans)
            ]
        ]);
    } catch (PDOException $e) {
        error_log("Error getting subscription plans: " . $e->getMessage());
        http_response_code(500);
        throw new Exception('Erreur lors de la récupération des plans');
    }
}

/**
 * Get subscription details by ID
 */
function getSubscriptionDetails($db, $id)
{
    try {
        // Get subscription plan details
        $query = "
            SELECT
                sp.*,
                COUNT(s.id) as active_subscriptions
            FROM subscription_plans sp
            LEFT JOIN subscriptions s ON sp.id = s.plan_id AND s.status = 'active'
            WHERE sp.id = ?
            GROUP BY sp.id
        ";

        $stmt = $db->prepare($query);
        $stmt->execute([$id]);
        $plan = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$plan) {
            http_response_code(404);
            echo json_encode(['success' => false, 'error' => 'Plan non trouvé']);
            return;
        }

        // Get recent subscriptions for this plan
        $recentQuery = "
            SELECT
                s.*,
                u.email as user_email,
                u.name as user_name
            FROM subscriptions s
            LEFT JOIN users u ON s.user_id = u.id
            WHERE s.plan_id = ?
            ORDER BY s.created_at DESC
            LIMIT 10
        ";

        $recentStmt = $db->prepare($recentQuery);
        $recentStmt->execute([$id]);
        $recentSubscriptions = $recentStmt->fetchAll(PDO::FETCH_ASSOC);

        echo json_encode([
            'success' => true,
            'data' => [
                'plan' => $plan,
                'recent_subscriptions' => $recentSubscriptions
            ]
        ]);
    } catch (PDOException $e) {
        error_log("Error getting subscription details: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['success' => false, 'error' => 'Erreur lors de la récupération des détails']);
    }
}

/**
 * Get subscription statistics
 */
function getSubscriptionStats($db)
{
    try {
        // Get general stats
        $statsQuery = "
            SELECT
                COUNT(*) as total_subscriptions,
                COUNT(CASE WHEN status = 'active' THEN 1 END) as active_subscriptions,
                COUNT(CASE WHEN status = 'expired' THEN 1 END) as expired_subscriptions,
                COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_subscriptions,
                SUM(CASE WHEN status = 'active' THEN sp.price ELSE 0 END) as monthly_revenue
            FROM subscriptions s
            LEFT JOIN subscription_plans sp ON s.plan_id = sp.id
        ";

        $stats = $db->query($statsQuery)->fetch(PDO::FETCH_ASSOC);

        // Get plan distribution
        $planStatsQuery = "
            SELECT
                sp.name,
                sp.price,
                COUNT(s.id) as subscription_count,
                SUM(CASE WHEN s.status = 'active' THEN sp.price ELSE 0 END) as revenue
            FROM subscription_plans sp
            LEFT JOIN subscriptions s ON sp.id = s.plan_id
            GROUP BY sp.id, sp.name, sp.price
            ORDER BY subscription_count DESC
        ";

        $planStats = $db->query($planStatsQuery)->fetchAll(PDO::FETCH_ASSOC);

        echo json_encode([
            'success' => true,
            'data' => [
                'general_stats' => $stats,
                'plan_stats' => $planStats
            ]
        ]);
    } catch (PDOException $e) {
        error_log("Error getting subscription stats: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['success' => false, 'error' => 'Erreur lors de la récupération des statistiques']);
    }
}
