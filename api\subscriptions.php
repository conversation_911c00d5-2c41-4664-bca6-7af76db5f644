<?php

/**
 * Subscriptions API Endpoint
 * Handles subscription management
 */

require_once 'config/database.php';

// Set headers
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    $database = new Database();
    $db = $database->getConnection();

    // Get request method and path
    $method = $_SERVER['REQUEST_METHOD'];
    $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
    $pathParts = explode('/', trim($path, '/'));

    // Extract action from path or query parameters
    $action = isset($pathParts[2]) ? $pathParts[2] : (isset($_GET['action']) ? $_GET['action'] : '');
    $id = isset($pathParts[3]) ? $pathParts[3] : (isset($_GET['id']) ? $_GET['id'] : null);

    // Simple authentication check
    $headers = function_exists('getallheaders') ? getallheaders() : [];
    if (empty($headers) && isset($_SERVER['HTTP_AUTHORIZATION'])) {
        $headers['Authorization'] = $_SERVER['HTTP_AUTHORIZATION'];
    }
    $token = isset($headers['Authorization']) ? str_replace('Bearer ', '', $headers['Authorization']) : null;
    
    // For demo purposes, accept demo_token
    if ($token !== 'demo_token') {
        ApiResponse::error('Token d\'authentification requis', 401);
    }

    switch ($method) {
        case 'GET':
            if ($action === 'plans' || $action === '') {
                getAllSubscriptionPlans($db);
            } elseif ($action === 'user' && $id) {
                getUserSubscription($db, $id);
            } elseif ($action === 'plan' && $id) {
                getSubscriptionPlan($db, $id);
            } else {
                ApiResponse::error('Action non supportée', 404);
            }
            break;

        case 'POST':
            $input = json_decode(file_get_contents('php://input'), true);
            if ($action === 'subscribe') {
                subscribeUser($db, $input);
            } elseif ($action === 'plan') {
                createSubscriptionPlan($db, $input);
            } else {
                ApiResponse::error('Action non supportée', 404);
            }
            break;

        case 'PUT':
            $input = json_decode(file_get_contents('php://input'), true);
            if ($action === 'plan' && $id) {
                updateSubscriptionPlan($db, $id, $input);
            } elseif ($action === 'user' && $id) {
                updateUserSubscription($db, $id, $input);
            } else {
                ApiResponse::error('Action non supportée', 404);
            }
            break;

        case 'DELETE':
            if ($action === 'plan' && $id) {
                deleteSubscriptionPlan($db, $id);
            } elseif ($action === 'user' && $id) {
                cancelUserSubscription($db, $id);
            } else {
                ApiResponse::error('Action non supportée', 404);
            }
            break;

        default:
            ApiResponse::error('Méthode non supportée', 405);
    }
} catch (Exception $e) {
    error_log('Subscriptions API Error: ' . $e->getMessage());
    ApiResponse::error('Internal server error', 500);
}

/**
 * Get all subscription plans
 */
function getAllSubscriptionPlans($db)
{
    try {
        $query = "
            SELECT 
                s.*,
                COUNT(us.id) as active_subscribers
            FROM subscriptions s
            LEFT JOIN user_subscriptions us ON s.id = us.subscription_id AND us.status = 'active'
            WHERE s.is_active = 1
            GROUP BY s.id
            ORDER BY s.sort_order, s.price
        ";
        
        $stmt = $db->prepare($query);
        $stmt->execute();
        $plans = $stmt->fetchAll();
        
        // Process the data
        foreach ($plans as &$plan) {
            $plan['features'] = json_decode($plan['features'], true);
            $plan['ai_models'] = json_decode($plan['ai_models'], true);
            $plan['active_subscribers'] = (int)$plan['active_subscribers'];
            $plan['is_active'] = (bool)$plan['is_active'];
            $plan['is_featured'] = (bool)$plan['is_featured'];
            $plan['ai_enabled'] = (bool)$plan['ai_enabled'];
            $plan['custom_domain'] = (bool)$plan['custom_domain'];
            $plan['analytics_advanced'] = (bool)$plan['analytics_advanced'];
            $plan['white_label'] = (bool)$plan['white_label'];
            $plan['api_access'] = (bool)$plan['api_access'];
        }
        
        ApiResponse::success([
            'plans' => $plans,
            'total' => count($plans)
        ]);
        
    } catch (Exception $e) {
        error_log("Error getting subscription plans: " . $e->getMessage());
        ApiResponse::error('Erreur lors de la récupération des plans', 500);
    }
}

/**
 * Get user subscription
 */
function getUserSubscription($db, $user_id)
{
    try {
        $query = "
            SELECT 
                us.*,
                s.name as plan_name,
                s.display_name as plan_display_name,
                s.price as plan_price,
                s.max_products,
                s.max_landing_pages,
                s.max_categories,
                s.ai_enabled,
                s.ai_monthly_tokens
            FROM user_subscriptions us
            JOIN subscriptions s ON us.subscription_id = s.id
            WHERE us.user_id = ? AND us.status = 'active'
            ORDER BY us.created_at DESC
            LIMIT 1
        ";
        
        $stmt = $db->prepare($query);
        $stmt->execute([$user_id]);
        $subscription = $stmt->fetch();
        
        if (!$subscription) {
            ApiResponse::error('Aucun abonnement actif trouvé', 404);
        }
        
        ApiResponse::success($subscription);
        
    } catch (Exception $e) {
        error_log("Error getting user subscription: " . $e->getMessage());
        ApiResponse::error('Erreur lors de la récupération de l\'abonnement', 500);
    }
}

/**
 * Subscribe user to a plan
 */
function subscribeUser($db, $data)
{
    try {
        // Validate required fields
        if (!isset($data['user_id']) || !isset($data['subscription_id'])) {
            ApiResponse::error('user_id et subscription_id requis', 400);
        }
        
        // Check if plan exists
        $stmt = $db->prepare("SELECT * FROM subscriptions WHERE id = ? AND is_active = 1");
        $stmt->execute([$data['subscription_id']]);
        $plan = $stmt->fetch();
        
        if (!$plan) {
            ApiResponse::error('Plan d\'abonnement non trouvé', 404);
        }
        
        // Cancel existing active subscription
        $db->prepare("UPDATE user_subscriptions SET status = 'cancelled', cancelled_at = NOW() WHERE user_id = ? AND status = 'active'")->execute([$data['user_id']]);
        
        // Create new subscription
        $expires_at = date('Y-m-d H:i:s', strtotime('+1 month'));
        $next_payment = date('Y-m-d H:i:s', strtotime('+1 month'));
        
        $query = "
            INSERT INTO user_subscriptions 
            (user_id, subscription_id, status, expires_at, next_payment_at, payment_method) 
            VALUES (?, ?, 'active', ?, ?, ?)
        ";
        
        $stmt = $db->prepare($query);
        $stmt->execute([
            $data['user_id'],
            $data['subscription_id'],
            $expires_at,
            $next_payment,
            $data['payment_method'] ?? 'manual'
        ]);
        
        $subscription_id = $db->lastInsertId();
        
        // Get the created subscription
        $stmt = $db->prepare("
            SELECT us.*, s.display_name as plan_name 
            FROM user_subscriptions us 
            JOIN subscriptions s ON us.subscription_id = s.id 
            WHERE us.id = ?
        ");
        $stmt->execute([$subscription_id]);
        $created_subscription = $stmt->fetch();
        
        ApiResponse::success($created_subscription, 'Abonnement créé avec succès');
        
    } catch (Exception $e) {
        error_log("Error subscribing user: " . $e->getMessage());
        ApiResponse::error('Erreur lors de la création de l\'abonnement', 500);
    }
}

?>
