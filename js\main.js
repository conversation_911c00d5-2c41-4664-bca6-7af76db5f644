/**
 * JavaScript principal pour la landing page
 * Gestion des interactions, animations et fonctionnalités
 */

// Configuration globale
const CONFIG = {
    animationDuration: 300,
    scrollOffset: 80,
    stickyCtaOffset: 500,
    debounceDelay: 100
};

// État global de l'application
const AppState = {
    isMenuOpen: false,
    currentLanguage: 'ar',
    isScrolled: false,
    swiperInstances: {},
    observers: {}
};

/**
 * Initialisation de l'application
 */
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    console.log('🚀 Initialisation de la landing page...');
    
    // Initialisation des composants
    initializeNavigation();
    initializeScrollEffects();
    initializeAnimations();
    initializeForms();
    initializeSliders();
    initializeLanguageSwitcher();
    initializeStickyElements();
    initializeAccessibility();
    
    console.log('✅ Landing page initialisée avec succès');
}

/**
 * ===== NAVIGATION =====
 */
function initializeNavigation() {
    const mobileToggle = document.querySelector('.mobile-menu-toggle');
    const mobileMenu = document.querySelector('.mobile-menu');
    const navLinks = document.querySelectorAll('.nav-link');
    
    // Toggle menu mobile
    if (mobileToggle && mobileMenu) {
        mobileToggle.addEventListener('click', toggleMobileMenu);
        
        // Fermer le menu en cliquant sur un lien
        navLinks.forEach(link => {
            link.addEventListener('click', closeMobileMenu);
        });
        
        // Fermer le menu en cliquant à l'extérieur
        document.addEventListener('click', function(e) {
            if (!mobileToggle.contains(e.target) && !mobileMenu.contains(e.target)) {
                closeMobileMenu();
            }
        });
    }
    
    // Navigation fluide
    initializeSmoothScrolling();
    
    // Mise en surbrillance du lien actif
    updateActiveNavLink();
    window.addEventListener('scroll', debounce(updateActiveNavLink, CONFIG.debounceDelay));
}

function toggleMobileMenu() {
    const toggle = document.querySelector('.mobile-menu-toggle');
    const menu = document.querySelector('.mobile-menu');
    
    AppState.isMenuOpen = !AppState.isMenuOpen;
    
    toggle.classList.toggle('active', AppState.isMenuOpen);
    menu.classList.toggle('active', AppState.isMenuOpen);
    
    // Prévenir le scroll du body quand le menu est ouvert
    document.body.style.overflow = AppState.isMenuOpen ? 'hidden' : '';
    
    // Accessibilité
    toggle.setAttribute('aria-expanded', AppState.isMenuOpen);
    menu.setAttribute('aria-hidden', !AppState.isMenuOpen);
}

function closeMobileMenu() {
    if (AppState.isMenuOpen) {
        toggleMobileMenu();
    }
}

function initializeSmoothScrolling() {
    const links = document.querySelectorAll('a[href^="#"]');
    
    links.forEach(link => {
        link.addEventListener('click', function(e) {
            const href = this.getAttribute('href');
            if (href === '#') return;
            
            const target = document.querySelector(href);
            if (target) {
                e.preventDefault();
                smoothScrollTo(target);
            }
        });
    });
}

function smoothScrollTo(target) {
    const headerHeight = document.querySelector('.header')?.offsetHeight || 0;
    const targetPosition = target.offsetTop - headerHeight - 20;
    
    window.scrollTo({
        top: targetPosition,
        behavior: 'smooth'
    });
}

function updateActiveNavLink() {
    const sections = document.querySelectorAll('section[id]');
    const navLinks = document.querySelectorAll('.nav-link');
    const scrollPosition = window.scrollY + CONFIG.scrollOffset;
    
    let currentSection = '';
    
    sections.forEach(section => {
        const sectionTop = section.offsetTop;
        const sectionHeight = section.offsetHeight;
        
        if (scrollPosition >= sectionTop && scrollPosition < sectionTop + sectionHeight) {
            currentSection = section.getAttribute('id');
        }
    });
    
    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === `#${currentSection}`) {
            link.classList.add('active');
        }
    });
}

/**
 * ===== EFFETS DE SCROLL =====
 */
function initializeScrollEffects() {
    const header = document.querySelector('.header');
    
    window.addEventListener('scroll', debounce(function() {
        const scrolled = window.scrollY > 50;
        
        if (scrolled !== AppState.isScrolled) {
            AppState.isScrolled = scrolled;
            header?.classList.toggle('scrolled', scrolled);
        }
    }, CONFIG.debounceDelay));
}

/**
 * ===== ANIMATIONS =====
 */
function initializeAnimations() {
    // Intersection Observer pour les animations au scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    AppState.observers.animation = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-fade-in');
                AppState.observers.animation.unobserve(entry.target);
            }
        });
    }, observerOptions);
    
    // Observer les éléments à animer
    const animatedElements = document.querySelectorAll(
        '.feature-card, .template-card, .testimonial-card, .pricing-card, .contact-item'
    );
    
    animatedElements.forEach(el => {
        AppState.observers.animation.observe(el);
    });
    
    // Animation des compteurs
    initializeCounters();
}

function initializeCounters() {
    const counters = document.querySelectorAll('.hero-stat-number');
    
    const counterObserver = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateCounter(entry.target);
                counterObserver.unobserve(entry.target);
            }
        });
    }, { threshold: 0.5 });
    
    counters.forEach(counter => {
        counterObserver.observe(counter);
    });
}

function animateCounter(element) {
    const target = parseInt(element.textContent.replace(/[^0-9]/g, ''));
    const duration = 2000;
    const step = target / (duration / 16);
    let current = 0;
    
    const timer = setInterval(() => {
        current += step;
        if (current >= target) {
            current = target;
            clearInterval(timer);
        }
        
        const suffix = element.textContent.replace(/[0-9]/g, '');
        element.textContent = Math.floor(current).toLocaleString() + suffix;
    }, 16);
}

/**
 * ===== FORMULAIRES =====
 */
function initializeForms() {
    const forms = document.querySelectorAll('form');
    
    forms.forEach(form => {
        form.addEventListener('submit', handleFormSubmit);
        
        // Validation en temps réel
        const inputs = form.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            input.addEventListener('blur', () => validateField(input));
            input.addEventListener('input', () => clearFieldError(input));
        });
    });
}

function handleFormSubmit(e) {
    e.preventDefault();
    
    const form = e.target;
    const formData = new FormData(form);
    const isValid = validateForm(form);
    
    if (!isValid) {
        showNotification('يرجى تصحيح الأخطاء في النموذج', 'error');
        return;
    }
    
    // Afficher le loading
    showLoading();
    
    // Simuler l'envoi (remplacer par votre logique d'envoi)
    setTimeout(() => {
        hideLoading();
        showNotification('تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.', 'success');
        form.reset();
    }, 2000);
}

function validateForm(form) {
    const inputs = form.querySelectorAll('input[required], textarea[required], select[required]');
    let isValid = true;
    
    inputs.forEach(input => {
        if (!validateField(input)) {
            isValid = false;
        }
    });
    
    return isValid;
}

function validateField(field) {
    const value = field.value.trim();
    const type = field.type;
    const required = field.hasAttribute('required');
    
    clearFieldError(field);
    
    // Validation requis
    if (required && !value) {
        showFieldError(field, 'هذا الحقل مطلوب');
        return false;
    }
    
    // Validation email
    if (type === 'email' && value) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
            showFieldError(field, 'يرجى إدخال بريد إلكتروني صحيح');
            return false;
        }
    }
    
    // Validation téléphone
    if (type === 'tel' && value) {
        const phoneRegex = /^[\+]?[0-9\s\-\(\)]{8,20}$/;
        if (!phoneRegex.test(value)) {
            showFieldError(field, 'يرجى إدخال رقم هاتف صحيح');
            return false;
        }
    }
    
    return true;
}

function showFieldError(field, message) {
    field.classList.add('error');
    
    let errorElement = field.parentNode.querySelector('.form-error');
    if (!errorElement) {
        errorElement = document.createElement('div');
        errorElement.className = 'form-error';
        field.parentNode.appendChild(errorElement);
    }
    
    errorElement.textContent = message;
    errorElement.setAttribute('role', 'alert');
}

function clearFieldError(field) {
    field.classList.remove('error');
    const errorElement = field.parentNode.querySelector('.form-error');
    if (errorElement) {
        errorElement.remove();
    }
}

/**
 * ===== SLIDERS =====
 */
function initializeSliders() {
    // Slider des témoignages
    const testimonialsSlider = document.querySelector('.testimonials-slider');
    if (testimonialsSlider) {
        AppState.swiperInstances.testimonials = new Swiper(testimonialsSlider, {
            slidesPerView: 1,
            spaceBetween: 30,
            loop: true,
            autoplay: {
                delay: 5000,
                disableOnInteraction: false
            },
            pagination: {
                el: '.swiper-pagination',
                clickable: true
            },
            navigation: {
                nextEl: '.swiper-button-next',
                prevEl: '.swiper-button-prev'
            },
            breakpoints: {
                768: {
                    slidesPerView: 2
                },
                1024: {
                    slidesPerView: 3
                }
            }
        });
    }
    
    // Slider des templates
    const templatesSlider = document.querySelector('.templates-slider');
    if (templatesSlider) {
        AppState.swiperInstances.templates = new Swiper(templatesSlider, {
            slidesPerView: 1,
            spaceBetween: 20,
            loop: false,
            navigation: {
                nextEl: '.templates-next',
                prevEl: '.templates-prev'
            },
            breakpoints: {
                640: {
                    slidesPerView: 2
                },
                768: {
                    slidesPerView: 3
                },
                1024: {
                    slidesPerView: 4
                }
            }
        });
    }
}

/**
 * ===== CHANGEMENT DE LANGUE =====
 */
function initializeLanguageSwitcher() {
    const langButtons = document.querySelectorAll('.lang-btn');
    
    langButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            const lang = this.dataset.lang;
            if (lang && lang !== AppState.currentLanguage) {
                switchLanguage(lang);
            }
        });
    });
    
    // Initialiser la langue courante depuis le DOM
    const activeLangBtn = document.querySelector('.lang-btn.active');
    if (activeLangBtn) {
        AppState.currentLanguage = activeLangBtn.dataset.lang;
        updatePageDirection(AppState.currentLanguage);
    }
}

function switchLanguage(lang) {
    // Validation de la langue
    const supportedLanguages = ['ar', 'fr', 'en'];
    if (!supportedLanguages.includes(lang)) {
        console.error('Langue non supportée:', lang);
        return;
    }
    
    // Afficher l'indicateur de chargement
    showLanguageLoading(lang);
    
    // Envoyer la requête AJAX
    fetch('php/change-language.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        },
        body: JSON.stringify({ language: lang })
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            // Mettre à jour l'état de l'application
            AppState.currentLanguage = lang;
            
            // Mettre à jour l'interface utilisateur
            updateLanguageUI(lang, data.data);
            
            // Afficher un message de succès
            showLanguageSuccess(data.data.language_name);
            
            // Recharger la page après un court délai pour appliquer toutes les traductions
            setTimeout(() => {
                const currentUrl = new URL(window.location);
                currentUrl.searchParams.set('lang', lang);
                window.location.href = currentUrl.toString();
            }, 1000);
        } else {
            throw new Error(data.message || 'Erreur lors du changement de langue');
        }
    })
    .catch(error => {
        console.error('Erreur lors du changement de langue:', error);
        showLanguageError(error.message);
        hideLanguageLoading();
    });
}

function updateLanguageUI(lang, languageData) {
    // Mettre à jour les boutons de langue
    const langButtons = document.querySelectorAll('.lang-btn');
    langButtons.forEach(btn => {
        btn.classList.remove('active');
        btn.removeAttribute('aria-current');
        
        if (btn.dataset.lang === lang) {
            btn.classList.add('active');
            btn.setAttribute('aria-current', 'true');
        }
    });
    
    // Mettre à jour la direction du texte
    updatePageDirection(lang);
    
    // Mettre à jour l'attribut lang du document
    document.documentElement.setAttribute('lang', lang);
    
    // Mettre à jour les métadonnées si disponibles
    if (languageData) {
        document.documentElement.setAttribute('dir', languageData.direction || 'ltr');
    }
}

function updatePageDirection(lang) {
    const isRTL = lang === 'ar';
    document.documentElement.setAttribute('dir', isRTL ? 'rtl' : 'ltr');
    document.body.classList.toggle('rtl', isRTL);
    document.body.classList.toggle('ltr', !isRTL);
}

function showLanguageLoading(lang) {
    const langBtn = document.querySelector(`[data-lang="${lang}"]`);
    if (langBtn) {
        langBtn.classList.add('loading');
        langBtn.disabled = true;
        
        // Ajouter un spinner
        const originalContent = langBtn.innerHTML;
        langBtn.dataset.originalContent = originalContent;
        langBtn.innerHTML = '<span class="spinner">⏳</span> ' + originalContent;
    }
    
    // Afficher une notification globale
    showNotification('Changement de langue en cours...', 'info');
}

function hideLanguageLoading() {
    const langButtons = document.querySelectorAll('.lang-btn.loading');
    langButtons.forEach(btn => {
        btn.classList.remove('loading');
        btn.disabled = false;
        
        if (btn.dataset.originalContent) {
            btn.innerHTML = btn.dataset.originalContent;
            delete btn.dataset.originalContent;
        }
    });
}

function showLanguageSuccess(languageName) {
    showNotification(`Langue changée vers ${languageName}`, 'success');
}

function showLanguageError(message) {
    showNotification(`Erreur: ${message}`, 'error');
}

function showNotification(message, type = 'info') {
    // Créer ou réutiliser le conteneur de notifications
    let notificationContainer = document.querySelector('.notification-container');
    if (!notificationContainer) {
        notificationContainer = document.createElement('div');
        notificationContainer.className = 'notification-container';
        document.body.appendChild(notificationContainer);
    }
    
    // Créer la notification
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <span class="notification-icon">${getNotificationIcon(type)}</span>
        <span class="notification-message">${message}</span>
        <button class="notification-close" aria-label="Fermer">&times;</button>
    `;
    
    // Ajouter les événements
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.addEventListener('click', () => removeNotification(notification));
    
    // Ajouter au conteneur
    notificationContainer.appendChild(notification);
    
    // Animation d'entrée
    setTimeout(() => notification.classList.add('show'), 10);
    
    // Suppression automatique
    setTimeout(() => removeNotification(notification), type === 'error' ? 5000 : 3000);
}

function removeNotification(notification) {
    notification.classList.remove('show');
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 300);
}

function getNotificationIcon(type) {
    const icons = {
        'success': '✅',
        'error': '❌',
        'warning': '⚠️',
        'info': 'ℹ️'
    };
    return icons[type] || icons.info;
}

/**
 * ===== ÉLÉMENTS STICKY =====
 */
function initializeStickyElements() {
    const stickyCtaButton = document.querySelector('.sticky-cta');
    
    if (stickyCtaButton) {
        window.addEventListener('scroll', debounce(function() {
            const scrolled = window.scrollY > CONFIG.stickyCtaOffset;
            stickyCtaButton.classList.toggle('visible', scrolled);
        }, CONFIG.debounceDelay));
    }
}

/**
 * ===== ACCESSIBILITÉ =====
 */
function initializeAccessibility() {
    // Navigation au clavier
    document.addEventListener('keydown', function(e) {
        // Échapper pour fermer le menu mobile
        if (e.key === 'Escape' && AppState.isMenuOpen) {
            closeMobileMenu();
        }
        
        // Entrée pour activer les boutons
        if (e.key === 'Enter' && e.target.classList.contains('btn')) {
            e.target.click();
        }
    });
    
    // Focus visible pour la navigation au clavier
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Tab') {
            document.body.classList.add('keyboard-navigation');
        }
    });
    
    document.addEventListener('mousedown', function() {
        document.body.classList.remove('keyboard-navigation');
    });
    
    // Améliorer l'accessibilité des sliders
    Object.values(AppState.swiperInstances).forEach(swiper => {
        if (swiper && swiper.slides) {
            swiper.slides.forEach((slide, index) => {
                slide.setAttribute('role', 'tabpanel');
                slide.setAttribute('aria-label', `Slide ${index + 1}`);
            });
        }
    });
}

/**
 * ===== UTILITAIRES =====
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function showLoading() {
    const overlay = document.querySelector('.loading-overlay');
    if (overlay) {
        overlay.classList.add('active');
        overlay.setAttribute('aria-hidden', 'false');
    }
}

function hideLoading() {
    const overlay = document.querySelector('.loading-overlay');
    if (overlay) {
        overlay.classList.remove('active');
        overlay.setAttribute('aria-hidden', 'true');
    }
}

function showNotification(message, type = 'info') {
    // Créer la notification
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <span class="notification-message">${message}</span>
            <button class="notification-close" aria-label="إغلاق الإشعار">&times;</button>
        </div>
    `;
    
    // Styles inline pour la notification
    Object.assign(notification.style, {
        position: 'fixed',
        top: '20px',
        right: '20px',
        background: type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6',
        color: 'white',
        padding: '16px 20px',
        borderRadius: '8px',
        boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
        zIndex: '10000',
        maxWidth: '400px',
        transform: 'translateX(100%)',
        transition: 'transform 0.3s ease-in-out'
    });
    
    document.body.appendChild(notification);
    
    // Animation d'entrée
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Bouton de fermeture
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.addEventListener('click', () => removeNotification(notification));
    
    // Auto-suppression après 5 secondes
    setTimeout(() => {
        removeNotification(notification);
    }, 5000);
}

function removeNotification(notification) {
    notification.style.transform = 'translateX(100%)';
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 300);
}

// Gestion des erreurs globales
window.addEventListener('error', function(e) {
    console.error('Erreur JavaScript:', e.error);
    // En production, vous pourriez envoyer cette erreur à un service de monitoring
});

// Export pour utilisation dans d'autres scripts
window.LandingPageApp = {
    state: AppState,
    config: CONFIG,
    utils: {
        debounce,
        showLoading,
        hideLoading,
        showNotification,
        smoothScrollTo
    }
};

console.log('📱 Landing Page JavaScript chargé');