<?php
require_once 'php/config/database.php';

try {
    echo "🔍 Création des landing pages de démonstration...\n\n";
    
    // Trouver le store_id 3 (demo merchant)
    $storeQuery = "SELECT id, merchant_id, store_name FROM stores WHERE id = 3";
    $storeStmt = $pdo->prepare($storeQuery);
    $storeStmt->execute();
    $store = $storeStmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$store) {
        echo "❌ Store ID 3 non trouvé\n";
        exit;
    }
    
    echo "✅ Store trouvé: {$store['store_name']} (Store ID: {$store['id']})\n";
    $storeId = $store['id'];
    
    // Créer les tables si elles n'existent pas
    echo "\n📊 Création des tables landing pages...\n";
    
    // Table des templates
    $createTemplatesTable = "
        CREATE TABLE IF NOT EXISTS landing_page_templates (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VA<PERSON>HA<PERSON>(255) NOT NULL,
            description TEXT,
            preview_image VARCHAR(500),
            category VARCHAR(100) DEFAULT 'general',
            features <PERSON><PERSON><PERSON>,
            template_data JSON,
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $pdo->exec($createTemplatesTable);
    echo "  ✅ Table landing_page_templates créée\n";
    
    // Table des landing pages
    $createPagesTable = "
        CREATE TABLE IF NOT EXISTS landing_pages (
            id INT AUTO_INCREMENT PRIMARY KEY,
            store_id INT NOT NULL,
            title VARCHAR(255) NOT NULL,
            slug VARCHAR(255) NOT NULL,
            template_id INT,
            content JSON,
            seo_data JSON,
            status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
            views INT DEFAULT 0,
            conversions INT DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (template_id) REFERENCES landing_page_templates(id) ON DELETE SET NULL,
            UNIQUE KEY unique_store_slug (store_id, slug),
            INDEX idx_store_id (store_id),
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $pdo->exec($createPagesTable);
    echo "  ✅ Table landing_pages créée\n";
    
    // Insérer les templates par défaut
    echo "\n📝 Insertion des templates par défaut...\n";
    
    $templates = [
        [
            'name' => 'E-commerce Moderne',
            'description' => 'Template moderne pour boutique en ligne avec design épuré',
            'preview_image' => '/images/templates/ecommerce-modern.jpg',
            'category' => 'ecommerce',
            'features' => ['Responsive', 'SEO optimisé', 'Panier intégré', 'Paiement sécurisé']
        ],
        [
            'name' => 'Landing Page Produit',
            'description' => 'Page de vente dédiée à un produit spécifique',
            'preview_image' => '/images/templates/product-landing.jpg',
            'category' => 'product',
            'features' => ['Call-to-action', 'Témoignages', 'Galerie photos', 'Formulaire contact']
        ],
        [
            'name' => 'Portfolio Créatif',
            'description' => 'Showcase créatif pour présenter vos réalisations',
            'preview_image' => '/images/templates/creative-portfolio.jpg',
            'category' => 'portfolio',
            'features' => ['Galerie interactive', 'Animations', 'Contact form', 'Blog intégré']
        ],
        [
            'name' => 'Service Professionnel',
            'description' => 'Template pour présenter vos services professionnels',
            'preview_image' => '/images/templates/professional-service.jpg',
            'category' => 'service',
            'features' => ['Présentation équipe', 'Tarifs', 'Témoignages', 'Réservation en ligne']
        ],
        [
            'name' => 'Restaurant & Food',
            'description' => 'Template spécialisé pour restaurants et food business',
            'preview_image' => '/images/templates/restaurant-food.jpg',
            'category' => 'restaurant',
            'features' => ['Menu interactif', 'Réservation', 'Galerie plats', 'Livraison']
        ]
    ];
    
    // Supprimer les anciens templates pour recommencer proprement
    $pdo->exec("DELETE FROM landing_page_templates");
    $pdo->exec("DELETE FROM landing_pages WHERE store_id = $storeId");
    
    $templateIds = [];
    foreach ($templates as $template) {
        $query = "
            INSERT INTO landing_page_templates (name, description, preview_image, category, features)
            VALUES (?, ?, ?, ?, ?)
        ";
        
        $stmt = $pdo->prepare($query);
        $stmt->execute([
            $template['name'],
            $template['description'],
            $template['preview_image'],
            $template['category'],
            json_encode($template['features'])
        ]);
        
        $templateId = $pdo->lastInsertId();
        $templateIds[] = $templateId;
        echo "  ✅ Template '{$template['name']}' créé (ID: {$templateId})\n";
    }
    
    // Créer 5 landing pages de démonstration
    echo "\n🎨 Création des landing pages de démonstration...\n";
    
    $landingPages = [
        [
            'title' => 'Boutique Électronique Premium',
            'slug' => 'boutique-electronique-premium',
            'template_id' => $templateIds[0], // E-commerce Moderne
            'status' => 'published',
            'content' => [
                'hero_title' => 'Découvrez notre sélection d\'électronique premium',
                'hero_subtitle' => 'Les dernières technologies à prix imbattables',
                'hero_image' => '/images/hero-electronics.jpg',
                'sections' => [
                    'featured_products' => true,
                    'testimonials' => true,
                    'newsletter' => true
                ]
            ],
            'seo_data' => [
                'meta_title' => 'Boutique Électronique Premium - TechStore',
                'meta_description' => 'Découvrez notre sélection premium d\'appareils électroniques. Smartphones, ordinateurs, accessoires high-tech.',
                'keywords' => 'électronique, smartphone, ordinateur, high-tech'
            ]
        ],
        [
            'title' => 'iPhone 15 Pro - Offre Spéciale',
            'slug' => 'iphone-15-pro-offre-speciale',
            'template_id' => $templateIds[1], // Landing Page Produit
            'status' => 'published',
            'content' => [
                'product_name' => 'iPhone 15 Pro',
                'product_price' => '150000',
                'product_image' => '/images/iphone-15-pro.jpg',
                'features' => ['Puce A17 Pro', 'Caméra 48MP', 'Titanium', 'USB-C'],
                'cta_text' => 'Commander maintenant'
            ],
            'seo_data' => [
                'meta_title' => 'iPhone 15 Pro - Offre Spéciale | TechStore',
                'meta_description' => 'iPhone 15 Pro en promotion. Livraison gratuite en Algérie. Garantie officielle Apple.',
                'keywords' => 'iPhone 15 Pro, Apple, smartphone, promotion'
            ]
        ],
        [
            'title' => 'Portfolio Tech Solutions',
            'slug' => 'portfolio-tech-solutions',
            'template_id' => $templateIds[2], // Portfolio Créatif
            'status' => 'published',
            'content' => [
                'company_name' => 'Tech Solutions DZ',
                'tagline' => 'Innovation technologique made in Algeria',
                'projects' => [
                    'E-commerce Platform',
                    'Mobile App Development',
                    'AI Solutions',
                    'Cloud Infrastructure'
                ],
                'contact_email' => '<EMAIL>'
            ],
            'seo_data' => [
                'meta_title' => 'Portfolio Tech Solutions - Développement Web & Mobile',
                'meta_description' => 'Découvrez nos réalisations en développement web, mobile et solutions IA. Expertise technique en Algérie.',
                'keywords' => 'développement web, mobile, IA, portfolio, Algérie'
            ]
        ],
        [
            'title' => 'Services de Réparation Électronique',
            'slug' => 'services-reparation-electronique',
            'template_id' => $templateIds[3], // Service Professionnel
            'status' => 'published',
            'content' => [
                'service_name' => 'RepairTech Pro',
                'services' => [
                    'Réparation smartphones',
                    'Réparation ordinateurs',
                    'Récupération de données',
                    'Maintenance préventive'
                ],
                'pricing' => [
                    'Diagnostic' => '2000 DA',
                    'Réparation écran' => '8000 DA',
                    'Récupération données' => '5000 DA'
                ],
                'contact_phone' => '+213 555 123 456'
            ],
            'seo_data' => [
                'meta_title' => 'Services de Réparation Électronique | RepairTech Pro',
                'meta_description' => 'Réparation professionnelle de smartphones, ordinateurs. Service rapide et garanti en Algérie.',
                'keywords' => 'réparation smartphone, réparation ordinateur, service technique'
            ]
        ],
        [
            'title' => 'Restaurant Le Digital Café',
            'slug' => 'restaurant-le-digital-cafe',
            'template_id' => $templateIds[4], // Restaurant & Food
            'status' => 'published',
            'content' => [
                'restaurant_name' => 'Le Digital Café',
                'cuisine_type' => 'Fusion moderne avec tech',
                'specialties' => [
                    'Burger Code',
                    'Pizza Algorithm',
                    'Café Debug',
                    'Smoothie Cloud'
                ],
                'opening_hours' => '9h - 23h tous les jours',
                'reservation_phone' => '+213 555 987 654'
            ],
            'seo_data' => [
                'meta_title' => 'Le Digital Café - Restaurant Moderne Alger',
                'meta_description' => 'Restaurant moderne avec ambiance tech. Cuisine fusion, wifi gratuit, espace coworking. Réservation en ligne.',
                'keywords' => 'restaurant moderne, café, coworking, Alger, réservation'
            ]
        ]
    ];
    
    foreach ($landingPages as $page) {
        $query = "
            INSERT INTO landing_pages (
                store_id, title, slug, template_id, content, 
                seo_data, status, views, conversions, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
        ";
        
        $stmt = $pdo->prepare($query);
        $result = $stmt->execute([
            $storeId,
            $page['title'],
            $page['slug'],
            $page['template_id'],
            json_encode($page['content']),
            json_encode($page['seo_data']),
            $page['status'],
            rand(50, 500), // Vues aléatoires
            rand(5, 50)    // Conversions aléatoires
        ]);
        
        if ($result) {
            $pageId = $pdo->lastInsertId();
            echo "  ✅ Landing page '{$page['title']}' créée (ID: {$pageId})\n";
        } else {
            echo "  ❌ Erreur lors de la création de '{$page['title']}'\n";
        }
    }
    
    // Résumé final
    echo "\n📊 Résumé final:\n";
    $templateCount = $pdo->query("SELECT COUNT(*) FROM landing_page_templates")->fetchColumn();
    $pageCount = $pdo->query("SELECT COUNT(*) FROM landing_pages WHERE store_id = $storeId")->fetchColumn();
    
    echo "  - Templates créés: {$templateCount}\n";
    echo "  - Landing pages créées: {$pageCount}\n";
    
    echo "\n✅ Création des landing pages terminée avec succès!\n";
    echo "🔗 Testez l'API: /api/landing_pages.php?action=all&store_id=3\n";
    
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
    echo "📍 Fichier: " . $e->getFile() . " ligne " . $e->getLine() . "\n";
}
?>
