<?php
require_once 'api/config/database.php';

try {
    $database = new Database();
    $pdo = $database->getConnection();
    
    echo "Vérification de la table subscriptions...\n";
    
    // Vérifier si la table existe
    $stmt = $pdo->query("SHOW TABLES LIKE 'subscriptions'");
    if ($stmt->rowCount() > 0) {
        echo "✅ Table subscriptions existe\n";
        
        // Afficher la structure
        $stmt = $pdo->query("DESCRIBE subscriptions");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "\n📋 Structure actuelle :\n";
        foreach ($columns as $col) {
            echo "- {$col['Field']} ({$col['Type']})\n";
        }
        
        // Vérifier les données
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM subscriptions");
        $count = $stmt->fetch()['count'];
        echo "\n📊 Nombre d'enregistrements : $count\n";
        
        if ($count > 0) {
            $stmt = $pdo->query("SELECT * FROM subscriptions LIMIT 3");
            $subs = $stmt->fetchAll(PDO::FETCH_ASSOC);
            echo "\n🔍 Premiers enregistrements :\n";
            foreach ($subs as $sub) {
                echo "- ID: {$sub['id']}, Nom: " . (isset($sub['name']) ? $sub['name'] : 'N/A') . "\n";
            }
        }
    } else {
        echo "❌ Table subscriptions n'existe pas\n";
    }
    
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
}
?>
