<?php

/**
 * API simple pour les statistiques du dashboard
 * Compatible avec la base de données MySQL locale
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Configuration de la base de données
$host = 'localhost';
$port = 3307;
$dbname = 'landingpage_new';
$username = 'root';
$password = '';

try {
    // Connexion à la base de données
    $pdo = new PDO(
        "mysql:host={$host};port={$port};dbname={$dbname};charset=utf8mb4",
        $username,
        $password,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]
    );

    // Récupérer les statistiques
    $stats = [];

    // Nombre total d'utilisateurs (sans condition sur is_active si la colonne n'existe pas)
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM users WHERE is_active = 1");
        $stats['users'] = (int) $stmt->fetch()['count'];
    } catch (Exception $e) {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
        $stats['users'] = (int) $stmt->fetch()['count'];
    }

    // Nombre total de stores
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM stores WHERE status = 'active'");
        $stats['stores'] = (int) $stmt->fetch()['count'];
    } catch (Exception $e) {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM stores");
        $stats['stores'] = (int) $stmt->fetch()['count'];
    }

    // Nombre total de produits
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM products WHERE status = 'active'");
        $stats['products'] = (int) $stmt->fetch()['count'];
    } catch (Exception $e) {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM products");
        $stats['products'] = (int) $stmt->fetch()['count'];
    }

    // Nombre total de commandes
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM orders");
    $stats['orders'] = (int) $stmt->fetch()['count'];

    // Revenus total
    try {
        $stmt = $pdo->query("SELECT COALESCE(SUM(total_amount), 0) as total FROM orders WHERE payment_status = 'paid'");
        $stats['revenue'] = (float) $stmt->fetch()['total'];
    } catch (Exception $e) {
        $stmt = $pdo->query("SELECT COALESCE(SUM(total_amount), 0) as total FROM orders");
        $stats['revenue'] = (float) $stmt->fetch()['total'];
    }

    // Commandes ce mois
    $stmt = $pdo->query("
        SELECT COUNT(*) as count
        FROM orders
        WHERE MONTH(created_at) = MONTH(CURDATE())
        AND YEAR(created_at) = YEAR(CURDATE())
    ");
    $stats['monthly_orders'] = (int) $stmt->fetch()['count'];

    // Revenus ce mois
    try {
        $stmt = $pdo->query("
            SELECT COALESCE(SUM(total_amount), 0) as total
            FROM orders
            WHERE MONTH(created_at) = MONTH(CURDATE())
            AND YEAR(created_at) = YEAR(CURDATE())
            AND payment_status = 'paid'
        ");
        $stats['monthly_revenue'] = (float) $stmt->fetch()['total'];
    } catch (Exception $e) {
        $stmt = $pdo->query("
            SELECT COALESCE(SUM(total_amount), 0) as total
            FROM orders
            WHERE MONTH(created_at) = MONTH(CURDATE())
            AND YEAR(created_at) = YEAR(CURDATE())
        ");
        $stats['monthly_revenue'] = (float) $stmt->fetch()['total'];
    }

    // Taux de conversion (approximatif)
    $stats['conversion_rate'] = $stats['products'] > 0 ?
        round(($stats['orders'] / $stats['products']) * 100, 2) : 0;

    // Nombre d'abonnements (si la table existe)
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM subscriptions WHERE is_active = 1");
        $stats['subscriptions'] = (int) $stmt->fetch()['count'];
    } catch (Exception $e) {
        $stats['subscriptions'] = 0;
    }

    // Retourner les statistiques
    echo json_encode([
        'success' => true,
        'data' => $stats
    ]);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Erreur de base de données: ' . $e->getMessage()
    ]);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Erreur: ' . $e->getMessage()
    ]);
}
