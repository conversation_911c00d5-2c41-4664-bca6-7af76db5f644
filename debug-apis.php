<?php
require_once 'php/config/database.php';

try {
    echo "🔍 Diagnostic détaillé des erreurs APIs...\n\n";
    
    // Test de la base de données
    echo "📊 Test de la connexion base de données...\n";
    $database = new Database();
    $db = $database->getConnection();
    echo "  ✅ Connexion base de données OK\n";
    
    // Test direct des APIs avec capture d'erreurs
    echo "\n🔗 Test détaillé des APIs...\n";
    
    $apis = [
        'orders.php' => [
            'url' => 'http://localhost:8000/api/orders.php',
            'description' => 'API des commandes'
        ],
        'products.php' => [
            'url' => 'http://localhost:8000/api/products.php?store_id=3',
            'description' => 'API des produits'
        ],
        'firebase-users.php' => [
            'url' => 'http://localhost:8000/api/firebase-users.php?action=all',
            'description' => 'API des utilisateurs Firebase'
        ]
    ];
    
    foreach ($apis as $api => $config) {
        echo "  🧪 Test de {$config['description']} ($api)...\n";
        
        // Test avec cURL
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $config['url']);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer demo_token',
            'Content-Type: application/json'
        ]);
        curl_setopt($ch, CURLOPT_VERBOSE, true);
        curl_setopt($ch, CURLOPT_STDERR, fopen('php://temp', 'w+'));
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        echo "    📡 HTTP Code: $httpCode\n";
        
        if ($error) {
            echo "    ❌ Erreur cURL: $error\n";
        }
        
        if ($response) {
            echo "    📝 Réponse (premiers 200 caractères): " . substr($response, 0, 200) . "...\n";
            
            // Essayer de décoder le JSON
            $decoded = json_decode($response, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                if (isset($decoded['error'])) {
                    echo "    ❌ Erreur API: {$decoded['error']}\n";
                } elseif (isset($decoded['success'])) {
                    echo "    ✅ Réponse JSON valide\n";
                }
            } else {
                echo "    ⚠️ Réponse non-JSON ou malformée\n";
            }
        } else {
            echo "    ❌ Aucune réponse reçue\n";
        }
        
        // Test direct du fichier PHP
        echo "    🔧 Test direct du fichier PHP...\n";
        
        $apiPath = "api/$api";
        if (file_exists($apiPath)) {
            // Simuler les variables d'environnement
            $_SERVER['REQUEST_METHOD'] = 'GET';
            $_SERVER['REQUEST_URI'] = '/api/' . $api;
            $_SERVER['HTTP_AUTHORIZATION'] = 'Bearer demo_token';
            
            if ($api === 'products.php') {
                $_GET['store_id'] = '3';
            } elseif ($api === 'firebase-users.php') {
                $_GET['action'] = 'all';
            }
            
            // Capturer la sortie
            ob_start();
            $errorOccurred = false;
            
            try {
                // Inclure le fichier API
                include $apiPath;
            } catch (Exception $e) {
                $errorOccurred = true;
                echo "    ❌ Exception PHP: " . $e->getMessage() . "\n";
                echo "    📍 Fichier: " . $e->getFile() . " ligne " . $e->getLine() . "\n";
            } catch (Error $e) {
                $errorOccurred = true;
                echo "    ❌ Erreur PHP: " . $e->getMessage() . "\n";
                echo "    📍 Fichier: " . $e->getFile() . " ligne " . $e->getLine() . "\n";
            }
            
            $output = ob_get_clean();
            
            if (!$errorOccurred && $output) {
                echo "    ✅ Fichier PHP exécuté sans erreur\n";
                echo "    📝 Sortie: " . substr($output, 0, 100) . "...\n";
            } elseif (!$output && !$errorOccurred) {
                echo "    ⚠️ Fichier PHP exécuté mais aucune sortie\n";
            }
            
            // Nettoyer les variables globales
            unset($_GET['store_id'], $_GET['action']);
            
        } else {
            echo "    ❌ Fichier $apiPath non trouvé\n";
        }
        
        echo "\n";
    }
    
    // Vérifier les logs d'erreur PHP
    echo "📋 Vérification des logs d'erreur...\n";
    
    $errorLogPaths = [
        ini_get('error_log'),
        'error.log',
        'php_errors.log',
        '/var/log/php_errors.log'
    ];
    
    foreach ($errorLogPaths as $logPath) {
        if ($logPath && file_exists($logPath)) {
            echo "  📄 Log trouvé: $logPath\n";
            
            // Lire les dernières lignes
            $lines = file($logPath);
            if ($lines) {
                $recentLines = array_slice($lines, -10); // 10 dernières lignes
                foreach ($recentLines as $line) {
                    if (strpos($line, 'api/') !== false || strpos($line, 'Fatal') !== false || strpos($line, 'Error') !== false) {
                        echo "    🔍 " . trim($line) . "\n";
                    }
                }
            }
            break;
        }
    }
    
    // Test des fonctions critiques
    echo "\n🔧 Test des fonctions critiques...\n";
    
    // Test de la classe ApiResponse
    echo "  🧪 Test de la classe ApiResponse...\n";
    if (class_exists('ApiResponse')) {
        echo "    ✅ Classe ApiResponse existe\n";
    } else {
        echo "    ❌ Classe ApiResponse manquante\n";
    }
    
    // Test des fonctions de base de données
    echo "  🧪 Test des requêtes de base...\n";
    
    try {
        // Test requête simple
        $result = $db->query("SELECT COUNT(*) as count FROM users");
        $count = $result->fetch()['count'];
        echo "    ✅ Requête users OK ($count utilisateurs)\n";
    } catch (Exception $e) {
        echo "    ❌ Erreur requête users: " . $e->getMessage() . "\n";
    }
    
    try {
        // Test requête avec jointures (comme dans firebase-users.php)
        $query = "
            SELECT
                u.id,
                u.email,
                u.name,
                COALESCE(r.name, 'user') as role
            FROM users u
            LEFT JOIN user_roles ur ON u.id = ur.user_id
            LEFT JOIN roles r ON ur.role_id = r.id
            LIMIT 1
        ";
        $result = $db->query($query);
        $user = $result->fetch();
        echo "    ✅ Requête avec jointures OK\n";
    } catch (Exception $e) {
        echo "    ❌ Erreur requête avec jointures: " . $e->getMessage() . "\n";
    }
    
    // Recommandations
    echo "\n💡 Recommandations:\n";
    echo "  1. Vérifiez les logs d'erreur PHP pour plus de détails\n";
    echo "  2. Testez chaque API individuellement avec Postman ou curl\n";
    echo "  3. Vérifiez que toutes les tables existent et ont les bonnes colonnes\n";
    echo "  4. Assurez-vous que les classes et fonctions sont bien définies\n";
    
    echo "\n✅ Diagnostic terminé!\n";
    
} catch (Exception $e) {
    echo "❌ Erreur lors du diagnostic: " . $e->getMessage() . "\n";
    echo "📍 Fichier: " . $e->getFile() . " ligne " . $e->getLine() . "\n";
}
?>
