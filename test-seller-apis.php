<?php
/**
 * Test des APIs pour le dashboard vendeur
 */

echo "=== Test des APIs du dashboard vendeur ===\n\n";

// Configuration
$storeId = 3;
$baseUrl = 'http://localhost:8000';

// Test 1: API Products
echo "1. Test API Products\n";
echo "URL: {$baseUrl}/api/products-simple.php?store_id={$storeId}\n";

try {
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'header' => [
                'Authorization: Bearer demo_token',
                'Content-Type: application/json'
            ]
        ]
    ]);
    
    $response = file_get_contents("{$baseUrl}/api/products-simple.php?store_id={$storeId}", false, $context);
    
    if ($response === false) {
        echo "❌ Erreur lors de la requête\n";
    } else {
        echo "✅ Réponse reçue: " . substr($response, 0, 200) . "...\n";
        $data = json_decode($response, true);
        if ($data) {
            echo "✅ JSON valide\n";
            echo "Success: " . ($data['success'] ? 'true' : 'false') . "\n";
            if (isset($data['data'])) {
                echo "Nombre de produits: " . count($data['data']) . "\n";
            }
        } else {
            echo "❌ JSON invalide\n";
            echo "Réponse brute: $response\n";
        }
    }
} catch (Exception $e) {
    echo "❌ Exception: " . $e->getMessage() . "\n";
}

echo "\n" . str_repeat("-", 50) . "\n\n";

// Test 2: API Categories
echo "2. Test API Categories\n";
echo "URL: {$baseUrl}/api/categories.php?store_id={$storeId}\n";

try {
    $response = file_get_contents("{$baseUrl}/api/categories.php?store_id={$storeId}", false, $context);
    
    if ($response === false) {
        echo "❌ Erreur lors de la requête\n";
    } else {
        echo "✅ Réponse reçue: " . substr($response, 0, 200) . "...\n";
        $data = json_decode($response, true);
        if ($data) {
            echo "✅ JSON valide\n";
            echo "Success: " . ($data['success'] ? 'true' : 'false') . "\n";
            if (isset($data['data'])) {
                echo "Nombre de catégories: " . count($data['data']) . "\n";
            }
        } else {
            echo "❌ JSON invalide\n";
            echo "Réponse brute: $response\n";
        }
    }
} catch (Exception $e) {
    echo "❌ Exception: " . $e->getMessage() . "\n";
}

echo "\n" . str_repeat("-", 50) . "\n\n";

// Test 3: API Landing Pages
echo "3. Test API Landing Pages\n";
echo "URL: {$baseUrl}/api/landing-pages.php?store_id={$storeId}\n";

try {
    $response = file_get_contents("{$baseUrl}/api/landing-pages.php?store_id={$storeId}", false, $context);
    
    if ($response === false) {
        echo "❌ Erreur lors de la requête\n";
    } else {
        echo "✅ Réponse reçue: " . substr($response, 0, 200) . "...\n";
        $data = json_decode($response, true);
        if ($data) {
            echo "✅ JSON valide\n";
            echo "Success: " . ($data['success'] ? 'true' : 'false') . "\n";
            if (isset($data['data'])) {
                echo "Nombre de landing pages: " . count($data['data']) . "\n";
            }
        } else {
            echo "❌ JSON invalide\n";
            echo "Réponse brute: $response\n";
        }
    }
} catch (Exception $e) {
    echo "❌ Exception: " . $e->getMessage() . "\n";
}

echo "\n" . str_repeat("-", 50) . "\n\n";

// Test direct de la base de données
echo "4. Test direct base de données\n";

try {
    $pdo = new PDO(
        "mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4",
        'root',
        '',
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );

    // Test produits
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM products WHERE store_id = ?");
    $stmt->execute([$storeId]);
    $productCount = $stmt->fetch()['count'];
    echo "✅ Produits dans la DB pour store_id $storeId: $productCount\n";

    // Test catégories
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM categories WHERE store_id = ?");
    $stmt->execute([$storeId]);
    $categoryCount = $stmt->fetch()['count'];
    echo "✅ Catégories dans la DB pour store_id $storeId: $categoryCount\n";

    // Test landing pages
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM landing_pages WHERE store_id = ?");
    $stmt->execute([$storeId]);
    $landingPageCount = $stmt->fetch()['count'];
    echo "✅ Landing pages dans la DB pour store_id $storeId: $landingPageCount\n";

} catch (Exception $e) {
    echo "❌ Erreur DB: " . $e->getMessage() . "\n";
}

echo "\n=== Fin des tests ===\n";
?>
