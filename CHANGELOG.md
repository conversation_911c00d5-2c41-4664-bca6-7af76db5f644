# Changelog

## [1.0.0] - 2024-01-17

### Base de données
- ✨ Ajout de la colonne `domain` (varchar) à la table `stores`
- ✨ Ajout de la colonne `product_count` (int, default 0) à la table `stores`
- 📊 Création des index sur `domain`, `created_at` et `updated_at`
- ⚡️ Ajout de la procédure stockée `update_store_product_count`
- 🔄 Ajout des triggers `after_product_insert` et `after_product_delete`

### API
- 🌐 Mise à jour de `stores-simple.php` :
  - Support du multilangue (name_ar, name_en, description_ar, description_en)
  - Ajout du champ domain
  - Amélioration de la gestion des erreurs
  - Comptage des produits par store
- 🔄 Mise à jour de `stores.php` :
  - Support des champs bilingues
  - Validation améliorée des données
  - Intégration du comptage des produits

### Interface utilisateur
- 🎨 Mise à jour de `dashboard.html` :
  - Support complet du bilinguisme pour les stores
  - Ajout du bouton "Voir Détails"
  - Amélioration du formulaire d'édition
  - Intégration du champ domain
  - Affichage du nombre de produits
  - Amélioration des messages d'erreur
  - Utilisation de DEMO_TOKEN pour l'authentification

### Sécurité
- 🔒 Standardisation de l'authentification avec DEMO_TOKEN
- ✅ Validation renforcée des données côté serveur

### Performance
- ⚡️ Optimisation des requêtes SQL avec indexes
- 📊 Mise en cache du comptage des produits
- 🔄 Mise à jour automatique via triggers

### À venir
- [ ] Implémentation du cache Redis
- [ ] Optimisation des requêtes N+1
- [ ] Ajout de tests unitaires
- [ ] Documentation API complète
- [ ] Monitoring des performances