<?php
/**
 * Test AI API
 */

echo "<h2>AI API Test</h2>\n";
echo "<pre>\n";

// Test 1: Get AI Keys
echo "=== Test 1: Get AI Keys ===\n";
$_SERVER['REQUEST_METHOD'] = 'GET';
$_SERVER['REQUEST_URI'] = '/api/ai/keys';
$_SERVER['HTTP_AUTHORIZATION'] = 'Bearer demo_token';

ob_start();
try {
    include 'api/ai.php';
} catch (Exception $e) {
    echo "Exception: " . $e->getMessage() . "\n";
}
$output = ob_get_clean();
echo $output . "\n";

// Test 2: Get AI Usage
echo "=== Test 2: Get AI Usage ===\n";
$_SERVER['REQUEST_URI'] = '/api/ai/usage';

ob_start();
try {
    include 'api/ai.php';
} catch (Exception $e) {
    echo "Exception: " . $e->getMessage() . "\n";
}
$output = ob_get_clean();
echo $output . "\n";

// Test 3: Get AI Analytics
echo "=== Test 3: Get AI Analytics ===\n";
$_SERVER['REQUEST_URI'] = '/api/ai/analytics';

ob_start();
try {
    include 'api/ai.php';
} catch (Exception $e) {
    echo "Exception: " . $e->getMessage() . "\n";
}
$output = ob_get_clean();
echo $output . "\n";

echo "</pre>\n";
?>
