<?php

/**
 * Landing Page Preview
 * Displays a landing page by ID or slug
 */

require_once __DIR__ . '/api/config/database.php';

try {
    // Get page identifier (ID or slug)
    $id = isset($_GET['id']) ? $_GET['id'] : null;
    $slug = isset($_GET['slug']) ? $_GET['slug'] : null;

    if (!$id && !$slug) {
        http_response_code(400);
        echo "<!DOCTYPE html><html><head><title>Erreur</title></head><body><h1>ID ou slug requis</h1></body></html>";
        exit;
    }

    // Initialize database
    $database = new Database();
    $db = $database->getConnection();

    // Query based on ID or slug
    if ($id) {
        $query = "SELECT * FROM landing_pages WHERE id = ?";
        $stmt = $db->prepare($query);
        $stmt->execute([$id]);
    } else {
        $query = "SELECT * FROM landing_pages WHERE slug = ?";
        $stmt = $db->prepare($query);
        $stmt->execute([$slug]);
    }

    $page = $stmt->fetch();

    if (!$page) {
        http_response_code(404);
        echo "<!DOCTYPE html><html><head><title>Page non trouvée</title></head><body><h1>Landing page non trouvée</h1></body></html>";
        exit;
    }

    // Update view count
    $updateQuery = "UPDATE landing_pages SET views_count = views_count + 1 WHERE id = ?";
    $updateStmt = $db->prepare($updateQuery);
    $updateStmt->execute([$page['id']]);

    // If no content, generate from template
    $content = $page['content'];
    if (empty($content) && !empty($page['template_id'])) {
        $templateUrl = "http://localhost:8000/api/template-renderer.php?template=" . urlencode($page['template_id']);
        $content = @file_get_contents($templateUrl);

        if ($content === false) {
            $content = "<!DOCTYPE html><html><head><title>" . htmlspecialchars($page['title']) . "</title></head><body><h1>Contenu en cours de génération...</h1></body></html>";
        }
    }

    // Add analytics tracking if needed
    $analyticsScript = "
    <script>
        // Track page view
        console.log('Landing page viewed:', {
            id: " . $page['id'] . ",
            title: '" . addslashes($page['title']) . "',
            template: '" . addslashes($page['template_id']) . "'
        });

        // Track conversions on button clicks
        document.addEventListener('DOMContentLoaded', function() {
            const buttons = document.querySelectorAll('button, .btn, a[href*=\"contact\"], a[href*=\"buy\"], a[href*=\"order\"]');
            buttons.forEach(button => {
                button.addEventListener('click', function() {
                    console.log('Potential conversion tracked');
                    // Here you could send conversion data to your analytics
                });
            });
        });
    </script>
    ";

    // Inject analytics before closing body tag
    if (stripos($content, '</body>') !== false) {
        $content = str_ireplace('</body>', $analyticsScript . '</body>', $content);
    } else {
        $content .= $analyticsScript;
    }

    // Set proper headers
    header('Content-Type: text/html; charset=utf-8');

    // Output the content
    echo $content;
} catch (Exception $e) {
    http_response_code(500);
    echo "<!DOCTYPE html><html><head><title>Erreur</title></head><body><h1>Erreur serveur: " . htmlspecialchars($e->getMessage()) . "</h1></body></html>";
}
