<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payments API Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .result { background: #f5f5f5; padding: 10px; margin: 10px 0; }
        button { padding: 10px 15px; margin: 5px; }
        .success { color: green; }
        .error { color: red; }
    </style>
</head>
<body>
    <h1>Payments API Test</h1>
    
    <div class="test-section">
        <h3>Test 1: Get Payment Methods</h3>
        <button onclick="testGetPaymentMethods()">Get Payment Methods</button>
        <div id="methodsResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>Test 2: Create Payment Method</h3>
        <button onclick="testCreatePaymentMethod()">Create BaridiMob Method</button>
        <div id="createResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>Test 3: Get Payments</h3>
        <button onclick="testGetPayments()">Get Payments</button>
        <div id="paymentsResult" class="result"></div>
    </div>

    <script>
        async function testGetPaymentMethods() {
            try {
                const response = await fetch('/api/payments/methods', {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer demo_token',
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                document.getElementById('methodsResult').innerHTML = 
                    `<div class="success"><strong>Status:</strong> ${response.status}</div>
                     <strong>Payment Methods Found:</strong> ${data.success ? data.data.length : 0}<br>
                     <strong>Response:</strong><br>
                     <pre>${JSON.stringify(data, null, 2)}</pre>`;
            } catch (error) {
                document.getElementById('methodsResult').innerHTML = 
                    `<div class="error"><strong>Error:</strong> ${error.message}</div>`;
            }
        }
        
        async function testCreatePaymentMethod() {
            const methodData = {
                method_type: 'baridimob',
                method_name: 'BaridiMob - Paiement Mobile',
                configuration: {
                    number: '0555123456',
                    name: 'Nom du Commerçant',
                    instructions: 'Veuillez effectuer le paiement via BaridiMob vers le numéro indiqué et envoyer la capture d\'écran.'
                },
                is_active: 1,
                display_order: 1
            };
            
            try {
                const response = await fetch('/api/payments/methods', {
                    method: 'POST',
                    headers: {
                        'Authorization': 'Bearer demo_token',
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(methodData)
                });
                
                const data = await response.json();
                document.getElementById('createResult').innerHTML = 
                    `<div class="success"><strong>Status:</strong> ${response.status}</div>
                     <strong>Response:</strong><br>
                     <pre>${JSON.stringify(data, null, 2)}</pre>`;
            } catch (error) {
                document.getElementById('createResult').innerHTML = 
                    `<div class="error"><strong>Error:</strong> ${error.message}</div>`;
            }
        }
        
        async function testGetPayments() {
            try {
                const response = await fetch('/api/payments', {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer demo_token',
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                document.getElementById('paymentsResult').innerHTML = 
                    `<div class="success"><strong>Status:</strong> ${response.status}</div>
                     <strong>Payments Found:</strong> ${data.success ? data.data.payments.length : 0}<br>
                     <strong>Response:</strong><br>
                     <pre>${JSON.stringify(data, null, 2)}</pre>`;
            } catch (error) {
                document.getElementById('paymentsResult').innerHTML = 
                    `<div class="error"><strong>Error:</strong> ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
