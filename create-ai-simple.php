<?php
require_once 'api/config/database.php';

try {
    $database = new Database();
    $pdo = $database->getConnection();
    
    echo "Création des tables IA...\n";
    
    // Créer la table ai_models
    $createModels = "
    CREATE TABLE IF NOT EXISTS ai_models (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL UNIQUE,
        display_name VARCHAR(100) NOT NULL,
        provider VARCHAR(50) NOT NULL,
        model_type ENUM('text', 'image', 'audio', 'multimodal') DEFAULT 'text',
        cost_per_token DECIMAL(10,8) DEFAULT 0.0,
        max_tokens INT DEFAULT 4096,
        context_window INT DEFAULT 4096,
        is_active BOOLEAN DEFAULT TRUE,
        is_premium BOOLEAN DEFAULT FALSE,
        tier ENUM('economique', 'premium') DEFAULT 'economique',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    
    $pdo->exec($createModels);
    echo "✅ Table ai_models créée\n";
    
    // Créer la table ai_api_keys
    $createKeys = "
    CREATE TABLE IF NOT EXISTS ai_api_keys (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        provider VARCHAR(50) NOT NULL,
        api_key TEXT NOT NULL,
        status ENUM('active', 'inactive', 'expired', 'error') DEFAULT 'active',
        monthly_limit DECIMAL(10,2) DEFAULT 0.00,
        current_usage DECIMAL(10,2) DEFAULT 0.00,
        total_tokens_used BIGINT DEFAULT 0,
        total_requests INT DEFAULT 0,
        last_used_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    
    $pdo->exec($createKeys);
    echo "✅ Table ai_api_keys créée\n";
    
    // Insérer les modèles
    $insertModels = "
    INSERT IGNORE INTO ai_models (name, display_name, provider, cost_per_token, max_tokens, context_window, is_premium, tier) VALUES
    ('gpt-3.5-turbo', 'GPT-3.5-turbo', 'OpenAI', 0.0000020, 4096, 16385, FALSE, 'economique'),
    ('gpt-4o-mini', 'GPT-4o-mini', 'OpenAI', 0.0000005, 16384, 128000, FALSE, 'economique'),
    ('gpt-4', 'GPT-4', 'OpenAI', 0.0000300, 8192, 8192, TRUE, 'premium'),
    ('gpt-4-turbo', 'GPT-4-turbo', 'OpenAI', 0.0000200, 4096, 128000, TRUE, 'premium'),
    ('claude-3-haiku', 'Claude 3 Haiku', 'Anthropic', 0.00000025, 4096, 200000, FALSE, 'economique'),
    ('claude-3', 'Claude 3', 'Anthropic', 0.0000030, 4096, 200000, TRUE, 'premium'),
    ('gemini-1.5-flash', 'Gemini 1.5 Flash', 'Google', 0.00000015, 8192, 1000000, FALSE, 'economique'),
    ('gemini-pro', 'Gemini Pro', 'Google', 0.0000015, 32768, 2000000, TRUE, 'premium'),
    ('llama-3.1-8b', 'Llama 3.1 8B', 'Meta', 0.00000005, 8192, 128000, FALSE, 'economique')
    ";
    
    $pdo->exec($insertModels);
    echo "✅ Modèles IA insérés\n";
    
    // Insérer les API Keys
    $insertKeys = "
    INSERT IGNORE INTO ai_api_keys (name, provider, api_key, status, monthly_limit, current_usage, total_tokens_used, total_requests) VALUES
    ('OpenAI Production', 'OpenAI', 'sk-demo-key-openai-production-encrypted', 'active', 100.00, 45.72, 15240, 89),
    ('Anthropic Main', 'Anthropic', 'sk-demo-key-anthropic-main-encrypted', 'active', 50.00, 26.85, 8950, 67),
    ('Google AI Key', 'Google', 'demo-google-ai-key-encrypted', 'inactive', 30.00, 18.15, 12100, 45),
    ('OpenAI Backup', 'OpenAI', 'sk-demo-key-openai-backup-encrypted', 'active', 25.00, 5.14, 25680, 156),
    ('Meta Llama', 'Meta', 'demo-meta-llama-key-encrypted', 'inactive', 15.00, 2.26, 45200, 234)
    ";
    
    $pdo->exec($insertKeys);
    echo "✅ API Keys insérées\n";
    
    // Vérifier les résultats
    echo "\n📊 Modèles IA créés :\n";
    $stmt = $pdo->query("SELECT display_name, provider, tier, cost_per_token FROM ai_models ORDER BY provider, tier");
    $models = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($models as $model) {
        echo "- {$model['display_name']} ({$model['provider']}) - {$model['tier']} - \${$model['cost_per_token']}/token\n";
    }
    
    echo "\n🔑 API Keys créées :\n";
    $stmt = $pdo->query("SELECT name, provider, status, monthly_limit, current_usage, total_tokens_used FROM ai_api_keys ORDER BY provider");
    $keys = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($keys as $key) {
        $usage_percent = $key['monthly_limit'] > 0 ? round(($key['current_usage'] / $key['monthly_limit']) * 100, 1) : 0;
        echo "- {$key['name']} ({$key['provider']}) - {$key['status']} - \${$key['current_usage']}/\${$key['monthly_limit']} ({$usage_percent}%) - " . number_format($key['total_tokens_used']) . " tokens\n";
    }
    
    echo "\n✅ Structure IA créée avec succès !\n";
    
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
}
?>
