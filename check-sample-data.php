<?php

/**
 * Check sample data in database
 */

require_once 'api/config/database.php';

echo "<h2>Sample Data Check</h2>\n";
echo "<pre>\n";

try {
    $database = new Database();
    $db = $database->getConnection();

    // Check users
    echo "=== USERS ===\n";
    $users = $db->query("SELECT id, first_name, last_name, email, status FROM users LIMIT 5")->fetchAll();
    foreach ($users as $user) {
        $name = trim($user['first_name'] . ' ' . $user['last_name']);
        echo "ID: {$user['id']}, Name: {$name}, Email: {$user['email']}, Status: {$user['status']}\n";
    }

    // Check stores
    echo "\n=== STORES ===\n";
    $stores = $db->query("SELECT id, user_id, store_name, status FROM stores LIMIT 5")->fetchAll();
    foreach ($stores as $store) {
        echo "ID: {$store['id']}, User ID: {$store['user_id']}, Name: {$store['store_name']}, Status: {$store['status']}\n";
    }

    // Check user roles
    echo "\n=== USER ROLES ===\n";
    $roles = $db->query("SELECT user_id, role, is_active FROM user_roles LIMIT 5")->fetchAll();
    foreach ($roles as $role) {
        echo "User ID: {$role['user_id']}, Role: {$role['role']}, Active: {$role['is_active']}\n";
    }

    // Check products
    echo "\n=== PRODUCTS ===\n";
    $products = $db->query("SELECT id, store_id, name_ar, price, status FROM products LIMIT 5")->fetchAll();
    if (empty($products)) {
        echo "No products found.\n";
    } else {
        foreach ($products as $product) {
            echo "ID: {$product['id']}, Store ID: {$product['store_id']}, Name: {$product['name_ar']}, Price: {$product['price']}, Status: {$product['status']}\n";
        }
    }

    // Check if we need to create sample data
    if (empty($users) || empty($stores)) {
        echo "\n=== CREATING SAMPLE DATA ===\n";

        // Create sample user if none exists
        if (empty($users)) {
            $db->exec("INSERT INTO users (first_name, last_name, email, password_hash, status, created_at) VALUES ('Test', 'User', '<EMAIL>', 'password_hash', 'active', NOW())");
            $user_id = $db->lastInsertId();
            echo "Created sample user with ID: $user_id\n";
        } else {
            $user_id = $users[0]['id'];
        }

        // Create sample store if none exists
        if (empty($stores)) {
            $db->exec("INSERT INTO stores (user_id, store_name, store_name_ar, status, created_at) VALUES ($user_id, 'Test Store', 'متجر تجريبي', 'active', NOW())");
            $store_id = $db->lastInsertId();
            echo "Created sample store with ID: $store_id\n";
        }

        // Create user role if none exists
        if (empty($roles)) {
            $db->exec("INSERT INTO user_roles (user_id, role, is_active, created_at) VALUES ($user_id, 'seller', 1, NOW())");
            echo "Created sample user role for user ID: $user_id\n";
        }
    }
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

echo "</pre>\n";
