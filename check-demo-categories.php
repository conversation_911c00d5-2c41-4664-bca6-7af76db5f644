<?php
require_once 'config/database.php';

// Fonction pour le padding avec support UTF-8
if (!function_exists('mb_str_pad')) {
    function mb_str_pad($str, $pad_len, $pad_str = ' ', $dir = STR_PAD_RIGHT) {
        $str_len = mb_strlen($str);
        $pad_str_len = mb_strlen($pad_str);
        if (!$str_len && ($dir == STR_PAD_RIGHT || $dir == STR_PAD_LEFT)) {
            $str_len = 1;
        }
        if (!$pad_len || !$pad_str_len || $pad_len <= $str_len) {
            return $str;
        }
        $result = null;
        if ($dir == STR_PAD_BOTH) {
            $length = ($pad_len - $str_len) / 2;
            $repeat = ceil($length / $pad_str_len);
            $result = mb_substr(str_repeat($pad_str, $repeat), 0, floor($length))
                      . $str
                      . mb_substr(str_repeat($pad_str, $repeat), 0, ceil($length));
        } else {
            $repeat = ceil($pad_len / $pad_str_len);
            if ($dir == STR_PAD_RIGHT) {
                $result = $str . mb_substr(str_repeat($pad_str, $repeat), 0, $pad_len - $str_len);
            } else if ($dir == STR_PAD_LEFT) {
                $result = mb_substr(str_repeat($pad_str, $repeat), 0, $pad_len - $str_len) . $str;
            }
        }
        return $result;
    }
}

try {
    $db = new Database();
    $conn = $db->getConnection();

    // Définir l'encodage de la connexion
    $conn->exec("SET NAMES utf8mb4");
    $conn->exec("SET CHARACTER SET utf8mb4");

    // Vérifier les catégories créées
    $query = "SELECT id, name, name_fr, name_ar, slug, color FROM categories WHERE merchant_id = 1 ORDER BY sort_order";
    $stmt = $conn->prepare($query);
    $stmt->execute();

    echo "\nCatégories trouvées:\n";
    echo mb_str_pad('ID', 5) .
         mb_str_pad('Nom', 20) .
         mb_str_pad('Nom FR', 20) .
         mb_str_pad('Nom AR', 30) .
         mb_str_pad('Slug', 20) .
         "Couleur\n";
    echo str_repeat('-', 100) . "\n";

    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo mb_str_pad($row['id'], 5) .
             mb_str_pad($row['name'], 20) .
             mb_str_pad($row['name_fr'], 20) .
             mb_str_pad($row['name_ar'], 30) .
             mb_str_pad($row['slug'], 20) .
             $row['color'] . "\n";
    }

} catch (PDOException $e) {
    echo "Erreur de connexion : " . $e->getMessage();
}
