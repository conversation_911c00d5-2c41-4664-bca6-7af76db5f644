<?php

/**
 * Landing page pour les stores avec URLs personnalisées
 * Format: /store/{store_name} ou /{subdomain}
 */

require_once 'api/config/database.php';

// Configuration de la base de données
$host = 'localhost';
$port = 3307;
$dbname = 'landingpage_new';
$username = 'root';
$password = '';

try {
    // Connexion à la base de données
    $pdo = new PDO(
        "mysql:host={$host};port={$port};dbname={$dbname};charset=utf8mb4",
        $username,
        $password,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]
    );

    // Récupérer l'URL demandée
    $requestUri = $_SERVER['REQUEST_URI'];
    $path = parse_url($requestUri, PHP_URL_PATH);
    $pathParts = explode('/', trim($path, '/'));

    $storeIdentifier = null;
    $landingPageSlug = null;

    // Analyser l'URL pour déterminer le store et la landing page
    if (count($pathParts) >= 2 && $pathParts[0] === 'store') {
        // Format: /store/{store_name}/{landing_page_slug}
        $storeIdentifier = $pathParts[1];
        $landingPageSlug = $pathParts[2] ?? null;
        $identifierType = 'store_name';
    } else if (count($pathParts) >= 1) {
        // Format: /{subdomain}/{landing_page_slug}
        $storeIdentifier = $pathParts[0];
        $landingPageSlug = $pathParts[1] ?? null;
        $identifierType = 'subdomain';
    }

    if (!$storeIdentifier) {
        http_response_code(404);
        echo "Store non trouvé";
        exit;
    }

    // Récupérer le store
    if ($identifierType === 'store_name') {
        $storeQuery = "SELECT s.*, COUNT(p.id) as product_count
                       FROM stores s
                       LEFT JOIN products p ON s.id = p.store_id
                       WHERE (s.store_name = ? OR s.store_name_en = ? OR s.store_name_ar = ?)
                       AND s.status = 'active'
                       GROUP BY s.id";
        $storeStmt = $pdo->prepare($storeQuery);
        $storeStmt->execute([$storeIdentifier, $storeIdentifier, $storeIdentifier]);
    } else {
        $storeQuery = "SELECT s.*, COUNT(p.id) as product_count
                       FROM stores s
                       LEFT JOIN products p ON s.id = p.store_id
                       WHERE s.subdomain = ? AND s.status = 'active'
                       GROUP BY s.id";
        $storeStmt = $pdo->prepare($storeQuery);
        $storeStmt->execute([$storeIdentifier]);
    }

    $store = $storeStmt->fetch();

    if (!$store) {
        http_response_code(404);
        echo "Store non trouvé";
        exit;
    }

    // Si pas de landing page spécifiée, récupérer la landing page principale du store
    if (!$landingPageSlug) {
        $landingQuery = "SELECT * FROM landing_pages WHERE merchant_id = ? AND status = 'published' ORDER BY created_at DESC LIMIT 1";
        $landingStmt = $pdo->prepare($landingQuery);
        $landingStmt->execute([$store['id']]);
    } else {
        $landingQuery = "SELECT * FROM landing_pages WHERE merchant_id = ? AND slug = ? AND status = 'published'";
        $landingStmt = $pdo->prepare($landingQuery);
        $landingStmt->execute([$store['id'], $landingPageSlug]);
    }

    $landingPage = $landingStmt->fetch();

    if (!$landingPage) {
        // Si pas de landing page trouvée, créer une page par défaut
        $defaultContent = generateDefaultLandingPage($store);
        echo $defaultContent;
        exit;
    }

    // Récupérer le template
    $templateQuery = "SELECT * FROM templates WHERE id = ?";
    $templateStmt = $pdo->prepare($templateQuery);
    $templateStmt->execute([$landingPage['template_id']]);
    $template = $templateStmt->fetch();

    // Générer la page
    $pageContent = renderLandingPage($landingPage, $template, $store);
    echo $pageContent;
} catch (Exception $e) {
    http_response_code(500);
    echo "Erreur: " . $e->getMessage();
}

/**
 * Génère une landing page par défaut pour un store
 */
function generateDefaultLandingPage($store)
{
    global $pdo;

    $storeName = $store['store_name'] ?? $store['store_name_en'] ?? $store['store_name_ar'] ?? 'Mon Store';
    $description = $store['description'] ?? $store['description_en'] ?? $store['description_ar'] ?? 'Bienvenue dans notre store';
    $productCount = $store['product_count'] ?? 0;

    // Récupérer quelques produits du store
    $productsHtml = '';
    if ($productCount > 0) {
        $productsQuery = "SELECT * FROM products WHERE store_id = ? AND status = 'active' LIMIT 6";
        $productsStmt = $pdo->prepare($productsQuery);
        $productsStmt->execute([$store['id']]);
        $products = $productsStmt->fetchAll();

        if ($products) {
            $productsHtml = '<div class="products-section">
                <h2>Nos Produits</h2>
                <div class="products-grid">';

            foreach ($products as $product) {
                $productName = $product['name'] ?? 'Produit';
                $productPrice = $product['price'] ?? 0;
                $productImage = $product['image_url'] ?? 'https://via.placeholder.com/200x200?text=Produit';

                $productsHtml .= "
                    <div class='product-card'>
                        <img src='{$productImage}' alt='{$productName}' onerror='this.src=\"https://via.placeholder.com/200x200?text=Produit\"'>
                        <h3>{$productName}</h3>
                        <p class='price'>{$productPrice} DA</p>
                    </div>";
            }

            $productsHtml .= '</div></div>';
        }
    }

    return "<!DOCTYPE html>
<html lang='fr'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>{$storeName}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; text-align: center; margin-bottom: 20px; }
        h2 { color: #333; margin: 40px 0 20px 0; }
        p { color: #666; line-height: 1.6; text-align: center; }
        .cta { background: #007cba; color: white; padding: 15px 30px; border: none; border-radius: 5px; font-size: 16px; cursor: pointer; display: block; margin: 30px auto; }
        .cta:hover { background: #005a87; }
        .products-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-top: 20px; }
        .product-card { border: 1px solid #ddd; border-radius: 8px; padding: 15px; text-align: center; transition: transform 0.2s; }
        .product-card:hover { transform: translateY(-5px); box-shadow: 0 4px 15px rgba(0,0,0,0.1); }
        .product-card img { width: 100%; height: 150px; object-fit: cover; border-radius: 5px; }
        .product-card h3 { margin: 10px 0; color: #333; }
        .price { font-weight: bold; color: #007cba; font-size: 18px; }
        .stats { text-align: center; margin: 20px 0; color: #666; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>{$storeName}</h1>
        <p>{$description}</p>
        <div class='stats'>
            <strong>{$productCount}</strong> produit(s) disponible(s)
        </div>
        {$productsHtml}
        <button class='cta' onclick='alert(\"Contactez-nous pour plus d'informations!\")'>Contactez-nous</button>
    </div>
</body>
</html>";
}

/**
 * Rend une landing page avec son template
 */
function renderLandingPage($landingPage, $template, $store)
{
    $content = $landingPage['content'];

    // Remplacer les variables du store dans le contenu
    $storeName = $store['store_name'] ?? $store['store_name_en'] ?? $store['store_name_ar'] ?? 'Mon Store';
    $content = str_replace('{{store_name}}', $storeName, $content);
    $content = str_replace('{{store_description}}', $store['description'] ?? '', $content);

    if ($template) {
        // Si un template existe, l'utiliser
        $templateContent = $template['content'];
        $content = str_replace('{{content}}', $content, $templateContent);
    }

    return $content;
}
