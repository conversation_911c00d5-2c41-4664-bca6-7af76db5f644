/**
 * Gestion des rôles et permissions
 * @file roles.js
 */

// Fonction pour charger les rôles
async function loadRoles() {
    const container = document.getElementById('rolesContainer');
    if (!container) return;

    try {
        const token = await getFirebaseToken();
        const response = await fetch('/api/roles.php', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (!response.ok) {
            throw new Error(`Erreur HTTP: ${response.status}`);
        }

        const data = await response.json();
        if (!data.success) {
            throw new Error(data.message || 'Erreur lors du chargement des rôles');
        }

        const roles = data.data.roles;
        let html = `
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>Rôle</th>
                            <th>Description</th>
                            <th>Utilisateurs</th>
                            <th>Permissions</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        roles.forEach(role => {
            let permissions = {};
            try {
                // Handle different permission formats
                if (typeof role.permissions === 'string') {
                    permissions = JSON.parse(role.permissions);
                } else if (typeof role.permissions === 'object' && role.permissions !== null) {
                    permissions = role.permissions;
                } else {
                    permissions = {};
                }
            } catch (e) {
                console.warn('Invalid permissions format for role:', role.name, role.permissions);
                permissions = {};
            }

            const permissionBadges = Object.keys(permissions)
                .map(p => `<span class="badge bg-light text-dark me-1">${p}</span>`)
                .join('');

            html += `
                <tr>
                    <td>
                        <span class="badge bg-${role.is_system ? 'primary' : 'secondary'} fs-6">
                            ${role.display_name}
                        </span>
                    </td>
                    <td>${role.description || '-'}</td>
                    <td>
                        <span class="badge bg-info">${role.user_count} utilisateur(s)</span>
                    </td>
                    <td>${permissionBadges}</td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary me-1"
                                onclick="editRole(${role.id})"
                                ${role.is_system ? 'disabled' : ''}>
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-info me-1"
                                onclick="viewRoleUsers(${role.id})">
                            <i class="fas fa-users"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger"
                                onclick="deleteRole(${role.id})"
                                ${role.is_system ? 'disabled' : ''}>
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `;
        });

        html += `
                    </tbody>
                </table>
            </div>
        `;

        container.innerHTML = html;
    } catch (error) {
        console.error('❌ Erreur lors du chargement des rôles:', error);
        container.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                Erreur lors du chargement des rôles: ${error.message}
            </div>
        `;
    }
}

// Fonction pour créer un nouveau rôle
async function addNewRole() {
    const modalHtml = `
        <div class="modal fade" id="addRoleModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Créer un Nouveau Rôle</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="addRoleForm">
                            <div class="mb-3">
                                <label for="roleName" class="form-label">Nom du Rôle *</label>
                                <input type="text" class="form-control" id="roleName" required>
                            </div>
                            <div class="mb-3">
                                <label for="roleDisplayName" class="form-label">Nom d'affichage *</label>
                                <input type="text" class="form-control" id="roleDisplayName" required>
                            </div>
                            <div class="mb-3">
                                <label for="roleDescription" class="form-label">Description</label>
                                <textarea class="form-control" id="roleDescription" rows="3"></textarea>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Permissions</label>
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" id="permCreate" value="create">
                                    <label class="form-check-label" for="permCreate">Créer</label>
                                </div>
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" id="permRead" value="read">
                                    <label class="form-check-label" for="permRead">Lire</label>
                                </div>
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" id="permUpdate" value="update">
                                    <label class="form-check-label" for="permUpdate">Modifier</label>
                                </div>
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" id="permDelete" value="delete">
                                    <label class="form-check-label" for="permDelete">Supprimer</label>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Limites</label>
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label for="maxProducts" class="form-label">Produits max</label>
                                        <input type="number" class="form-control" id="maxProducts" value="10">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="maxLandingPages" class="form-label">Landing pages max</label>
                                        <input type="number" class="form-control" id="maxLandingPages" value="1">
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                        <button type="button" class="btn btn-primary" onclick="submitNewRole()">Créer</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Ajouter le modal au DOM s'il n'existe pas déjà
    if (!document.getElementById('addRoleModal')) {
        document.body.insertAdjacentHTML('beforeend', modalHtml);
    }

    // Afficher le modal
    const modal = new bootstrap.Modal(document.getElementById('addRoleModal'));
    modal.show();
}

// Fonction pour soumettre un nouveau rôle
async function submitNewRole() {
    const name = document.getElementById('roleName').value;
    const displayName = document.getElementById('roleDisplayName').value;
    const description = document.getElementById('roleDescription').value;

    if (!name.trim() || !displayName.trim()) {
        alert('Le nom et le nom d\'affichage sont requis');
        return;
    }

    // Collecter les permissions
    const permissions = {};
    document.querySelectorAll('#addRoleForm input[type="checkbox"]:checked').forEach(checkbox => {
        permissions[checkbox.value] = true;
    });

    const roleData = {
        name: name.toLowerCase().replace(/\s+/g, '_'),
        display_name: displayName,
        description: description,
        permissions: permissions,
        max_products: parseInt(document.getElementById('maxProducts').value) || 10,
        max_landing_pages: parseInt(document.getElementById('maxLandingPages').value) || 1
    };

    try {
        const token = await getFirebaseToken();
        const response = await fetch('/api/roles.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify(roleData)
        });

        const data = await response.json();
        if (!data.success) {
            throw new Error(data.message);
        }

        // Fermer le modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('addRoleModal'));
        modal.hide();

        // Recharger la liste des rôles
        loadRoles();

        // Afficher un message de succès
        showToast('Succès', 'Le rôle a été créé avec succès', 'success');
    } catch (error) {
        console.error('❌ Erreur lors de la création du rôle:', error);
        showToast('Erreur', error.message, 'error');
    }
}

// Fonction pour éditer un rôle
async function editRole(roleId) {
    try {
        const token = await getFirebaseToken();
        const response = await fetch(`/api/roles.php/${roleId}`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (!response.ok) {
            throw new Error(`Erreur HTTP: ${response.status}`);
        }

        const data = await response.json();
        if (!data.success) {
            throw new Error(data.message);
        }

        const role = data.data;
        const modalHtml = `
            <div class="modal fade" id="editRoleModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Modifier le Rôle</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="editRoleForm">
                                <input type="hidden" id="editRoleId" value="${role.id}">
                                <div class="mb-3">
                                    <label for="editRoleName" class="form-label">Nom du Rôle *</label>
                                    <input type="text" class="form-control" id="editRoleName" value="${role.name}" required>
                                </div>
                                <div class="mb-3">
                                    <label for="editRoleDisplayName" class="form-label">Nom d'affichage *</label>
                                    <input type="text" class="form-control" id="editRoleDisplayName" value="${role.display_name}" required>
                                </div>
                                <div class="mb-3">
                                    <label for="editRoleDescription" class="form-label">Description</label>
                                    <textarea class="form-control" id="editRoleDescription" rows="3">${role.description || ''}</textarea>
                                </div>
                                <!-- Permissions -->
                                <div class="mb-3">
                                    <label class="form-label">Permissions</label>
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="editPermCreate" value="create" ${role.permissions.create ? 'checked' : ''}>
                                        <label class="form-check-label" for="editPermCreate">Créer</label>
                                    </div>
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="editPermRead" value="read" ${role.permissions.read ? 'checked' : ''}>
                                        <label class="form-check-label" for="editPermRead">Lire</label>
                                    </div>
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="editPermUpdate" value="update" ${role.permissions.update ? 'checked' : ''}>
                                        <label class="form-check-label" for="editPermUpdate">Modifier</label>
                                    </div>
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="editPermDelete" value="delete" ${role.permissions.delete ? 'checked' : ''}>
                                        <label class="form-check-label" for="editPermDelete">Supprimer</label>
                                    </div>
                                </div>
                                <!-- Limites -->
                                <div class="mb-3">
                                    <label class="form-label">Limites</label>
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <label for="editMaxProducts" class="form-label">Produits max</label>
                                            <input type="number" class="form-control" id="editMaxProducts" value="${role.max_products}">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="editMaxLandingPages" class="form-label">Landing pages max</label>
                                            <input type="number" class="form-control" id="editMaxLandingPages" value="${role.max_landing_pages}">
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                            <button type="button" class="btn btn-primary" onclick="submitEditRole()">Enregistrer</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Ajouter le modal au DOM s'il n'existe pas déjà
        if (!document.getElementById('editRoleModal')) {
            document.body.insertAdjacentHTML('beforeend', modalHtml);
        }

        // Afficher le modal
        const modal = new bootstrap.Modal(document.getElementById('editRoleModal'));
        modal.show();
    } catch (error) {
        console.error('❌ Erreur lors du chargement du rôle:', error);
        showToast('Erreur', error.message, 'error');
    }
}

// Fonction pour soumettre les modifications d'un rôle
async function submitEditRole() {
    const roleId = document.getElementById('editRoleId').value;
    const name = document.getElementById('editRoleName').value;
    const displayName = document.getElementById('editRoleDisplayName').value;
    const description = document.getElementById('editRoleDescription').value;

    if (!name.trim() || !displayName.trim()) {
        alert('Le nom et le nom d\'affichage sont requis');
        return;
    }

    // Collecter les permissions
    const permissions = {};
    document.querySelectorAll('#editRoleForm input[type="checkbox"]:checked').forEach(checkbox => {
        permissions[checkbox.value] = true;
    });

    const roleData = {
        name: name.toLowerCase().replace(/\s+/g, '_'),
        display_name: displayName,
        description: description,
        permissions: permissions,
        max_products: parseInt(document.getElementById('editMaxProducts').value) || 10,
        max_landing_pages: parseInt(document.getElementById('editMaxLandingPages').value) || 1
    };

    try {
        const token = await getFirebaseToken();
        const response = await fetch(`/api/roles.php/${roleId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify(roleData)
        });

        const data = await response.json();
        if (!data.success) {
            throw new Error(data.message);
        }

        // Fermer le modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('editRoleModal'));
        modal.hide();

        // Recharger la liste des rôles
        loadRoles();

        // Afficher un message de succès
        showToast('Succès', 'Le rôle a été modifié avec succès', 'success');
    } catch (error) {
        console.error('❌ Erreur lors de la modification du rôle:', error);
        showToast('Erreur', error.message, 'error');
    }
}

// Fonction pour voir les utilisateurs d'un rôle
async function viewRoleUsers(roleId) {
    try {
        const token = await getFirebaseToken();
        const response = await fetch(`/api/roles.php/${roleId}/users`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (!response.ok) {
            throw new Error(`Erreur HTTP: ${response.status}`);
        }

        const data = await response.json();
        if (!data.success) {
            throw new Error(data.message);
        }

        const users = data.data?.users || [];
        const modalHtml = `
            <div class="modal fade" id="viewRoleUsersModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Utilisateurs du Rôle</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Email</th>
                                            <th>Nom</th>
                                            <th>Statut</th>
                                            <th>Dernière connexion</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${Array.isArray(users) && users.length > 0 ? users.map(user => `
                                            <tr>
                                                <td>${user.email || '-'}</td>
                                                <td>${user.display_name || '-'}</td>
                                                <td>
                                                    <span class="badge bg-${user.status === 'active' ? 'success' : 'danger'}">
                                                        ${(user.status || 'inactive').toUpperCase()}
                                                    </span>
                                                </td>
                                                <td>${user.last_login ? new Date(user.last_login).toLocaleDateString() : 'Jamais'}</td>
                                            </tr>
                                        `).join('') : '<tr><td colspan="4" class="text-center">Aucun utilisateur trouvé pour ce rôle</td></tr>'}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Ajouter le modal au DOM s'il n'existe pas déjà
        if (!document.getElementById('viewRoleUsersModal')) {
            document.body.insertAdjacentHTML('beforeend', modalHtml);
        }

        // Afficher le modal
        const modal = new bootstrap.Modal(document.getElementById('viewRoleUsersModal'));
        modal.show();
    } catch (error) {
        console.error('❌ Erreur lors du chargement des utilisateurs:', error);
        showToast('Erreur', error.message, 'error');
    }
}

// Fonction pour supprimer un rôle
async function deleteRole(roleId) {
    if (!confirm('Êtes-vous sûr de vouloir supprimer ce rôle ? Cette action est irréversible.')) {
        return;
    }

    try {
        const token = await getFirebaseToken();
        const response = await fetch(`/api/roles.php/${roleId}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        const data = await response.json();
        if (!data.success) {
            throw new Error(data.message);
        }

        // Recharger la liste des rôles
        loadRoles();

        // Afficher un message de succès
        showToast('Succès', 'Le rôle a été supprimé avec succès', 'success');
    } catch (error) {
        console.error('❌ Erreur lors de la suppression du rôle:', error);
        showToast('Erreur', error.message, 'error');
    }
}

// Fonction utilitaire pour afficher les toasts
function showToast(title, message, type = 'info') {
    const toastHtml = `
        <div class="toast align-items-center text-white bg-${type === 'error' ? 'danger' : 'success'} border-0" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body">
                    <strong>${title}</strong><br>
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `;

    // Créer le conteneur de toast s'il n'existe pas
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
        document.body.appendChild(toastContainer);
    }

    // Ajouter le nouveau toast
    toastContainer.insertAdjacentHTML('beforeend', toastHtml);

    // Initialiser et afficher le toast
    const toastElement = toastContainer.lastElementChild;
    const toast = new bootstrap.Toast(toastElement, {
        autohide: true,
        delay: 5000
    });
    toast.show();

    // Supprimer le toast après qu'il soit caché
    toastElement.addEventListener('hidden.bs.toast', () => {
        toastElement.remove();
    });
}
