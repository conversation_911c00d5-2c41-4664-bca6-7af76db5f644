# API Configuration for Landing Page SaaS
# Rewrite rules and security settings

# Enable rewrite engine
RewriteEngine On

# Security Headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
Header always set Referrer-Policy "strict-origin-when-cross-origin"
Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' https:; connect-src 'self' https:;"

# CORS Headers for API
Header always set Access-Control-Allow-Origin "*"
Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With"
Header always set Access-Control-Max-Age "3600"

# Handle preflight OPTIONS requests
RewriteCond %{REQUEST_METHOD} OPTIONS
RewriteRule ^(.*)$ $1 [R=200,L]

# API Routes
# Route all API requests to appropriate endpoints

# Health check
RewriteRule ^health/?$ index.php [L,QSA]

# Documentation
RewriteRule ^(docs|documentation)/?$ index.php [L,QSA]

# Products API
RewriteRule ^products/?$ products.php [L,QSA]
RewriteRule ^products/([0-9]+)/?$ products.php [L,QSA]
RewriteRule ^products/([0-9]+)/(.+)/?$ products.php [L,QSA]

# Orders API
RewriteRule ^orders/?$ orders.php [L,QSA]
RewriteRule ^orders/([0-9]+)/?$ orders.php [L,QSA]
RewriteRule ^orders/([0-9]+)/(.+)/?$ orders.php [L,QSA]
RewriteRule ^orders/(stats|statistics)/?$ orders.php [L,QSA]

# Payments API
RewriteRule ^payments/?$ payments.php [L,QSA]
RewriteRule ^payments/([0-9]+)/?$ payments.php [L,QSA]
RewriteRule ^payments/(methods)/?$ payments.php [L,QSA]
RewriteRule ^payments/methods/([0-9]+)/?$ payments.php [L,QSA]

# Stores API
RewriteRule ^stores/?$ stores.php [L,QSA]
RewriteRule ^stores/([0-9]+)/?$ stores.php [L,QSA]
RewriteRule ^stores/(settings|all)/?$ stores.php [L,QSA]

# Users API
RewriteRule ^users/?$ users.php [L,QSA]
RewriteRule ^users/([a-zA-Z0-9_-]+)/?$ users.php [L,QSA]
RewriteRule ^users/(profile|roles|all|permissions)/?$ users.php [L,QSA]
RewriteRule ^users/permissions/([a-zA-Z0-9_-]+)/?$ users.php [L,QSA]

# AI API
RewriteRule ^ai/?$ ai.php [L,QSA]
RewriteRule ^ai/([0-9]+)/?$ ai.php [L,QSA]
RewriteRule ^ai/(keys|usage|analytics)/?$ ai.php [L,QSA]
RewriteRule ^ai/keys/([0-9]+)/?$ ai.php [L,QSA]
RewriteRule ^ai/keys/([0-9]+)/(test)/?$ ai.php [L,QSA]

# Analytics API
RewriteRule ^analytics/?$ analytics.php [L,QSA]
RewriteRule ^analytics/(dashboard|sales|products|orders|customers|payments)/?$ analytics.php [L,QSA]

# Default route to index.php for documentation
RewriteRule ^$ index.php [L,QSA]

# Security: Deny access to sensitive files
<Files "*.sql">
    Require all denied
</Files>

<Files "*.log">
    Require all denied
</Files>

<Files "config.php">
    Require all denied
</Files>

<Files ".env">
    Require all denied
</Files>

# Deny access to .htaccess itself
<Files ".htaccess">
    Require all denied
</Files>

# Prevent access to backup files
<FilesMatch "\.(bak|backup|old|tmp|temp)$">
    Require all denied
</FilesMatch>

# Set proper MIME types
AddType application/json .json
AddType text/plain .txt
AddType text/xml .xml

# Compression for better performance
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# Cache control for API responses
<IfModule mod_expires.c>
    ExpiresActive On
    
    # No cache for API responses by default
    ExpiresByType application/json "access plus 0 seconds"
    
    # Cache static documentation for 1 hour
    ExpiresByType text/html "access plus 1 hour"
    ExpiresByType text/css "access plus 1 day"
    ExpiresByType application/javascript "access plus 1 day"
</IfModule>

# Rate limiting (if mod_evasive is available)
<IfModule mod_evasive24.c>
    DOSHashTableSize    2048
    DOSPageCount        10
    DOSPageInterval     1
    DOSSiteCount        50
    DOSSiteInterval     1
    DOSBlockingPeriod   600
</IfModule>

# Logging
LogLevel warn
ErrorLog logs/api_error.log
CustomLog logs/api_access.log combined

# PHP Settings
<IfModule mod_php.c>
    php_value upload_max_filesize 10M
    php_value post_max_size 10M
    php_value max_execution_time 30
    php_value max_input_time 30
    php_value memory_limit 128M
    php_flag display_errors Off
    php_flag log_errors On
    php_value error_log logs/php_errors.log
</IfModule>

# Prevent directory browsing
Options -Indexes

# Follow symbolic links
Options +FollowSymLinks

# Charset
AddDefaultCharset UTF-8

# Server signature
ServerSignature Off

# Hide server information
Header unset Server
Header unset X-Powered-By

# Prevent hotlinking (optional)
# RewriteCond %{HTTP_REFERER} !^$
# RewriteCond %{HTTP_REFERER} !^https?://(www\.)?yourdomain\.com [NC]
# RewriteRule \.(jpg|jpeg|png|gif|svg|pdf)$ - [F,L]

# Force HTTPS (uncomment if needed)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# API versioning support (for future use)
# RewriteRule ^v1/(.*)$ $1 [L,QSA]
# RewriteRule ^v2/(.*)$ v2/$1 [L,QSA]