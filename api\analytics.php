<?php
/**
 * Analytics API Endpoint
 * Handles analytics and statistics for seller dashboard
 */

require_once 'config/database.php';

// Set headers
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    $database = new Database();
    $db = $database->getConnection();
    $auth = new Auth($db);
    
    // Get request method and path
    $method = $_SERVER['REQUEST_METHOD'];
    $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
    $pathParts = explode('/', trim($path, '/'));
    
    // Extract action from path (e.g., /api/analytics/dashboard, /api/analytics/sales)
    $action = isset($pathParts[2]) ? $pathParts[2] : 'dashboard';
    
    // Authenticate user
    $user = $auth->authenticateUser();
    if (!$user) {
        ApiResponse::error('Authentication required', 401);
    }
    
    // Get user's store
    $store = $auth->getUserStore($user['uid']);
    if (!$store && !$auth->hasPermission($user['uid'], 'admin')) {
        ApiResponse::error('Store access required', 403);
    }
    
    if ($method !== 'GET') {
        ApiResponse::error('Method not allowed', 405);
    }
    
    switch ($action) {
        case 'dashboard':
            getDashboardStats($db, $store['id'], $_GET);
            break;
        case 'sales':
            getSalesAnalytics($db, $store['id'], $_GET);
            break;
        case 'products':
            getProductAnalytics($db, $store['id'], $_GET);
            break;
        case 'orders':
            getOrderAnalytics($db, $store['id'], $_GET);
            break;
        case 'payments':
            getPaymentAnalytics($db, $store['id'], $_GET);
            break;
        case 'customers':
            getCustomerAnalytics($db, $store['id'], $_GET);
            break;
        default:
            ApiResponse::error('Invalid analytics endpoint', 404);
    }
    
} catch (Exception $e) {
    error_log('Analytics API Error: ' . $e->getMessage());
    ApiResponse::error('Internal server error', 500);
}

/**
 * Get dashboard overview statistics
 */
function getDashboardStats($db, $store_id, $params) {
    try {
        $period = isset($params['period']) ? $params['period'] : '30d';
        
        // Determine date range
        switch ($period) {
            case '7d':
                $date_from = date('Y-m-d', strtotime('-7 days'));
                break;
            case '30d':
                $date_from = date('Y-m-d', strtotime('-30 days'));
                break;
            case '90d':
                $date_from = date('Y-m-d', strtotime('-90 days'));
                break;
            case '1y':
                $date_from = date('Y-m-d', strtotime('-1 year'));
                break;
            default:
                $date_from = date('Y-m-d', strtotime('-30 days'));
        }
        
        // Get overall statistics
        $stats_query = "
            SELECT 
                COUNT(DISTINCT o.id) as total_orders,
                COALESCE(SUM(o.total_amount), 0) as total_revenue,
                COUNT(DISTINCT p.id) as total_products,
                COUNT(DISTINCT o.customer_email) as total_customers,
                COUNT(DISTINCT CASE WHEN o.created_at >= ? THEN o.id END) as orders_period,
                COALESCE(SUM(CASE WHEN o.created_at >= ? THEN o.total_amount ELSE 0 END), 0) as revenue_period,
                COUNT(DISTINCT CASE WHEN p.status = 'active' THEN p.id END) as active_products,
                COUNT(DISTINCT CASE WHEN o.created_at >= ? AND o.status = 'pending' THEN o.id END) as pending_orders
            FROM stores s
            LEFT JOIN orders o ON s.id = o.store_id
            LEFT JOIN products p ON s.id = p.store_id
            WHERE s.id = ?
        ";
        
        $stats_stmt = $db->prepare($stats_query);
        $stats_stmt->execute([$date_from, $date_from, $date_from, $store_id]);
        $stats = $stats_stmt->fetch(PDO::FETCH_ASSOC);
        
        // Calculate conversion rate
        $conversion_query = "
            SELECT 
                COUNT(DISTINCT lp.id) as total_page_views,
                COUNT(DISTINCT o.id) as total_conversions
            FROM landing_pages lp
            LEFT JOIN orders o ON lp.merchant_id = (SELECT merchant_id FROM stores WHERE id = ?)
            WHERE lp.merchant_id = (SELECT merchant_id FROM stores WHERE id = ?)
            AND lp.created_at >= ?
        ";
        
        $conversion_stmt = $db->prepare($conversion_query);
        $conversion_stmt->execute([$store_id, $store_id, $date_from]);
        $conversion_data = $conversion_stmt->fetch(PDO::FETCH_ASSOC);
        
        $conversion_rate = $conversion_data['total_page_views'] > 0 
            ? round(($conversion_data['total_conversions'] / $conversion_data['total_page_views']) * 100, 2)
            : 0;
        
        // Get recent orders
        $recent_orders_query = "
            SELECT id, order_number, customer_name, total_amount, status, created_at
            FROM orders 
            WHERE store_id = ? 
            ORDER BY created_at DESC 
            LIMIT 5
        ";
        
        $recent_orders_stmt = $db->prepare($recent_orders_query);
        $recent_orders_stmt->execute([$store_id]);
        $recent_orders = $recent_orders_stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Get top products
        $top_products_query = "
            SELECT 
                p.id, p.name, p.name_ar, p.price,
                COUNT(oi.id) as order_count,
                SUM(oi.quantity) as total_sold,
                SUM(oi.quantity * oi.price) as total_revenue
            FROM products p
            LEFT JOIN order_items oi ON p.id = oi.product_id
            LEFT JOIN orders o ON oi.order_id = o.id
            WHERE p.store_id = ? AND (o.created_at >= ? OR o.created_at IS NULL)
            GROUP BY p.id
            ORDER BY total_sold DESC
            LIMIT 5
        ";
        
        $top_products_stmt = $db->prepare($top_products_query);
        $top_products_stmt->execute([$store_id, $date_from]);
        $top_products = $top_products_stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Get AI usage summary
        $ai_usage_query = "
            SELECT 
                COUNT(*) as total_requests,
                SUM(tokens_used) as total_tokens,
                SUM(cost) as total_cost
            FROM ai_usage 
            WHERE store_id = ? AND created_at >= ?
        ";
        
        $ai_usage_stmt = $db->prepare($ai_usage_query);
        $ai_usage_stmt->execute([$store_id, $date_from]);
        $ai_usage = $ai_usage_stmt->fetch(PDO::FETCH_ASSOC);
        
        ApiResponse::success([
            'period' => $period,
            'date_range' => [
                'from' => $date_from,
                'to' => date('Y-m-d')
            ],
            'stats' => $stats,
            'conversion_rate' => $conversion_rate,
            'recent_orders' => $recent_orders,
            'top_products' => $top_products,
            'ai_usage' => $ai_usage
        ]);
        
    } catch (Exception $e) {
        error_log('Get dashboard stats error: ' . $e->getMessage());
        ApiResponse::error('Failed to retrieve dashboard statistics');
    }
}

/**
 * Get sales analytics
 */
function getSalesAnalytics($db, $store_id, $params) {
    try {
        $period = isset($params['period']) ? $params['period'] : '30d';
        $groupBy = isset($params['group_by']) ? $params['group_by'] : 'day';
        
        // Determine date range and grouping
        switch ($period) {
            case '7d':
                $date_from = date('Y-m-d', strtotime('-7 days'));
                $group_format = '%Y-%m-%d';
                break;
            case '30d':
                $date_from = date('Y-m-d', strtotime('-30 days'));
                $group_format = $groupBy === 'week' ? '%Y-%u' : '%Y-%m-%d';
                break;
            case '90d':
                $date_from = date('Y-m-d', strtotime('-90 days'));
                $group_format = '%Y-%u';
                break;
            case '1y':
                $date_from = date('Y-m-d', strtotime('-1 year'));
                $group_format = '%Y-%m';
                break;
            default:
                $date_from = date('Y-m-d', strtotime('-30 days'));
                $group_format = '%Y-%m-%d';
        }
        
        // Get sales by period
        $sales_query = "
            SELECT 
                DATE_FORMAT(created_at, ?) as period,
                COUNT(*) as order_count,
                SUM(total_amount) as revenue,
                AVG(total_amount) as avg_order_value
            FROM orders 
            WHERE store_id = ? AND created_at >= ? AND status != 'cancelled'
            GROUP BY DATE_FORMAT(created_at, ?)
            ORDER BY period ASC
        ";
        
        $sales_stmt = $db->prepare($sales_query);
        $sales_stmt->execute([$group_format, $store_id, $date_from, $group_format]);
        $sales_data = $sales_stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Get sales by payment method
        $payment_method_query = "
            SELECT 
                payment_method,
                COUNT(*) as order_count,
                SUM(total_amount) as revenue
            FROM orders 
            WHERE store_id = ? AND created_at >= ? AND status != 'cancelled'
            GROUP BY payment_method
            ORDER BY revenue DESC
        ";
        
        $payment_method_stmt = $db->prepare($payment_method_query);
        $payment_method_stmt->execute([$store_id, $date_from]);
        $payment_methods = $payment_method_stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Get sales by status
        $status_query = "
            SELECT 
                status,
                COUNT(*) as order_count,
                SUM(total_amount) as revenue
            FROM orders 
            WHERE store_id = ? AND created_at >= ?
            GROUP BY status
            ORDER BY order_count DESC
        ";
        
        $status_stmt = $db->prepare($status_query);
        $status_stmt->execute([$store_id, $date_from]);
        $order_status = $status_stmt->fetchAll(PDO::FETCH_ASSOC);
        
        ApiResponse::success([
            'period' => $period,
            'group_by' => $groupBy,
            'date_range' => [
                'from' => $date_from,
                'to' => date('Y-m-d')
            ],
            'sales_data' => $sales_data,
            'payment_methods' => $payment_methods,
            'order_status' => $order_status
        ]);
        
    } catch (Exception $e) {
        error_log('Get sales analytics error: ' . $e->getMessage());
        ApiResponse::error('Failed to retrieve sales analytics');
    }
}

/**
 * Get product analytics
 */
function getProductAnalytics($db, $store_id, $params) {
    try {
        $period = isset($params['period']) ? $params['period'] : '30d';
        
        switch ($period) {
            case '7d':
                $date_from = date('Y-m-d', strtotime('-7 days'));
                break;
            case '30d':
                $date_from = date('Y-m-d', strtotime('-30 days'));
                break;
            case '90d':
                $date_from = date('Y-m-d', strtotime('-90 days'));
                break;
            case '1y':
                $date_from = date('Y-m-d', strtotime('-1 year'));
                break;
            default:
                $date_from = date('Y-m-d', strtotime('-30 days'));
        }
        
        // Get product performance
        $products_query = "
            SELECT 
                p.id, p.name, p.name_ar, p.price, p.stock_quantity, p.status,
                COUNT(oi.id) as order_count,
                SUM(oi.quantity) as total_sold,
                SUM(oi.quantity * oi.price) as total_revenue,
                AVG(oi.price) as avg_selling_price
            FROM products p
            LEFT JOIN order_items oi ON p.id = oi.product_id
            LEFT JOIN orders o ON oi.order_id = o.id AND o.created_at >= ?
            WHERE p.store_id = ?
            GROUP BY p.id
            ORDER BY total_sold DESC
        ";
        
        $products_stmt = $db->prepare($products_query);
        $products_stmt->execute([$date_from, $store_id]);
        $products = $products_stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Get category performance
        $category_query = "
            SELECT 
                pc.name as category_name,
                pc.name_ar as category_name_ar,
                COUNT(DISTINCT p.id) as product_count,
                COUNT(oi.id) as order_count,
                SUM(oi.quantity) as total_sold,
                SUM(oi.quantity * oi.price) as total_revenue
            FROM product_categories pc
            LEFT JOIN products p ON pc.id = p.category_id AND p.store_id = ?
            LEFT JOIN order_items oi ON p.id = oi.product_id
            LEFT JOIN orders o ON oi.order_id = o.id AND o.created_at >= ?
            GROUP BY pc.id
            HAVING product_count > 0
            ORDER BY total_revenue DESC
        ";
        
        $category_stmt = $db->prepare($category_query);
        $category_stmt->execute([$store_id, $date_from]);
        $categories = $category_stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Get low stock products
        $low_stock_query = "
            SELECT id, name, name_ar, stock_quantity, price
            FROM products 
            WHERE store_id = ? AND status = 'active' AND stock_quantity <= 10
            ORDER BY stock_quantity ASC
            LIMIT 10
        ";
        
        $low_stock_stmt = $db->prepare($low_stock_query);
        $low_stock_stmt->execute([$store_id]);
        $low_stock = $low_stock_stmt->fetchAll(PDO::FETCH_ASSOC);
        
        ApiResponse::success([
            'period' => $period,
            'date_range' => [
                'from' => $date_from,
                'to' => date('Y-m-d')
            ],
            'products' => $products,
            'categories' => $categories,
            'low_stock' => $low_stock
        ]);
        
    } catch (Exception $e) {
        error_log('Get product analytics error: ' . $e->getMessage());
        ApiResponse::error('Failed to retrieve product analytics');
    }
}

/**
 * Get order analytics
 */
function getOrderAnalytics($db, $store_id, $params) {
    try {
        $period = isset($params['period']) ? $params['period'] : '30d';
        
        switch ($period) {
            case '7d':
                $date_from = date('Y-m-d', strtotime('-7 days'));
                break;
            case '30d':
                $date_from = date('Y-m-d', strtotime('-30 days'));
                break;
            case '90d':
                $date_from = date('Y-m-d', strtotime('-90 days'));
                break;
            case '1y':
                $date_from = date('Y-m-d', strtotime('-1 year'));
                break;
            default:
                $date_from = date('Y-m-d', strtotime('-30 days'));
        }
        
        // Get order statistics
        $order_stats_query = "
            SELECT 
                COUNT(*) as total_orders,
                AVG(total_amount) as avg_order_value,
                MIN(total_amount) as min_order_value,
                MAX(total_amount) as max_order_value,
                COUNT(DISTINCT customer_email) as unique_customers
            FROM orders 
            WHERE store_id = ? AND created_at >= ?
        ";
        
        $order_stats_stmt = $db->prepare($order_stats_query);
        $order_stats_stmt->execute([$store_id, $date_from]);
        $order_stats = $order_stats_stmt->fetch(PDO::FETCH_ASSOC);
        
        // Get orders by hour of day
        $hourly_query = "
            SELECT 
                HOUR(created_at) as hour,
                COUNT(*) as order_count
            FROM orders 
            WHERE store_id = ? AND created_at >= ?
            GROUP BY HOUR(created_at)
            ORDER BY hour ASC
        ";
        
        $hourly_stmt = $db->prepare($hourly_query);
        $hourly_stmt->execute([$store_id, $date_from]);
        $hourly_orders = $hourly_stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Get orders by day of week
        $weekly_query = "
            SELECT 
                DAYOFWEEK(created_at) as day_of_week,
                DAYNAME(created_at) as day_name,
                COUNT(*) as order_count,
                SUM(total_amount) as revenue
            FROM orders 
            WHERE store_id = ? AND created_at >= ?
            GROUP BY DAYOFWEEK(created_at), DAYNAME(created_at)
            ORDER BY day_of_week ASC
        ";
        
        $weekly_stmt = $db->prepare($weekly_query);
        $weekly_stmt->execute([$store_id, $date_from]);
        $weekly_orders = $weekly_stmt->fetchAll(PDO::FETCH_ASSOC);
        
        ApiResponse::success([
            'period' => $period,
            'date_range' => [
                'from' => $date_from,
                'to' => date('Y-m-d')
            ],
            'order_stats' => $order_stats,
            'hourly_orders' => $hourly_orders,
            'weekly_orders' => $weekly_orders
        ]);
        
    } catch (Exception $e) {
        error_log('Get order analytics error: ' . $e->getMessage());
        ApiResponse::error('Failed to retrieve order analytics');
    }
}

/**
 * Get payment analytics
 */
function getPaymentAnalytics($db, $store_id, $params) {
    try {
        $period = isset($params['period']) ? $params['period'] : '30d';
        
        switch ($period) {
            case '7d':
                $date_from = date('Y-m-d', strtotime('-7 days'));
                break;
            case '30d':
                $date_from = date('Y-m-d', strtotime('-30 days'));
                break;
            case '90d':
                $date_from = date('Y-m-d', strtotime('-90 days'));
                break;
            case '1y':
                $date_from = date('Y-m-d', strtotime('-1 year'));
                break;
            default:
                $date_from = date('Y-m-d', strtotime('-30 days'));
        }
        
        // Get payment statistics
        $payment_stats_query = "
            SELECT 
                COUNT(*) as total_payments,
                SUM(amount) as total_amount,
                AVG(amount) as avg_payment_amount,
                COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_payments,
                COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_payments,
                COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_payments
            FROM payments 
            WHERE store_id = ? AND created_at >= ?
        ";
        
        $payment_stats_stmt = $db->prepare($payment_stats_query);
        $payment_stats_stmt->execute([$store_id, $date_from]);
        $payment_stats = $payment_stats_stmt->fetch(PDO::FETCH_ASSOC);
        
        // Get payment methods performance
        $payment_methods_query = "
            SELECT 
                payment_method,
                COUNT(*) as payment_count,
                SUM(amount) as total_amount,
                AVG(amount) as avg_amount,
                COUNT(CASE WHEN status = 'completed' THEN 1 END) as success_count,
                ROUND((COUNT(CASE WHEN status = 'completed' THEN 1 END) / COUNT(*)) * 100, 2) as success_rate
            FROM payments 
            WHERE store_id = ? AND created_at >= ?
            GROUP BY payment_method
            ORDER BY total_amount DESC
        ";
        
        $payment_methods_stmt = $db->prepare($payment_methods_query);
        $payment_methods_stmt->execute([$store_id, $date_from]);
        $payment_methods = $payment_methods_stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Get daily payment trends
        $daily_payments_query = "
            SELECT 
                DATE(created_at) as date,
                COUNT(*) as payment_count,
                SUM(amount) as total_amount,
                COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_count
            FROM payments 
            WHERE store_id = ? AND created_at >= ?
            GROUP BY DATE(created_at)
            ORDER BY date ASC
        ";
        
        $daily_payments_stmt = $db->prepare($daily_payments_query);
        $daily_payments_stmt->execute([$store_id, $date_from]);
        $daily_payments = $daily_payments_stmt->fetchAll(PDO::FETCH_ASSOC);
        
        ApiResponse::success([
            'period' => $period,
            'date_range' => [
                'from' => $date_from,
                'to' => date('Y-m-d')
            ],
            'payment_stats' => $payment_stats,
            'payment_methods' => $payment_methods,
            'daily_payments' => $daily_payments
        ]);
        
    } catch (Exception $e) {
        error_log('Get payment analytics error: ' . $e->getMessage());
        ApiResponse::error('Failed to retrieve payment analytics');
    }
}

/**
 * Get customer analytics
 */
function getCustomerAnalytics($db, $store_id, $params) {
    try {
        $period = isset($params['period']) ? $params['period'] : '30d';
        
        switch ($period) {
            case '7d':
                $date_from = date('Y-m-d', strtotime('-7 days'));
                break;
            case '30d':
                $date_from = date('Y-m-d', strtotime('-30 days'));
                break;
            case '90d':
                $date_from = date('Y-m-d', strtotime('-90 days'));
                break;
            case '1y':
                $date_from = date('Y-m-d', strtotime('-1 year'));
                break;
            default:
                $date_from = date('Y-m-d', strtotime('-30 days'));
        }
        
        // Get customer statistics
        $customer_stats_query = "
            SELECT 
                COUNT(DISTINCT customer_email) as total_customers,
                COUNT(DISTINCT CASE WHEN created_at >= ? THEN customer_email END) as new_customers,
                COUNT(*) as total_orders,
                AVG(total_amount) as avg_order_value,
                COUNT(*) / COUNT(DISTINCT customer_email) as avg_orders_per_customer
            FROM orders 
            WHERE store_id = ?
        ";
        
        $customer_stats_stmt = $db->prepare($customer_stats_query);
        $customer_stats_stmt->execute([$date_from, $store_id]);
        $customer_stats = $customer_stats_stmt->fetch(PDO::FETCH_ASSOC);
        
        // Get top customers
        $top_customers_query = "
            SELECT 
                customer_name,
                customer_email,
                customer_phone,
                COUNT(*) as order_count,
                SUM(total_amount) as total_spent,
                AVG(total_amount) as avg_order_value,
                MAX(created_at) as last_order_date
            FROM orders 
            WHERE store_id = ? AND created_at >= ?
            GROUP BY customer_email
            ORDER BY total_spent DESC
            LIMIT 10
        ";
        
        $top_customers_stmt = $db->prepare($top_customers_query);
        $top_customers_stmt->execute([$store_id, $date_from]);
        $top_customers = $top_customers_stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Get customer acquisition by day
        $acquisition_query = "
            SELECT 
                DATE(MIN(created_at)) as acquisition_date,
                COUNT(DISTINCT customer_email) as new_customers
            FROM orders 
            WHERE store_id = ? AND created_at >= ?
            GROUP BY customer_email
            HAVING acquisition_date >= ?
            ORDER BY acquisition_date ASC
        ";
        
        $acquisition_stmt = $db->prepare($acquisition_query);
        $acquisition_stmt->execute([$store_id, $date_from, $date_from]);
        $customer_acquisition = $acquisition_stmt->fetchAll(PDO::FETCH_ASSOC);
        
        ApiResponse::success([
            'period' => $period,
            'date_range' => [
                'from' => $date_from,
                'to' => date('Y-m-d')
            ],
            'customer_stats' => $customer_stats,
            'top_customers' => $top_customers,
            'customer_acquisition' => $customer_acquisition
        ]);
        
    } catch (Exception $e) {
        error_log('Get customer analytics error: ' . $e->getMessage());
        ApiResponse::error('Failed to retrieve customer analytics');
    }
}
?>