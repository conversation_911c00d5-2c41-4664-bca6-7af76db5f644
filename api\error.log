[02-Aug-2025 09:32:45 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 09:32:45 UTC] Erreur lors de l'exécution de la requête: SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.user_id' in 'on clause'
[02-Aug-2025 09:32:45 UTC] Exception générale: Erreur lors de la récupération des stores: SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.user_id' in 'on clause'
[02-Aug-2025 09:34:04 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 09:34:04 UTC] Erreur lors de l'exécution de la requête: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'm.first_name' in 'field list'
[02-Aug-2025 09:34:04 UTC] Exception générale: <PERSON><PERSON><PERSON> lors de la récupération des stores: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'm.first_name' in 'field list'
[02-Aug-2025 09:35:20 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 09:37:51 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 09:42:20 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 09:42:22 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 09:43:33 UTC] Exception générale: Action non valide
[02-Aug-2025 11:07:51 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.name' in 'field list'
[02-Aug-2025 11:07:52 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.name' in 'field list'
[02-Aug-2025 11:15:11 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.name' in 'field list'
[02-Aug-2025 11:15:11 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.name' in 'field list'
[02-Aug-2025 11:15:12 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.name' in 'field list'
[02-Aug-2025 11:15:12 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.name' in 'field list'
[02-Aug-2025 11:15:21 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.name' in 'field list'
[02-Aug-2025 11:15:21 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.name' in 'field list'
[02-Aug-2025 11:16:11 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.name' in 'field list'
[02-Aug-2025 11:16:11 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.name' in 'field list'
[02-Aug-2025 11:19:56 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.name' in 'field list'
[02-Aug-2025 11:19:56 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.name' in 'field list'
[02-Aug-2025 10:20:04 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 10:20:04 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 10:43:03 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 10:43:04 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 11:43:06 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'subscription_plan_id' in 'field list'
[02-Aug-2025 11:43:06 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.name' in 'field list'
[02-Aug-2025 11:43:06 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'subscription_plan_id' in 'field list'
[02-Aug-2025 11:43:06 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.name' in 'field list'
[02-Aug-2025 11:43:10 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'subscription_plan_id' in 'field list'
[02-Aug-2025 11:43:10 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.name' in 'field list'
[02-Aug-2025 11:43:10 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'subscription_plan_id' in 'field list'
[02-Aug-2025 11:43:10 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.name' in 'field list'
[02-Aug-2025 11:43:10 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'subscription_plan_id' in 'field list'
[02-Aug-2025 11:43:11 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.name' in 'field list'
[02-Aug-2025 11:43:11 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'subscription_plan_id' in 'field list'
[02-Aug-2025 11:43:11 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.name' in 'field list'
[02-Aug-2025 11:43:11 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'subscription_plan_id' in 'field list'
[02-Aug-2025 11:43:11 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.name' in 'field list'
[02-Aug-2025 11:43:11 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'subscription_plan_id' in 'field list'
[02-Aug-2025 11:43:11 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.name' in 'field list'
[02-Aug-2025 11:43:12 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'subscription_plan_id' in 'field list'
[02-Aug-2025 11:43:12 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.name' in 'field list'
[02-Aug-2025 11:43:12 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'subscription_plan_id' in 'field list'
[02-Aug-2025 11:43:12 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.name' in 'field list'
[02-Aug-2025 11:43:17 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'subscription_plan_id' in 'field list'
[02-Aug-2025 11:43:17 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.name' in 'field list'
[02-Aug-2025 11:43:17 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'subscription_plan_id' in 'field list'
[02-Aug-2025 11:43:17 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.name' in 'field list'
[02-Aug-2025 11:43:24 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'subscription_plan_id' in 'field list'
[02-Aug-2025 11:43:24 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.name' in 'field list'
[02-Aug-2025 11:43:24 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'subscription_plan_id' in 'field list'
[02-Aug-2025 11:43:24 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.name' in 'field list'
[02-Aug-2025 11:43:43 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'subscription_plan_id' in 'field list'
[02-Aug-2025 10:46:47 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 10:46:47 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 10:46:51 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 10:46:56 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 11:59:09 Africa/Algiers] Token reçu : 
[02-Aug-2025 11:59:09 Africa/Algiers] Token non reconnu, utilisation de l'ID par défaut
[02-Aug-2025 11:59:09 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 11:59:09 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(118): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(35): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 11:59:09 Africa/Algiers] Début de getLandingPages avec userId: 1
[02-Aug-2025 11:59:09 Africa/Algiers] Préparation de la requête SQL
[02-Aug-2025 11:59:09 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 11:59:09 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                s.store_name as store_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            LEFT JOIN stores s ON lp.store_id = s.id
            LEFT JOIN merchants m ON s.merchant_id = m.id
            LEFT JOIN users u ON m.email = u.email
            WHERE u.id = ? AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 11:59:09 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'lp.store_id' in 'on clause'
[02-Aug-2025 12:02:55 Africa/Algiers] Token reçu : 
[02-Aug-2025 12:02:56 Africa/Algiers] Token non reconnu, utilisation de l'ID par défaut
[02-Aug-2025 12:02:56 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:02:56 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(118): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(35): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:02:56 Africa/Algiers] Début de getLandingPages avec userId: 1
[02-Aug-2025 12:02:56 Africa/Algiers] Préparation de la requête SQL
[02-Aug-2025 12:02:56 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:02:56 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                s.store_name as store_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            LEFT JOIN stores s ON lp.store_id = s.id
            LEFT JOIN merchants m ON s.merchant_id = m.id
            LEFT JOIN users u ON m.email = u.email
            WHERE u.id = ? AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:02:57 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'lp.store_id' in 'on clause'
[02-Aug-2025 12:03:38 Africa/Algiers] Token reçu : demo_token
[02-Aug-2025 12:03:38 Africa/Algiers] Utilisation du token de démo
[02-Aug-2025 12:03:38 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:03:38 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(118): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(35): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:03:38 Africa/Algiers] Début de getLandingPages avec userId: 1
[02-Aug-2025 12:03:38 Africa/Algiers] Préparation de la requête SQL
[02-Aug-2025 12:03:38 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:03:38 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                s.store_name as store_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            LEFT JOIN stores s ON lp.store_id = s.id
            LEFT JOIN merchants m ON s.merchant_id = m.id
            LEFT JOIN users u ON m.email = u.email
            WHERE u.id = ? AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:03:38 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'lp.store_id' in 'on clause'
[02-Aug-2025 12:03:38 Africa/Algiers] Token reçu : demo_token
[02-Aug-2025 12:03:38 Africa/Algiers] Utilisation du token de démo
[02-Aug-2025 12:03:38 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:03:38 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(118): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(35): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:03:38 Africa/Algiers] Début de getLandingPages avec userId: 1
[02-Aug-2025 12:03:38 Africa/Algiers] Préparation de la requête SQL
[02-Aug-2025 12:03:38 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:03:38 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                s.store_name as store_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            LEFT JOIN stores s ON lp.store_id = s.id
            LEFT JOIN merchants m ON s.merchant_id = m.id
            LEFT JOIN users u ON m.email = u.email
            WHERE u.id = ? AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:03:38 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'lp.store_id' in 'on clause'
[02-Aug-2025 12:06:28 Africa/Algiers] Token reçu : 
[02-Aug-2025 12:06:28 Africa/Algiers] Token non reconnu, utilisation de l'ID par défaut
[02-Aug-2025 12:06:28 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:06:28 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(118): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(35): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:06:28 Africa/Algiers] Début de getLandingPages avec userId: 1
[02-Aug-2025 12:06:28 Africa/Algiers] Préparation de la requête SQL
[02-Aug-2025 12:06:28 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:06:28 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                s.store_name as store_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            LEFT JOIN stores s ON lp.store_id = s.id
            LEFT JOIN merchants m ON s.merchant_id = m.id
            LEFT JOIN users u ON m.email = u.email
            WHERE u.id = ? AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:06:28 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'lp.store_id' in 'on clause'
[02-Aug-2025 12:07:02 Africa/Algiers] Token reçu : 
[02-Aug-2025 12:07:02 Africa/Algiers] Token non reconnu, utilisation de l'ID par défaut
[02-Aug-2025 12:07:02 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:07:02 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(118): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(35): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:07:02 Africa/Algiers] Début de getLandingPages avec userId: 1
[02-Aug-2025 12:07:02 Africa/Algiers] Préparation de la requête SQL
[02-Aug-2025 12:07:02 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:07:02 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                s.store_name as store_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            LEFT JOIN stores s ON lp.store_id = s.id
            LEFT JOIN merchants m ON s.merchant_id = m.id
            LEFT JOIN users u ON m.email = u.email
            WHERE u.id = ? AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:07:02 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'lp.store_id' in 'on clause'
[02-Aug-2025 12:09:20 Africa/Algiers] Token reçu : 
[02-Aug-2025 12:09:22 Africa/Algiers] Token non reconnu, utilisation de l'ID par défaut
[02-Aug-2025 12:09:22 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:09:22 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(118): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(35): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:09:22 Africa/Algiers] Début de getLandingPages avec userId: 1
[02-Aug-2025 12:09:22 Africa/Algiers] Préparation de la requête SQL
[02-Aug-2025 12:09:22 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:09:22 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                s.store_name as store_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            LEFT JOIN stores s ON lp.store_id = s.id
            LEFT JOIN merchants m ON s.merchant_id = m.id
            LEFT JOIN users u ON m.email = u.email
            WHERE u.id = ? AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:09:22 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'lp.store_id' in 'on clause'
[02-Aug-2025 12:09:23 Africa/Algiers] Token reçu : 
[02-Aug-2025 12:09:23 Africa/Algiers] Token non reconnu, utilisation de l'ID par défaut
[02-Aug-2025 12:09:23 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:09:23 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(118): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(35): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:09:23 Africa/Algiers] Début de getLandingPages avec userId: 1
[02-Aug-2025 12:09:23 Africa/Algiers] Préparation de la requête SQL
[02-Aug-2025 12:09:23 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:09:23 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                s.store_name as store_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            LEFT JOIN stores s ON lp.store_id = s.id
            LEFT JOIN merchants m ON s.merchant_id = m.id
            LEFT JOIN users u ON m.email = u.email
            WHERE u.id = ? AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:09:23 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'lp.store_id' in 'on clause'
[02-Aug-2025 12:09:25 Africa/Algiers] Token reçu : 
[02-Aug-2025 12:09:25 Africa/Algiers] Token non reconnu, utilisation de l'ID par défaut
[02-Aug-2025 12:09:25 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:09:25 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(118): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(35): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:09:25 Africa/Algiers] Début de getLandingPages avec userId: 1
[02-Aug-2025 12:09:25 Africa/Algiers] Préparation de la requête SQL
[02-Aug-2025 12:09:25 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:09:25 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                s.store_name as store_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            LEFT JOIN stores s ON lp.store_id = s.id
            LEFT JOIN merchants m ON s.merchant_id = m.id
            LEFT JOIN users u ON m.email = u.email
            WHERE u.id = ? AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:09:25 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'lp.store_id' in 'on clause'
[02-Aug-2025 12:09:26 Africa/Algiers] Token reçu : 
[02-Aug-2025 12:09:26 Africa/Algiers] Token non reconnu, utilisation de l'ID par défaut
[02-Aug-2025 12:09:26 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:09:26 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(118): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(35): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:09:26 Africa/Algiers] Début de getLandingPages avec userId: 1
[02-Aug-2025 12:09:26 Africa/Algiers] Préparation de la requête SQL
[02-Aug-2025 12:09:26 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:09:26 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                s.store_name as store_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            LEFT JOIN stores s ON lp.store_id = s.id
            LEFT JOIN merchants m ON s.merchant_id = m.id
            LEFT JOIN users u ON m.email = u.email
            WHERE u.id = ? AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:09:26 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'lp.store_id' in 'on clause'
[02-Aug-2025 12:09:38 Africa/Algiers] Token reçu : demo_token
[02-Aug-2025 12:09:38 Africa/Algiers] Utilisation du token de démo
[02-Aug-2025 12:09:38 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:09:38 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(118): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(35): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:09:38 Africa/Algiers] Début de getLandingPages avec userId: 1
[02-Aug-2025 12:09:38 Africa/Algiers] Préparation de la requête SQL
[02-Aug-2025 12:09:38 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:09:38 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                s.store_name as store_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            LEFT JOIN stores s ON lp.store_id = s.id
            LEFT JOIN merchants m ON s.merchant_id = m.id
            LEFT JOIN users u ON m.email = u.email
            WHERE u.id = ? AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:09:38 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'lp.store_id' in 'on clause'
[02-Aug-2025 12:09:38 Africa/Algiers] Token reçu : demo_token
[02-Aug-2025 12:09:38 Africa/Algiers] Utilisation du token de démo
[02-Aug-2025 12:09:38 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:09:38 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(118): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(35): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:09:38 Africa/Algiers] Début de getLandingPages avec userId: 1
[02-Aug-2025 12:09:38 Africa/Algiers] Préparation de la requête SQL
[02-Aug-2025 12:09:38 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:09:38 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                s.store_name as store_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            LEFT JOIN stores s ON lp.store_id = s.id
            LEFT JOIN merchants m ON s.merchant_id = m.id
            LEFT JOIN users u ON m.email = u.email
            WHERE u.id = ? AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:09:38 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'lp.store_id' in 'on clause'
[02-Aug-2025 12:09:50 Africa/Algiers] Token reçu : demo_token
[02-Aug-2025 12:09:50 Africa/Algiers] Utilisation du token de démo
[02-Aug-2025 12:09:50 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:09:50 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(118): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(35): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:09:50 Africa/Algiers] Début de getLandingPages avec userId: 1
[02-Aug-2025 12:09:50 Africa/Algiers] Préparation de la requête SQL
[02-Aug-2025 12:09:50 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:09:50 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                s.store_name as store_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            LEFT JOIN stores s ON lp.store_id = s.id
            LEFT JOIN merchants m ON s.merchant_id = m.id
            LEFT JOIN users u ON m.email = u.email
            WHERE u.id = ? AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:09:50 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'lp.store_id' in 'on clause'
[02-Aug-2025 12:09:50 Africa/Algiers] Token reçu : demo_token
[02-Aug-2025 12:09:51 Africa/Algiers] Utilisation du token de démo
[02-Aug-2025 12:09:51 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:09:51 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(118): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(35): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:09:51 Africa/Algiers] Début de getLandingPages avec userId: 1
[02-Aug-2025 12:09:51 Africa/Algiers] Préparation de la requête SQL
[02-Aug-2025 12:09:51 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:09:51 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                s.store_name as store_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            LEFT JOIN stores s ON lp.store_id = s.id
            LEFT JOIN merchants m ON s.merchant_id = m.id
            LEFT JOIN users u ON m.email = u.email
            WHERE u.id = ? AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:09:51 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'lp.store_id' in 'on clause'
[02-Aug-2025 11:11:38 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 11:11:40 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 12:11:43 Africa/Algiers] Token reçu : demo_token
[02-Aug-2025 12:11:43 Africa/Algiers] Utilisation du token de démo
[02-Aug-2025 12:11:43 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:11:43 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(118): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(35): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:11:43 Africa/Algiers] Début de getLandingPages avec userId: 1
[02-Aug-2025 12:11:43 Africa/Algiers] Préparation de la requête SQL
[02-Aug-2025 12:11:43 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:11:43 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                s.store_name as store_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            LEFT JOIN stores s ON lp.store_id = s.id
            LEFT JOIN merchants m ON s.merchant_id = m.id
            LEFT JOIN users u ON m.email = u.email
            WHERE u.id = ? AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:11:43 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'lp.store_id' in 'on clause'
[02-Aug-2025 12:11:43 Africa/Algiers] Token reçu : demo_token
[02-Aug-2025 12:11:43 Africa/Algiers] Utilisation du token de démo
[02-Aug-2025 12:11:43 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:11:43 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(118): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(35): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:11:43 Africa/Algiers] Début de getLandingPages avec userId: 1
[02-Aug-2025 12:11:43 Africa/Algiers] Préparation de la requête SQL
[02-Aug-2025 12:11:43 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:11:43 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                s.store_name as store_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            LEFT JOIN stores s ON lp.store_id = s.id
            LEFT JOIN merchants m ON s.merchant_id = m.id
            LEFT JOIN users u ON m.email = u.email
            WHERE u.id = ? AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:11:43 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'lp.store_id' in 'on clause'
[02-Aug-2025 12:13:26 Africa/Algiers] Token reçu : 
[02-Aug-2025 12:13:26 Africa/Algiers] Token non reconnu, utilisation de l'ID par défaut
[02-Aug-2025 12:13:26 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:13:26 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(118): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(35): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:13:26 Africa/Algiers] Début de getLandingPages avec userId: 1
[02-Aug-2025 12:13:26 Africa/Algiers] Préparation de la requête SQL
[02-Aug-2025 12:13:26 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:13:26 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            LEFT JOIN merchants m ON lp.merchant_id = m.id
            LEFT JOIN users u ON m.email = u.email
            WHERE u.id = ? AND u.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:13:26 Africa/Algiers] Nombre de pages trouvées: 0
[02-Aug-2025 12:13:26 Africa/Algiers] Requête SQL exécutée avec succès
[02-Aug-2025 12:14:06 Africa/Algiers] === Début getUserIdFromToken ===
[02-Aug-2025 12:14:06 Africa/Algiers] Token reçu : null
[02-Aug-2025 12:14:06 Africa/Algiers] Headers reçus : {"Host":"localhost:8000","Connection":"keep-alive","Upgrade-Insecure-Requests":"1","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Trae\/1.100.3 Chrome\/132.0.6834.210 Electron\/34.5.1 Safari\/537.36","Accept":"text\/html,application\/xhtml+xml,application\/xml;q=0.9,image\/avif,image\/webp,image\/apng,*\/*;q=0.8,application\/signed-exchange;v=b3;q=0.7","Sec-Fetch-Site":"none","Sec-Fetch-Mode":"navigate","Sec-Fetch-User":"?1","Sec-Fetch-Dest":"document","Accept-Encoding":"gzip, deflate, br, zstd","Accept-Language":"en-US"}
[02-Aug-2025 12:14:06 Africa/Algiers] Aucun token fourni, utilisation de l'ID par défaut
[02-Aug-2025 12:14:06 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:14:06 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(128): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(35): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:14:06 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 12:14:06 Africa/Algiers] userId reçu: 1
[02-Aug-2025 12:14:06 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 12:14:06 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:14:06 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            LEFT JOIN merchants m ON lp.merchant_id = m.id
            LEFT JOIN users u ON m.email = u.email
            WHERE u.id = ? AND u.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:14:06 Africa/Algiers] Nombre de pages trouvées: 0
[02-Aug-2025 12:14:06 Africa/Algiers] Requête SQL exécutée avec succès
[02-Aug-2025 12:16:53 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 12:16:53 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 12:16:53 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 12:16:53 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 12:16:53 Africa/Algiers] === Début getUserIdFromToken ===
[02-Aug-2025 12:16:53 Africa/Algiers] Token reçu : null
[02-Aug-2025 12:16:53 Africa/Algiers] Headers reçus : {"Host":"localhost:8000","Connection":"keep-alive","Upgrade-Insecure-Requests":"1","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Trae\/1.100.3 Chrome\/132.0.6834.210 Electron\/34.5.1 Safari\/537.36","Accept":"text\/html,application\/xhtml+xml,application\/xml;q=0.9,image\/avif,image\/webp,image\/apng,*\/*;q=0.8,application\/signed-exchange;v=b3;q=0.7","Sec-Fetch-Site":"none","Sec-Fetch-Mode":"navigate","Sec-Fetch-User":"?1","Sec-Fetch-Dest":"document","Accept-Encoding":"gzip, deflate, br, zstd","Accept-Language":"en-US"}
[02-Aug-2025 12:16:53 Africa/Algiers] Aucun token fourni, utilisation de l'ID par défaut
[02-Aug-2025 12:16:53 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:16:53 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(128): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(35): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:16:53 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 12:16:53 Africa/Algiers] userId reçu: 1
[02-Aug-2025 12:16:53 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 12:16:53 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:16:53 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id
            INNER JOIN users u ON m.email = u.email
            WHERE u.id = ? AND u.status = 'active' AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:16:53 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 12:16:53 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id
            INNER JOIN users u ON m.email = u.email
            WHERE u.id = ? AND u.status = 'active' AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:16:53 Africa/Algiers] UserId: 1
[02-Aug-2025 12:16:53 Africa/Algiers] Nombre de pages trouvées: 0
[02-Aug-2025 12:16:53 Africa/Algiers] Aucune page trouvée
[02-Aug-2025 12:16:53 Africa/Algiers] Dernière erreur SQL: ["00000",null,null]
[02-Aug-2025 12:18:16 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 12:18:16 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 12:18:16 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 12:18:16 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 12:18:16 Africa/Algiers] === Vérification de l'authentification ===
[02-Aug-2025 12:18:16 Africa/Algiers] Headers reçus: {"Host":"localhost:8000","Connection":"keep-alive","sec-ch-ua-platform":"\"Windows\"","Authorization":"Bearer demo_token","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Brave\";v=\"138\"","sec-ch-ua-mobile":"?0","Accept":"*\/*","Sec-GPC":"1","Accept-Language":"en-US,en;q=0.6","Sec-Fetch-Site":"same-origin","Sec-Fetch-Mode":"cors","Sec-Fetch-Dest":"empty","Referer":"http:\/\/localhost:8000\/dashboard.html","Accept-Encoding":"gzip, deflate, br, zstd"}
[02-Aug-2025 12:18:16 Africa/Algiers] Token extrait: demo_token
[02-Aug-2025 12:18:16 Africa/Algiers] === Début getUserIdFromToken ===
[02-Aug-2025 12:18:16 Africa/Algiers] Token reçu : demo_token
[02-Aug-2025 12:18:16 Africa/Algiers] Headers reçus : {"Host":"localhost:8000","Connection":"keep-alive","sec-ch-ua-platform":"\"Windows\"","Authorization":"Bearer demo_token","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Brave\";v=\"138\"","sec-ch-ua-mobile":"?0","Accept":"*\/*","Sec-GPC":"1","Accept-Language":"en-US,en;q=0.6","Sec-Fetch-Site":"same-origin","Sec-Fetch-Mode":"cors","Sec-Fetch-Dest":"empty","Referer":"http:\/\/localhost:8000\/dashboard.html","Accept-Encoding":"gzip, deflate, br, zstd"}
[02-Aug-2025 12:18:16 Africa/Algiers] Utilisation du token de démo
[02-Aug-2025 12:18:16 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:18:16 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(131): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(38): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:18:16 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 12:18:16 Africa/Algiers] userId reçu: 1
[02-Aug-2025 12:18:16 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 12:18:16 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:18:16 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id
            INNER JOIN users u ON m.email = u.email
            WHERE u.id = ? AND u.status = 'active' AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:18:16 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 12:18:16 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id
            INNER JOIN users u ON m.email = u.email
            WHERE u.id = ? AND u.status = 'active' AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:18:16 Africa/Algiers] UserId: 1
[02-Aug-2025 12:18:16 Africa/Algiers] Nombre de pages trouvées: 0
[02-Aug-2025 12:18:16 Africa/Algiers] Aucune page trouvée
[02-Aug-2025 12:18:16 Africa/Algiers] Dernière erreur SQL: ["00000",null,null]
[02-Aug-2025 12:18:17 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 12:18:17 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 12:18:17 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 12:18:17 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 12:18:17 Africa/Algiers] === Vérification de l'authentification ===
[02-Aug-2025 12:18:17 Africa/Algiers] Headers reçus: {"Host":"localhost:8000","Connection":"keep-alive","sec-ch-ua-platform":"\"Windows\"","Authorization":"Bearer demo_token","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Brave\";v=\"138\"","sec-ch-ua-mobile":"?0","Accept":"*\/*","Sec-GPC":"1","Accept-Language":"en-US,en;q=0.6","Sec-Fetch-Site":"same-origin","Sec-Fetch-Mode":"cors","Sec-Fetch-Dest":"empty","Referer":"http:\/\/localhost:8000\/dashboard.html","Accept-Encoding":"gzip, deflate, br, zstd"}
[02-Aug-2025 12:18:17 Africa/Algiers] Token extrait: demo_token
[02-Aug-2025 12:18:17 Africa/Algiers] === Début getUserIdFromToken ===
[02-Aug-2025 12:18:17 Africa/Algiers] Token reçu : demo_token
[02-Aug-2025 12:18:17 Africa/Algiers] Headers reçus : {"Host":"localhost:8000","Connection":"keep-alive","sec-ch-ua-platform":"\"Windows\"","Authorization":"Bearer demo_token","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Brave\";v=\"138\"","sec-ch-ua-mobile":"?0","Accept":"*\/*","Sec-GPC":"1","Accept-Language":"en-US,en;q=0.6","Sec-Fetch-Site":"same-origin","Sec-Fetch-Mode":"cors","Sec-Fetch-Dest":"empty","Referer":"http:\/\/localhost:8000\/dashboard.html","Accept-Encoding":"gzip, deflate, br, zstd"}
[02-Aug-2025 12:18:17 Africa/Algiers] Utilisation du token de démo
[02-Aug-2025 12:18:17 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:18:17 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(131): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(38): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:18:17 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 12:18:17 Africa/Algiers] userId reçu: 1
[02-Aug-2025 12:18:17 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 12:18:17 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:18:17 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id
            INNER JOIN users u ON m.email = u.email
            WHERE u.id = ? AND u.status = 'active' AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:18:17 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 12:18:17 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id
            INNER JOIN users u ON m.email = u.email
            WHERE u.id = ? AND u.status = 'active' AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:18:17 Africa/Algiers] UserId: 1
[02-Aug-2025 12:18:17 Africa/Algiers] Nombre de pages trouvées: 0
[02-Aug-2025 12:18:17 Africa/Algiers] Aucune page trouvée
[02-Aug-2025 12:18:17 Africa/Algiers] Dernière erreur SQL: ["00000",null,null]
[02-Aug-2025 12:18:25 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 12:18:25 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 12:18:25 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 12:18:25 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 12:18:25 Africa/Algiers] === Vérification de l'authentification ===
[02-Aug-2025 12:18:25 Africa/Algiers] Headers reçus: {"Host":"localhost:8000","Connection":"keep-alive","sec-ch-ua-platform":"\"Windows\"","Authorization":"Bearer demo_token","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Brave\";v=\"138\"","sec-ch-ua-mobile":"?0","Accept":"*\/*","Sec-GPC":"1","Accept-Language":"en-US,en;q=0.6","Sec-Fetch-Site":"same-origin","Sec-Fetch-Mode":"cors","Sec-Fetch-Dest":"empty","Referer":"http:\/\/localhost:8000\/dashboard.html","Accept-Encoding":"gzip, deflate, br, zstd"}
[02-Aug-2025 12:18:25 Africa/Algiers] Token extrait: demo_token
[02-Aug-2025 12:18:25 Africa/Algiers] === Début getUserIdFromToken ===
[02-Aug-2025 12:18:25 Africa/Algiers] Token reçu : demo_token
[02-Aug-2025 12:18:25 Africa/Algiers] Headers reçus : {"Host":"localhost:8000","Connection":"keep-alive","sec-ch-ua-platform":"\"Windows\"","Authorization":"Bearer demo_token","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Brave\";v=\"138\"","sec-ch-ua-mobile":"?0","Accept":"*\/*","Sec-GPC":"1","Accept-Language":"en-US,en;q=0.6","Sec-Fetch-Site":"same-origin","Sec-Fetch-Mode":"cors","Sec-Fetch-Dest":"empty","Referer":"http:\/\/localhost:8000\/dashboard.html","Accept-Encoding":"gzip, deflate, br, zstd"}
[02-Aug-2025 12:18:25 Africa/Algiers] Utilisation du token de démo
[02-Aug-2025 12:18:25 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:18:25 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(131): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(38): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:18:25 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 12:18:25 Africa/Algiers] userId reçu: 1
[02-Aug-2025 12:18:25 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 12:18:25 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:18:25 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id
            INNER JOIN users u ON m.email = u.email
            WHERE u.id = ? AND u.status = 'active' AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:18:25 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 12:18:25 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id
            INNER JOIN users u ON m.email = u.email
            WHERE u.id = ? AND u.status = 'active' AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:18:25 Africa/Algiers] UserId: 1
[02-Aug-2025 12:18:25 Africa/Algiers] Nombre de pages trouvées: 0
[02-Aug-2025 12:18:25 Africa/Algiers] Aucune page trouvée
[02-Aug-2025 12:18:25 Africa/Algiers] Dernière erreur SQL: ["00000",null,null]
[02-Aug-2025 12:18:25 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 12:18:25 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 12:18:25 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 12:18:25 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 12:18:25 Africa/Algiers] === Vérification de l'authentification ===
[02-Aug-2025 12:18:25 Africa/Algiers] Headers reçus: {"Host":"localhost:8000","Connection":"keep-alive","sec-ch-ua-platform":"\"Windows\"","Authorization":"Bearer demo_token","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Brave\";v=\"138\"","sec-ch-ua-mobile":"?0","Accept":"*\/*","Sec-GPC":"1","Accept-Language":"en-US,en;q=0.6","Sec-Fetch-Site":"same-origin","Sec-Fetch-Mode":"cors","Sec-Fetch-Dest":"empty","Referer":"http:\/\/localhost:8000\/dashboard.html","Accept-Encoding":"gzip, deflate, br, zstd"}
[02-Aug-2025 12:18:25 Africa/Algiers] Token extrait: demo_token
[02-Aug-2025 12:18:25 Africa/Algiers] === Début getUserIdFromToken ===
[02-Aug-2025 12:18:25 Africa/Algiers] Token reçu : demo_token
[02-Aug-2025 12:18:25 Africa/Algiers] Headers reçus : {"Host":"localhost:8000","Connection":"keep-alive","sec-ch-ua-platform":"\"Windows\"","Authorization":"Bearer demo_token","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Brave\";v=\"138\"","sec-ch-ua-mobile":"?0","Accept":"*\/*","Sec-GPC":"1","Accept-Language":"en-US,en;q=0.6","Sec-Fetch-Site":"same-origin","Sec-Fetch-Mode":"cors","Sec-Fetch-Dest":"empty","Referer":"http:\/\/localhost:8000\/dashboard.html","Accept-Encoding":"gzip, deflate, br, zstd"}
[02-Aug-2025 12:18:25 Africa/Algiers] Utilisation du token de démo
[02-Aug-2025 12:18:25 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:18:25 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(131): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(38): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:18:25 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 12:18:25 Africa/Algiers] userId reçu: 1
[02-Aug-2025 12:18:25 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 12:18:25 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:18:25 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id
            INNER JOIN users u ON m.email = u.email
            WHERE u.id = ? AND u.status = 'active' AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:18:25 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 12:18:25 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id
            INNER JOIN users u ON m.email = u.email
            WHERE u.id = ? AND u.status = 'active' AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:18:25 Africa/Algiers] UserId: 1
[02-Aug-2025 12:18:25 Africa/Algiers] Nombre de pages trouvées: 0
[02-Aug-2025 12:18:25 Africa/Algiers] Aucune page trouvée
[02-Aug-2025 12:18:25 Africa/Algiers] Dernière erreur SQL: ["00000",null,null]
[02-Aug-2025 12:19:53 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 12:19:55 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 12:19:55 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 12:19:55 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 12:19:55 Africa/Algiers] === Vérification de l'authentification ===
[02-Aug-2025 12:19:55 Africa/Algiers] Headers reçus: {"Host":"localhost:8000","Connection":"keep-alive","Pragma":"no-cache","Cache-Control":"no-cache","Upgrade-Insecure-Requests":"1","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Trae\/1.100.3 Chrome\/132.0.6834.210 Electron\/34.5.1 Safari\/537.36","Accept":"text\/html,application\/xhtml+xml,application\/xml;q=0.9,image\/avif,image\/webp,image\/apng,*\/*;q=0.8,application\/signed-exchange;v=b3;q=0.7","Sec-Fetch-Site":"none","Sec-Fetch-Mode":"navigate","Sec-Fetch-User":"?1","Sec-Fetch-Dest":"document","Accept-Encoding":"gzip, deflate, br, zstd","Accept-Language":"en-US"}
[02-Aug-2025 12:19:55 Africa/Algiers] Token extrait: null
[02-Aug-2025 12:19:55 Africa/Algiers] === Début getUserIdFromToken ===
[02-Aug-2025 12:19:55 Africa/Algiers] Token reçu : null
[02-Aug-2025 12:19:55 Africa/Algiers] Headers reçus : {"Host":"localhost:8000","Connection":"keep-alive","Pragma":"no-cache","Cache-Control":"no-cache","Upgrade-Insecure-Requests":"1","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Trae\/1.100.3 Chrome\/132.0.6834.210 Electron\/34.5.1 Safari\/537.36","Accept":"text\/html,application\/xhtml+xml,application\/xml;q=0.9,image\/avif,image\/webp,image\/apng,*\/*;q=0.8,application\/signed-exchange;v=b3;q=0.7","Sec-Fetch-Site":"none","Sec-Fetch-Mode":"navigate","Sec-Fetch-User":"?1","Sec-Fetch-Dest":"document","Accept-Encoding":"gzip, deflate, br, zstd","Accept-Language":"en-US"}
[02-Aug-2025 12:19:55 Africa/Algiers] Aucun token fourni, utilisation de l'ID par défaut
[02-Aug-2025 12:19:55 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:19:55 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(131): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(38): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:19:55 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 12:19:55 Africa/Algiers] userId reçu: 1
[02-Aug-2025 12:19:55 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 12:19:55 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:19:55 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id
            INNER JOIN users u ON m.email = u.email
            WHERE u.id = ? AND u.status = 'active' AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:19:55 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 12:19:55 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id
            INNER JOIN users u ON m.email = u.email
            WHERE u.id = ? AND u.status = 'active' AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:19:55 Africa/Algiers] UserId: 1
[02-Aug-2025 12:19:55 Africa/Algiers] Nombre de pages trouvées: 0
[02-Aug-2025 12:19:55 Africa/Algiers] Aucune page trouvée
[02-Aug-2025 12:19:55 Africa/Algiers] Dernière erreur SQL: ["00000",null,null]
[02-Aug-2025 12:20:15 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 12:20:15 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 12:20:15 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 12:20:15 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 12:20:15 Africa/Algiers] === Vérification de l'authentification ===
[02-Aug-2025 12:20:15 Africa/Algiers] Headers reçus: {"Host":"localhost:8000","Connection":"keep-alive","Upgrade-Insecure-Requests":"1","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Trae\/1.100.3 Chrome\/132.0.6834.210 Electron\/34.5.1 Safari\/537.36","Accept":"text\/html,application\/xhtml+xml,application\/xml;q=0.9,image\/avif,image\/webp,image\/apng,*\/*;q=0.8,application\/signed-exchange;v=b3;q=0.7","Sec-Fetch-Site":"none","Sec-Fetch-Mode":"navigate","Sec-Fetch-User":"?1","Sec-Fetch-Dest":"document","Accept-Encoding":"gzip, deflate, br, zstd","Accept-Language":"en-US"}
[02-Aug-2025 12:20:15 Africa/Algiers] Token extrait: null
[02-Aug-2025 12:20:15 Africa/Algiers] === Début getUserIdFromToken ===
[02-Aug-2025 12:20:15 Africa/Algiers] Token reçu : null
[02-Aug-2025 12:20:15 Africa/Algiers] Headers reçus : {"Host":"localhost:8000","Connection":"keep-alive","Upgrade-Insecure-Requests":"1","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Trae\/1.100.3 Chrome\/132.0.6834.210 Electron\/34.5.1 Safari\/537.36","Accept":"text\/html,application\/xhtml+xml,application\/xml;q=0.9,image\/avif,image\/webp,image\/apng,*\/*;q=0.8,application\/signed-exchange;v=b3;q=0.7","Sec-Fetch-Site":"none","Sec-Fetch-Mode":"navigate","Sec-Fetch-User":"?1","Sec-Fetch-Dest":"document","Accept-Encoding":"gzip, deflate, br, zstd","Accept-Language":"en-US"}
[02-Aug-2025 12:20:15 Africa/Algiers] Aucun token fourni, utilisation de l'ID par défaut
[02-Aug-2025 12:20:15 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:20:15 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(131): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(38): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:20:15 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 12:20:15 Africa/Algiers] userId reçu: 1
[02-Aug-2025 12:20:15 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 12:20:15 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:20:15 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id
            INNER JOIN users u ON m.email = u.email
            WHERE u.id = 4 AND u.status = 'active' AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:20:15 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 12:20:15 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id
            INNER JOIN users u ON m.email = u.email
            WHERE u.id = 4 AND u.status = 'active' AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:20:15 Africa/Algiers] UserId: 1
[02-Aug-2025 12:20:15 Africa/Algiers] SQLSTATE[HY093]: Invalid parameter number
[02-Aug-2025 12:20:20 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 12:20:20 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 12:20:20 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 12:20:20 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 12:20:20 Africa/Algiers] === Vérification de l'authentification ===
[02-Aug-2025 12:20:20 Africa/Algiers] Headers reçus: {"Host":"localhost:8000","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko\/20100101 Firefox\/141.0","Accept":"*\/*","Accept-Language":"fr,fr-FR;q=0.8,en-US;q=0.5,en;q=0.3","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"http:\/\/localhost:8000\/seller-dashboard.html","Authorization":"Bearer demo_token","Connection":"keep-alive","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Priority":"u=4"}
[02-Aug-2025 12:20:20 Africa/Algiers] Token extrait: demo_token
[02-Aug-2025 12:20:20 Africa/Algiers] === Début getUserIdFromToken ===
[02-Aug-2025 12:20:20 Africa/Algiers] Token reçu : demo_token
[02-Aug-2025 12:20:20 Africa/Algiers] Headers reçus : {"Host":"localhost:8000","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko\/20100101 Firefox\/141.0","Accept":"*\/*","Accept-Language":"fr,fr-FR;q=0.8,en-US;q=0.5,en;q=0.3","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"http:\/\/localhost:8000\/seller-dashboard.html","Authorization":"Bearer demo_token","Connection":"keep-alive","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Priority":"u=4"}
[02-Aug-2025 12:20:20 Africa/Algiers] Utilisation du token de démo
[02-Aug-2025 12:20:20 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:20:20 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(131): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(38): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:25:50 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 12:25:52 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 12:25:52 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 12:25:52 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 12:25:52 Africa/Algiers] === Vérification de l'authentification ===
[02-Aug-2025 12:25:52 Africa/Algiers] Headers reçus: {"Host":"localhost:8000","Connection":"keep-alive","Pragma":"no-cache","Cache-Control":"no-cache","Upgrade-Insecure-Requests":"1","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Trae\/1.100.3 Chrome\/132.0.6834.210 Electron\/34.5.1 Safari\/537.36","Accept":"text\/html,application\/xhtml+xml,application\/xml;q=0.9,image\/avif,image\/webp,image\/apng,*\/*;q=0.8,application\/signed-exchange;v=b3;q=0.7","Sec-Fetch-Site":"none","Sec-Fetch-Mode":"navigate","Sec-Fetch-User":"?1","Sec-Fetch-Dest":"document","Accept-Encoding":"gzip, deflate, br, zstd","Accept-Language":"en-US"}
[02-Aug-2025 12:25:52 Africa/Algiers] Token extrait: null
[02-Aug-2025 12:25:52 Africa/Algiers] === Début getUserIdFromToken ===
[02-Aug-2025 12:25:52 Africa/Algiers] Token reçu : null
[02-Aug-2025 12:25:52 Africa/Algiers] Headers reçus : {"Host":"localhost:8000","Connection":"keep-alive","Pragma":"no-cache","Cache-Control":"no-cache","Upgrade-Insecure-Requests":"1","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Trae\/1.100.3 Chrome\/132.0.6834.210 Electron\/34.5.1 Safari\/537.36","Accept":"text\/html,application\/xhtml+xml,application\/xml;q=0.9,image\/avif,image\/webp,image\/apng,*\/*;q=0.8,application\/signed-exchange;v=b3;q=0.7","Sec-Fetch-Site":"none","Sec-Fetch-Mode":"navigate","Sec-Fetch-User":"?1","Sec-Fetch-Dest":"document","Accept-Encoding":"gzip, deflate, br, zstd","Accept-Language":"en-US"}
[02-Aug-2025 12:25:52 Africa/Algiers] Aucun token fourni, utilisation de l'ID par défaut
[02-Aug-2025 12:25:52 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:25:52 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(131): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(38): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:25:52 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 12:25:52 Africa/Algiers] userId reçu: 1
[02-Aug-2025 12:25:52 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 12:25:52 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:25:52 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id
            INNER JOIN users u ON m.email = u.email
            WHERE u.id = 4 AND u.status = 'active' AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:25:52 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 12:25:52 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id
            INNER JOIN users u ON m.email = u.email
            WHERE u.id = 4 AND u.status = 'active' AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:25:52 Africa/Algiers] UserId: 1
[02-Aug-2025 12:25:52 Africa/Algiers] SQLSTATE[HY093]: Invalid parameter number
[02-Aug-2025 12:26:09 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 12:26:09 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 12:26:09 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 12:26:09 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 12:26:09 Africa/Algiers] === Vérification de l'authentification ===
[02-Aug-2025 12:26:09 Africa/Algiers] Headers reçus: {"Host":"localhost:8000","Connection":"keep-alive","sec-ch-ua-platform":"\"Windows\"","Authorization":"Bearer demo_token","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Brave\";v=\"138\"","sec-ch-ua-mobile":"?0","Accept":"*\/*","Sec-GPC":"1","Accept-Language":"en-US,en;q=0.6","Sec-Fetch-Site":"same-origin","Sec-Fetch-Mode":"cors","Sec-Fetch-Dest":"empty","Referer":"http:\/\/localhost:8000\/dashboard.html","Accept-Encoding":"gzip, deflate, br, zstd"}
[02-Aug-2025 12:26:09 Africa/Algiers] Token extrait: demo_token
[02-Aug-2025 12:26:09 Africa/Algiers] === Début getUserIdFromToken ===
[02-Aug-2025 12:26:09 Africa/Algiers] Token reçu : demo_token
[02-Aug-2025 12:26:09 Africa/Algiers] Headers reçus : {"Host":"localhost:8000","Connection":"keep-alive","sec-ch-ua-platform":"\"Windows\"","Authorization":"Bearer demo_token","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Brave\";v=\"138\"","sec-ch-ua-mobile":"?0","Accept":"*\/*","Sec-GPC":"1","Accept-Language":"en-US,en;q=0.6","Sec-Fetch-Site":"same-origin","Sec-Fetch-Mode":"cors","Sec-Fetch-Dest":"empty","Referer":"http:\/\/localhost:8000\/dashboard.html","Accept-Encoding":"gzip, deflate, br, zstd"}
[02-Aug-2025 12:26:09 Africa/Algiers] Utilisation du token de démo
[02-Aug-2025 12:26:09 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:26:09 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(131): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(38): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:26:09 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 12:26:09 Africa/Algiers] userId reçu: 1
[02-Aug-2025 12:26:09 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 12:26:09 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:26:09 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id
            INNER JOIN users u ON m.email = u.email
            WHERE u.id = 4 AND u.status = 'active' AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:26:09 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 12:26:09 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id
            INNER JOIN users u ON m.email = u.email
            WHERE u.id = 4 AND u.status = 'active' AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:26:09 Africa/Algiers] UserId: 1
[02-Aug-2025 12:26:09 Africa/Algiers] SQLSTATE[HY093]: Invalid parameter number
[02-Aug-2025 12:26:10 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 12:26:10 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 12:26:10 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 12:26:10 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 12:26:10 Africa/Algiers] === Vérification de l'authentification ===
[02-Aug-2025 12:26:10 Africa/Algiers] Headers reçus: {"Host":"localhost:8000","Connection":"keep-alive","sec-ch-ua-platform":"\"Windows\"","Authorization":"Bearer demo_token","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Brave\";v=\"138\"","sec-ch-ua-mobile":"?0","Accept":"*\/*","Sec-GPC":"1","Accept-Language":"en-US,en;q=0.6","Sec-Fetch-Site":"same-origin","Sec-Fetch-Mode":"cors","Sec-Fetch-Dest":"empty","Referer":"http:\/\/localhost:8000\/dashboard.html","Accept-Encoding":"gzip, deflate, br, zstd"}
[02-Aug-2025 12:26:10 Africa/Algiers] Token extrait: demo_token
[02-Aug-2025 12:26:10 Africa/Algiers] === Début getUserIdFromToken ===
[02-Aug-2025 12:26:10 Africa/Algiers] Token reçu : demo_token
[02-Aug-2025 12:26:10 Africa/Algiers] Headers reçus : {"Host":"localhost:8000","Connection":"keep-alive","sec-ch-ua-platform":"\"Windows\"","Authorization":"Bearer demo_token","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Brave\";v=\"138\"","sec-ch-ua-mobile":"?0","Accept":"*\/*","Sec-GPC":"1","Accept-Language":"en-US,en;q=0.6","Sec-Fetch-Site":"same-origin","Sec-Fetch-Mode":"cors","Sec-Fetch-Dest":"empty","Referer":"http:\/\/localhost:8000\/dashboard.html","Accept-Encoding":"gzip, deflate, br, zstd"}
[02-Aug-2025 12:26:10 Africa/Algiers] Utilisation du token de démo
[02-Aug-2025 12:26:10 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:26:10 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(131): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(38): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:26:10 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 12:26:10 Africa/Algiers] userId reçu: 1
[02-Aug-2025 12:26:10 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 12:26:10 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:26:10 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id
            INNER JOIN users u ON m.email = u.email
            WHERE u.id = 4 AND u.status = 'active' AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:26:10 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 12:26:10 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id
            INNER JOIN users u ON m.email = u.email
            WHERE u.id = 4 AND u.status = 'active' AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:26:10 Africa/Algiers] UserId: 1
[02-Aug-2025 12:26:10 Africa/Algiers] SQLSTATE[HY093]: Invalid parameter number
[02-Aug-2025 12:26:28 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 12:26:28 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 12:26:28 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 12:26:28 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 12:26:28 Africa/Algiers] === Vérification de l'authentification ===
[02-Aug-2025 12:26:28 Africa/Algiers] Headers reçus: {"Host":"localhost:8000","Connection":"keep-alive","sec-ch-ua-platform":"\"Windows\"","Authorization":"Bearer demo_token","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Brave\";v=\"138\"","sec-ch-ua-mobile":"?0","Accept":"*\/*","Sec-GPC":"1","Accept-Language":"en-US,en;q=0.6","Sec-Fetch-Site":"same-origin","Sec-Fetch-Mode":"cors","Sec-Fetch-Dest":"empty","Referer":"http:\/\/localhost:8000\/dashboard.html","Accept-Encoding":"gzip, deflate, br, zstd"}
[02-Aug-2025 12:26:28 Africa/Algiers] Token extrait: demo_token
[02-Aug-2025 12:26:28 Africa/Algiers] === Début getUserIdFromToken ===
[02-Aug-2025 12:26:28 Africa/Algiers] Token reçu : demo_token
[02-Aug-2025 12:26:28 Africa/Algiers] Headers reçus : {"Host":"localhost:8000","Connection":"keep-alive","sec-ch-ua-platform":"\"Windows\"","Authorization":"Bearer demo_token","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Brave\";v=\"138\"","sec-ch-ua-mobile":"?0","Accept":"*\/*","Sec-GPC":"1","Accept-Language":"en-US,en;q=0.6","Sec-Fetch-Site":"same-origin","Sec-Fetch-Mode":"cors","Sec-Fetch-Dest":"empty","Referer":"http:\/\/localhost:8000\/dashboard.html","Accept-Encoding":"gzip, deflate, br, zstd"}
[02-Aug-2025 12:26:28 Africa/Algiers] Utilisation du token de démo
[02-Aug-2025 12:26:28 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:26:28 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(131): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(38): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:26:28 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 12:26:28 Africa/Algiers] userId reçu: 1
[02-Aug-2025 12:26:28 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 12:26:28 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:26:28 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id
            INNER JOIN users u ON m.email = u.email
            WHERE u.id = 4 AND u.status = 'active' AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:26:28 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 12:26:28 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id
            INNER JOIN users u ON m.email = u.email
            WHERE u.id = 4 AND u.status = 'active' AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:26:28 Africa/Algiers] UserId: 1
[02-Aug-2025 12:26:28 Africa/Algiers] SQLSTATE[HY093]: Invalid parameter number
[02-Aug-2025 12:26:28 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 12:26:28 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 12:26:28 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 12:26:28 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 12:26:28 Africa/Algiers] === Vérification de l'authentification ===
[02-Aug-2025 12:26:28 Africa/Algiers] Headers reçus: {"Host":"localhost:8000","Connection":"keep-alive","sec-ch-ua-platform":"\"Windows\"","Authorization":"Bearer demo_token","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Brave\";v=\"138\"","sec-ch-ua-mobile":"?0","Accept":"*\/*","Sec-GPC":"1","Accept-Language":"en-US,en;q=0.6","Sec-Fetch-Site":"same-origin","Sec-Fetch-Mode":"cors","Sec-Fetch-Dest":"empty","Referer":"http:\/\/localhost:8000\/dashboard.html","Accept-Encoding":"gzip, deflate, br, zstd"}
[02-Aug-2025 12:26:28 Africa/Algiers] Token extrait: demo_token
[02-Aug-2025 12:26:28 Africa/Algiers] === Début getUserIdFromToken ===
[02-Aug-2025 12:26:28 Africa/Algiers] Token reçu : demo_token
[02-Aug-2025 12:26:28 Africa/Algiers] Headers reçus : {"Host":"localhost:8000","Connection":"keep-alive","sec-ch-ua-platform":"\"Windows\"","Authorization":"Bearer demo_token","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Brave\";v=\"138\"","sec-ch-ua-mobile":"?0","Accept":"*\/*","Sec-GPC":"1","Accept-Language":"en-US,en;q=0.6","Sec-Fetch-Site":"same-origin","Sec-Fetch-Mode":"cors","Sec-Fetch-Dest":"empty","Referer":"http:\/\/localhost:8000\/dashboard.html","Accept-Encoding":"gzip, deflate, br, zstd"}
[02-Aug-2025 12:26:28 Africa/Algiers] Utilisation du token de démo
[02-Aug-2025 12:26:28 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:26:28 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(131): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(38): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:26:28 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 12:26:28 Africa/Algiers] userId reçu: 1
[02-Aug-2025 12:26:28 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 12:26:28 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:26:28 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id
            INNER JOIN users u ON m.email = u.email
            WHERE u.id = 4 AND u.status = 'active' AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:26:28 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 12:26:28 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id
            INNER JOIN users u ON m.email = u.email
            WHERE u.id = 4 AND u.status = 'active' AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:26:28 Africa/Algiers] UserId: 1
[02-Aug-2025 12:26:28 Africa/Algiers] SQLSTATE[HY093]: Invalid parameter number
[02-Aug-2025 12:28:04 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 12:28:04 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 12:28:04 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 12:28:04 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 12:28:04 Africa/Algiers] === Vérification de l'authentification ===
[02-Aug-2025 12:28:04 Africa/Algiers] Headers reçus: {"Host":"localhost:8000","Connection":"keep-alive","Upgrade-Insecure-Requests":"1","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Trae\/1.100.3 Chrome\/132.0.6834.210 Electron\/34.5.1 Safari\/537.36","Accept":"text\/html,application\/xhtml+xml,application\/xml;q=0.9,image\/avif,image\/webp,image\/apng,*\/*;q=0.8,application\/signed-exchange;v=b3;q=0.7","Sec-Fetch-Site":"none","Sec-Fetch-Mode":"navigate","Sec-Fetch-User":"?1","Sec-Fetch-Dest":"document","Accept-Encoding":"gzip, deflate, br, zstd","Accept-Language":"en-US"}
[02-Aug-2025 12:28:04 Africa/Algiers] Token extrait: null
[02-Aug-2025 12:28:04 Africa/Algiers] === Début getUserIdFromToken ===
[02-Aug-2025 12:28:04 Africa/Algiers] Token reçu : null
[02-Aug-2025 12:28:04 Africa/Algiers] Headers reçus : {"Host":"localhost:8000","Connection":"keep-alive","Upgrade-Insecure-Requests":"1","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Trae\/1.100.3 Chrome\/132.0.6834.210 Electron\/34.5.1 Safari\/537.36","Accept":"text\/html,application\/xhtml+xml,application\/xml;q=0.9,image\/avif,image\/webp,image\/apng,*\/*;q=0.8,application\/signed-exchange;v=b3;q=0.7","Sec-Fetch-Site":"none","Sec-Fetch-Mode":"navigate","Sec-Fetch-User":"?1","Sec-Fetch-Dest":"document","Accept-Encoding":"gzip, deflate, br, zstd","Accept-Language":"en-US"}
[02-Aug-2025 12:28:04 Africa/Algiers] Aucun token fourni, utilisation de l'ID par défaut
[02-Aug-2025 12:28:04 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:28:04 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(131): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(38): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:28:04 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 12:28:04 Africa/Algiers] userId reçu: 1
[02-Aug-2025 12:28:04 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 12:28:04 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:28:04 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id
            INNER JOIN users u ON m.email = u.email
            WHERE u.id = 4 AND u.status = 'active' AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:28:04 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 12:28:04 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id
            INNER JOIN users u ON m.email = u.email
            WHERE u.id = 4 AND u.status = 'active' AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:28:04 Africa/Algiers] UserId: 1
[02-Aug-2025 12:28:04 Africa/Algiers] SQLSTATE[HY093]: Invalid parameter number
[02-Aug-2025 12:29:24 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 12:29:24 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 12:29:24 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 12:29:24 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 12:29:24 Africa/Algiers] === Vérification de l'authentification ===
[02-Aug-2025 12:29:24 Africa/Algiers] Headers reçus: {"Host":"localhost:8000","Connection":"keep-alive","Upgrade-Insecure-Requests":"1","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Trae\/1.100.3 Chrome\/132.0.6834.210 Electron\/34.5.1 Safari\/537.36","Accept":"text\/html,application\/xhtml+xml,application\/xml;q=0.9,image\/avif,image\/webp,image\/apng,*\/*;q=0.8,application\/signed-exchange;v=b3;q=0.7","Sec-Fetch-Site":"none","Sec-Fetch-Mode":"navigate","Sec-Fetch-User":"?1","Sec-Fetch-Dest":"document","Accept-Encoding":"gzip, deflate, br, zstd","Accept-Language":"en-US"}
[02-Aug-2025 12:29:24 Africa/Algiers] Token extrait: null
[02-Aug-2025 12:29:24 Africa/Algiers] === Début getUserIdFromToken ===
[02-Aug-2025 12:29:24 Africa/Algiers] Token reçu : null
[02-Aug-2025 12:29:24 Africa/Algiers] Headers reçus : {"Host":"localhost:8000","Connection":"keep-alive","Upgrade-Insecure-Requests":"1","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Trae\/1.100.3 Chrome\/132.0.6834.210 Electron\/34.5.1 Safari\/537.36","Accept":"text\/html,application\/xhtml+xml,application\/xml;q=0.9,image\/avif,image\/webp,image\/apng,*\/*;q=0.8,application\/signed-exchange;v=b3;q=0.7","Sec-Fetch-Site":"none","Sec-Fetch-Mode":"navigate","Sec-Fetch-User":"?1","Sec-Fetch-Dest":"document","Accept-Encoding":"gzip, deflate, br, zstd","Accept-Language":"en-US"}
[02-Aug-2025 12:29:24 Africa/Algiers] Aucun token fourni, utilisation de l'ID par défaut
[02-Aug-2025 12:29:24 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:29:24 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(131): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(38): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:29:24 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 12:29:24 Africa/Algiers] userId reçu: 1
[02-Aug-2025 12:29:24 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 12:29:24 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:29:24 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:29:24 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 12:29:24 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:29:24 Africa/Algiers] UserId: 1
[02-Aug-2025 12:29:24 Africa/Algiers] SQLSTATE[HY093]: Invalid parameter number
[02-Aug-2025 12:29:55 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 12:29:55 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 12:29:55 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 12:29:55 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 12:29:55 Africa/Algiers] === Vérification de l'authentification ===
[02-Aug-2025 12:29:55 Africa/Algiers] Headers reçus: {"Host":"localhost:8000","Connection":"keep-alive","Pragma":"no-cache","Cache-Control":"no-cache","Upgrade-Insecure-Requests":"1","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Trae\/1.100.3 Chrome\/132.0.6834.210 Electron\/34.5.1 Safari\/537.36","Accept":"text\/html,application\/xhtml+xml,application\/xml;q=0.9,image\/avif,image\/webp,image\/apng,*\/*;q=0.8,application\/signed-exchange;v=b3;q=0.7","Sec-Fetch-Site":"none","Sec-Fetch-Mode":"navigate","Sec-Fetch-User":"?1","Sec-Fetch-Dest":"document","Accept-Encoding":"gzip, deflate, br, zstd","Accept-Language":"en-US"}
[02-Aug-2025 12:29:55 Africa/Algiers] Token extrait: null
[02-Aug-2025 12:29:55 Africa/Algiers] === Début getUserIdFromToken ===
[02-Aug-2025 12:29:55 Africa/Algiers] Token reçu : null
[02-Aug-2025 12:29:55 Africa/Algiers] Headers reçus : {"Host":"localhost:8000","Connection":"keep-alive","Pragma":"no-cache","Cache-Control":"no-cache","Upgrade-Insecure-Requests":"1","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Trae\/1.100.3 Chrome\/132.0.6834.210 Electron\/34.5.1 Safari\/537.36","Accept":"text\/html,application\/xhtml+xml,application\/xml;q=0.9,image\/avif,image\/webp,image\/apng,*\/*;q=0.8,application\/signed-exchange;v=b3;q=0.7","Sec-Fetch-Site":"none","Sec-Fetch-Mode":"navigate","Sec-Fetch-User":"?1","Sec-Fetch-Dest":"document","Accept-Encoding":"gzip, deflate, br, zstd","Accept-Language":"en-US"}
[02-Aug-2025 12:29:55 Africa/Algiers] Aucun token fourni, utilisation de l'ID par défaut
[02-Aug-2025 12:29:55 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:29:55 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(131): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(38): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:29:55 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 12:29:55 Africa/Algiers] userId reçu: 1
[02-Aug-2025 12:29:55 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 12:29:55 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:29:55 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:29:55 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 12:29:55 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:29:55 Africa/Algiers] UserId: 1
[02-Aug-2025 12:29:55 Africa/Algiers] SQLSTATE[HY093]: Invalid parameter number
[02-Aug-2025 12:30:01 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 12:30:01 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 12:30:01 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 12:30:01 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 12:30:01 Africa/Algiers] === Utilisation du merchant_id de test ===
[02-Aug-2025 12:30:01 Africa/Algiers] MerchantId: 1
[02-Aug-2025 12:30:01 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:30:01 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(128): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(35): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:30:01 Africa/Algiers] PHP Warning:  Undefined variable $userId in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 41
[02-Aug-2025 12:30:01 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 12:30:01 Africa/Algiers] userId reçu: 
[02-Aug-2025 12:30:01 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 12:30:01 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:30:01 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:30:01 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 12:30:01 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:30:01 Africa/Algiers] UserId: 
[02-Aug-2025 12:30:01 Africa/Algiers] SQLSTATE[HY093]: Invalid parameter number
[02-Aug-2025 12:30:04 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 12:30:04 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 12:30:04 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 12:30:04 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 12:30:04 Africa/Algiers] === Utilisation du merchant_id de test ===
[02-Aug-2025 12:30:04 Africa/Algiers] MerchantId: 1
[02-Aug-2025 12:30:04 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:30:04 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(128): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(35): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:30:04 Africa/Algiers] PHP Warning:  Undefined variable $userId in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 41
[02-Aug-2025 12:30:04 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 12:30:04 Africa/Algiers] userId reçu: 
[02-Aug-2025 12:30:04 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 12:30:04 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:30:04 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:30:04 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 12:30:04 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:30:04 Africa/Algiers] UserId: 
[02-Aug-2025 12:30:04 Africa/Algiers] SQLSTATE[HY093]: Invalid parameter number
[02-Aug-2025 12:30:08 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 12:30:08 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 12:30:08 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 12:30:08 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 12:30:08 Africa/Algiers] === Utilisation du merchant_id de test ===
[02-Aug-2025 12:30:08 Africa/Algiers] MerchantId: 1
[02-Aug-2025 12:30:08 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:30:08 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(128): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(35): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:30:08 Africa/Algiers] PHP Warning:  Undefined variable $userId in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 41
[02-Aug-2025 12:30:08 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 12:30:08 Africa/Algiers] userId reçu: 
[02-Aug-2025 12:30:08 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 12:30:08 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:30:08 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:30:08 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 12:30:08 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:30:08 Africa/Algiers] UserId: 
[02-Aug-2025 12:30:08 Africa/Algiers] SQLSTATE[HY093]: Invalid parameter number
[02-Aug-2025 12:30:09 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 12:30:09 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 12:30:09 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 12:30:09 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 12:30:09 Africa/Algiers] === Utilisation du merchant_id de test ===
[02-Aug-2025 12:30:09 Africa/Algiers] MerchantId: 1
[02-Aug-2025 12:30:09 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:30:09 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(128): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(35): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:30:09 Africa/Algiers] PHP Warning:  Undefined variable $userId in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 41
[02-Aug-2025 12:30:09 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 12:30:09 Africa/Algiers] userId reçu: 
[02-Aug-2025 12:30:09 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 12:30:09 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:30:09 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:30:09 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 12:30:09 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:30:09 Africa/Algiers] UserId: 
[02-Aug-2025 12:30:09 Africa/Algiers] SQLSTATE[HY093]: Invalid parameter number
[02-Aug-2025 12:30:44 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 12:30:44 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 12:30:44 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 12:30:44 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 12:30:44 Africa/Algiers] === Utilisation du merchant_id de test ===
[02-Aug-2025 12:30:44 Africa/Algiers] MerchantId: 1
[02-Aug-2025 12:30:44 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:30:44 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(129): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(35): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:30:44 Africa/Algiers] PHP Warning:  Undefined variable $userId in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 41
[02-Aug-2025 12:30:44 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 12:30:44 Africa/Algiers] userId reçu: 
[02-Aug-2025 12:30:44 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 12:30:44 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:30:44 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:30:44 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 12:30:44 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:30:44 Africa/Algiers] UserId: 
[02-Aug-2025 12:30:44 Africa/Algiers] SQLSTATE[HY093]: Invalid parameter number
[02-Aug-2025 12:31:50 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 12:31:50 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 12:31:50 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 12:31:50 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 12:31:50 Africa/Algiers] === Utilisation du merchant_id de test ===
[02-Aug-2025 12:31:50 Africa/Algiers] MerchantId: 1
[02-Aug-2025 12:31:50 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 12:31:50 Africa/Algiers] PHP Warning:  Undefined variable $userId in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 41
[02-Aug-2025 12:31:50 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 12:31:50 Africa/Algiers] userId reçu: 
[02-Aug-2025 12:31:50 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 12:31:50 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:31:50 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:31:50 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 12:31:50 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:31:50 Africa/Algiers] UserId: 
[02-Aug-2025 12:31:50 Africa/Algiers] SQLSTATE[HY093]: Invalid parameter number
[02-Aug-2025 12:32:48 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 12:32:48 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 12:32:48 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 12:32:48 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 12:32:48 Africa/Algiers] === Utilisation du merchant_id de test ===
[02-Aug-2025 12:32:48 Africa/Algiers] MerchantId: 1
[02-Aug-2025 12:32:48 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 12:32:48 Africa/Algiers] PHP Warning:  Undefined variable $userId in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 41
[02-Aug-2025 12:32:48 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 12:32:48 Africa/Algiers] userId reçu: 
[02-Aug-2025 12:32:48 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 12:32:48 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:32:48 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:32:48 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 12:32:48 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:32:48 Africa/Algiers] UserId: 
[02-Aug-2025 12:32:48 Africa/Algiers] Paramètres de la requête: [null]
[02-Aug-2025 12:32:48 Africa/Algiers] SQLSTATE[HY093]: Invalid parameter number
[02-Aug-2025 12:34:05 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 12:34:07 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 12:34:07 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 12:34:07 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 12:34:07 Africa/Algiers] === Utilisation du merchant_id de test ===
[02-Aug-2025 12:34:07 Africa/Algiers] MerchantId: 1
[02-Aug-2025 12:34:07 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 12:34:07 Africa/Algiers] PHP Warning:  Undefined variable $userId in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 41
[02-Aug-2025 12:34:07 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 12:34:07 Africa/Algiers] userId reçu: 
[02-Aug-2025 12:34:07 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 12:34:07 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:34:07 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:34:07 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 12:34:07 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:34:07 Africa/Algiers] UserId: 
[02-Aug-2025 12:34:07 Africa/Algiers] Paramètres de la requête: [null]
[02-Aug-2025 12:34:07 Africa/Algiers] SQLSTATE[HY093]: Invalid parameter number
[02-Aug-2025 12:35:04 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 12:35:04 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 12:35:04 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 12:35:04 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 12:35:04 Africa/Algiers] === Utilisation du merchant_id de test ===
[02-Aug-2025 12:35:04 Africa/Algiers] MerchantId: 1
[02-Aug-2025 12:35:04 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 12:35:04 Africa/Algiers] PHP Warning:  Undefined variable $userId in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 46
[02-Aug-2025 12:35:04 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 12:35:04 Africa/Algiers] userId reçu: 
[02-Aug-2025 12:35:04 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 12:35:04 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:35:04 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:35:04 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 12:35:04 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:35:04 Africa/Algiers] UserId: 
[02-Aug-2025 12:35:04 Africa/Algiers] Paramètres de la requête: [null]
[02-Aug-2025 12:35:04 Africa/Algiers] SQLSTATE[HY093]: Invalid parameter number
[02-Aug-2025 12:58:25 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 12:58:25 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 12:58:25 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 12:58:25 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 12:58:25 Africa/Algiers] === Utilisation du merchant_id de test ===
[02-Aug-2025 12:58:25 Africa/Algiers] MerchantId: 1
[02-Aug-2025 12:58:25 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 12:58:25 Africa/Algiers] PHP Warning:  Undefined variable $userId in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 46
[02-Aug-2025 12:58:25 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 12:58:25 Africa/Algiers] userId reçu: 
[02-Aug-2025 12:58:25 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 12:58:25 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:58:25 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:58:25 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 12:58:25 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:58:25 Africa/Algiers] UserId: 
[02-Aug-2025 12:58:25 Africa/Algiers] Paramètres de la requête: [null]
[02-Aug-2025 12:58:25 Africa/Algiers] SQLSTATE[HY093]: Invalid parameter number
[02-Aug-2025 12:58:25 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 12:58:25 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 12:58:25 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 12:58:25 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 12:58:25 Africa/Algiers] === Utilisation du merchant_id de test ===
[02-Aug-2025 12:58:25 Africa/Algiers] MerchantId: 1
[02-Aug-2025 12:58:25 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 12:58:25 Africa/Algiers] PHP Warning:  Undefined variable $userId in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 46
[02-Aug-2025 12:58:25 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 12:58:25 Africa/Algiers] userId reçu: 
[02-Aug-2025 12:58:25 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 12:58:25 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:58:25 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:58:25 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 12:58:25 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:58:25 Africa/Algiers] UserId: 
[02-Aug-2025 12:58:25 Africa/Algiers] Paramètres de la requête: [null]
[02-Aug-2025 12:58:25 Africa/Algiers] SQLSTATE[HY093]: Invalid parameter number
[02-Aug-2025 13:01:20 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 13:01:20 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 13:01:20 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 13:01:20 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 13:01:20 Africa/Algiers] === Utilisation du merchant_id de test ===
[02-Aug-2025 13:01:20 Africa/Algiers] MerchantId: 1
[02-Aug-2025 13:01:20 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 13:01:20 Africa/Algiers] PHP Warning:  Undefined variable $userId in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 46
[02-Aug-2025 13:01:20 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 13:01:20 Africa/Algiers] userId reçu: 
[02-Aug-2025 13:01:20 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 13:01:20 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 13:01:20 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 13:01:20 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 13:01:20 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 13:01:20 Africa/Algiers] UserId: 
[02-Aug-2025 13:01:20 Africa/Algiers] Paramètres de la requête: [null]
[02-Aug-2025 13:01:20 Africa/Algiers] SQLSTATE[HY093]: Invalid parameter number
[02-Aug-2025 13:01:20 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 13:01:20 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 13:01:20 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 13:01:20 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 13:01:20 Africa/Algiers] === Utilisation du merchant_id de test ===
[02-Aug-2025 13:01:20 Africa/Algiers] MerchantId: 1
[02-Aug-2025 13:01:20 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 13:01:20 Africa/Algiers] PHP Warning:  Undefined variable $userId in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 46
[02-Aug-2025 13:01:20 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 13:01:20 Africa/Algiers] userId reçu: 
[02-Aug-2025 13:01:20 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 13:01:20 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 13:01:20 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 13:01:20 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 13:01:20 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 13:01:20 Africa/Algiers] UserId: 
[02-Aug-2025 13:01:20 Africa/Algiers] Paramètres de la requête: [null]
[02-Aug-2025 13:01:20 Africa/Algiers] SQLSTATE[HY093]: Invalid parameter number
[02-Aug-2025 13:14:03 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 13:14:05 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 13:14:05 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 13:14:05 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 13:14:05 Africa/Algiers] === Utilisation du merchant_id de test ===
[02-Aug-2025 13:14:05 Africa/Algiers] MerchantId: 1
[02-Aug-2025 13:14:05 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 13:14:05 Africa/Algiers] PHP Warning:  Undefined variable $userId in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 46
[02-Aug-2025 13:14:05 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 13:14:05 Africa/Algiers] userId reçu: 
[02-Aug-2025 13:14:05 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 13:14:05 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 13:14:05 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 13:14:05 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 13:14:05 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 13:14:05 Africa/Algiers] UserId: 
[02-Aug-2025 13:14:05 Africa/Algiers] Paramètres de la requête: [null]
[02-Aug-2025 13:14:05 Africa/Algiers] SQLSTATE[HY093]: Invalid parameter number
[02-Aug-2025 13:18:48 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 13:18:50 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 13:18:50 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 13:18:50 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 13:18:50 Africa/Algiers] PHP Fatal error:  Uncaught Error: Call to undefined function getMerchantId() in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:35
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 35
[02-Aug-2025 12:37:08 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 12:37:11 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 13:37:13 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 13:37:13 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 13:37:13 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 13:37:13 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 13:37:13 Africa/Algiers] PHP Fatal error:  Uncaught Error: Call to undefined function getMerchantId() in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:35
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 35
[02-Aug-2025 13:37:13 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 13:37:13 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 13:37:13 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 13:37:13 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 13:37:13 Africa/Algiers] PHP Fatal error:  Uncaught Error: Call to undefined function getMerchantId() in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:35
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 35
[02-Aug-2025 13:37:29 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 13:37:29 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 13:37:29 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 13:37:29 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 13:37:29 Africa/Algiers] PHP Fatal error:  Uncaught Error: Call to undefined function getMerchantId() in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:35
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 35
[02-Aug-2025 13:37:29 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 13:37:29 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 13:37:29 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 13:37:29 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 13:37:29 Africa/Algiers] PHP Fatal error:  Uncaught Error: Call to undefined function getMerchantId() in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:35
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 35
[02-Aug-2025 13:43:52 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 13:43:54 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 13:43:54 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 13:43:54 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 13:43:54 Africa/Algiers] PHP Fatal error:  Uncaught Error: Call to undefined function getMerchantId() in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:35
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 35
[02-Aug-2025 13:43:54 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 13:43:54 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 13:43:54 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 13:43:54 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 13:43:54 Africa/Algiers] PHP Fatal error:  Uncaught Error: Call to undefined function getMerchantId() in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:35
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 35
[02-Aug-2025 13:49:16 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 13:49:18 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 13:49:19 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 13:49:20 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 13:49:20 Africa/Algiers] PHP Fatal error:  Uncaught Error: Call to undefined function getMerchantId() in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:35
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 35
[02-Aug-2025 13:53:52 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 13:53:54 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 13:53:54 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 13:53:54 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 13:53:54 Africa/Algiers] PHP Fatal error:  Uncaught Error: Call to undefined function getMerchantId() in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:35
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 35
[02-Aug-2025 13:59:27 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 13:59:29 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 13:59:29 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 13:59:29 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 13:59:29 Africa/Algiers] PHP Fatal error:  Uncaught Error: Call to undefined function getMerchantId() in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:35
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 35
[02-Aug-2025 14:03:21 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 14:03:23 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 14:03:23 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 14:03:23 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 14:03:23 Africa/Algiers] PHP Fatal error:  Uncaught Error: Call to undefined function getMerchantId() in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:35
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 35
[02-Aug-2025 14:03:23 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 14:03:23 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 14:03:23 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 14:03:23 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 14:03:23 Africa/Algiers] PHP Fatal error:  Uncaught Error: Call to undefined function getMerchantId() in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:35
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 35
[02-Aug-2025 14:03:52 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 14:03:52 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 14:03:52 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 14:03:52 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 14:03:52 Africa/Algiers] PHP Fatal error:  Uncaught Error: Call to undefined function getMerchantId() in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:35
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 35
[02-Aug-2025 14:03:52 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 14:03:53 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 14:03:53 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 14:03:53 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 14:03:53 Africa/Algiers] PHP Fatal error:  Uncaught Error: Call to undefined function getMerchantId() in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:35
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 35
[02-Aug-2025 14:05:09 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 14:05:09 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 14:05:09 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 14:05:09 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 14:05:09 Africa/Algiers] PHP Fatal error:  Uncaught Error: Call to undefined function getMerchantId() in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:35
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 35
[02-Aug-2025 14:05:11 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 14:05:11 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 14:05:11 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 14:05:11 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 14:05:11 Africa/Algiers] PHP Fatal error:  Uncaught Error: Call to undefined function getMerchantId() in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:35
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 35
[02-Aug-2025 14:07:43 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 14:07:45 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 14:07:45 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 14:07:45 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 14:07:45 Africa/Algiers] Début de getMerchantId
[02-Aug-2025 14:07:45 Africa/Algiers] === MerchantId récupéré ===
[02-Aug-2025 14:07:45 Africa/Algiers] MerchantId: 1
[02-Aug-2025 14:07:45 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 14:07:45 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 14:07:45 Africa/Algiers] merchantId reçu: 1
[02-Aug-2025 14:07:45 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 14:07:45 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 14:07:45 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 14:07:45 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 14:07:45 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 14:07:45 Africa/Algiers] MerchantId: 1
[02-Aug-2025 14:07:45 Africa/Algiers] Paramètres de la requête: [1]
[02-Aug-2025 14:07:45 Africa/Algiers] Nombre de pages trouvées: 1
[02-Aug-2025 14:07:45 Africa/Algiers] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 14:07:45 Africa/Algiers] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 14:10:31 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 14:10:33 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 14:10:33 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 14:10:33 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 14:10:33 Africa/Algiers] Début de getMerchantId
[02-Aug-2025 14:10:33 Africa/Algiers] === MerchantId récupéré ===
[02-Aug-2025 14:10:33 Africa/Algiers] MerchantId: 1
[02-Aug-2025 14:10:33 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 14:10:33 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 14:10:33 Africa/Algiers] merchantId reçu: 1
[02-Aug-2025 14:10:33 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 14:10:33 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 14:10:33 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 14:10:33 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 14:10:33 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 14:10:33 Africa/Algiers] MerchantId: 1
[02-Aug-2025 14:10:33 Africa/Algiers] Paramètres de la requête: [1]
[02-Aug-2025 14:10:33 Africa/Algiers] Nombre de pages trouvées: 1
[02-Aug-2025 14:10:33 Africa/Algiers] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 14:10:33 Africa/Algiers] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 14:10:33 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 14:10:33 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 14:10:33 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 14:10:33 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 14:10:33 Africa/Algiers] Début de getMerchantId
[02-Aug-2025 14:10:33 Africa/Algiers] === MerchantId récupéré ===
[02-Aug-2025 14:10:33 Africa/Algiers] MerchantId: 1
[02-Aug-2025 14:10:33 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 14:10:33 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 14:10:33 Africa/Algiers] merchantId reçu: 1
[02-Aug-2025 14:10:33 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 14:10:33 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 14:10:33 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 14:10:33 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 14:10:33 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 14:10:33 Africa/Algiers] MerchantId: 1
[02-Aug-2025 14:10:33 Africa/Algiers] Paramètres de la requête: [1]
[02-Aug-2025 14:10:33 Africa/Algiers] Nombre de pages trouvées: 1
[02-Aug-2025 14:10:33 Africa/Algiers] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 14:10:33 Africa/Algiers] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 14:18:39 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 14:18:41 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 14:18:41 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 14:18:41 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 14:18:41 Africa/Algiers] Début de getMerchantId
[02-Aug-2025 14:18:41 Africa/Algiers] === MerchantId récupéré ===
[02-Aug-2025 14:18:41 Africa/Algiers] MerchantId: 1
[02-Aug-2025 14:18:41 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 14:18:41 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 14:18:41 Africa/Algiers] merchantId reçu: 1
[02-Aug-2025 14:18:41 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 14:18:41 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 14:18:41 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 14:18:41 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 14:18:41 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 14:18:41 Africa/Algiers] MerchantId: 1
[02-Aug-2025 14:18:41 Africa/Algiers] Paramètres de la requête: [1]
[02-Aug-2025 14:18:41 Africa/Algiers] Nombre de pages trouvées: 1
[02-Aug-2025 14:18:41 Africa/Algiers] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 14:18:41 Africa/Algiers] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 15:59:04 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 15:59:04 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 16:59:06 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 16:59:06 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 16:59:06 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 16:59:06 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 16:59:06 Africa/Algiers] Début de getMerchantId
[02-Aug-2025 16:59:06 Africa/Algiers] === MerchantId récupéré ===
[02-Aug-2025 16:59:06 Africa/Algiers] MerchantId: 1
[02-Aug-2025 16:59:06 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 16:59:06 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 16:59:06 Africa/Algiers] merchantId reçu: 1
[02-Aug-2025 16:59:06 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 16:59:06 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 16:59:06 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 16:59:06 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 16:59:06 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 16:59:06 Africa/Algiers] MerchantId: 1
[02-Aug-2025 16:59:06 Africa/Algiers] Paramètres de la requête: [1]
[02-Aug-2025 16:59:06 Africa/Algiers] Nombre de pages trouvées: 1
[02-Aug-2025 16:59:06 Africa/Algiers] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 16:59:06 Africa/Algiers] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 16:59:06 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 16:59:06 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 16:59:06 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 16:59:06 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 16:59:06 Africa/Algiers] Début de getMerchantId
[02-Aug-2025 16:59:06 Africa/Algiers] === MerchantId récupéré ===
[02-Aug-2025 16:59:06 Africa/Algiers] MerchantId: 1
[02-Aug-2025 16:59:06 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 16:59:06 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 16:59:06 Africa/Algiers] merchantId reçu: 1
[02-Aug-2025 16:59:06 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 16:59:06 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 16:59:06 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 16:59:06 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 16:59:06 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 16:59:06 Africa/Algiers] MerchantId: 1
[02-Aug-2025 16:59:06 Africa/Algiers] Paramètres de la requête: [1]
[02-Aug-2025 16:59:06 Africa/Algiers] Nombre de pages trouvées: 1
[02-Aug-2025 16:59:06 Africa/Algiers] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 16:59:06 Africa/Algiers] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 16:28:25 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 16:28:25 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 17:28:27 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 17:28:27 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 17:28:27 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 17:28:27 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 17:28:27 Africa/Algiers] Début de getMerchantId
[02-Aug-2025 17:28:27 Africa/Algiers] === MerchantId récupéré ===
[02-Aug-2025 17:28:27 Africa/Algiers] MerchantId: 1
[02-Aug-2025 17:28:27 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 17:28:27 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 17:28:27 Africa/Algiers] merchantId reçu: 1
[02-Aug-2025 17:28:27 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 17:28:27 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 17:28:27 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 17:28:27 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 17:28:27 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 17:28:27 Africa/Algiers] MerchantId: 1
[02-Aug-2025 17:28:27 Africa/Algiers] Paramètres de la requête: [1]
[02-Aug-2025 17:28:27 Africa/Algiers] Nombre de pages trouvées: 1
[02-Aug-2025 17:28:27 Africa/Algiers] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 17:28:27 Africa/Algiers] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 17:28:27 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 17:28:27 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 17:28:27 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 17:28:27 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 17:28:27 Africa/Algiers] Début de getMerchantId
[02-Aug-2025 17:28:27 Africa/Algiers] === MerchantId récupéré ===
[02-Aug-2025 17:28:27 Africa/Algiers] MerchantId: 1
[02-Aug-2025 17:28:27 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 17:28:27 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 17:28:27 Africa/Algiers] merchantId reçu: 1
[02-Aug-2025 17:28:27 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 17:28:27 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 17:28:27 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 17:28:27 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 17:28:27 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 17:28:27 Africa/Algiers] MerchantId: 1
[02-Aug-2025 17:28:27 Africa/Algiers] Paramètres de la requête: [1]
[02-Aug-2025 17:28:27 Africa/Algiers] Nombre de pages trouvées: 1
[02-Aug-2025 17:28:27 Africa/Algiers] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 17:28:27 Africa/Algiers] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 17:18:55 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 17:18:57 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 18:18:59 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 18:18:59 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 18:18:59 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 18:18:59 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 18:18:59 Africa/Algiers] Début de getMerchantId
[02-Aug-2025 18:18:59 Africa/Algiers] === MerchantId récupéré ===
[02-Aug-2025 18:18:59 Africa/Algiers] MerchantId: 1
[02-Aug-2025 18:18:59 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 18:18:59 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 18:18:59 Africa/Algiers] merchantId reçu: 1
[02-Aug-2025 18:18:59 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 18:18:59 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 18:18:59 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 18:18:59 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 18:18:59 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 18:18:59 Africa/Algiers] MerchantId: 1
[02-Aug-2025 18:18:59 Africa/Algiers] Paramètres de la requête: [1]
[02-Aug-2025 18:18:59 Africa/Algiers] Nombre de pages trouvées: 1
[02-Aug-2025 18:18:59 Africa/Algiers] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 18:18:59 Africa/Algiers] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 18:18:59 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 18:18:59 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 18:18:59 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 18:18:59 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 18:18:59 Africa/Algiers] Début de getMerchantId
[02-Aug-2025 18:18:59 Africa/Algiers] === MerchantId récupéré ===
[02-Aug-2025 18:18:59 Africa/Algiers] MerchantId: 1
[02-Aug-2025 18:18:59 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 18:18:59 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 18:18:59 Africa/Algiers] merchantId reçu: 1
[02-Aug-2025 18:18:59 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 18:18:59 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 18:18:59 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 18:18:59 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 18:18:59 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 18:18:59 Africa/Algiers] MerchantId: 1
[02-Aug-2025 18:18:59 Africa/Algiers] Paramètres de la requête: [1]
[02-Aug-2025 18:18:59 Africa/Algiers] Nombre de pages trouvées: 1
[02-Aug-2025 18:18:59 Africa/Algiers] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 18:18:59 Africa/Algiers] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 18:42:49 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 18:42:49 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 18:42:50 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 18:42:50 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 18:42:50 Africa/Algiers] Début de getMerchantId
[02-Aug-2025 18:42:50 Africa/Algiers] === MerchantId récupéré ===
[02-Aug-2025 18:42:50 Africa/Algiers] MerchantId: 1
[02-Aug-2025 18:42:50 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 18:42:50 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 18:42:50 Africa/Algiers] merchantId reçu: 1
[02-Aug-2025 18:42:50 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 18:42:50 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 18:42:50 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 18:42:50 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 18:42:50 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 18:42:50 Africa/Algiers] MerchantId: 1
[02-Aug-2025 18:42:50 Africa/Algiers] Paramètres de la requête: [1]
[02-Aug-2025 18:42:50 Africa/Algiers] Nombre de pages trouvées: 1
[02-Aug-2025 18:42:50 Africa/Algiers] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 18:42:50 Africa/Algiers] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 18:42:51 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 18:42:51 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 18:42:51 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 18:42:51 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 18:42:51 Africa/Algiers] Début de getMerchantId
[02-Aug-2025 18:42:51 Africa/Algiers] === MerchantId récupéré ===
[02-Aug-2025 18:42:51 Africa/Algiers] MerchantId: 1
[02-Aug-2025 18:42:51 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 18:42:51 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 18:42:51 Africa/Algiers] merchantId reçu: 1
[02-Aug-2025 18:42:51 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 18:42:51 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 18:42:51 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 18:42:51 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 18:42:51 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 18:42:51 Africa/Algiers] MerchantId: 1
[02-Aug-2025 18:42:51 Africa/Algiers] Paramètres de la requête: [1]
[02-Aug-2025 18:42:51 Africa/Algiers] Nombre de pages trouvées: 1
[02-Aug-2025 18:42:51 Africa/Algiers] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 18:42:51 Africa/Algiers] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 18:56:01 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 18:56:01 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 18:56:01 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 18:56:01 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 18:56:01 Africa/Algiers] Début de getMerchantId
[02-Aug-2025 18:56:01 Africa/Algiers] === MerchantId récupéré ===
[02-Aug-2025 18:56:01 Africa/Algiers] MerchantId: 1
[02-Aug-2025 18:56:01 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 18:56:01 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 18:56:01 Africa/Algiers] merchantId reçu: 1
[02-Aug-2025 18:56:01 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 18:56:01 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 18:56:01 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 18:56:01 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 18:56:01 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 18:56:01 Africa/Algiers] MerchantId: 1
[02-Aug-2025 18:56:01 Africa/Algiers] Paramètres de la requête: [1]
[02-Aug-2025 18:56:01 Africa/Algiers] Nombre de pages trouvées: 1
[02-Aug-2025 18:56:01 Africa/Algiers] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 18:56:01 Africa/Algiers] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 18:56:01 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 18:56:01 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 18:56:01 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 18:56:01 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 18:56:01 Africa/Algiers] Début de getMerchantId
[02-Aug-2025 18:56:01 Africa/Algiers] === MerchantId récupéré ===
[02-Aug-2025 18:56:01 Africa/Algiers] MerchantId: 1
[02-Aug-2025 18:56:01 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 18:56:01 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 18:56:01 Africa/Algiers] merchantId reçu: 1
[02-Aug-2025 18:56:01 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 18:56:01 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 18:56:01 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 18:56:01 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 18:56:01 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 18:56:01 Africa/Algiers] MerchantId: 1
[02-Aug-2025 18:56:01 Africa/Algiers] Paramètres de la requête: [1]
[02-Aug-2025 18:56:01 Africa/Algiers] Nombre de pages trouvées: 1
[02-Aug-2025 18:56:01 Africa/Algiers] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 18:56:01 Africa/Algiers] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 17:56:06 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 17:56:06 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 18:56:50 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 18:56:50 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 18:56:50 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 18:56:50 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 18:56:50 Africa/Algiers] Début de getMerchantId
[02-Aug-2025 18:56:50 Africa/Algiers] === MerchantId récupéré ===
[02-Aug-2025 18:56:50 Africa/Algiers] MerchantId: 1
[02-Aug-2025 18:56:50 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 19:21:13 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 19:21:13 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 19:21:13 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 19:21:13 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 19:21:13 Africa/Algiers] Début de getMerchantId
[02-Aug-2025 19:21:13 Africa/Algiers] === MerchantId récupéré ===
[02-Aug-2025 19:21:13 Africa/Algiers] MerchantId: 1
[02-Aug-2025 19:21:13 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 19:21:13 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 19:21:13 Africa/Algiers] merchantId reçu: 1
[02-Aug-2025 19:21:13 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 19:21:13 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 19:21:13 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 19:21:13 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 19:21:13 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 19:21:13 Africa/Algiers] MerchantId: 1
[02-Aug-2025 19:21:13 Africa/Algiers] Paramètres de la requête: [1]
[02-Aug-2025 19:21:13 Africa/Algiers] Nombre de pages trouvées: 1
[02-Aug-2025 19:21:13 Africa/Algiers] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 19:21:13 Africa/Algiers] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 19:21:14 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 19:21:14 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 19:21:14 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 19:21:14 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 19:21:14 Africa/Algiers] Début de getMerchantId
[02-Aug-2025 19:21:14 Africa/Algiers] === MerchantId récupéré ===
[02-Aug-2025 19:21:14 Africa/Algiers] MerchantId: 1
[02-Aug-2025 19:21:14 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 19:21:14 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 19:21:14 Africa/Algiers] merchantId reçu: 1
[02-Aug-2025 19:21:14 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 19:21:14 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 19:21:14 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 19:21:14 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 19:21:14 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 19:21:14 Africa/Algiers] MerchantId: 1
[02-Aug-2025 19:21:14 Africa/Algiers] Paramètres de la requête: [1]
[02-Aug-2025 19:21:14 Africa/Algiers] Nombre de pages trouvées: 1
[02-Aug-2025 19:21:14 Africa/Algiers] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 19:21:14 Africa/Algiers] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 19:21:22 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 19:21:22 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 19:21:22 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 19:21:22 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 19:21:22 Africa/Algiers] Début de getMerchantId
[02-Aug-2025 19:21:22 Africa/Algiers] === MerchantId récupéré ===
[02-Aug-2025 19:21:22 Africa/Algiers] MerchantId: 1
[02-Aug-2025 19:21:22 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 19:21:22 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 19:21:22 Africa/Algiers] merchantId reçu: 1
[02-Aug-2025 19:21:22 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 19:21:22 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 19:21:22 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 19:21:22 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 19:21:22 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 19:21:22 Africa/Algiers] MerchantId: 1
[02-Aug-2025 19:21:22 Africa/Algiers] Paramètres de la requête: [1]
[02-Aug-2025 19:21:22 Africa/Algiers] Nombre de pages trouvées: 1
[02-Aug-2025 19:21:22 Africa/Algiers] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 19:21:22 Africa/Algiers] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 19:21:22 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 19:21:22 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 19:21:22 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 19:21:22 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 19:21:22 Africa/Algiers] Début de getMerchantId
[02-Aug-2025 19:21:22 Africa/Algiers] === MerchantId récupéré ===
[02-Aug-2025 19:21:22 Africa/Algiers] MerchantId: 1
[02-Aug-2025 19:21:22 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 19:21:22 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 19:21:22 Africa/Algiers] merchantId reçu: 1
[02-Aug-2025 19:21:22 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 19:21:22 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 19:21:22 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 19:21:22 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 19:21:22 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 19:21:22 Africa/Algiers] MerchantId: 1
[02-Aug-2025 19:21:22 Africa/Algiers] Paramètres de la requête: [1]
[02-Aug-2025 19:21:22 Africa/Algiers] Nombre de pages trouvées: 1
[02-Aug-2025 19:21:22 Africa/Algiers] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 19:21:22 Africa/Algiers] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 19:21:29 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 19:21:29 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 19:21:29 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 19:21:29 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 19:21:29 Africa/Algiers] Début de getMerchantId
[02-Aug-2025 19:21:29 Africa/Algiers] === MerchantId récupéré ===
[02-Aug-2025 19:21:29 Africa/Algiers] MerchantId: 1
[02-Aug-2025 19:21:29 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 19:37:07 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 19:37:07 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 19:37:07 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 19:37:07 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 19:37:07 Africa/Algiers] Début de getMerchantId
[02-Aug-2025 19:37:07 Africa/Algiers] === MerchantId récupéré ===
[02-Aug-2025 19:37:07 Africa/Algiers] MerchantId: 1
[02-Aug-2025 19:37:07 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 19:37:07 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 19:37:07 Africa/Algiers] merchantId reçu: 1
[02-Aug-2025 19:37:07 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 19:37:07 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 19:37:07 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 19:37:07 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 19:37:07 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 19:37:07 Africa/Algiers] MerchantId: 1
[02-Aug-2025 19:37:07 Africa/Algiers] Paramètres de la requête: [1]
[02-Aug-2025 19:37:07 Africa/Algiers] Nombre de pages trouvées: 1
[02-Aug-2025 19:37:07 Africa/Algiers] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 19:37:07 Africa/Algiers] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 19:37:07 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 19:37:08 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 19:37:08 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 19:37:08 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 19:37:08 Africa/Algiers] Début de getMerchantId
[02-Aug-2025 19:37:08 Africa/Algiers] === MerchantId récupéré ===
[02-Aug-2025 19:37:08 Africa/Algiers] MerchantId: 1
[02-Aug-2025 19:37:08 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 19:37:08 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 19:37:08 Africa/Algiers] merchantId reçu: 1
[02-Aug-2025 19:37:08 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 19:37:08 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 19:37:08 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 19:37:08 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 19:37:08 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 19:37:08 Africa/Algiers] MerchantId: 1
[02-Aug-2025 19:37:08 Africa/Algiers] Paramètres de la requête: [1]
[02-Aug-2025 19:37:08 Africa/Algiers] Nombre de pages trouvées: 1
[02-Aug-2025 19:37:08 Africa/Algiers] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 19:37:08 Africa/Algiers] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 19:37:14 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 19:37:14 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 19:37:14 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 19:37:14 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 19:37:14 Africa/Algiers] Début de getMerchantId
[02-Aug-2025 19:37:14 Africa/Algiers] === MerchantId récupéré ===
[02-Aug-2025 19:37:14 Africa/Algiers] MerchantId: 1
[02-Aug-2025 19:37:14 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 19:37:14 Africa/Algiers] PHP Warning:  Undefined variable $userId in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 70
[02-Aug-2025 19:37:17 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 19:37:17 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 19:37:17 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 19:37:17 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 19:37:17 Africa/Algiers] Début de getMerchantId
[02-Aug-2025 19:37:17 Africa/Algiers] === MerchantId récupéré ===
[02-Aug-2025 19:37:17 Africa/Algiers] MerchantId: 1
[02-Aug-2025 19:37:17 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 19:37:17 Africa/Algiers] PHP Warning:  Undefined variable $userId in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 70
[02-Aug-2025 18:37:59 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 18:37:59 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 19:01:12 UTC] Error getting subscription stats: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'status' in field list is ambiguous
[02-Aug-2025 19:01:12 UTC] Error getting subscription stats: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'status' in field list is ambiguous
[02-Aug-2025 19:01:15 UTC] Error getting subscription stats: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'status' in field list is ambiguous
[02-Aug-2025 19:01:15 UTC] Error getting subscription stats: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'status' in field list is ambiguous
[02-Aug-2025 19:01:31 UTC] Error getting subscription stats: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'status' in field list is ambiguous
[02-Aug-2025 19:01:38 UTC] Error getting subscription stats: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'status' in field list is ambiguous
[02-Aug-2025 19:02:13 UTC] Début de getMerchantId
[02-Aug-2025 19:02:13 UTC] === MerchantId récupéré ===
[02-Aug-2025 19:02:13 UTC] MerchantId: 1
[02-Aug-2025 19:02:13 UTC] Plan d'abonnement trouvé: 2
[02-Aug-2025 19:02:13 UTC] === Début getLandingPages ===
[02-Aug-2025 19:02:13 UTC] merchantId reçu: 1
[02-Aug-2025 19:02:13 UTC] Type de connexion DB: PDO
[02-Aug-2025 19:02:13 UTC] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 19:02:13 UTC] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 19:02:13 UTC] === Exécution de la requête SQL ===
[02-Aug-2025 19:02:13 UTC] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 19:02:13 UTC] MerchantId: 1
[02-Aug-2025 19:02:13 UTC] Paramètres de la requête: [1]
[02-Aug-2025 19:02:13 UTC] Nombre de pages trouvées: 1
[02-Aug-2025 19:02:13 UTC] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 19:02:13 UTC] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 19:02:13 UTC] Début de getMerchantId
[02-Aug-2025 19:02:13 UTC] === MerchantId récupéré ===
[02-Aug-2025 19:02:13 UTC] MerchantId: 1
[02-Aug-2025 19:02:13 UTC] Plan d'abonnement trouvé: 2
[02-Aug-2025 19:02:13 UTC] === Début getLandingPages ===
[02-Aug-2025 19:02:13 UTC] merchantId reçu: 1
[02-Aug-2025 19:02:13 UTC] Type de connexion DB: PDO
[02-Aug-2025 19:02:13 UTC] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 19:02:13 UTC] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 19:02:13 UTC] === Exécution de la requête SQL ===
[02-Aug-2025 19:02:13 UTC] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 19:02:13 UTC] MerchantId: 1
[02-Aug-2025 19:02:13 UTC] Paramètres de la requête: [1]
[02-Aug-2025 19:02:13 UTC] Nombre de pages trouvées: 1
[02-Aug-2025 19:02:13 UTC] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 19:02:13 UTC] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 19:02:16 UTC] Error getting subscription stats: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'status' in field list is ambiguous
[02-Aug-2025 19:02:16 UTC] Error getting subscription stats: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'status' in field list is ambiguous
[02-Aug-2025 19:02:30 UTC] Début de getMerchantId
[02-Aug-2025 19:02:30 UTC] === MerchantId récupéré ===
[02-Aug-2025 19:02:30 UTC] MerchantId: 1
[02-Aug-2025 19:02:30 UTC] Plan d'abonnement trouvé: 2
