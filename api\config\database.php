<?php

/**
 * Configuration de la base de données
 * Database configuration for the seller dashboard API
 */

class Database
{
    private $host;
    private $port;
    private $db_name;
    private $username;
    private $password;
    private $charset = 'utf8mb4';
    public $conn;

    public function __construct()
    {
        // Load environment variables from .env file
        $this->loadEnvVariables();

        // Set database configuration
        $this->host = 'localhost';
        $this->port = '3307';
        $this->db_name = 'landingpage_new';
        $this->username = 'root';
        $this->password = '';
    }

    /**
     * Load environment variables from .env file
     */
    private function loadEnvVariables()
    {
        $envFile = __DIR__ . '/../../.env';
        if (file_exists($envFile)) {
            $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            foreach ($lines as $line) {
                if (strpos(trim($line), '#') === 0) {
                    continue; // Skip comments
                }
                list($name, $value) = explode('=', $line, 2);
                $name = trim($name);
                $value = trim($value);
                if (!array_key_exists($name, $_ENV)) {
                    $_ENV[$name] = $value;
                }
            }
        }
    }

    /**
     * Établir la connexion à la base de données
     * @return PDO|null
     */
    public function getConnection()
    {
        $this->conn = null;
        error_log('=== Tentative de connexion à la base de données ===');
        error_log("Host: {$this->host}, Port: {$this->port}, Database: {$this->db_name}");

        try {
            $dsn = "mysql:host={$this->host};port={$this->port};dbname={$this->db_name};charset={$this->charset}";
            error_log("DSN: {$dsn}");

            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
            ];

            $this->conn = new PDO($dsn, $this->username, $this->password, $options);
            error_log('Connexion à la base de données établie avec succès');
        } catch (PDOException $exception) {
            error_log("Erreur de connexion à la base de données: " . $exception->getMessage());
            error_log("Stack trace: " . $exception->getTraceAsString());
            throw new Exception("Échec de la connexion à la base de données");
        }

        return $this->conn;
    }

    /**
     * Fermer la connexion
     */
    public function closeConnection()
    {
        $this->conn = null;
    }

    /**
     * Commencer une transaction
     */
    public function beginTransaction()
    {
        return $this->conn->beginTransaction();
    }

    /**
     * Valider une transaction
     */
    public function commit()
    {
        return $this->conn->commit();
    }

    /**
     * Annuler une transaction
     */
    public function rollback()
    {
        return $this->conn->rollback();
    }

    /**
     * Obtenir le dernier ID inséré
     */
    public function lastInsertId()
    {
        return $this->conn->lastInsertId();
    }

    /**
     * Préparer une requête
     */
    public function prepare($sql)
    {
        return $this->conn->prepare($sql);
    }

    /**
     * Exécuter une requête
     */
    public function query($sql)
    {
        return $this->conn->query($sql);
    }
}

/**
 * Classe utilitaire pour les réponses API
 */
class ApiResponse
{

    /**
     * Envoyer une réponse de succès
     */
    public static function success($data = null, $message = 'Success', $code = 200)
    {
        http_response_code($code);
        echo json_encode([
            'success' => true,
            'message' => $message,
            'data' => $data,
            'timestamp' => date('Y-m-d H:i:s')
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }

    /**
     * Envoyer une réponse d'erreur
     */
    public static function error($message = 'Error', $code = 400, $details = null)
    {
        http_response_code($code);
        echo json_encode([
            'success' => false,
            'message' => $message,
            'details' => $details,
            'timestamp' => date('Y-m-d H:i:s')
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }

    /**
     * Valider les données requises
     */
    public static function validateRequired($data, $required_fields)
    {
        $missing = [];
        foreach ($required_fields as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                $missing[] = $field;
            }
        }

        if (!empty($missing)) {
            self::error('Champs requis manquants: ' . implode(', ', $missing), 400);
        }
    }

    /**
     * Nettoyer les données d'entrée
     */
    public static function sanitizeInput($data)
    {
        if (is_array($data)) {
            return array_map([self::class, 'sanitizeInput'], $data);
        }
        return htmlspecialchars(strip_tags(trim($data)), ENT_QUOTES, 'UTF-8');
    }
}

/**
 * Classe pour l'authentification et l'autorisation
 */
class Auth
{
    private $db;

    public function __construct($database)
    {
        $this->db = $database;
    }

    /**
     * Vérifier le token Firebase et obtenir l'utilisateur
     */
    public function verifyFirebaseToken($token)
    {
        // Ici vous devriez implémenter la vérification du token Firebase
        // Pour le moment, on simule avec un token simple
        if (empty($token)) {
            ApiResponse::error('Token d\'authentification requis', 401);
        }

        // Simulation - remplacer par la vraie vérification Firebase
        if ($token === 'demo_token') {
            return 'firebase_uid_1';
        }

        ApiResponse::error('Token invalide', 401);
    }

    /**
     * Obtenir le magasin de l'utilisateur
     */
    public function getUserStore($user_id)
    {
        try {
            // D'abord, vérifier si l'utilisateur a un rôle admin
            $admin_stmt = $this->db->prepare("
                SELECT 'admin' as role, NULL as id, NULL as store_name
                FROM user_roles 
                WHERE user_id = ? AND role = 'admin' AND is_active = 1
            ");
            $admin_stmt->execute([$user_id]);
            $admin_role = $admin_stmt->fetch();
            
            if ($admin_role) {
                return $admin_role;
            }
            
            // Sinon, chercher le store de l'utilisateur
            $stmt = $this->db->prepare("
                SELECT s.*, ur.role
                FROM stores s
                JOIN user_roles ur ON s.id = ur.store_id
                WHERE ur.user_id = ? AND ur.is_active = 1
            ");
            $stmt->execute([$user_id]);
            $store = $stmt->fetch();

            if (!$store) {
                ApiResponse::error('Magasin non trouvé pour cet utilisateur', 404);
            }

            return $store;
        } catch (Exception $e) {
            error_log("Error getting user store: " . $e->getMessage());
            ApiResponse::error('Erreur lors de la récupération du magasin', 500);
        }
    }

    /**
     * Vérifier les permissions
     */
    public function checkPermission($user_role, $required_permission)
    {
        $permissions = [
            'admin' => ['all'],
            'seller' => ['products', 'orders', 'payments', 'ai', 'analytics', 'store_settings'],
            'agent' => ['orders', 'products'],
            'customer' => []
        ];

        if (in_array('all', $permissions[$user_role]) || in_array($required_permission, $permissions[$user_role])) {
            return true;
        }

        ApiResponse::error('Permission insuffisante', 403);
    }
}

/**
 * Classe utilitaire pour le chiffrement
 */
class Encryption
{
    private static $key = 'your-secret-encryption-key-32-chars'; // Changez ceci en production
    private static $cipher = 'AES-256-CBC';

    /**
     * Chiffrer une chaîne
     */
    public static function encrypt($data)
    {
        $iv = openssl_random_pseudo_bytes(openssl_cipher_iv_length(self::$cipher));
        $encrypted = openssl_encrypt($data, self::$cipher, self::$key, 0, $iv);
        return base64_encode($encrypted . '::' . $iv);
    }

    /**
     * Déchiffrer une chaîne
     */
    public static function decrypt($data)
    {
        list($encrypted_data, $iv) = explode('::', base64_decode($data), 2);
        return openssl_decrypt($encrypted_data, self::$cipher, self::$key, 0, $iv);
    }
}

// Configuration des en-têtes CORS
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// Gérer les requêtes OPTIONS (preflight)
if (isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Définir le fuseau horaire
date_default_timezone_set('Africa/Algiers');

// Configuration des erreurs
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../../logs/api_errors.log');
