<?php
require_once 'php/config/database.php';

try {
    echo "🔍 Diagnostic et correction du merchant_id pour les catégories...\n\n";
    
    // 1. Vérifier le store_id 3 et son merchant_id
    echo "📊 VÉRIFICATION DU STORE 3:\n";
    $storeQuery = "SELECT id, merchant_id, store_name FROM stores WHERE id = 3";
    $storeStmt = $pdo->prepare($storeQuery);
    $storeStmt->execute();
    $store = $storeStmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$store) {
        echo "❌ Store ID 3 non trouvé\n";
        exit;
    }
    
    echo "  - Store ID: {$store['id']}\n";
    echo "  - Merchant ID: {$store['merchant_id']}\n";
    echo "  - Nom: {$store['store_name']}\n";
    
    $correctMerchantId = $store['merchant_id'];
    
    // 2. Vérifier les catégories existantes
    echo "\n📂 CATÉGORIES EXISTANTES:\n";
    $allCatQuery = "SELECT id, user_id, name, name_ar, slug FROM categories";
    $allCategories = $pdo->query($allCatQuery)->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($allCategories)) {
        echo "  ❌ Aucune catégorie trouvée\n";
    } else {
        foreach ($allCategories as $cat) {
            echo "  - ID: {$cat['id']}, User: {$cat['user_id']}, Nom: {$cat['name']}, Slug: {$cat['slug']}\n";
        }
    }
    
    // 3. Vérifier les catégories pour le bon merchant
    echo "\n📂 CATÉGORIES POUR MERCHANT {$correctMerchantId}:\n";
    $correctCatQuery = "SELECT id, user_id, name, name_ar, slug FROM categories WHERE user_id = ?";
    $correctCatStmt = $pdo->prepare($correctCatQuery);
    $correctCatStmt->execute([$correctMerchantId]);
    $correctCategories = $correctCatStmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($correctCategories)) {
        echo "  ❌ Aucune catégorie pour merchant {$correctMerchantId}\n";
        
        // 4. Corriger en mettant à jour les catégories existantes
        if (!empty($allCategories)) {
            echo "\n🔧 CORRECTION: Mise à jour des catégories existantes...\n";
            
            $updateQuery = "UPDATE categories SET user_id = ? WHERE user_id != ?";
            $updateStmt = $pdo->prepare($updateQuery);
            $result = $updateStmt->execute([$correctMerchantId, $correctMerchantId]);
            
            if ($result) {
                $affectedRows = $updateStmt->rowCount();
                echo "  ✅ {$affectedRows} catégories mises à jour vers merchant {$correctMerchantId}\n";
            } else {
                echo "  ❌ Erreur lors de la mise à jour\n";
            }
        } else {
            // 5. Créer de nouvelles catégories si aucune n'existe
            echo "\n🔧 CRÉATION: Nouvelles catégories pour merchant {$correctMerchantId}...\n";
            
            $categories = [
                ['name' => 'Électronique', 'name_ar' => 'إلكترونيات', 'slug' => 'electronique', 'color' => '#007bff', 'icon' => 'fas fa-microchip'],
                ['name' => 'Informatique', 'name_ar' => 'معلوماتية', 'slug' => 'informatique', 'color' => '#28a745', 'icon' => 'fas fa-laptop'],
                ['name' => 'Smartphones', 'name_ar' => 'هواتف ذكية', 'slug' => 'smartphones', 'color' => '#17a2b8', 'icon' => 'fas fa-mobile-alt'],
                ['name' => 'Audio/Vidéo', 'name_ar' => 'صوت/فيديو', 'slug' => 'audio-video', 'color' => '#ffc107', 'icon' => 'fas fa-headphones'],
                ['name' => 'Gaming', 'name_ar' => 'ألعاب', 'slug' => 'gaming', 'color' => '#dc3545', 'icon' => 'fas fa-gamepad']
            ];
            
            foreach ($categories as $index => $cat) {
                $insertQuery = "
                    INSERT INTO categories (
                        user_id, name, name_ar, name_fr, name_en, slug, description,
                        description_ar, description_fr, description_en, color, icon,
                        category_type, sort_order, status, created_at, updated_at
                    ) VALUES (
                        ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'category', ?, 'active', NOW(), NOW()
                    )
                ";
                
                $description = "Catégorie " . $cat['name'];
                $description_ar = "فئة " . $cat['name_ar'];
                
                $insertStmt = $pdo->prepare($insertQuery);
                $result = $insertStmt->execute([
                    $correctMerchantId,
                    $cat['name'],
                    $cat['name_ar'],
                    $cat['name'],
                    $cat['name'],
                    $cat['slug'],
                    $description,
                    $description_ar,
                    $description,
                    $description,
                    $cat['color'],
                    $cat['icon'],
                    $index + 1
                ]);
                
                if ($result) {
                    $categoryId = $pdo->lastInsertId();
                    echo "  ✅ Catégorie '{$cat['name']}' créée (ID: {$categoryId})\n";
                } else {
                    echo "  ❌ Erreur lors de la création de '{$cat['name']}'\n";
                }
            }
        }
    } else {
        echo "  ✅ {count($correctCategories)} catégories trouvées pour merchant {$correctMerchantId}\n";
        foreach ($correctCategories as $cat) {
            echo "    * {$cat['name']} ({$cat['name_ar']}) - Slug: {$cat['slug']}\n";
        }
    }
    
    // 6. Vérification finale
    echo "\n📊 VÉRIFICATION FINALE:\n";
    $finalQuery = "SELECT COUNT(*) as count FROM categories WHERE user_id = ?";
    $finalStmt = $pdo->prepare($finalQuery);
    $finalStmt->execute([$correctMerchantId]);
    $finalCount = $finalStmt->fetch()['count'];
    
    echo "  - Catégories pour merchant {$correctMerchantId}: {$finalCount}\n";
    
    // 7. Test de l'API
    echo "\n🔗 TEST DE L'API:\n";
    echo "  URL: /api/categories.php?store_id=3\n";
    
    echo "\n✅ Correction terminée!\n";
    
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
    echo "📍 Fichier: " . $e->getFile() . " ligne " . $e->getLine() . "\n";
}
?>
