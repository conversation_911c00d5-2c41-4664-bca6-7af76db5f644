<?php

/**
 * Payments API Endpoint
 * Handles CRUD operations for payments and payment methods
 */

require_once 'config/database.php';

// Set headers
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    $database = new Database();
    $db = $database->getConnection();
    $auth = new Auth($db);

    // Get request method and path
    $method = $_SERVER['REQUEST_METHOD'];
    $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
    $pathParts = explode('/', trim($path, '/'));

    // Extract action from path (e.g., /api/payments/methods)
    $action = isset($pathParts[2]) ? $pathParts[2] : (isset($_GET['action']) ? $_GET['action'] : '');
    $id = isset($pathParts[3]) ? intval($pathParts[3]) : null;

    // Authenticate user
    $headers = function_exists('getallheaders') ? getallheaders() : [];

    // Fallback for CLI and some servers
    if (empty($headers) && isset($_SERVER['HTTP_AUTHORIZATION'])) {
        $headers['Authorization'] = $_SERVER['HTTP_AUTHORIZATION'];
    }

    $token = isset($headers['Authorization']) ? str_replace('Bearer ', '', $headers['Authorization']) : null;
    $user_id = $auth->verifyFirebaseToken($token);
    $store = $auth->getUserStore($user_id);

    if (!$store) {
        ApiResponse::error('Store access required', 403);
    }

    switch ($method) {
        case 'GET':
            if ($action === 'methods') {
                getPaymentMethods($db, $store['id']);
            } elseif ($action === 'get_gateway_config') {
                getGatewayConfig($db, $store['id'], $_GET['gateway']);
            } else {
                getPayments($db, $store['id'], $_GET);
            }
            break;

        case 'POST':
            $input = json_decode(file_get_contents('php://input'), true);
            if ($action === 'methods') {
                createPaymentMethod($db, $store['id'], $input);
            } elseif ($action === 'save_gateway_config') {
                saveGatewayConfig($db, $store['id'], $input);
            } else {
                createPayment($db, $store['id'], $input);
            }
            break;

        case 'PUT':
            $input = json_decode(file_get_contents('php://input'), true);
            if ($action === 'methods') {
                updatePaymentMethod($db, $store['id'], $id, $input);
            } else {
                updatePayment($db, $store['id'], $id, $input);
            }
            break;

        case 'DELETE':
            if ($action === 'methods') {
                deletePaymentMethod($db, $store['id'], $id);
            } else {
                deletePayment($db, $store['id'], $id);
            }
            break;

        default:
            ApiResponse::error('Method not allowed', 405);
    }
} catch (Exception $e) {
    error_log('Payments API Error: ' . $e->getMessage());
    ApiResponse::error('Internal server error', 500);
}

/**
 * Get payment methods for a store
 */
function getPaymentMethods($db, $store_id)
{
    try {
        $query = "SELECT * FROM payment_methods WHERE store_id = ? ORDER BY display_order ASC, method_name ASC";
        $stmt = $db->prepare($query);
        $stmt->execute([$store_id]);

        $methods = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Decode JSON configuration for each method
        foreach ($methods as &$method) {
            $method['configuration'] = json_decode($method['configuration'], true);
        }

        ApiResponse::success($methods);
    } catch (Exception $e) {
        error_log('Get payment methods error: ' . $e->getMessage());
        ApiResponse::error('Failed to retrieve payment methods');
    }
}

/**
 * Get payments with filtering and pagination
 */
function getPayments($db, $store_id, $params)
{
    try {
        $page = isset($params['page']) ? max(1, intval($params['page'])) : 1;
        $limit = isset($params['limit']) ? min(100, max(1, intval($params['limit']))) : 20;
        $offset = ($page - 1) * $limit;

        $where_conditions = ['p.store_id = ?'];
        $where_params = [$store_id];

        // Add filters
        if (!empty($params['status'])) {
            $where_conditions[] = 'p.status = ?';
            $where_params[] = $params['status'];
        }

        if (!empty($params['method'])) {
            $where_conditions[] = 'p.payment_method = ?';
            $where_params[] = $params['method'];
        }

        if (!empty($params['date_from'])) {
            $where_conditions[] = 'DATE(p.created_at) >= ?';
            $where_params[] = $params['date_from'];
        }

        if (!empty($params['date_to'])) {
            $where_conditions[] = 'DATE(p.created_at) <= ?';
            $where_params[] = $params['date_to'];
        }

        $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);

        // Get total count
        $count_query = "SELECT COUNT(*) as total FROM payments p $where_clause";
        $count_stmt = $db->prepare($count_query);
        $count_stmt->execute($where_params);
        $total = $count_stmt->fetch(PDO::FETCH_ASSOC)['total'];

        // Get payments with order details
        $query = "
            SELECT p.*, o.order_number, o.customer_name, o.customer_email, o.total_amount as order_total
            FROM payments p
            LEFT JOIN orders o ON p.order_id = o.id
            $where_clause
            ORDER BY p.created_at DESC
            LIMIT ? OFFSET ?
        ";

        $stmt = $db->prepare($query);
        $stmt->execute(array_merge($where_params, [$limit, $offset]));
        $payments = $stmt->fetchAll(PDO::FETCH_ASSOC);

        ApiResponse::success([
            'payments' => $payments,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => $total,
                'pages' => ceil($total / $limit)
            ]
        ]);
    } catch (Exception $e) {
        error_log('Get payments error: ' . $e->getMessage());
        ApiResponse::error('Failed to retrieve payments');
    }
}

/**
 * Create a new payment method
 */
function createPaymentMethod($db, $store_id, $data)
{
    try {
        // Validate required fields
        $required = ['method_type', 'method_name', 'configuration'];
        foreach ($required as $field) {
            if (empty($data[$field])) {
                ApiResponse::error("Field '$field' is required", 400);
            }
        }

        // Validate method type
        $valid_types = ['baridimob', 'ccp', 'bank_transfer', 'cod', 'paypal', 'stripe'];
        if (!in_array($data['method_type'], $valid_types)) {
            ApiResponse::error('Invalid payment method type', 400);
        }

        $query = "
            INSERT INTO payment_methods (store_id, method_type, method_name, configuration, is_active, display_order)
            VALUES (?, ?, ?, ?, ?, ?)
        ";

        $stmt = $db->prepare($query);
        $stmt->execute([
            $store_id,
            $data['method_type'],
            $data['method_name'],
            json_encode($data['configuration']),
            isset($data['is_active']) ? $data['is_active'] : 1,
            isset($data['display_order']) ? $data['display_order'] : 0
        ]);

        $method_id = $db->lastInsertId();

        // Get the created method
        $get_query = "SELECT * FROM payment_methods WHERE id = ?";
        $get_stmt = $db->prepare($get_query);
        $get_stmt->execute([$method_id]);
        $method = $get_stmt->fetch(PDO::FETCH_ASSOC);
        $method['configuration'] = json_decode($method['configuration'], true);

        ApiResponse::success($method, 'Payment method created successfully', 201);
    } catch (Exception $e) {
        error_log('Create payment method error: ' . $e->getMessage());
        ApiResponse::error('Failed to create payment method');
    }
}

/**
 * Update a payment method
 */
function updatePaymentMethod($db, $store_id, $method_id, $data)
{
    try {
        if (!$method_id) {
            ApiResponse::error('Payment method ID is required', 400);
        }

        // Check if method exists and belongs to store
        $check_query = "SELECT id FROM payment_methods WHERE id = ? AND store_id = ?";
        $check_stmt = $db->prepare($check_query);
        $check_stmt->execute([$method_id, $store_id]);

        if (!$check_stmt->fetch()) {
            ApiResponse::error('Payment method not found', 404);
        }

        $update_fields = [];
        $update_params = [];

        if (isset($data['method_name'])) {
            $update_fields[] = 'method_name = ?';
            $update_params[] = $data['method_name'];
        }

        if (isset($data['configuration'])) {
            $update_fields[] = 'configuration = ?';
            $update_params[] = json_encode($data['configuration']);
        }

        if (isset($data['is_active'])) {
            $update_fields[] = 'is_active = ?';
            $update_params[] = $data['is_active'];
        }

        if (isset($data['display_order'])) {
            $update_fields[] = 'display_order = ?';
            $update_params[] = $data['display_order'];
        }

        if (empty($update_fields)) {
            ApiResponse::error('No fields to update', 400);
        }

        $update_fields[] = 'updated_at = NOW()';
        $update_params[] = $method_id;
        $update_params[] = $store_id;

        $query = "UPDATE payment_methods SET " . implode(', ', $update_fields) . " WHERE id = ? AND store_id = ?";
        $stmt = $db->prepare($query);
        $stmt->execute($update_params);

        // Get updated method
        $get_query = "SELECT * FROM payment_methods WHERE id = ?";
        $get_stmt = $db->prepare($get_query);
        $get_stmt->execute([$method_id]);
        $method = $get_stmt->fetch(PDO::FETCH_ASSOC);
        $method['configuration'] = json_decode($method['configuration'], true);

        ApiResponse::success($method, 'Payment method updated successfully');
    } catch (Exception $e) {
        error_log('Update payment method error: ' . $e->getMessage());
        ApiResponse::error('Failed to update payment method');
    }
}

/**
 * Delete a payment method
 */
function deletePaymentMethod($db, $store_id, $method_id)
{
    try {
        if (!$method_id) {
            ApiResponse::error('Payment method ID is required', 400);
        }

        // Check if method exists and belongs to store
        $check_query = "SELECT id FROM payment_methods WHERE id = ? AND store_id = ?";
        $check_stmt = $db->prepare($check_query);
        $check_stmt->execute([$method_id, $store_id]);

        if (!$check_stmt->fetch()) {
            ApiResponse::error('Payment method not found', 404);
        }

        // Check if method is being used in any orders
        $usage_query = "SELECT COUNT(*) as count FROM orders WHERE payment_method = (SELECT method_type FROM payment_methods WHERE id = ?)";
        $usage_stmt = $db->prepare($usage_query);
        $usage_stmt->execute([$method_id]);
        $usage_count = $usage_stmt->fetch(PDO::FETCH_ASSOC)['count'];

        if ($usage_count > 0) {
            ApiResponse::error('Cannot delete payment method that is being used in orders', 400);
        }

        $query = "DELETE FROM payment_methods WHERE id = ? AND store_id = ?";
        $stmt = $db->prepare($query);
        $stmt->execute([$method_id, $store_id]);

        ApiResponse::success(null, 'Payment method deleted successfully');
    } catch (Exception $e) {
        error_log('Delete payment method error: ' . $e->getMessage());
        ApiResponse::error('Failed to delete payment method');
    }
}

/**
 * Create a new payment record
 */
function createPayment($db, $store_id, $data)
{
    try {
        // Validate required fields
        $required = ['order_id', 'payment_method', 'amount'];
        foreach ($required as $field) {
            if (empty($data[$field])) {
                ApiResponse::error("Field '$field' is required", 400);
            }
        }

        // Verify order belongs to store
        $order_query = "SELECT id, total_amount FROM orders WHERE id = ? AND store_id = ?";
        $order_stmt = $db->prepare($order_query);
        $order_stmt->execute([$data['order_id'], $store_id]);
        $order = $order_stmt->fetch(PDO::FETCH_ASSOC);

        if (!$order) {
            ApiResponse::error('Order not found', 404);
        }

        $query = "
            INSERT INTO payments (order_id, store_id, payment_method, amount, status, transaction_id, payment_proof_url, notes)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ";

        $stmt = $db->prepare($query);
        $stmt->execute([
            $data['order_id'],
            $store_id,
            $data['payment_method'],
            $data['amount'],
            isset($data['status']) ? $data['status'] : 'pending',
            isset($data['transaction_id']) ? $data['transaction_id'] : null,
            isset($data['payment_proof_url']) ? $data['payment_proof_url'] : null,
            isset($data['notes']) ? $data['notes'] : null
        ]);

        $payment_id = $db->lastInsertId();

        // Get the created payment
        $get_query = "SELECT * FROM payments WHERE id = ?";
        $get_stmt = $db->prepare($get_query);
        $get_stmt->execute([$payment_id]);
        $payment = $get_stmt->fetch(PDO::FETCH_ASSOC);

        ApiResponse::success($payment, 'Payment created successfully', 201);
    } catch (Exception $e) {
        error_log('Create payment error: ' . $e->getMessage());
        ApiResponse::error('Failed to create payment');
    }
}

/**
 * Update a payment
 */
function updatePayment($db, $store_id, $payment_id, $data)
{
    try {
        if (!$payment_id) {
            ApiResponse::error('Payment ID is required', 400);
        }

        // Check if payment exists and belongs to store
        $check_query = "SELECT id FROM payments WHERE id = ? AND store_id = ?";
        $check_stmt = $db->prepare($check_query);
        $check_stmt->execute([$payment_id, $store_id]);

        if (!$check_stmt->fetch()) {
            ApiResponse::error('Payment not found', 404);
        }

        $update_fields = [];
        $update_params = [];

        $allowed_fields = ['status', 'transaction_id', 'payment_proof_url', 'notes', 'amount'];
        foreach ($allowed_fields as $field) {
            if (isset($data[$field])) {
                $update_fields[] = "$field = ?";
                $update_params[] = $data[$field];
            }
        }

        if (empty($update_fields)) {
            ApiResponse::error('No fields to update', 400);
        }

        $update_fields[] = 'updated_at = NOW()';
        $update_params[] = $payment_id;
        $update_params[] = $store_id;

        $query = "UPDATE payments SET " . implode(', ', $update_fields) . " WHERE id = ? AND store_id = ?";
        $stmt = $db->prepare($query);
        $stmt->execute($update_params);

        // Get updated payment
        $get_query = "SELECT * FROM payments WHERE id = ?";
        $get_stmt = $db->prepare($get_query);
        $get_stmt->execute([$payment_id]);
        $payment = $get_stmt->fetch(PDO::FETCH_ASSOC);

        ApiResponse::success($payment, 'Payment updated successfully');
    } catch (Exception $e) {
        error_log('Update payment error: ' . $e->getMessage());
        ApiResponse::error('Failed to update payment');
    }
}

/**
 * Delete a payment
 */
function deletePayment($db, $store_id, $payment_id)
{
    try {
        if (!$payment_id) {
            ApiResponse::error('Payment ID is required', 400);
        }

        // Check if payment exists and belongs to store
        $check_query = "SELECT id, status FROM payments WHERE id = ? AND store_id = ?";
        $check_stmt = $db->prepare($check_query);
        $check_stmt->execute([$payment_id, $store_id]);
        $payment = $check_stmt->fetch(PDO::FETCH_ASSOC);

        if (!$payment) {
            ApiResponse::error('Payment not found', 404);
        }

        // Only allow deletion of pending or failed payments
        if (!in_array($payment['status'], ['pending', 'failed', 'cancelled'])) {
            ApiResponse::error('Cannot delete completed or processing payments', 400);
        }

        $query = "DELETE FROM payments WHERE id = ? AND store_id = ?";
        $stmt = $db->prepare($query);
        $stmt->execute([$payment_id, $store_id]);

        ApiResponse::success(null, 'Payment deleted successfully');
    } catch (Exception $e) {
        error_log('Delete payment error: ' . $e->getMessage());
        ApiResponse::error('Failed to delete payment');
    }
}

/**
 * Get gateway configuration
 */
function getGatewayConfig($db, $store_id, $gateway_name)
{
    try {
        if (!$gateway_name) {
            ApiResponse::error('Gateway name is required', 400);
            return;
        }

        $query = "SELECT * FROM payment_gateway_configs WHERE store_id = ? AND gateway_name = ?";
        $stmt = $db->prepare($query);
        $stmt->execute([$store_id, $gateway_name]);
        $config = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$config) {
            // Return default configuration
            $defaultConfig = [
                'gateway_name' => $gateway_name,
                'api_key' => '',
                'secret_key' => '',
                'endpoint' => '',
                'currency' => 'DZD',
                'mode' => 'sandbox',
                'enabled' => false
            ];
            ApiResponse::success($defaultConfig, 'Default gateway configuration');
        } else {
            // Remove sensitive data for security
            unset($config['id'], $config['store_id'], $config['created_at'], $config['updated_at']);
            ApiResponse::success($config, 'Gateway configuration retrieved');
        }
    } catch (Exception $e) {
        error_log('Get gateway config error: ' . $e->getMessage());
        ApiResponse::error('Failed to retrieve gateway configuration');
    }
}

/**
 * Save gateway configuration
 */
function saveGatewayConfig($db, $store_id, $data)
{
    try {
        $required_fields = ['gateway_name', 'api_key', 'endpoint', 'currency', 'mode'];
        foreach ($required_fields as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                ApiResponse::error("Field '$field' is required", 400);
                return;
            }
        }

        // Check if configuration already exists
        $query = "SELECT id FROM payment_gateway_configs WHERE store_id = ? AND gateway_name = ?";
        $stmt = $db->prepare($query);
        $stmt->execute([$store_id, $data['gateway_name']]);
        $existing = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($existing) {
            // Update existing configuration
            $query = "
                UPDATE payment_gateway_configs 
                SET api_key = ?, secret_key = ?, endpoint = ?, currency = ?, mode = ?, enabled = ?, updated_at = NOW()
                WHERE store_id = ? AND gateway_name = ?
            ";
            $stmt = $db->prepare($query);
            $stmt->execute([
                $data['api_key'],
                $data['secret_key'] ?? '',
                $data['endpoint'],
                $data['currency'],
                $data['mode'],
                $data['enabled'] ?? false,
                $store_id,
                $data['gateway_name']
            ]);
        } else {
            // Create new configuration
            $query = "
                INSERT INTO payment_gateway_configs 
                (store_id, gateway_name, api_key, secret_key, endpoint, currency, mode, enabled, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
            ";
            $stmt = $db->prepare($query);
            $stmt->execute([
                $store_id,
                $data['gateway_name'],
                $data['api_key'],
                $data['secret_key'] ?? '',
                $data['endpoint'],
                $data['currency'],
                $data['mode'],
                $data['enabled'] ?? false
            ]);
        }

        ApiResponse::success(null, 'Gateway configuration saved successfully');
    } catch (Exception $e) {
        error_log('Save gateway config error: ' . $e->getMessage());
        ApiResponse::error('Failed to save gateway configuration');
    }
}
