<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard API Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .result { background: #f5f5f5; padding: 10px; margin: 10px 0; }
        button { padding: 10px 15px; margin: 5px; }
        .success { color: green; }
        .error { color: red; }
    </style>
</head>
<body>
    <h1>Dashboard API Test</h1>
    
    <div class="test-section">
        <h3>Test 1: Dashboard Stats</h3>
        <button onclick="testDashboardStats()">Get Dashboard Stats</button>
        <div id="statsResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>Test 2: System Overview</h3>
        <button onclick="testSystemOverview()">Get System Overview</button>
        <div id="overviewResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>Test 3: Analytics</h3>
        <button onclick="testAnalytics()">Get Analytics</button>
        <div id="analyticsResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>Test 4: Recent Activity</h3>
        <button onclick="testRecentActivity()">Get Recent Activity</button>
        <div id="activityResult" class="result"></div>
    </div>

    <script>
        async function testDashboardStats() {
            try {
                const response = await fetch('/firebase-dashboard-api.php/stats', {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer demo_token',
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                document.getElementById('statsResult').innerHTML = 
                    `<div class="success"><strong>Status:</strong> ${response.status}</div>
                     <strong>Response:</strong><br>
                     <pre>${JSON.stringify(data, null, 2)}</pre>`;
            } catch (error) {
                document.getElementById('statsResult').innerHTML = 
                    `<div class="error"><strong>Error:</strong> ${error.message}</div>`;
            }
        }
        
        async function testSystemOverview() {
            try {
                const response = await fetch('/firebase-dashboard-api.php/overview', {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer demo_token',
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                document.getElementById('overviewResult').innerHTML = 
                    `<div class="success"><strong>Status:</strong> ${response.status}</div>
                     <strong>Response:</strong><br>
                     <pre>${JSON.stringify(data, null, 2)}</pre>`;
            } catch (error) {
                document.getElementById('overviewResult').innerHTML = 
                    `<div class="error"><strong>Error:</strong> ${error.message}</div>`;
            }
        }
        
        async function testAnalytics() {
            try {
                const response = await fetch('/firebase-dashboard-api.php/analytics', {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer demo_token',
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                document.getElementById('analyticsResult').innerHTML = 
                    `<div class="success"><strong>Status:</strong> ${response.status}</div>
                     <strong>Response:</strong><br>
                     <pre>${JSON.stringify(data, null, 2)}</pre>`;
            } catch (error) {
                document.getElementById('analyticsResult').innerHTML = 
                    `<div class="error"><strong>Error:</strong> ${error.message}</div>`;
            }
        }
        
        async function testRecentActivity() {
            try {
                const response = await fetch('/firebase-dashboard-api.php/recent-activity', {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer demo_token',
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                document.getElementById('activityResult').innerHTML = 
                    `<div class="success"><strong>Status:</strong> ${response.status}</div>
                     <strong>Response:</strong><br>
                     <pre>${JSON.stringify(data, null, 2)}</pre>`;
            } catch (error) {
                document.getElementById('activityResult').innerHTML = 
                    `<div class="error"><strong>Error:</strong> ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
