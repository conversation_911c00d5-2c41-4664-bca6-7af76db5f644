/**
 * Script principal pour exécuter tous les tests Puppeteer
 * Orchestrateur des tests de la plateforme de landing pages
 */

const chalk = require('chalk');
const fs = require('fs-extra');
const path = require('path');

// Importer les classes de test
const LoginTest = require('./tests/login-test');
const RegisterTest = require('./tests/register-test');
const HomepageTest = require('./tests/homepage-test');

class TestRunner {
    constructor(options = {}) {
        this.options = {
            headless: options.headless !== false,
            slowMo: options.slowMo || 100,
            baseUrl: options.baseUrl || 'http://localhost:8000',
            outputDir: options.outputDir || './test-results',
            generateReport: options.generateReport !== false,
            ...options
        };
        
        this.results = {
            startTime: new Date(),
            tests: [],
            summary: {
                total: 0,
                passed: 0,
                failed: 0,
                skipped: 0
            }
        };
    }
    
    /**
     * Exécuter tous les tests
     */
    async runAllTests() {
        console.log(chalk.blue.bold('🚀 === DÉMARRAGE DES TESTS AUTOMATISÉS ==='));
        console.log(chalk.gray(`Base URL: ${this.options.baseUrl}`));
        console.log(chalk.gray(`Mode: ${this.options.headless ? 'Headless' : 'Visible'}`));
        console.log(chalk.gray(`Vitesse: ${this.options.slowMo}ms\n`));
        
        // Créer le dossier de résultats
        await fs.ensureDir(this.options.outputDir);
        
        try {
            // Exécuter les tests dans l'ordre
            await this.runTest('Homepage', HomepageTest);
            await this.runTest('Login', LoginTest);
            await this.runTest('Register', RegisterTest);
            
            // Générer le rapport final
            if (this.options.generateReport) {
                await this.generateReport();
            }
            
            this.printSummary();
            
        } catch (error) {
            console.error(chalk.red.bold('❌ Erreur critique lors des tests:'), error.message);
            process.exit(1);
        }
    }
    
    /**
     * Exécuter un test spécifique
     */
    async runTest(testName, TestClass) {
        console.log(chalk.blue.bold(`\n📋 === TEST ${testName.toUpperCase()} ===`));
        
        const testResult = {
            name: testName,
            startTime: new Date(),
            endTime: null,
            duration: 0,
            status: 'running',
            error: null,
            screenshots: []
        };
        
        try {
            const testInstance = new TestClass({
                ...this.options,
                screenshotPath: path.join(this.options.outputDir, 'screenshots', testName.toLowerCase())
            });
            
            await testInstance.runAllTests();
            
            testResult.status = 'passed';
            testResult.endTime = new Date();
            testResult.duration = testResult.endTime - testResult.startTime;
            
            this.results.summary.passed++;
            console.log(chalk.green.bold(`✅ Test ${testName} réussi (${testResult.duration}ms)`));
            
        } catch (error) {
            testResult.status = 'failed';
            testResult.error = error.message;
            testResult.endTime = new Date();
            testResult.duration = testResult.endTime - testResult.startTime;
            
            this.results.summary.failed++;
            console.error(chalk.red.bold(`❌ Test ${testName} échoué:`, error.message));
        }
        
        this.results.tests.push(testResult);
        this.results.summary.total++;
    }
    
    /**
     * Exécuter un test spécifique par nom
     */
    async runSpecificTest(testName) {
        const tests = {
            'homepage': HomepageTest,
            'login': LoginTest,
            'register': RegisterTest
        };
        
        const TestClass = tests[testName.toLowerCase()];
        if (!TestClass) {
            throw new Error(`Test "${testName}" non trouvé. Tests disponibles: ${Object.keys(tests).join(', ')}`);
        }
        
        console.log(chalk.blue.bold(`🎯 Exécution du test spécifique: ${testName}`));
        await this.runTest(testName, TestClass);
        
        if (this.options.generateReport) {
            await this.generateReport();
        }
        
        this.printSummary();
    }
    
    /**
     * Générer un rapport HTML des résultats
     */
    async generateReport() {
        console.log(chalk.cyan('📊 Génération du rapport...'));
        
        const reportData = {
            ...this.results,
            endTime: new Date(),
            totalDuration: new Date() - this.results.startTime,
            successRate: Math.round((this.results.summary.passed / this.results.summary.total) * 100)
        };
        
        const htmlReport = this.generateHTMLReport(reportData);
        const reportPath = path.join(this.options.outputDir, 'test-report.html');
        
        await fs.writeFile(reportPath, htmlReport, 'utf8');
        
        // Générer aussi un rapport JSON
        const jsonPath = path.join(this.options.outputDir, 'test-results.json');
        await fs.writeFile(jsonPath, JSON.stringify(reportData, null, 2), 'utf8');
        
        console.log(chalk.green(`✅ Rapport généré: ${reportPath}`));
        console.log(chalk.green(`✅ Données JSON: ${jsonPath}`));
    }
    
    /**
     * Générer le HTML du rapport
     */
    generateHTMLReport(data) {
        return `
<!DOCTYPE html>
<html lang="fr" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rapport de Tests - Landing Pages</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        .summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .summary-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .summary-card h3 {
            font-size: 2em;
            margin-bottom: 10px;
        }
        .passed { color: #28a745; }
        .failed { color: #dc3545; }
        .total { color: #007bff; }
        .duration { color: #6c757d; }
        .tests-grid {
            display: grid;
            gap: 20px;
        }
        .test-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .test-header {
            padding: 20px;
            border-bottom: 1px solid #eee;
        }
        .test-header h3 {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .status-badge {
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: bold;
        }
        .status-passed {
            background: #d4edda;
            color: #155724;
        }
        .status-failed {
            background: #f8d7da;
            color: #721c24;
        }
        .test-details {
            padding: 20px;
        }
        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
            font-family: monospace;
        }
        .footer {
            text-align: center;
            margin-top: 40px;
            padding: 20px;
            color: #6c757d;
        }
        @media (max-width: 768px) {
            .container { padding: 10px; }
            .header h1 { font-size: 2em; }
            .summary { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 Rapport de Tests Automatisés</h1>
            <p>Plateforme Landing Pages - ${data.endTime.toLocaleString('fr-FR')}</p>
        </div>
        
        <div class="summary">
            <div class="summary-card">
                <h3 class="total">${data.summary.total}</h3>
                <p>Tests Total</p>
            </div>
            <div class="summary-card">
                <h3 class="passed">${data.summary.passed}</h3>
                <p>Réussis</p>
            </div>
            <div class="summary-card">
                <h3 class="failed">${data.summary.failed}</h3>
                <p>Échoués</p>
            </div>
            <div class="summary-card">
                <h3 class="duration">${Math.round(data.totalDuration / 1000)}s</h3>
                <p>Durée Totale</p>
            </div>
        </div>
        
        <div class="tests-grid">
            ${data.tests.map(test => `
                <div class="test-card">
                    <div class="test-header">
                        <h3>
                            ${test.name}
                            <span class="status-badge status-${test.status}">
                                ${test.status === 'passed' ? '✅ Réussi' : '❌ Échoué'}
                            </span>
                        </h3>
                        <p>Durée: ${Math.round(test.duration / 1000)}s</p>
                    </div>
                    <div class="test-details">
                        <p><strong>Début:</strong> ${test.startTime.toLocaleTimeString('fr-FR')}</p>
                        <p><strong>Fin:</strong> ${test.endTime.toLocaleTimeString('fr-FR')}</p>
                        ${test.error ? `<div class="error-message">${test.error}</div>` : ''}
                    </div>
                </div>
            `).join('')}
        </div>
        
        <div class="footer">
            <p>Rapport généré automatiquement par Puppeteer</p>
            <p>Taux de réussite: <strong>${data.successRate}%</strong></p>
        </div>
    </div>
</body>
</html>`;
    }
    
    /**
     * Afficher le résumé des tests
     */
    printSummary() {
        console.log(chalk.blue.bold('\n📊 === RÉSUMÉ DES TESTS ==='));
        console.log(chalk.gray(`Durée totale: ${Math.round((new Date() - this.results.startTime) / 1000)}s`));
        console.log(chalk.blue(`Total: ${this.results.summary.total}`));
        console.log(chalk.green(`Réussis: ${this.results.summary.passed}`));
        console.log(chalk.red(`Échoués: ${this.results.summary.failed}`));
        
        const successRate = Math.round((this.results.summary.passed / this.results.summary.total) * 100);
        console.log(chalk.yellow(`Taux de réussite: ${successRate}%`));
        
        if (this.results.summary.failed === 0) {
            console.log(chalk.green.bold('\n🎉 Tous les tests sont passés avec succès!'));
        } else {
            console.log(chalk.red.bold(`\n⚠️  ${this.results.summary.failed} test(s) ont échoué`));
        }
    }
    
    /**
     * Créer des captures d'écran de démonstration
     */
    async createDemo() {
        console.log(chalk.blue.bold('🎬 === CRÉATION DE DÉMO ==='));
        
        const HomepageTest = require('./tests/homepage-test');
        const demo = new HomepageTest({
            headless: false,
            slowMo: 500,
            screenshotPath: path.join(this.options.outputDir, 'demo')
        });
        
        try {
            await demo.controller.init();
            
            // Démonstration de la page d'accueil
            await demo.controller.navigateTo('index.html');
            await demo.controller.takeScreenshot('demo_homepage');
            
            // Changement de langues
            await demo.controller.changeLanguage('fr');
            await demo.controller.takeScreenshot('demo_french');
            
            await demo.controller.changeLanguage('en');
            await demo.controller.takeScreenshot('demo_english');
            
            await demo.controller.changeLanguage('ar');
            await demo.controller.takeScreenshot('demo_arabic');
            
            // Test responsive
            await demo.controller.testResponsiveness();
            
            console.log(chalk.green.bold('✅ Démo créée avec succès!'));
            
        } catch (error) {
            console.error(chalk.red.bold('❌ Erreur lors de la création de la démo:'), error.message);
        } finally {
            await demo.controller.close();
        }
    }
}

// Fonction principale
async function main() {
    const args = process.argv.slice(2);
    const command = args[0];
    
    const runner = new TestRunner({
        headless: !args.includes('--visible'),
        slowMo: args.includes('--fast') ? 50 : 200,
        baseUrl: args.find(arg => arg.startsWith('--url='))?.split('=')[1] || 'http://localhost:8000'
    });
    
    try {
        switch (command) {
            case 'all':
                await runner.runAllTests();
                break;
                
            case 'homepage':
            case 'login':
            case 'register':
                await runner.runSpecificTest(command);
                break;
                
            case 'demo':
                await runner.createDemo();
                break;
                
            default:
                console.log(chalk.yellow('Usage:'));
                console.log(chalk.gray('  node run-tests.js all              # Tous les tests'));
                console.log(chalk.gray('  node run-tests.js homepage         # Test page d\'accueil'));
                console.log(chalk.gray('  node run-tests.js login            # Test connexion'));
                console.log(chalk.gray('  node run-tests.js register         # Test inscription'));
                console.log(chalk.gray('  node run-tests.js demo             # Créer une démo'));
                console.log(chalk.gray('\nOptions:'));
                console.log(chalk.gray('  --visible                          # Mode visible (non-headless)'));
                console.log(chalk.gray('  --fast                             # Exécution rapide'));
                console.log(chalk.gray('  --url=http://localhost:3000        # URL personnalisée'));
                break;
        }
    } catch (error) {
        console.error(chalk.red.bold('❌ Erreur:'), error.message);
        process.exit(1);
    }
}

// Exporter pour utilisation en module
module.exports = TestRunner;

// Exécution directe
if (require.main === module) {
    main();
}