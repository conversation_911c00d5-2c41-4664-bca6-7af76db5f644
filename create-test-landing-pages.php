<?php
/**
 * C<PERSON>er des landing pages de test
 */

require_once __DIR__ . '/api/config/database.php';

try {
    $database = new Database();
    $db = $database->getConnection();

    echo "🚀 Création de landing pages de test...\n\n";

    // Vérifier/créer l'utilisateur demo avec les bons champs
    $checkUser = $db->prepare("SELECT id FROM users WHERE email = ?");
    $checkUser->execute(['<EMAIL>']);
    $demoUser = $checkUser->fetch();

    if (!$demoUser) {
        $createUser = $db->prepare("
            INSERT INTO users (email, first_name, last_name, role) 
            VALUES (?, ?, ?, ?)
        ");
        $createUser->execute(['<EMAIL>', 'Demo', 'Merchant', 'merchant']);
        $demoUserId = $db->lastInsertId();
        echo "✅ Utilisateur demo créé avec ID: $demoUserId\n";
    } else {
        $demoUserId = $demoUser['id'];
        echo "✅ Utilisateur demo trouvé avec ID: $demoUserId\n";
    }

    // Landing pages de test
    $landingPages = [
        [
            'title' => 'Boutique E-commerce TechStore',
            'slug' => 'boutique-ecommerce-techstore',
            'template_id' => 1,
            'status' => 'published',
            'content' => '<h1>Boutique E-commerce TechStore</h1><p>Découvrez notre sélection de produits high-tech.</p>'
        ],
        [
            'title' => 'Service SaaS LandingCraft Pro',
            'slug' => 'service-saas-landingcraft-pro',
            'template_id' => 2,
            'status' => 'published',
            'content' => '<h1>LandingCraft Pro</h1><p>Créez des landing pages professionnelles en quelques minutes.</p>'
        ],
        [
            'title' => 'Portfolio Créatif Ahmed Design',
            'slug' => 'portfolio-creatif-ahmed-design',
            'template_id' => 3,
            'status' => 'draft',
            'content' => '<h1>Ahmed Design</h1><p>Portfolio de designer graphique spécialisé en identité visuelle.</p>'
        ],
        [
            'title' => 'Services Conseil ConseilPro',
            'slug' => 'services-conseil-conseilpro',
            'template_id' => 4,
            'status' => 'published',
            'content' => '<h1>ConseilPro</h1><p>Services de conseil en management et stratégie d\'entreprise.</p>'
        ],
        [
            'title' => 'App Mobile FoodieApp',
            'slug' => 'app-mobile-foodieapp',
            'template_id' => 5,
            'status' => 'published',
            'content' => '<h1>FoodieApp</h1><p>Commandez vos plats préférés et faites-vous livrer rapidement.</p>'
        ]
    ];

    foreach ($landingPages as $page) {
        // Vérifier si la page existe déjà
        $checkPage = $db->prepare("SELECT id FROM landing_pages WHERE slug = ?");
        $checkPage->execute([$page['slug']]);
        if ($checkPage->fetch()) {
            echo "⚠️  Landing page '{$page['title']}' existe déjà\n";
            continue;
        }

        // Insérer la landing page
        $insertPage = $db->prepare("
            INSERT INTO landing_pages (
                merchant_id, title, slug, template_id, content, status, user_id,
                views_count, conversions_count, created_at, updated_at
            ) VALUES (
                ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW()
            )
        ");

        $views = rand(50, 500);
        $conversions = rand(5, 50);

        $success = $insertPage->execute([
            $demoUserId, // merchant_id
            $page['title'],
            $page['slug'],
            $page['template_id'],
            $page['content'],
            $page['status'],
            $demoUserId, // user_id
            $views,
            $conversions
        ]);

        if ($success) {
            $pageId = $db->lastInsertId();
            echo "✅ Landing page créée: '{$page['title']}' (ID: $pageId, Vues: $views, Conversions: $conversions)\n";
        } else {
            echo "❌ Erreur lors de la création de '{$page['title']}'\n";
        }
    }

    echo "\n🎉 Création terminée !\n";
    echo "🔗 Testez le dashboard: http://localhost:8000/dashboard.html\n";

} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
}
?>
