<?php
/**
 * Direct API test without HTTP requests
 */

echo "<h2>Direct API Test</h2>\n";
echo "<pre>\n";

// Simulate API request environment
$_SERVER['REQUEST_METHOD'] = 'GET';
$_SERVER['REQUEST_URI'] = '/api/products';
$_SERVER['HTTP_AUTHORIZATION'] = 'Bearer demo_token';

// Capture output
ob_start();

try {
    echo "Testing products API directly...\n";
    
    // Include the products API
    include 'api/products.php';
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

$output = ob_get_clean();
echo $output;

echo "</pre>\n";
?>
