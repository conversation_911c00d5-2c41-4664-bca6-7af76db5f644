<?php

/**
 * Firebase Dashboard API
 * Provides endpoints for dashboard statistics, user management, and system overview
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

require_once 'api/config/database.php';

try {
    // Initialize database and auth
    $database = new Database();
    $db = $database->getConnection();
    $auth = new Auth($db);

    // Get request path and method
    $request_uri = $_SERVER['REQUEST_URI'];
    $path = parse_url($request_uri, PHP_URL_PATH);
    $path_parts = explode('/', trim($path, '/'));

    // Extract endpoint from path
    $endpoint = isset($path_parts[1]) ? $path_parts[1] : '';
    $action = isset($path_parts[2]) ? $path_parts[2] : '';

    // Authentication
    $headers = function_exists('getallheaders') ? getallheaders() : [];
    if (empty($headers) && isset($_SERVER['HTTP_AUTHORIZATION'])) {
        $headers['Authorization'] = $_SERVER['HTTP_AUTHORIZATION'];
    }

    $token = isset($headers['Authorization']) ? str_replace('Bearer ', '', $headers['Authorization']) : null;
    $user_id = $auth->verifyFirebaseToken($token);
    $store = $auth->getUserStore($user_id);

    // Route requests
    switch ($_SERVER['REQUEST_METHOD']) {
        case 'GET':
            handleGetRequest($db, $endpoint, $action, $store);
            break;
        case 'POST':
            $input = json_decode(file_get_contents('php://input'), true);
            handlePostRequest($db, $endpoint, $action, $store, $input);
            break;
        default:
            ApiResponse::error('Method not allowed', 405);
    }
} catch (Exception $e) {
    ApiResponse::error('Server error: ' . $e->getMessage(), 500);
}

/**
 * Handle GET requests
 */
function handleGetRequest($db, $endpoint, $action, $store)
{
    switch ($endpoint) {
        case 'stats':
            getDashboardStats($db, $store);
            break;
        case 'overview':
            getSystemOverview($db, $store);
            break;
        case 'analytics':
            getAnalytics($db, $store, $action);
            break;
        case 'recent-activity':
            getRecentActivity($db, $store);
            break;
        default:
            ApiResponse::error('Invalid endpoint', 404);
    }
}

/**
 * Handle POST requests
 */
function handlePostRequest($db, $endpoint, $action, $store, $input)
{
    switch ($endpoint) {
        case 'analytics':
            if ($action === 'custom') {
                getCustomAnalytics($db, $store, $input);
            } else {
                ApiResponse::error('Invalid action', 404);
            }
            break;
        default:
            ApiResponse::error('Invalid endpoint', 404);
    }
}

/**
 * Get dashboard statistics
 */
function getDashboardStats($db, $store)
{
    try {
        $store_id = $store['id'];

        // Get user count
        $users_stmt = $db->query("SELECT COUNT(*) as count FROM users WHERE status = 'active'");
        $users_count = $users_stmt->fetch()['count'];

        // Get stores count
        $stores_stmt = $db->query("SELECT COUNT(*) as count FROM stores WHERE status = 'active'");
        $stores_count = $stores_stmt->fetch()['count'];

        // Get products count for this store
        $products_stmt = $db->prepare("SELECT COUNT(*) as count FROM products WHERE store_id = ? AND status = 'active'");
        $products_stmt->execute([$store_id]);
        $products_count = $products_stmt->fetch()['count'];

        // Get orders count for this store
        $orders_stmt = $db->prepare("SELECT COUNT(*) as count FROM orders WHERE store_id = ?");
        $orders_stmt->execute([$store_id]);
        $orders_count = $orders_stmt->fetch()['count'];

        // Get revenue for this store
        $revenue_stmt = $db->prepare("
            SELECT COALESCE(SUM(total_amount), 0) as revenue
            FROM orders
            WHERE store_id = ? AND status IN ('completed', 'delivered')
        ");
        $revenue_stmt->execute([$store_id]);
        $revenue = $revenue_stmt->fetch()['revenue'];

        // Get monthly stats
        $monthly_orders_stmt = $db->prepare("
            SELECT COUNT(*) as count
            FROM orders
            WHERE store_id = ? AND MONTH(created_at) = MONTH(CURRENT_DATE()) AND YEAR(created_at) = YEAR(CURRENT_DATE())
        ");
        $monthly_orders_stmt->execute([$store_id]);
        $monthly_orders = $monthly_orders_stmt->fetch()['count'];

        $monthly_revenue_stmt = $db->prepare("
            SELECT COALESCE(SUM(total_amount), 0) as revenue
            FROM orders
            WHERE store_id = ? AND status IN ('completed', 'delivered')
            AND MONTH(created_at) = MONTH(CURRENT_DATE()) AND YEAR(created_at) = YEAR(CURRENT_DATE())
        ");
        $monthly_revenue_stmt->execute([$store_id]);
        $monthly_revenue = $monthly_revenue_stmt->fetch()['revenue'];

        $stats = [
            'users' => (int)$users_count,
            'stores' => (int)$stores_count,
            'products' => (int)$products_count,
            'orders' => (int)$orders_count,
            'revenue' => (float)$revenue,
            'monthly_orders' => (int)$monthly_orders,
            'monthly_revenue' => (float)$monthly_revenue,
            'conversion_rate' => $products_count > 0 ? round(($orders_count / $products_count) * 100, 2) : 0
        ];

        ApiResponse::success($stats);
    } catch (Exception $e) {
        ApiResponse::error('Failed to get dashboard stats: ' . $e->getMessage(), 500);
    }
}

/**
 * Get system overview
 */
function getSystemOverview($db, $store)
{
    try {
        // Database status
        $tables = ['users', 'stores', 'products', 'orders', 'payments'];
        $database_status = [];

        foreach ($tables as $table) {
            $stmt = $db->query("SELECT COUNT(*) as count FROM $table");
            $count = $stmt->fetch()['count'];
            $database_status[$table] = [
                'records' => (int)$count,
                'status' => $count > 0 ? 'active' : 'empty'
            ];
        }

        // System health
        $system_health = [
            'database' => 'healthy',
            'api' => 'healthy',
            'storage' => 'healthy',
            'last_backup' => date('Y-m-d H:i:s')
        ];

        // Recent activity summary
        $recent_orders_stmt = $db->prepare("
            SELECT COUNT(*) as count
            FROM orders
            WHERE store_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        ");
        $recent_orders_stmt->execute([$store['id']]);
        $recent_orders = $recent_orders_stmt->fetch()['count'];

        $recent_products_stmt = $db->prepare("
            SELECT COUNT(*) as count
            FROM products
            WHERE store_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        ");
        $recent_products_stmt->execute([$store['id']]);
        $recent_products = $recent_products_stmt->fetch()['count'];

        $overview = [
            'database_status' => $database_status,
            'system_health' => $system_health,
            'recent_activity' => [
                'orders_24h' => (int)$recent_orders,
                'products_24h' => (int)$recent_products
            ]
        ];

        ApiResponse::success($overview);
    } catch (Exception $e) {
        ApiResponse::error('Failed to get system overview: ' . $e->getMessage(), 500);
    }
}

/**
 * Get analytics data
 */
function getAnalytics($db, $store, $type)
{
    try {
        $store_id = $store['id'];

        switch ($type) {
            case 'sales':
                getSalesAnalytics($db, $store_id);
                break;
            case 'products':
                getProductAnalytics($db, $store_id);
                break;
            case 'users':
                getUserAnalytics($db, $store_id);
                break;
            default:
                // Default: return all analytics
                $sales = getSalesAnalyticsData($db, $store_id);
                $products = getProductAnalyticsData($db, $store_id);
                $users = getUserAnalyticsData($db, $store_id);

                ApiResponse::success([
                    'sales' => $sales,
                    'products' => $products,
                    'users' => $users
                ]);
        }
    } catch (Exception $e) {
        ApiResponse::error('Failed to get analytics: ' . $e->getMessage(), 500);
    }
}

/**
 * Get recent activity
 */
function getRecentActivity($db, $store)
{
    try {
        $store_id = $store['id'];

        // Recent orders
        $orders_stmt = $db->prepare("
            SELECT id, customer_name, total_amount, status, created_at
            FROM orders
            WHERE store_id = ?
            ORDER BY created_at DESC
            LIMIT 10
        ");
        $orders_stmt->execute([$store_id]);
        $recent_orders = $orders_stmt->fetchAll();

        // Recent products
        $products_stmt = $db->prepare("
            SELECT id, name_ar, price, status, created_at
            FROM products
            WHERE store_id = ?
            ORDER BY created_at DESC
            LIMIT 10
        ");
        $products_stmt->execute([$store_id]);
        $recent_products = $products_stmt->fetchAll();

        $activity = [
            'recent_orders' => $recent_orders,
            'recent_products' => $recent_products
        ];

        ApiResponse::success($activity);
    } catch (Exception $e) {
        ApiResponse::error('Failed to get recent activity: ' . $e->getMessage(), 500);
    }
}

/**
 * Helper functions for analytics data
 */
function getSalesAnalyticsData($db, $store_id)
{
    // Get sales data for the last 30 days
    $stmt = $db->prepare("
        SELECT
            DATE(created_at) as date,
            COUNT(*) as orders,
            SUM(total_amount) as revenue
        FROM orders
        WHERE store_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        GROUP BY DATE(created_at)
        ORDER BY date ASC
    ");
    $stmt->execute([$store_id]);
    return $stmt->fetchAll();
}

function getProductAnalyticsData($db, $store_id)
{
    // Get top products by orders
    $stmt = $db->prepare("
        SELECT
            p.name_ar,
            COUNT(oi.product_id) as orders_count,
            SUM(oi.quantity) as total_quantity
        FROM products p
        LEFT JOIN order_items oi ON p.id = oi.product_id
        WHERE p.store_id = ?
        GROUP BY p.id, p.name_ar
        ORDER BY orders_count DESC
        LIMIT 10
    ");
    $stmt->execute([$store_id]);
    return $stmt->fetchAll();
}

function getUserAnalyticsData($db, $store_id = null)
{
    // Get user registration trends (global data, store_id not used)
    $stmt = $db->query("
        SELECT
            DATE(created_at) as date,
            COUNT(*) as new_users
        FROM users
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        GROUP BY DATE(created_at)
        ORDER BY date ASC
    ");
    return $stmt->fetchAll();
}

/**
 * Individual analytics functions
 */
function getSalesAnalytics($db, $store_id)
{
    $data = getSalesAnalyticsData($db, $store_id);
    ApiResponse::success(['sales_data' => $data]);
}

function getProductAnalytics($db, $store_id)
{
    $data = getProductAnalyticsData($db, $store_id);
    ApiResponse::success(['product_data' => $data]);
}

function getUserAnalytics($db, $store_id)
{
    $data = getUserAnalyticsData($db, $store_id);
    ApiResponse::success(['user_data' => $data]);
}

/**
 * Get custom analytics based on input parameters
 */
function getCustomAnalytics($db, $store, $input)
{
    try {
        $store_id = $store['id'];
        $date_from = $input['date_from'] ?? date('Y-m-d', strtotime('-30 days'));
        $date_to = $input['date_to'] ?? date('Y-m-d');
        $metric = $input['metric'] ?? 'sales';

        switch ($metric) {
            case 'sales':
                $stmt = $db->prepare("
                    SELECT
                        DATE(created_at) as date,
                        COUNT(*) as orders,
                        SUM(total_amount) as revenue
                    FROM orders
                    WHERE store_id = ? AND DATE(created_at) BETWEEN ? AND ?
                    GROUP BY DATE(created_at)
                    ORDER BY date ASC
                ");
                $stmt->execute([$store_id, $date_from, $date_to]);
                break;

            case 'products':
                $stmt = $db->prepare("
                    SELECT
                        DATE(created_at) as date,
                        COUNT(*) as new_products
                    FROM products
                    WHERE store_id = ? AND DATE(created_at) BETWEEN ? AND ?
                    GROUP BY DATE(created_at)
                    ORDER BY date ASC
                ");
                $stmt->execute([$store_id, $date_from, $date_to]);
                break;

            default:
                ApiResponse::error('Invalid metric', 400);
                return;
        }

        $data = $stmt->fetchAll();
        ApiResponse::success([
            'metric' => $metric,
            'date_range' => ['from' => $date_from, 'to' => $date_to],
            'data' => $data
        ]);
    } catch (Exception $e) {
        ApiResponse::error('Failed to get custom analytics: ' . $e->getMessage(), 500);
    }
}
