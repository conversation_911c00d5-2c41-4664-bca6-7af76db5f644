<?php

/**
 * Firebase Dashboard API
 * Provides endpoints for dashboard statistics, user management, and system overview
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

require_once 'api/config/database.php';

try {
    // Initialize database
    $database = new Database();
    $db = $database->getConnection();

    // Get request parameters
    $action = isset($_GET['action']) ? $_GET['action'] : '';

    // Simple authentication check
    $headers = function_exists('getallheaders') ? getallheaders() : [];
    if (empty($headers) && isset($_SERVER['HTTP_AUTHORIZATION'])) {
        $headers['Authorization'] = $_SERVER['HTTP_AUTHORIZATION'];
    }

    $token = isset($headers['Authorization']) ? str_replace('Bearer ', '', $headers['Authorization']) : null;

    // For demo purposes, accept demo_token or skip auth for stats
    if ($token !== 'demo_token' && $action !== 'stats') {
        http_response_code(401);
        echo json_encode(['error' => 'Token d\'authentification requis']);
        exit;
    }

    // Mock store data
    $store = ['id' => 1, 'name' => 'Demo Store'];

    // Route requests based on action
    switch ($_SERVER['REQUEST_METHOD']) {
        case 'GET':
            handleGetRequest($db, $action, $store);
            break;
        case 'POST':
            $input = json_decode(file_get_contents('php://input'), true);
            handlePostRequest($db, $action, $store, $input);
            break;
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Server error: ' . $e->getMessage()]);
}

/**
 * Handle GET requests
 */
function handleGetRequest($db, $action, $store)
{
    switch ($action) {
        case 'stats':
            getDashboardStats($db, $store);
            break;
        case 'overview':
            getSystemOverview($db, $store);
            break;
        case 'analytics':
            getAnalytics($db, $store);
            break;
        case 'recent-activity':
            getRecentActivity($db, $store);
            break;
        default:
            http_response_code(404);
            echo json_encode(['error' => 'Invalid action']);
    }
}

/**
 * Handle POST requests
 */
function handlePostRequest($db, $endpoint, $action, $store, $input)
{
    switch ($endpoint) {
        case 'analytics':
            if ($action === 'custom') {
                getCustomAnalytics($db, $store, $input);
            } else {
                http_response_code(404); echo json_encode(['error' => 'Invalid action']);
            }
            break;
        default:
            http_response_code(404); echo json_encode(['error' => 'Invalid endpoint']);
    }
}

/**
 * Get dashboard statistics
 */
function getDashboardStats($db, $store)
{
    try {
        $store_id = $store['id'];

        // Get user count
        $users_stmt = $db->query("SELECT COUNT(*) as count FROM users WHERE status = 'active'");
        $users_count = $users_stmt->fetch()['count'];

        // Get stores count
        $stores_stmt = $db->query("SELECT COUNT(*) as count FROM stores WHERE status = 'active'");
        $stores_count = $stores_stmt->fetch()['count'];

        // Get products count for this store
        $products_stmt = $db->prepare("SELECT COUNT(*) as count FROM products WHERE store_id = ? AND status = 'active'");
        $products_stmt->execute([$store_id]);
        $products_count = $products_stmt->fetch()['count'];

        // Get orders count for this store
        $orders_stmt = $db->prepare("SELECT COUNT(*) as count FROM orders WHERE store_id = ?");
        $orders_stmt->execute([$store_id]);
        $orders_count = $orders_stmt->fetch()['count'];

        // Get revenue for this store
        $revenue_stmt = $db->prepare("
            SELECT COALESCE(SUM(total_amount), 0) as revenue
            FROM orders
            WHERE store_id = ? AND status IN ('completed', 'delivered')
        ");
        $revenue_stmt->execute([$store_id]);
        $revenue = $revenue_stmt->fetch()['revenue'];

        // Get monthly stats
        $monthly_orders_stmt = $db->prepare("
            SELECT COUNT(*) as count
            FROM orders
            WHERE store_id = ? AND MONTH(created_at) = MONTH(CURRENT_DATE()) AND YEAR(created_at) = YEAR(CURRENT_DATE())
        ");
        $monthly_orders_stmt->execute([$store_id]);
        $monthly_orders = $monthly_orders_stmt->fetch()['count'];

        $monthly_revenue_stmt = $db->prepare("
            SELECT COALESCE(SUM(total_amount), 0) as revenue
            FROM orders
            WHERE store_id = ? AND status IN ('completed', 'delivered')
            AND MONTH(created_at) = MONTH(CURRENT_DATE()) AND YEAR(created_at) = YEAR(CURRENT_DATE())
        ");
        $monthly_revenue_stmt->execute([$store_id]);
        $monthly_revenue = $monthly_revenue_stmt->fetch()['revenue'];

        $stats = [
            'users' => (int)$users_count,
            'stores' => (int)$stores_count,
            'products' => (int)$products_count,
            'orders' => (int)$orders_count,
            'revenue' => (float)$revenue,
            'monthly_orders' => (int)$monthly_orders,
            'monthly_revenue' => (float)$monthly_revenue,
            'conversion_rate' => $products_count > 0 ? round(($orders_count / $products_count) * 100, 2) : 0
        ];

        echo json_encode(['success' => true, 'data' => $stats]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to get dashboard stats: ' . $e->getMessage()]);
    }
}

/**
 * Get system overview
 */
function getSystemOverview($db, $store)
{
    try {
        // Database status
        $tables = ['users', 'stores', 'products', 'orders', 'payments'];
        $database_status = [];

        foreach ($tables as $table) {
            $stmt = $db->query("SELECT COUNT(*) as count FROM $table");
            $count = $stmt->fetch()['count'];
            $database_status[$table] = [
                'records' => (int)$count,
                'status' => $count > 0 ? 'active' : 'empty'
            ];
        }

        // System health
        $system_health = [
            'database' => 'healthy',
            'api' => 'healthy',
            'storage' => 'healthy',
            'last_backup' => date('Y-m-d H:i:s')
        ];

        // Recent activity summary
        $recent_orders_stmt = $db->prepare("
            SELECT COUNT(*) as count
            FROM orders
            WHERE store_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        ");
        $recent_orders_stmt->execute([$store['id']]);
        $recent_orders = $recent_orders_stmt->fetch()['count'];

        $recent_products_stmt = $db->prepare("
            SELECT COUNT(*) as count
            FROM products
            WHERE store_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        ");
        $recent_products_stmt->execute([$store['id']]);
        $recent_products = $recent_products_stmt->fetch()['count'];

        $overview = [
            'database_status' => $database_status,
            'system_health' => $system_health,
            'recent_activity' => [
                'orders_24h' => (int)$recent_orders,
                'products_24h' => (int)$recent_products
            ]
        ];

        echo json_encode(['success' => true, 'data' => $overview]);
    } catch (Exception $e) {
        http_response_code(500); echo json_encode(['error' => 'Failed to get system overview: ' . $e->getMessage()]);
    }
}

/**
 * Get analytics data
 */
function getAnalytics($db, $store, $type)
{
    try {
        $store_id = $store['id'];

        switch ($type) {
            case 'sales':
                getSalesAnalytics($db, $store_id);
                break;
            case 'products':
                getProductAnalytics($db, $store_id);
                break;
            case 'users':
                getUserAnalytics($db, $store_id);
                break;
            default:
                // Default: return all analytics
                $sales = getSalesAnalyticsData($db, $store_id);
                $products = getProductAnalyticsData($db, $store_id);
                $users = getUserAnalyticsData($db, $store_id);

                echo json_encode(['success' => true, 'data' => [
                    'sales' => $sales,
                    'products' => $products,
                    'users' => $users
                ]]);
        }
    } catch (Exception $e) {
        http_response_code(500); echo json_encode(['error' => 'Failed to get analytics: ' . $e->getMessage()]);
    }
}

/**
 * Get recent activity
 */
function getRecentActivity($db, $store)
{
    try {
        $store_id = $store['id'];

        // Recent orders
        $orders_stmt = $db->prepare("
            SELECT id, customer_name, total_amount, status, created_at
            FROM orders
            WHERE store_id = ?
            ORDER BY created_at DESC
            LIMIT 10
        ");
        $orders_stmt->execute([$store_id]);
        $recent_orders = $orders_stmt->fetchAll();

        // Recent products
        $products_stmt = $db->prepare("
            SELECT id, name_ar, price, status, created_at
            FROM products
            WHERE store_id = ?
            ORDER BY created_at DESC
            LIMIT 10
        ");
        $products_stmt->execute([$store_id]);
        $recent_products = $products_stmt->fetchAll();

        $activity = [
            'recent_orders' => $recent_orders,
            'recent_products' => $recent_products
        ];

        echo json_encode(['success' => true, 'data' => $activity]);
    } catch (Exception $e) {
        http_response_code(500); echo json_encode(['error' => 'Failed to get recent activity: ' . $e->getMessage()]);
    }
}

/**
 * Helper functions for analytics data
 */
function getSalesAnalyticsData($db, $store_id)
{
    // Get sales data for the last 30 days
    $stmt = $db->prepare("
        SELECT
            DATE(created_at) as date,
            COUNT(*) as orders,
            SUM(total_amount) as revenue
        FROM orders
        WHERE store_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        GROUP BY DATE(created_at)
        ORDER BY date ASC
    ");
    $stmt->execute([$store_id]);
    return $stmt->fetchAll();
}

function getProductAnalyticsData($db, $store_id)
{
    // Get top products by orders
    $stmt = $db->prepare("
        SELECT
            p.name_ar,
            COUNT(oi.product_id) as orders_count,
            SUM(oi.quantity) as total_quantity
        FROM products p
        LEFT JOIN order_items oi ON p.id = oi.product_id
        WHERE p.store_id = ?
        GROUP BY p.id, p.name_ar
        ORDER BY orders_count DESC
        LIMIT 10
    ");
    $stmt->execute([$store_id]);
    return $stmt->fetchAll();
}

function getUserAnalyticsData($db, $store_id = null)
{
    // Get user registration trends (global data, store_id not used)
    $stmt = $db->query("
        SELECT
            DATE(created_at) as date,
            COUNT(*) as new_users
        FROM users
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        GROUP BY DATE(created_at)
        ORDER BY date ASC
    ");
    return $stmt->fetchAll();
}

/**
 * Individual analytics functions
 */
function getSalesAnalytics($db, $store_id)
{
    $data = getSalesAnalyticsData($db, $store_id);
    echo json_encode(['success' => true, 'data' => ['sales_data' => $data]]);
}

function getProductAnalytics($db, $store_id)
{
    $data = getProductAnalyticsData($db, $store_id);
    echo json_encode(['success' => true, 'data' => ['product_data' => $data]]);
}

function getUserAnalytics($db, $store_id)
{
    $data = getUserAnalyticsData($db, $store_id);
    echo json_encode(['success' => true, 'data' => ['user_data' => $data]]);
}

/**
 * Get custom analytics based on input parameters
 */
function getCustomAnalytics($db, $store, $input)
{
    try {
        $store_id = $store['id'];
        $date_from = $input['date_from'] ?? date('Y-m-d', strtotime('-30 days'));
        $date_to = $input['date_to'] ?? date('Y-m-d');
        $metric = $input['metric'] ?? 'sales';

        switch ($metric) {
            case 'sales':
                $stmt = $db->prepare("
                    SELECT
                        DATE(created_at) as date,
                        COUNT(*) as orders,
                        SUM(total_amount) as revenue
                    FROM orders
                    WHERE store_id = ? AND DATE(created_at) BETWEEN ? AND ?
                    GROUP BY DATE(created_at)
                    ORDER BY date ASC
                ");
                $stmt->execute([$store_id, $date_from, $date_to]);
                break;

            case 'products':
                $stmt = $db->prepare("
                    SELECT
                        DATE(created_at) as date,
                        COUNT(*) as new_products
                    FROM products
                    WHERE store_id = ? AND DATE(created_at) BETWEEN ? AND ?
                    GROUP BY DATE(created_at)
                    ORDER BY date ASC
                ");
                $stmt->execute([$store_id, $date_from, $date_to]);
                break;

            default:
                http_response_code(400); echo json_encode(['error' => 'Invalid metric']);
                return;
        }

        $data = $stmt->fetchAll();
        echo json_encode(['success' => true, 'data' => [
            'metric' => $metric,
            'date_range' => ['from' => $date_from, 'to' => $date_to],
            'data' => $data
        ]]);
    } catch (Exception $e) {
        http_response_code(500); echo json_encode(['error' => 'Failed to get custom analytics: ' . $e->getMessage()]);
    }
}
