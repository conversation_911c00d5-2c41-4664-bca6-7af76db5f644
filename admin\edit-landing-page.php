<?php
require_once '../api/config/database.php';

// Initialize database connection
$database = new Database();
$db = $database->getConnection();

// Get page ID from URL
$page_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

// Get page data
$query = "
    SELECT 
        lp.*,
        t.name as template_name,
        t.html_content as template_html,
        t.css_content as template_css,
        t.js_content as template_js
    FROM landing_pages lp
    LEFT JOIN templates t ON lp.template_id = t.id
    WHERE lp.id = ?
";

$stmt = $db->prepare($query);
$stmt->execute([$page_id]);
$page = $stmt->fetch(PDO::FETCH_ASSOC);

// Get all available templates
$query = "SELECT id, name, category FROM templates WHERE status = 'active' ORDER BY category, name";
$stmt = $db->prepare($query);
$stmt->execute();
$templates = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get all stores
$query = "SELECT id, name FROM stores ORDER BY name";
$stmt = $db->prepare($query);
$stmt->execute();
$stores = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modifier Landing Page</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/codemirror@5.65.2/lib/codemirror.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/codemirror@5.65.2/theme/monokai.css" rel="stylesheet">
    <style>
        .editor-container {
            position: relative;
            height: calc(100vh - 300px);
        }
        .CodeMirror {
            height: 100%;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .preview-frame {
            width: 100%;
            height: 100%;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .nav-tabs .nav-link {
            padding: 0.5rem 1rem;
            border-radius: 0;
            border: none;
            border-bottom: 2px solid transparent;
        }
        .nav-tabs .nav-link.active {
            border-bottom: 2px solid #0d6efd;
        }
    </style>
</head>
<body>

<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>Modifier Landing Page</h2>
        <div>
            <button type="button" class="btn btn-secondary me-2" onclick="window.location.href='landing-pages.php'">
                <i class="bi bi-arrow-left"></i> Retour
            </button>
            <button type="button" class="btn btn-success" onclick="savePage()">
                <i class="bi bi-save"></i> Enregistrer
            </button>
        </div>
    </div>

    <div class="row">
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Paramètres</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">Titre</label>
                        <input type="text" class="form-control" id="title" value="<?php echo htmlspecialchars($page['title']); ?>">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Slug</label>
                        <input type="text" class="form-control" id="slug" value="<?php echo htmlspecialchars($page['slug']); ?>">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Template</label>
                        <select class="form-select" id="template_id">
                            <option value="">Sélectionner un template</option>
                            <?php foreach ($templates as $template): ?>
                            <option value="<?php echo $template['id']; ?>" <?php echo $template['id'] == $page['template_id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($template['name']); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Boutique</label>
                        <select class="form-select" id="store_id">
                            <option value="">Sélectionner une boutique</option>
                            <?php foreach ($stores as $store): ?>
                            <option value="<?php echo $store['id']; ?>" <?php echo $store['id'] == $page['store_id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($store['name']); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Statut</label>
                        <select class="form-select" id="status">
                            <option value="draft" <?php echo $page['status'] == 'draft' ? 'selected' : ''; ?>>Brouillon</option>
                            <option value="published" <?php echo $page['status'] == 'published' ? 'selected' : ''; ?>>Publié</option>
                            <option value="archived" <?php echo $page['status'] == 'archived' ? 'selected' : ''; ?>>Archivé</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Statistiques</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <div class="mb-3">
                                <label class="form-label">Vues</label>
                                <input type="text" class="form-control" value="<?php echo number_format($page['views']); ?>" readonly>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="mb-3">
                                <label class="form-label">Conversions</label>
                                <input type="text" class="form-control" value="<?php echo number_format($page['conversions']); ?>" readonly>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card h-100">
                <div class="card-header">
                    <ul class="nav nav-tabs card-header-tabs" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" data-bs-toggle="tab" href="#html-tab">HTML</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#css-tab">CSS</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#js-tab">JavaScript</a>
                        </li>
                    </ul>
                </div>
                <div class="card-body p-0">
                    <div class="tab-content">
                        <div class="tab-pane fade show active" id="html-tab">
                            <div class="editor-container">
                                <textarea id="html-editor"><?php echo htmlspecialchars($page['content'] ?: $page['template_html']); ?></textarea>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="css-tab">
                            <div class="editor-container">
                                <textarea id="css-editor"><?php echo htmlspecialchars($page['custom_css'] ?: $page['template_css']); ?></textarea>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="js-tab">
                            <div class="editor-container">
                                <textarea id="js-editor"><?php echo htmlspecialchars($page['custom_js'] ?: $page['template_js']); ?></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="card-title mb-0">Aperçu en direct</h5>
                </div>
                <div class="card-body p-0">
                    <iframe id="preview-frame" class="preview-frame"></iframe>
                </div>
            </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/codemirror@5.65.2/lib/codemirror.js"></script>
<script src="https://cdn.jsdelivr.net/npm/codemirror@5.65.2/mode/xml/xml.js"></script>
<script src="https://cdn.jsdelivr.net/npm/codemirror@5.65.2/mode/javascript/javascript.js"></script>
<script src="https://cdn.jsdelivr.net/npm/codemirror@5.65.2/mode/css/css.js"></script>
<script src="https://cdn.jsdelivr.net/npm/codemirror@5.65.2/mode/htmlmixed/htmlmixed.js"></script>

<script>
// Initialize CodeMirror editors
const htmlEditor = CodeMirror.fromTextArea(document.getElementById('html-editor'), {
    mode: 'htmlmixed',
    theme: 'monokai',
    lineNumbers: true,
    autoCloseTags: true,
    autoCloseBrackets: true,
    matchBrackets: true,
    indentUnit: 4,
    lineWrapping: true
});

const cssEditor = CodeMirror.fromTextArea(document.getElementById('css-editor'), {
    mode: 'css',
    theme: 'monokai',
    lineNumbers: true,
    autoCloseBrackets: true,
    matchBrackets: true,
    indentUnit: 4,
    lineWrapping: true
});

const jsEditor = CodeMirror.fromTextArea(document.getElementById('js-editor'), {
    mode: 'javascript',
    theme: 'monokai',
    lineNumbers: true,
    autoCloseBrackets: true,
    matchBrackets: true,
    indentUnit: 4,
    lineWrapping: true
});

// Update preview when code changes
let updateTimeout;
function updatePreview() {
    clearTimeout(updateTimeout);
    updateTimeout = setTimeout(() => {
        const preview = document.getElementById('preview-frame').contentWindow.document;
        preview.open();
        preview.write(`
            <!DOCTYPE html>
            <html>
            <head>
                <style>${cssEditor.getValue()}</style>
            </head>
            <body>
                ${htmlEditor.getValue()}
                <script>${jsEditor.getValue()}<\/script>
            </body>
            </html>
        `);
        preview.close();
    }, 500);
}

htmlEditor.on('change', updatePreview);
cssEditor.on('change', updatePreview);
jsEditor.on('change', updatePreview);

// Initial preview
updatePreview();

// Save page function
function savePage() {
    const data = {
        id: <?php echo $page_id; ?>,
        title: document.getElementById('title').value,
        slug: document.getElementById('slug').value,
        store_id: document.getElementById('store_id').value,
        template_id: document.getElementById('template_id').value,
        status: document.getElementById('status').value,
        content: htmlEditor.getValue(),
        custom_css: cssEditor.getValue(),
        custom_js: jsEditor.getValue()
    };

    fetch('/api/landing-pages.php?action=update', {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer demo_token'
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            alert('Page enregistrée avec succès');
        } else {
            alert('Erreur lors de l\'enregistrement : ' + result.error);
        }
    })
    .catch(error => {
        alert('Erreur lors de l\'enregistrement : ' + error);
    });
}

const cssEditor = CodeMirror.fromTextArea(document.getElementById('css-editor'), {
    mode: 'css',
    theme: 'monokai',
    lineNumbers: true
});

const jsEditor = CodeMirror.fromTextArea(document.getElementById('js-editor'), {
    mode: 'javascript',
    theme: 'monokai',
    lineNumbers: true
});

// Update preview when switching to preview tab
document.querySelector('a[href="#preview-tab"]').addEventListener('click', updatePreview);

function updatePreview() {
    const html = htmlEditor.getValue();
    const css = cssEditor.getValue();
    const js = jsEditor.getValue();

    const preview = document.getElementById('preview-frame').contentWindow.document;
    preview.open();
    preview.write(`
        <!DOCTYPE html>
        <html>
        <head>
            <style>${css}</style>
        </head>
        <body>
            ${html}
            <script>${js}<\/script>
        </body>
        </html>
    `);
    preview.close();
}

function savePage() {
    const data = {
        id: <?php echo $page_id; ?>,
        title: document.getElementById('title').value,
        slug: document.getElementById('slug').value,
        template_id: document.getElementById('template_id').value,
        store_id: document.getElementById('store_id').value,
        status: document.getElementById('status').value,
        content: htmlEditor.getValue(),
        custom_css: cssEditor.getValue(),
        custom_js: jsEditor.getValue()
    };

    fetch('/api/landing-pages.php?action=update', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer demo_token'
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Landing page enregistrée avec succès');
        } else {
            alert('Erreur lors de l\'enregistrement : ' + data.error);
        }
    })
    .catch(error => {
        console.error('Erreur:', error);
        alert('Une erreur est survenue lors de l\'enregistrement');
    });
}

// Auto-generate slug from title
document.getElementById('title').addEventListener('input', function() {
    const slug = this.value
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)/g, '');
    document.getElementById('slug').value = slug;
});

// Load template content when template is selected
document.getElementById('template_id').addEventListener('change', function() {
    if (this.value) {
        fetch(`/api/landing-pages.php?action=template&id=${this.value}`, {
            headers: {
                'Authorization': 'Bearer demo_token'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                htmlEditor.setValue(data.template.html_content || '');
                cssEditor.setValue(data.template.css_content || '');
                jsEditor.setValue(data.template.js_content || '');
                updatePreview();
            }
        })
        .catch(error => console.error('Erreur:', error));
    }
});
</script>

</body>
</html>