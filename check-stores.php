<?php
/**
 * Script pour vérifier les stores
 */

// Configuration de la base de données
$host = 'localhost';
$port = 3307;
$dbname = 'landingpage_new';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;port=$port;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    echo "=== Vérification des stores ===\n\n";

    // Vérifier tous les stores
    $stmt = $pdo->prepare("SELECT id, store_name, subdomain, status FROM stores ORDER BY id");
    $stmt->execute();
    $stores = $stmt->fetchAll();

    foreach ($stores as $store) {
        echo "ID: {$store['id']}\n";
        echo "Name: {$store['store_name']}\n";
        echo "Subdomain: {$store['subdomain']}\n";
        echo "Status: {$store['status']}\n";
        echo "---\n";
    }

    // Vérifier spécifiquement techstore-algeria
    echo "\n=== Recherche techstore-algeria ===\n";
    $stmt = $pdo->prepare("SELECT * FROM stores WHERE subdomain = 'techstore-algeria' OR store_name LIKE '%TechStore%'");
    $stmt->execute();
    $techStore = $stmt->fetchAll();

    if ($techStore) {
        foreach ($techStore as $store) {
            echo "Trouvé: ID {$store['id']}, Name: {$store['store_name']}, Subdomain: {$store['subdomain']}\n";
        }
    } else {
        echo "Aucun store trouvé avec techstore-algeria\n";
        
        // Mettre à jour le store TechStore Algeria
        echo "Mise à jour du subdomain...\n";
        $updateStmt = $pdo->prepare("UPDATE stores SET subdomain = 'techstore-algeria' WHERE store_name = 'TechStore Algeria'");
        $updateStmt->execute();
        echo "Subdomain mis à jour pour TechStore Algeria\n";
    }

} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
}
?>
