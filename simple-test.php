<?php
echo "🔍 Test simple des APIs...\n";

try {
    // Test de la base de données
    echo "📊 Test de la connexion base de données...\n";
    require_once 'php/config/database.php';
    $database = new Database();
    $db = $database->getConnection();
    echo "  ✅ Connexion base de données OK\n";
    
    // Test de la classe ApiResponse
    echo "\n🧪 Test de la classe ApiResponse...\n";
    
    class ApiResponse
    {
        public static function success($data = null, $message = null)
        {
            $response = ['success' => true];
            if ($data !== null) $response['data'] = $data;
            if ($message !== null) $response['message'] = $message;
            return json_encode($response);
        }

        public static function error($message, $code = 400)
        {
            return json_encode(['success' => false, 'error' => $message]);
        }
    }
    
    echo "  ✅ Classe ApiResponse créée\n";
    
    // Test d'une requête simple
    echo "\n📋 Test d'une requête simple...\n";
    $result = $db->query("SELECT COUNT(*) as count FROM products WHERE store_id = 3");
    $count = $result->fetch()['count'];
    echo "  ✅ Produits trouvés: $count\n";
    
    // Test de la fonction getAllProducts
    echo "\n🛍️ Test de la fonction getAllProducts...\n";
    
    function getAllProducts($db, $storeId) {
        try {
            $query = "
                SELECT 
                    p.id, p.name_ar as name, p.sku, p.price, p.compare_price,
                    p.stock_quantity, p.status, p.created_at,
                    GROUP_CONCAT(pi.image_url) as images
                FROM products p
                LEFT JOIN product_images pi ON p.id = pi.product_id
                WHERE p.store_id = ?
                GROUP BY p.id
                ORDER BY p.created_at DESC
            ";

            $stmt = $db->prepare($query);
            $stmt->execute([$storeId]);
            $products = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Process images
            foreach ($products as &$product) {
                $product['images'] = $product['images'] ? explode(',', $product['images']) : [];
                $product['price'] = (float) $product['price'];
                $product['compare_price'] = $product['compare_price'] ? (float) $product['compare_price'] : null;
                $product['stock_quantity'] = (int) $product['stock_quantity'];
            }

            return ApiResponse::success($products);
        } catch (Exception $e) {
            error_log("Error getting products: " . $e->getMessage());
            return ApiResponse::error('Erreur lors de la récupération des produits', 500);
        }
    }
    
    $result = getAllProducts($db, 3);
    echo "  ✅ Fonction getAllProducts OK\n";
    echo "  📝 Résultat: " . substr($result, 0, 100) . "...\n";
    
    // Test de l'API orders
    echo "\n📦 Test de l'API orders...\n";
    
    function getAllOrders($db, $storeId) {
        try {
            $query = "
                SELECT 
                    o.id, o.order_number, o.customer_name, o.customer_email,
                    o.status, o.payment_status, o.total_amount, o.created_at
                FROM orders o
                WHERE o.store_id = ?
                ORDER BY o.created_at DESC
            ";

            $stmt = $db->prepare($query);
            $stmt->execute([$storeId]);
            $orders = $stmt->fetchAll(PDO::FETCH_ASSOC);

            return ApiResponse::success($orders);
        } catch (Exception $e) {
            error_log("Error getting orders: " . $e->getMessage());
            return ApiResponse::error('Erreur lors de la récupération des commandes', 500);
        }
    }
    
    $result = getAllOrders($db, 3);
    echo "  ✅ Fonction getAllOrders OK\n";
    echo "  📝 Résultat: " . substr($result, 0, 100) . "...\n";
    
    // Test de l'API users
    echo "\n👥 Test de l'API users...\n";
    
    function getAllUsers($db) {
        try {
            $query = "
                SELECT
                    u.id,
                    u.email,
                    u.name,
                    u.status,
                    u.created_at,
                    COALESCE(r.name, 'user') as role
                FROM users u
                LEFT JOIN user_roles ur ON u.id = ur.user_id
                LEFT JOIN roles r ON ur.role_id = r.id
                ORDER BY u.created_at DESC
                LIMIT 10
            ";

            $stmt = $db->prepare($query);
            $stmt->execute();
            $users = $stmt->fetchAll(PDO::FETCH_ASSOC);

            return ApiResponse::success($users);
        } catch (Exception $e) {
            error_log("Error getting users: " . $e->getMessage());
            return ApiResponse::error('Erreur lors de la récupération des utilisateurs', 500);
        }
    }
    
    $result = getAllUsers($db);
    echo "  ✅ Fonction getAllUsers OK\n";
    echo "  📝 Résultat: " . substr($result, 0, 100) . "...\n";
    
    echo "\n✅ Tous les tests sont passés avec succès!\n";
    echo "🎉 Les APIs devraient maintenant fonctionner correctement.\n";
    
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
    echo "📍 Fichier: " . $e->getFile() . " ligne " . $e->getLine() . "\n";
}
?>
