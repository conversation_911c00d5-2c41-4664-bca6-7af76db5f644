<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Debug Roles API</h1>";

try {
    // Simulate the API call
    $_SERVER['REQUEST_METHOD'] = 'GET';
    $_SERVER['REQUEST_URI'] = '/api/roles.php';
    $_SERVER['HTTP_AUTHORIZATION'] = 'Bearer demo_token';
    
    // Include the API file
    include 'api/roles.php';
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
    echo "<br>Trace: " . $e->getTraceAsString();
}
?>
