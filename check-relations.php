<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "=== Relations entre stores et merchants ===\n";

try {
    $pdo = new PDO(
        "mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4",
        'root',
        '',
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    
    // Vérifier les stores
    echo "--- Stores ---\n";
    $stmt = $pdo->query("SELECT id, merchant_id, store_name FROM stores");
    $stores = $stmt->fetchAll();
    foreach ($stores as $store) {
        echo "Store ID: {$store['id']}, Merchant ID: {$store['merchant_id']}, Name: {$store['store_name']}\n";
    }
    
    // Pour store_id = 3, quel est le merchant_id ?
    echo "\n--- Store ID 3 ---\n";
    $stmt = $pdo->prepare("SELECT * FROM stores WHERE id = ?");
    $stmt->execute([3]);
    $store = $stmt->fetch();
    
    if ($store) {
        $merchantId = $store['merchant_id'];
        echo "Store 3 -> Merchant ID: $merchantId\n";
        
        // Compter les éléments pour ce merchant
        echo "\n--- Données pour Merchant ID $merchantId ---\n";
        
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM products WHERE merchant_id = ?");
        $stmt->execute([$merchantId]);
        $result = $stmt->fetch();
        echo "Produits: " . $result['count'] . "\n";
        
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM categories WHERE merchant_id = ?");
        $stmt->execute([$merchantId]);
        $result = $stmt->fetch();
        echo "Catégories: " . $result['count'] . "\n";
        
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM landing_pages WHERE merchant_id = ?");
        $stmt->execute([$merchantId]);
        $result = $stmt->fetch();
        echo "Landing pages: " . $result['count'] . "\n";
        
    } else {
        echo "❌ Store 3 non trouvé\n";
    }
    
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
}
?>
