<?php
require_once 'php/config/database.php';

// Configuration de l'encodage UTF-8
header('Content-Type: text/html; charset=UTF-8');

echo "<h1>🔧 Test des problèmes identifiés</h1>";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .success{color:green;} .error{color:red;} .warning{color:orange;} table{border-collapse:collapse;width:100%;margin:20px 0;} th,td{border:1px solid #ddd;padding:8px;text-align:left;} th{background-color:#f2f2f2;}</style>";

// Test 1: Problème d'affichage de l'arabe
echo "<h2>🔤 Test 1: Affichage des noms en arabe</h2>";
try {
    $sql = "SELECT id, store_name, store_name_ar, store_name_en FROM stores";
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $stores = $stmt->fetchAll();

    if ($stores) {
        echo "<table>";
        echo "<tr><th>ID</th><th>Store Name</th><th>Store Name AR (Raw)</th><th>Store Name AR (UTF-8)</th><th>Store Name EN</th></tr>";

        foreach ($stores as $row) {
            echo "<tr>";
            echo "<td>" . $row['id'] . "</td>";
            echo "<td>" . htmlspecialchars($row['store_name']) . "</td>";
            echo "<td>" . htmlspecialchars($row['store_name_ar']) . "</td>";
            echo "<td dir='rtl'>" . htmlspecialchars($row['store_name_ar'], ENT_QUOTES, 'UTF-8') . "</td>";
            echo "<td>" . htmlspecialchars($row['store_name_en']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "<p class='success'>✅ Données récupérées avec succès</p>";
    } else {
        echo "<p class='error'>❌ Aucune donnée trouvée</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ Exception: " . $e->getMessage() . "</p>";
}

// Test 2: Test de l'API products avec un produit spécifique
echo "<h2>📦 Test 2: API Products - Détails du produit</h2>";
try {
    // Simuler une requête API pour le produit ID 2
    $product_id = 2;
    $sql = "
        SELECT
            p.*,
            COALESCE(AVG(r.rating), 0) as average_rating,
            COUNT(r.id) as review_count
        FROM products p
        LEFT JOIN reviews r ON p.id = r.product_id
        WHERE p.id = ?
        GROUP BY p.id
    ";

    $stmt = $pdo->prepare($sql);
    $stmt->execute([$product_id]);
    $product = $stmt->fetch();

    if ($product) {
        echo "<table>";
        echo "<tr><th>Champ</th><th>Valeur</th></tr>";
        foreach ($product as $key => $value) {
            echo "<tr><td>" . htmlspecialchars($key) . "</td><td>" . htmlspecialchars($value) . "</td></tr>";
        }
        echo "</table>";
        echo "<p class='success'>✅ Produit trouvé avec succès</p>";
    } else {
        echo "<p class='error'>❌ Produit non trouvé</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ Exception: " . $e->getMessage() . "</p>";
}

// Test 3: Vérification de la table reviews
echo "<h2>⭐ Test 3: Vérification de la table reviews</h2>";
try {
    $sql = "SHOW TABLES LIKE 'reviews'";
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $result = $stmt->fetchAll();

    if (count($result) > 0) {
        echo "<p class='success'>✅ Table reviews existe</p>";

        // Vérifier la structure
        $sql = "DESCRIBE reviews";
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        $columns = $stmt->fetchAll();

        echo "<table>";
        echo "<tr><th>Colonne</th><th>Type</th><th>Null</th><th>Clé</th><th>Défaut</th></tr>";
        foreach ($columns as $row) {
            echo "<tr>";
            echo "<td>" . $row['Field'] . "</td>";
            echo "<td>" . $row['Type'] . "</td>";
            echo "<td>" . $row['Null'] . "</td>";
            echo "<td>" . $row['Key'] . "</td>";
            echo "<td>" . $row['Default'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='warning'>⚠️ Table reviews n'existe pas - création nécessaire</p>";

        // Créer la table reviews
        $sql = "
        CREATE TABLE reviews (
            id INT AUTO_INCREMENT PRIMARY KEY,
            product_id INT NOT NULL,
            user_id VARCHAR(255) NOT NULL,
            rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
            comment TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
            INDEX idx_product_id (product_id),
            INDEX idx_user_id (user_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";

        if ($pdo->exec($sql)) {
            echo "<p class='success'>✅ Table reviews créée avec succès</p>";
        } else {
            echo "<p class='error'>❌ Erreur lors de la création de la table reviews</p>";
        }
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ Exception: " . $e->getMessage() . "</p>";
}

// Test 4: Vérification de la table roles
echo "<h2>👥 Test 4: Vérification de la table roles</h2>";
try {
    $sql = "SHOW TABLES LIKE 'roles'";
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $result = $stmt->fetchAll();

    if (count($result) > 0) {
        echo "<p class='success'>✅ Table roles existe</p>";

        // Afficher les rôles existants
        $sql = "SELECT * FROM roles";
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        $roles = $stmt->fetchAll();

        echo "<table>";
        echo "<tr><th>ID</th><th>Nom</th><th>Permissions</th><th>Description</th></tr>";
        foreach ($roles as $row) {
            echo "<tr>";
            echo "<td>" . $row['id'] . "</td>";
            echo "<td>" . htmlspecialchars($row['name']) . "</td>";
            echo "<td>" . htmlspecialchars(substr($row['permissions'], 0, 100)) . "...</td>";
            echo "<td>" . htmlspecialchars($row['description']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='warning'>⚠️ Table roles n'existe pas</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ Exception: " . $e->getMessage() . "</p>";
}
