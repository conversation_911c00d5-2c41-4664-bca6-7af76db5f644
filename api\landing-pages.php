<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

require_once 'config/database.php';
require_once 'utils/auth.php';

error_log('Début du script landing-pages.php');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Error logging
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/error.log');

try {
    $database = new Database();
    $db = $database->getConnection();

    // Get request method and action
    $method = $_SERVER['REQUEST_METHOD'];
    $action = isset($_GET['action']) ? $_GET['action'] : '';
    $id = isset($_GET['id']) ? intval($_GET['id']) : null;

    // Pour le développement, on utilise un merchant_id fixe
    $merchantId = getMerchantId($db);
    error_log('=== MerchantId récupéré ===');
    error_log('MerchantId: ' . $merchantId);
    
    // Get user's subscription plan
    $subscriptionPlanId = getSubscriptionPlanId($db, $merchantId);

    switch ($method) {
        case 'GET':
            switch ($action) {
                case 'all':
                    getLandingPages($db, $merchantId);
                    break;
                case 'template':
                    getTemplate($db, $id, $subscriptionPlanId);
                    break;
                case 'available-templates':
                    getAvailableTemplates($db, $subscriptionPlanId);
                    break;
                default:
                    if ($id) {
                        getLandingPage($db, $id, $userId);
                    } else {
                        http_response_code(400);
                        echo json_encode(['error' => 'Action ou ID requis']);
                    }
            }
            break;

        case 'POST':
            $data = json_decode(file_get_contents('php://input'), true);
            
            switch ($action) {
                case 'create':
                    createLandingPage($db, $data, $userId, $subscriptionPlanId);
                    break;
                case 'update':
                    updateLandingPage($db, $data, $userId);
                    break;
                case 'clone':
                    cloneLandingPage($db, $id, $userId);
                    break;
                default:
                    http_response_code(400);
                    echo json_encode(['error' => 'Action invalide']);
            }
            break;

        case 'DELETE':
            if ($action === 'delete' && $id) {
                deleteLandingPage($db, $id, $userId);
            } else {
                http_response_code(400);
                echo json_encode(['error' => 'ID requis']);
            }
            break;

        default:
            http_response_code(405);
            echo json_encode(['error' => 'Méthode non autorisée']);
    }
} catch (Exception $e) {
    error_log($e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Erreur serveur interne']);
}

function getUserIdFromToken($token) {
    error_log('=== Début getUserIdFromToken ===');
    error_log('Token reçu : ' . ($token ?? 'null'));
    error_log('Headers reçus : ' . json_encode(getallheaders()));
    
    if (empty($token)) {
        error_log('Aucun token fourni, utilisation de l\'ID par défaut');
        return 1;
    }
    
    if ($token === 'demo_token') {
        error_log('Utilisation du token de démo');
        return 1;
    }
    
    // TODO: Implement proper token validation
    error_log('Token non reconnu, utilisation de l\'ID par défaut');
    error_log('=== Fin getUserIdFromToken ===');
    return 1; // Return default user ID for testing
}

function getSubscriptionPlanId($db, $merchantId) {
    try {
        $query = "SELECT u.subscription_id 
                  FROM users u 
                  INNER JOIN merchants m ON m.email = u.email 
                  WHERE m.id = ? 
                  AND u.status = 'active'";
        $stmt = $db->prepare($query);
        $stmt->execute([$merchantId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        error_log('Plan d\'abonnement trouvé: ' . ($result ? $result['subscription_id'] : 1));
        return $result ? $result['subscription_id'] : 1; // Default to basic plan if no subscription found
    } catch (Exception $e) {
        error_log('Erreur dans getSubscriptionPlanId: ' . $e->getMessage());
        error_log('Stack trace: ' . $e->getTraceAsString());
        return 1; // Default to basic plan on error
    }
}

function getLandingPages($db, $merchantId) {
    error_log('=== Début getLandingPages ===');
    error_log('merchantId reçu: ' . $merchantId);
    error_log('Type de connexion DB: ' . get_class($db));
    try {
        error_log('Préparation de la requête SQL pour getLandingPages');
        $query = "SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        ";
        error_log('Requête SQL construite: ' . $query);

            $stmt = $db->prepare($query);
            error_log('=== Exécution de la requête SQL ===');
            error_log('Query: ' . $query);
            error_log('MerchantId: ' . $merchantId);
            error_log('Paramètres de la requête: ' . json_encode([$merchantId]));
            
            if (!$stmt->execute([$merchantId])) {
                $errorInfo = $stmt->errorInfo();
                error_log('Erreur lors de l\'exécution de la requête: ' . implode(', ', $errorInfo));
                error_log('Code erreur SQL: ' . $errorInfo[0]);
                error_log('Code erreur driver: ' . $errorInfo[1]);
                error_log('Message erreur: ' . $errorInfo[2]);
                throw new Exception('Erreur d\'exécution de la requête SQL');
            }
            
            $pages = $stmt->fetchAll(PDO::FETCH_ASSOC);
            error_log('Nombre de pages trouvées: ' . count($pages));
            if (count($pages) > 0) {
                error_log('Première page: ' . json_encode($pages[0]));
                error_log('Dernière page: ' . json_encode($pages[count($pages)-1]));
            } else {
                error_log('Aucune page trouvée');
                error_log('Dernière erreur SQL: ' . json_encode($stmt->errorInfo()));
                error_log('État de la connexion: ' . json_encode($db->getAttribute(PDO::ATTR_CONNECTION_STATUS)));
            }

            echo json_encode([
            'success' => true,
            'data' => ['pages' => $pages]
        ]);
    } catch (Exception $e) {
        error_log($e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la récupération des landing pages']);
    }
}

function getLandingPage($db, $id, $userId) {
    try {
        $query = "
            SELECT 
                lp.*,
                t.name as template_name,
                t.html_content as template_html,
                t.css_content as template_css,
                t.js_content as template_js
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            LEFT JOIN stores s ON lp.store_id = s.id
            LEFT JOIN merchants m ON s.merchant_id = m.id
            LEFT JOIN users u ON m.email = u.email
            WHERE lp.id = ? AND u.id = ? AND lp.status != 'archived'
        ";

        $stmt = $db->prepare($query);
        $stmt->execute([$id, $userId]);
        $page = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($page) {
            echo json_encode([
                'success' => true,
                'data' => $page
            ]);
        } else {
            http_response_code(404);
            echo json_encode(['error' => 'Landing page non trouvée']);
        }
    } catch (Exception $e) {
        error_log($e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la récupération de la landing page']);
    }
}

function getTemplate($db, $id, $subscriptionPlanId) {
    try {
        error_log('getTemplate - id: ' . $id . ', subscriptionPlanId: ' . $subscriptionPlanId);
        
        // Vérifier d'abord si le template existe
        $query = "SELECT t.* FROM templates t WHERE t.id = ?";
        $stmt = $db->prepare($query);
        $stmt->execute([$id]);
        $template = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$template) {
            http_response_code(404);
            echo json_encode(['error' => 'Template non trouvé']);
            return;
        }

        // Vérifier si le template est disponible dans l'abonnement
        $query = "SELECT 1 FROM subscription_templates 
                 WHERE template_id = ? AND subscription_plan_id = ?";
        $stmt = $db->prepare($query);
        $stmt->execute([$id, $subscriptionPlanId]);
        $hasAccess = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($hasAccess) {
            echo json_encode([
                'success' => true,
                'template' => $template
            ]);
        } else {
            error_log('Template non disponible dans l\'abonnement - template_id: ' . $id . ', plan_id: ' . $subscriptionPlanId);
            http_response_code(403);
            echo json_encode(['error' => 'Ce template n\'est pas disponible dans votre abonnement']);
        }
    } catch (Exception $e) {
        error_log('Erreur dans getTemplate: ' . $e->getMessage());
        error_log('Stack trace: ' . $e->getTraceAsString());
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la récupération du template']);
    }
}

function getAvailableTemplates($db, $subscriptionPlanId) {
    try {
        error_log('getAvailableTemplates - subscriptionPlanId: ' . $subscriptionPlanId);
        
        // Récupérer tous les templates avec indication de disponibilité
        $query = "
            SELECT 
                t.*,
                CASE WHEN st.template_id IS NOT NULL THEN 1 ELSE 0 END as is_available
            FROM templates t
            LEFT JOIN subscription_templates st ON t.id = st.template_id 
                AND st.subscription_plan_id = ?
            WHERE t.status = 'active'
            ORDER BY t.name ASC
        ";
        
        $stmt = $db->prepare($query);
        $stmt->execute([$subscriptionPlanId]);
        $templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        error_log('Nombre de templates trouvés: ' . count($templates));
        
        // Grouper les templates par catégorie
        $groupedTemplates = [];
        foreach ($templates as $template) {
            $category = $template['category'] ?? 'other';
            if (!isset($groupedTemplates[$category])) {
                $groupedTemplates[$category] = [];
            }
            $groupedTemplates[$category][] = $template;
        }
        
        echo json_encode([
            'success' => true,
            'templates' => $groupedTemplates
        ]);
    } catch (Exception $e) {
        error_log('Erreur dans getAvailableTemplates: ' . $e->getMessage());
        error_log('Stack trace: ' . $e->getTraceAsString());
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la récupération des templates']);
    }
}

function createLandingPage($db, $data, $userId, $subscriptionPlanId) {
    try {
        // Validate required fields
        if (empty($data['title']) || empty($data['slug']) || empty($data['template_id']) || empty($data['store_id'])) {
            http_response_code(400);
            echo json_encode(['error' => 'Champs requis manquants']);
            return;
        }

        // Check if template is available in user's subscription
        $query = "SELECT COUNT(*) FROM subscription_templates WHERE subscription_plan_id = ? AND template_id = ?";
        $stmt = $db->prepare($query);
        $stmt->execute([$subscriptionPlanId, $data['template_id']]);
        if ($stmt->fetchColumn() == 0) {
            http_response_code(403);
            echo json_encode(['error' => 'Ce template n\'est pas disponible dans votre abonnement']);
            return;
        }

        // Get template content
        $query = "SELECT html_content, css_content, js_content FROM templates WHERE id = ?";
        $stmt = $db->prepare($query);
        $stmt->execute([$data['template_id']]);
        $template = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$template) {
            http_response_code(404);
            echo json_encode(['error' => 'Template non trouvé']);
            return;
        }

        // Check store ownership
        $query = "SELECT user_id FROM stores WHERE id = ?";
        $stmt = $db->prepare($query);
        $stmt->execute([$data['store_id']]);
        $store = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$store || $store['user_id'] != $userId) {
            http_response_code(403);
            echo json_encode(['error' => 'Accès non autorisé à ce magasin']);
            return;
        }

        // Insert new landing page
        $query = "
            INSERT INTO landing_pages 
            (title, slug, store_id, template_id, content, custom_css, custom_js, status) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ";

        $stmt = $db->prepare($query);
        $stmt->execute([
            $data['title'],
            $data['slug'],
            $data['store_id'],
            $data['template_id'],
            $template['html_content'],
            $template['css_content'],
            $template['js_content'],
            'draft'
        ]);

        $newId = $db->lastInsertId();

        echo json_encode([
            'success' => true,
            'message' => 'Landing page créée avec succès',
            'data' => ['id' => $newId]
        ]);
    } catch (Exception $e) {
        error_log($e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la création de la landing page']);
    }
}

function updateLandingPage($db, $data, $userId) {
    try {
        if (empty($data['id'])) {
            http_response_code(400);
            echo json_encode(['error' => 'ID de la landing page requis']);
            return;
        }

        // Check ownership
        $query = "
            SELECT lp.id 
            FROM landing_pages lp
            JOIN stores s ON lp.store_id = s.id
            WHERE lp.id = ? AND s.user_id = ?
        ";
        $stmt = $db->prepare($query);
        $stmt->execute([$data['id'], $userId]);
        if (!$stmt->fetch()) {
            http_response_code(403);
            echo json_encode(['error' => 'Accès non autorisé']);
            return;
        }

        $updateFields = [];
        $params = [];

        if (isset($data['title'])) {
            $updateFields[] = 'title = ?';
            $params[] = $data['title'];
        }
        if (isset($data['slug'])) {
            $updateFields[] = 'slug = ?';
            $params[] = $data['slug'];
        }
        if (isset($data['content'])) {
            $updateFields[] = 'content = ?';
            $params[] = $data['content'];
        }
        if (isset($data['custom_css'])) {
            $updateFields[] = 'custom_css = ?';
            $params[] = $data['custom_css'];
        }
        if (isset($data['custom_js'])) {
            $updateFields[] = 'custom_js = ?';
            $params[] = $data['custom_js'];
        }
        if (isset($data['status'])) {
            $updateFields[] = 'status = ?';
            $params[] = $data['status'];
        }

        if (empty($updateFields)) {
            http_response_code(400);
            echo json_encode(['error' => 'Aucun champ à mettre à jour']);
            return;
        }

        $params[] = $data['id'];

        $query = "UPDATE landing_pages SET " . implode(', ', $updateFields) . " WHERE id = ?";
        $stmt = $db->prepare($query);
        $stmt->execute($params);

        echo json_encode([
            'success' => true,
            'message' => 'Landing page mise à jour avec succès'
        ]);
    } catch (Exception $e) {
        error_log($e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la mise à jour de la landing page']);
    }
}

function cloneLandingPage($db, $id, $userId) {
    try {
        // Check ownership and get original page
        $query = "
            SELECT lp.* 
            FROM landing_pages lp
            JOIN stores s ON lp.store_id = s.id
            WHERE lp.id = ? AND s.user_id = ?
        ";
        $stmt = $db->prepare($query);
        $stmt->execute([$id, $userId]);
        $original = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$original) {
            http_response_code(403);
            echo json_encode(['error' => 'Accès non autorisé']);
            return;
        }

        // Create new slug
        $newSlug = $original['slug'] . '-copy-' . time();

        // Clone the page
        $query = "
            INSERT INTO landing_pages 
            (title, slug, store_id, template_id, content, custom_css, custom_js, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ";

        $stmt = $db->prepare($query);
        $stmt->execute([
            $original['title'] . ' (Copie)',
            $newSlug,
            $original['store_id'],
            $original['template_id'],
            $original['content'],
            $original['custom_css'],
            $original['custom_js'],
            'draft'
        ]);

        $newId = $db->lastInsertId();

        echo json_encode([
            'success' => true,
            'message' => 'Landing page clonée avec succès',
            'data' => ['id' => $newId]
        ]);
    } catch (Exception $e) {
        error_log($e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors du clonage de la landing page']);
    }
}

function deleteLandingPage($db, $id, $userId) {
    try {
        // Check ownership
        $query = "
            SELECT lp.id 
            FROM landing_pages lp
            JOIN stores s ON lp.store_id = s.id
            WHERE lp.id = ? AND s.user_id = ?
        ";
        $stmt = $db->prepare($query);
        $stmt->execute([$id, $userId]);
        if (!$stmt->fetch()) {
            http_response_code(403);
            echo json_encode(['error' => 'Accès non autorisé']);
            return;
        }

        $query = "DELETE FROM landing_pages WHERE id = ?";
        $stmt = $db->prepare($query);
        $stmt->execute([$id]);

        echo json_encode([
            'success' => true,
            'message' => 'Landing page supprimée avec succès'
        ]);
    } catch (Exception $e) {
        error_log($e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la suppression de la landing page']);
    }
}
