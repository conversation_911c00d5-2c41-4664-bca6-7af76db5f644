<?php
header('Content-Type: application/json');
require_once __DIR__ . '/config/database.php';

try {
    // Initialiser la base de données et l'authentification
    $database = new Database();
    $db = $database->getConnection();
    $auth = new Auth($db);

    // Vérifier l'authentification
    $headers = function_exists('getallheaders') ? getallheaders() : [];

    // Fallback for CLI and some servers
    if (empty($headers) && isset($_SERVER['HTTP_AUTHORIZATION'])) {
        $headers['Authorization'] = $_SERVER['HTTP_AUTHORIZATION'];
    }

    $token = isset($headers['Authorization']) ? str_replace('Bearer ', '', $headers['Authorization']) : null;
    $user_id = $auth->verifyFirebaseToken($token);
    $store = $auth->getUserStore($user_id);
    $auth->checkPermission($store['role'], 'landing_pages');

// Vérifier la méthode HTTP
$method = $_SERVER['REQUEST_METHOD'];

// Récupérer les données de la requête
$data = json_decode(file_get_contents('php://input'), true);



switch($method) {
    case 'POST':
        if (isset($data['action']) && $data['action'] === 'create_from_template') {
            $templateId = $data['template_id'] ?? '';
            
            // Validation du template_id
            if (!in_array($templateId, ['product', 'service'])) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Template ID invalide']);
                exit;
            }
            
            try {
                // Insérer la nouvelle landing page
                $stmt = $db->prepare(
                    'INSERT INTO landing_pages (title, template_id, status, created_at, created_by, store_id) '
                    . 'VALUES (?, ?, "draft", NOW(), ?, ?)'
                );
                
                $title = $templateId === 'product' ? 'Nouvelle Page Produit' : 'Nouvelle Page Service';
                $stmt->execute([$title, $templateId, $user_id, $store['id']]);
                
                $pageId = $db->lastInsertId();
                
                echo json_encode([
                    'success' => true,
                    'message' => 'Landing page créée avec succès',
                    'page_id' => $pageId
                ]);
            } catch(PDOException $e) {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'message' => 'Erreur lors de la création de la landing page'
                ]);
            }
        } else {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Action non spécifiée']);
        }
        break;
        
    default:
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Méthode non autorisée']);
        break;
}
} catch(Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Erreur serveur']);
}