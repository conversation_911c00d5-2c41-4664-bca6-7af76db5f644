<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once __DIR__ . '/config/database.php';

try {
    // Initialiser la base de données
    $database = new Database();
    $db = $database->getConnection();

    // Simple authentication check (for demo purposes)
    $headers = function_exists('getallheaders') ? getallheaders() : [];
    if (empty($headers) && isset($_SERVER['HTTP_AUTHORIZATION'])) {
        $headers['Authorization'] = $_SERVER['HTTP_AUTHORIZATION'];
    }

    $token = isset($headers['Authorization']) ? str_replace('Bearer ', '', $headers['Authorization']) : null;

    // For demo purposes, accept demo_token
    if ($token !== 'demo_token') {
        http_response_code(401);
        echo json_encode(['error' => 'Token d\'authentification requis']);
        exit;
    }

    // Create landing_pages table if it doesn't exist
    createLandingPagesTableIfNotExists($db);

    $method = $_SERVER['REQUEST_METHOD'];
    $action = isset($_GET['action']) ? $_GET['action'] : '';

    switch ($method) {
        case 'GET':
            if ($action === 'all') {
                getAllLandingPages($db);
            } elseif ($action === 'templates') {
                getTemplates($db);
            } elseif ($action === 'preview') {
                $id = isset($_GET['id']) ? $_GET['id'] : null;
                if ($id) {
                    previewLandingPage($db, $id);
                } else {
                    http_response_code(400);
                    echo json_encode(['error' => 'ID requis pour l\'aperçu']);
                }
            } else {
                http_response_code(404);
                echo json_encode(['error' => 'Action non supportée']);
            }
            break;

        case 'POST':
            $input = json_decode(file_get_contents('php://input'), true);
            if ($action === 'create') {
                createLandingPage($db, $input);
            } else {
                http_response_code(404);
                echo json_encode(['error' => 'Action non supportée']);
            }
            break;

        case 'PUT':
            $input = json_decode(file_get_contents('php://input'), true);
            $id = isset($_GET['id']) ? $_GET['id'] : null;
            if ($action === 'update' && $id) {
                updateLandingPage($db, $id, $input);
            } else {
                http_response_code(404);
                echo json_encode(['error' => 'Action non supportée']);
            }
            break;

        case 'DELETE':
            $id = isset($_GET['id']) ? $_GET['id'] : null;
            if ($id) {
                deleteLandingPage($db, $id);
            } else {
                http_response_code(400);
                echo json_encode(['error' => 'ID requis']);
            }
            break;

        case 'PATCH':
            $input = json_decode(file_get_contents('php://input'), true);
            $id = isset($_GET['id']) ? $_GET['id'] : null;
            if ($action === 'clone' && $id) {
                cloneLandingPage($db, $id, $input);
            } elseif ($action === 'block' && $id) {
                blockLandingPage($db, $id);
            } elseif ($action === 'unblock' && $id) {
                unblockLandingPage($db, $id);
            } else {
                http_response_code(404);
                echo json_encode(['error' => 'Action non supportée']);
            }
            break;

        default:
            http_response_code(405);
            echo json_encode(['error' => 'Méthode non supportée']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Erreur serveur: ' . $e->getMessage()]);
}

/**
 * Create landing_pages table if it doesn't exist
 */
function createLandingPagesTableIfNotExists($db)
{
    try {
        $createTableQuery = "
            CREATE TABLE IF NOT EXISTS landing_pages (
                id INT AUTO_INCREMENT PRIMARY KEY,
                title VARCHAR(255) NOT NULL,
                slug VARCHAR(255) UNIQUE NULL,
                template_id VARCHAR(50) NOT NULL,
                content LONGTEXT NULL,
                meta_title VARCHAR(255) NULL,
                meta_description TEXT NULL,
                status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
                user_id INT NULL,
                views_count INT DEFAULT 0,
                conversions_count INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_template_id (template_id),
                INDEX idx_status (status),
                INDEX idx_user_id (user_id),
                INDEX idx_slug (slug)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";

        $db->exec($createTableQuery);

        // Insert demo landing pages if table is empty
        $countQuery = "SELECT COUNT(*) as count FROM landing_pages";
        $stmt = $db->query($countQuery);
        $count = $stmt->fetch()['count'];

        if ($count == 0) {
            $demoPages = [
                ['Landing Page Produit A', 'product-a', 'ecommerce', 'published', 1250, 89],
                ['Page SaaS Service', 'saas-service', 'saas', 'published', 0, 0],
                ['Portfolio Créatif', 'portfolio-creative', 'portfolio', 'draft', 450, 12],
                ['Service Consulting', 'consulting-service', 'service', 'published', 780, 34],
                ['App Mobile', 'mobile-app', 'app', 'draft', 320, 8]
            ];

            $insertQuery = "
                INSERT INTO landing_pages (title, slug, template_id, status, views, conversions, created_at)
                VALUES (?, ?, ?, ?, ?, ?, NOW())
            ";

            $stmt = $db->prepare($insertQuery);
            foreach ($demoPages as $page) {
                $stmt->execute($page);
            }
        }
    } catch (Exception $e) {
        error_log("Erreur création table landing_pages: " . $e->getMessage());
    }
}

/**
 * Get all landing pages
 */
function getAllLandingPages($db)
{
    try {
        $query = "
            SELECT
                lp.id,
                lp.title,
                lp.slug,
                lp.template_id,
                lp.status,
                lp.views_count as views,
                lp.conversions_count as conversions,
                lp.created_at,
                lp.updated_at,
                lp.user_id,
                COALESCE(
                    CASE
                        WHEN u.first_name IS NOT NULL AND u.last_name IS NOT NULL
                        THEN CONCAT(u.first_name, ' ', u.last_name)
                        WHEN u.first_name IS NOT NULL
                        THEN u.first_name
                        ELSE u.email
                    END,
                    'Utilisateur inconnu'
                ) as owner_name,
                u.email as owner_email
            FROM landing_pages lp
            LEFT JOIN users u ON lp.user_id = u.id
            ORDER BY lp.created_at DESC
        ";

        $stmt = $db->query($query);
        $pages = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Calculate stats
        $stats = [
            'total' => count($pages),
            'published' => 0,
            'draft' => 0,
            'total_views' => 0,
            'total_conversions' => 0
        ];

        foreach ($pages as $page) {
            if ($page['status'] === 'published') {
                $stats['published']++;
            } elseif ($page['status'] === 'draft') {
                $stats['draft']++;
            }
            $stats['total_views'] += $page['views'];
            $stats['total_conversions'] += $page['conversions'];
        }

        echo json_encode([
            'success' => true,
            'data' => [
                'pages' => $pages,
                'stats' => $stats
            ]
        ]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la récupération des pages: ' . $e->getMessage()]);
    }
}

/**
 * Preview landing page
 */
function previewLandingPage($db, $id)
{
    try {
        $query = "SELECT * FROM landing_pages WHERE id = ?";
        $stmt = $db->prepare($query);
        $stmt->execute([$id]);
        $page = $stmt->fetch();

        if (!$page) {
            http_response_code(404);
            echo json_encode(['error' => 'Landing page non trouvée']);
            return;
        }

        // Update view count
        $updateQuery = "UPDATE landing_pages SET views_count = views_count + 1 WHERE id = ?";
        $updateStmt = $db->prepare($updateQuery);
        $updateStmt->execute([$id]);

        // Return HTML content directly
        header('Content-Type: text/html; charset=utf-8');
        echo $page['content'] ?: '<!-- Contenu de la landing page -->';
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de l\'aperçu: ' . $e->getMessage()]);
    }
}

/**
 * Clone landing page
 */
function cloneLandingPage($db, $id, $data)
{
    try {
        // Get original page
        $query = "SELECT * FROM landing_pages WHERE id = ?";
        $stmt = $db->prepare($query);
        $stmt->execute([$id]);
        $originalPage = $stmt->fetch();

        if (!$originalPage) {
            http_response_code(404);
            echo json_encode(['error' => 'Landing page non trouvée']);
            return;
        }

        // Generate new title and slug
        $newTitle = isset($data['title']) ? $data['title'] : $originalPage['title'] . ' (Copie)';
        $newSlug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $newTitle)));

        // Ensure unique slug
        $counter = 1;
        $baseSlug = $newSlug;
        while (true) {
            $checkQuery = "SELECT COUNT(*) FROM landing_pages WHERE slug = ?";
            $checkStmt = $db->prepare($checkQuery);
            $checkStmt->execute([$newSlug]);
            if ($checkStmt->fetchColumn() == 0) break;
            $newSlug = $baseSlug . '-' . $counter++;
        }

        // Insert cloned page
        $insertQuery = "
            INSERT INTO landing_pages (
                title, slug, template_id, content, meta_title, meta_description,
                status, user_id, created_at
            ) VALUES (
                ?, ?, ?, ?, ?, ?, 'draft', ?, NOW()
            )
        ";

        $stmt = $db->prepare($insertQuery);
        $stmt->execute([
            $newTitle,
            $newSlug,
            $originalPage['template_id'],
            $originalPage['content'],
            $newTitle,
            $originalPage['meta_description'],
            $originalPage['user_id']
        ]);

        $newId = $db->lastInsertId();

        echo json_encode([
            'success' => true,
            'data' => [
                'id' => $newId,
                'title' => $newTitle,
                'slug' => $newSlug,
                'message' => 'Landing page clonée avec succès'
            ]
        ]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors du clonage: ' . $e->getMessage()]);
    }
}

/**
 * Block landing page
 */
function blockLandingPage($db, $id)
{
    try {
        $query = "UPDATE landing_pages SET status = 'archived' WHERE id = ?";
        $stmt = $db->prepare($query);
        $stmt->execute([$id]);

        if ($stmt->rowCount() === 0) {
            http_response_code(404);
            echo json_encode(['error' => 'Landing page non trouvée']);
            return;
        }

        echo json_encode([
            'success' => true,
            'data' => ['message' => 'Landing page bloquée avec succès']
        ]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors du blocage: ' . $e->getMessage()]);
    }
}

/**
 * Unblock landing page
 */
function unblockLandingPage($db, $id)
{
    try {
        $query = "UPDATE landing_pages SET status = 'published' WHERE id = ?";
        $stmt = $db->prepare($query);
        $stmt->execute([$id]);

        if ($stmt->rowCount() === 0) {
            http_response_code(404);
            echo json_encode(['error' => 'Landing page non trouvée']);
            return;
        }

        echo json_encode([
            'success' => true,
            'data' => ['message' => 'Landing page débloquée avec succès']
        ]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors du déblocage: ' . $e->getMessage()]);
    }
}

/**
 * Get available templates
 */
function getTemplates($db)
{
    try {
        $templates = [
            [
                'id' => 'ecommerce',
                'name' => 'E-commerce',
                'description' => 'Template pour vente de produits',
                'preview' => '/templates/previews/ecommerce.jpg',
                'category' => 'business'
            ],
            [
                'id' => 'saas',
                'name' => 'SaaS',
                'description' => 'Template pour services logiciels',
                'preview' => '/templates/previews/saas.jpg',
                'category' => 'technology'
            ],
            [
                'id' => 'portfolio',
                'name' => 'Portfolio',
                'description' => 'Template pour portfolio créatif',
                'preview' => '/templates/previews/portfolio.jpg',
                'category' => 'creative'
            ],
            [
                'id' => 'service',
                'name' => 'Service',
                'description' => 'Template pour services professionnels',
                'preview' => '/templates/previews/service.jpg',
                'category' => 'business'
            ],
            [
                'id' => 'app',
                'name' => 'App Mobile',
                'description' => 'Template pour applications mobiles',
                'preview' => '/templates/previews/app.jpg',
                'category' => 'technology'
            ]
        ];

        echo json_encode([
            'success' => true,
            'data' => $templates
        ]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la récupération des templates: ' . $e->getMessage()]);
    }
}

/**
 * Create new landing page
 */
function createLandingPage($db, $data)
{
    try {
        if (empty($data['title']) || empty($data['template_id'])) {
            http_response_code(400);
            echo json_encode(['error' => 'Titre et template requis']);
            return;
        }

        // Generate slug from title
        $slug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $data['title'])));

        // Generate content from template if it's a known template
        $content = $data['content'] ?? '';
        if (in_array($data['template_id'], ['ecommerce', 'saas', 'portfolio', 'service', 'app'])) {
            // Get rendered template content
            $templateUrl = "http://localhost:8000/api/template-renderer.php?template=" . urlencode($data['template_id']);
            $renderedContent = @file_get_contents($templateUrl);
            if ($renderedContent !== false) {
                $content = $renderedContent;
            }
        }

        $query = "
            INSERT INTO landing_pages (
                title, slug, template_id, content, meta_title, meta_description,
                status, user_id, created_at
            ) VALUES (
                ?, ?, ?, ?, ?, ?, ?, ?, NOW()
            )
        ";

        $stmt = $db->prepare($query);
        $stmt->execute([
            $data['title'],
            $slug,
            $data['template_id'],
            $content,
            $data['meta_title'] ?? $data['title'],
            $data['meta_description'] ?? '',
            $data['status'] ?? 'draft',
            $data['user_id'] ?? 1
        ]);

        $pageId = $db->lastInsertId();

        echo json_encode([
            'success' => true,
            'data' => [
                'id' => $pageId,
                'message' => 'Landing page créée avec succès'
            ]
        ]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la création: ' . $e->getMessage()]);
    }
}

/**
 * Update landing page
 */
function updateLandingPage($db, $id, $data)
{
    try {
        $query = "
            UPDATE landing_pages SET
                title = ?,
                template_id = ?,
                content = ?,
                meta_title = ?,
                meta_description = ?,
                status = ?,
                updated_at = NOW()
            WHERE id = ?
        ";

        $stmt = $db->prepare($query);
        $stmt->execute([
            $data['title'],
            $data['template_id'],
            $data['content'] ?? '',
            $data['meta_title'] ?? $data['title'],
            $data['meta_description'] ?? '',
            $data['status'] ?? 'draft',
            $id
        ]);

        if ($stmt->rowCount() === 0) {
            http_response_code(404);
            echo json_encode(['error' => 'Landing page non trouvée']);
            return;
        }

        echo json_encode([
            'success' => true,
            'data' => [
                'message' => 'Landing page mise à jour avec succès'
            ]
        ]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la mise à jour: ' . $e->getMessage()]);
    }
}

/**
 * Delete landing page
 */
function deleteLandingPage($db, $id)
{
    try {
        $query = "DELETE FROM landing_pages WHERE id = ?";
        $stmt = $db->prepare($query);
        $stmt->execute([$id]);

        if ($stmt->rowCount() === 0) {
            http_response_code(404);
            echo json_encode(['error' => 'Landing page non trouvée']);
            return;
        }

        echo json_encode([
            'success' => true,
            'data' => [
                'message' => 'Landing page supprimée avec succès'
            ]
        ]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la suppression: ' . $e->getMessage()]);
    }
}
