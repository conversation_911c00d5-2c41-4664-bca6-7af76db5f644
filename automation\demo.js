/**
 * Script de démonstration Puppeteer
 * Exemple simple d'utilisation du contrôleur de pages
 */

const PageController = require('./page-controller');
const chalk = require('chalk');

async function runDemo() {
    console.log(chalk.blue.bold('🎬 === DÉMONSTRATION PUPPETEER ==='));
    console.log(chalk.gray('Contrôle automatisé des pages de destination\n'));
    
    const controller = new PageController({
        headless: false, // Mode visible pour la démo
        slowMo: 800,     // Ralentir pour voir les actions
        debug: true
    });
    
    try {
        // Initialiser Puppeteer
        await controller.init();
        
        console.log(chalk.cyan('\n🏠 === TEST DE LA PAGE D\'ACCUEIL ==='));
        
        // Naviguer vers la page d'accueil
        await controller.navigateTo('index.html');
        await controller.takeScreenshot('demo_homepage_initial');
        
        // Tester le changement de langue
        console.log(chalk.yellow('\n🌍 Changement de langue vers le français...'));
        await controller.changeLanguage('fr');
        await controller.wait(1000);
        await controller.takeScreenshot('demo_homepage_french');
        
        console.log(chalk.yellow('🌍 Changement de langue vers l\'anglais...'));
        await controller.changeLanguage('en');
        await controller.wait(1000);
        await controller.takeScreenshot('demo_homepage_english');
        
        console.log(chalk.yellow('🌍 Retour à l\'arabe...'));
        await controller.changeLanguage('ar');
        await controller.wait(1000);
        await controller.takeScreenshot('demo_homepage_arabic');
        
        // Tester le bouton "Start Now"
        console.log(chalk.cyan('\n🚀 Test du bouton "Start Now"...'));
        
        // Chercher et cliquer sur le bouton principal
        const startButton = await controller.evaluate(() => {
            const buttons = document.querySelectorAll('a[href*="register"], .btn-primary');
            return buttons.length > 0;
        });
        
        if (startButton) {
            await controller.clickElement('a[href*="register"]', { waitForNavigation: true });
            await controller.takeScreenshot('demo_navigated_to_register');
            
            console.log(chalk.green('✅ Navigation vers la page d\'inscription réussie!'));
            
            // Tester le formulaire d'inscription
            console.log(chalk.cyan('\n📝 Test du formulaire d\'inscription...'));
            
            await controller.fillForm({
                '#email': '<EMAIL>',
                '#password': 'DemoPassword123!',
                '#confirmPassword': 'DemoPassword123!'
            });
            
            await controller.takeScreenshot('demo_register_form_filled');
            console.log(chalk.green('✅ Formulaire rempli!'));
            
            // Retourner à la page d'accueil
            await controller.navigateTo('index.html');
        }
        
        // Test de responsivité
        console.log(chalk.cyan('\n📱 Test de responsivité...'));
        
        const viewports = [
            { name: 'Mobile', width: 375, height: 667 },
            { name: 'Tablet', width: 768, height: 1024 },
            { name: 'Desktop', width: 1366, height: 768 }
        ];
        
        for (const viewport of viewports) {
            console.log(chalk.yellow(`📐 Test ${viewport.name}: ${viewport.width}x${viewport.height}`));
            await controller.page.setViewport(viewport);
            await controller.wait(1000);
            await controller.takeScreenshot(`demo_responsive_${viewport.name.toLowerCase()}`);
        }
        
        // Restaurer la taille originale
        await controller.page.setViewport({ width: 1366, height: 768 });
        
        // Test de performance
        console.log(chalk.cyan('\n⚡ Test de performance...'));
        const metrics = await controller.getPerformanceMetrics();
        
        console.log(chalk.green('📊 Métriques collectées:'));
        console.log(chalk.gray(`   Nodes DOM: ${metrics.puppeteerMetrics.Nodes}`));
        console.log(chalk.gray(`   Taille JS Heap: ${Math.round(metrics.puppeteerMetrics.JSHeapUsedSize / 1024 / 1024)}MB`));
        
        // Test d'accessibilité basique
        console.log(chalk.cyan('\n♿ Test d\'accessibilité...'));
        
        const accessibility = await controller.evaluate(() => {
            return {
                hasLangAttribute: !!document.documentElement.getAttribute('lang'),
                hasH1: document.querySelectorAll('h1').length === 1,
                hasAltTexts: Array.from(document.querySelectorAll('img')).every(img => img.alt),
                hasAriaLabels: document.querySelectorAll('[aria-label]').length > 0
            };
        });
        
        Object.entries(accessibility).forEach(([check, passed]) => {
            console.log(chalk[passed ? 'green' : 'red'](`${passed ? '✅' : '❌'} ${check}: ${passed ? 'OK' : 'MANQUANT'}`));
        });
        
        // Navigation au clavier
        console.log(chalk.cyan('\n🎹 Test de navigation clavier...'));
        for (let i = 0; i < 5; i++) {
            await controller.page.keyboard.press('Tab');
            await controller.wait(500);
        }
        
        await controller.takeScreenshot('demo_keyboard_navigation');
        
        console.log(chalk.green.bold('\n🎉 Démonstration terminée avec succès!'));
        console.log(chalk.gray('Captures d\'écran sauvées dans ./screenshots/'));
        
    } catch (error) {
        console.error(chalk.red.bold('❌ Erreur lors de la démonstration:'), error.message);
        throw error;
    } finally {
        await controller.close();
    }
}

// Fonction pour une démonstration rapide
async function quickDemo() {
    console.log(chalk.blue.bold('⚡ === DÉMONSTRATION RAPIDE ==='));
    
    const controller = new PageController({
        headless: false,
        slowMo: 300
    });
    
    try {
        await controller.init();
        
        // Test rapide de chaque page
        const pages = ['index.html', 'register.html', 'login.html'];
        
        for (const page of pages) {
            console.log(chalk.cyan(`📄 Test de ${page}...`));
            await controller.navigateTo(page);
            await controller.wait(1000);
            
            const pageInfo = await controller.getPageInfo();
            console.log(chalk.green(`✅ ${pageInfo.title} - Langue: ${pageInfo.language}`));
            
            await controller.takeScreenshot(`quick_demo_${page.replace('.html', '')}`);
        }
        
        console.log(chalk.green.bold('✅ Démonstration rapide terminée!'));
        
    } catch (error) {
        console.error(chalk.red.bold('❌ Erreur:'), error.message);
    } finally {
        await controller.close();
    }
}

// Fonction pour tester une fonctionnalité spécifique
async function testFeature(feature) {
    const controller = new PageController({
        headless: false,
        slowMo: 500
    });
    
    try {
        await controller.init();
        await controller.navigateTo('index.html');
        
        switch (feature) {
            case 'language':
                console.log(chalk.cyan('🌍 Test du changement de langue...'));
                const languages = ['fr', 'en', 'ar'];
                for (const lang of languages) {
                    await controller.changeLanguage(lang);
                    await controller.wait(1000);
                    console.log(chalk.green(`✅ Langue changée vers: ${lang}`));
                }
                break;
                
            case 'responsive':
                console.log(chalk.cyan('📱 Test de responsivité...'));
                await controller.testResponsiveness();
                break;
                
            case 'navigation':
                console.log(chalk.cyan('🧭 Test de navigation...'));
                await controller.clickElement('a[href*="register"]', { waitForNavigation: true });
                await controller.wait(1000);
                await controller.clickElement('a[href*="login"]', { waitForNavigation: true });
                await controller.wait(1000);
                break;
                
            default:
                console.log(chalk.red('❌ Fonctionnalité non reconnue'));
        }
        
    } catch (error) {
        console.error(chalk.red.bold('❌ Erreur:'), error.message);
    } finally {
        await controller.close();
    }
}

// Gestion des arguments de ligne de commande
if (require.main === module) {
    const args = process.argv.slice(2);
    const command = args[0];
    
    switch (command) {
        case 'full':
            runDemo().catch(console.error);
            break;
            
        case 'quick':
            quickDemo().catch(console.error);
            break;
            
        case 'language':
        case 'responsive':
        case 'navigation':
            testFeature(command).catch(console.error);
            break;
            
        default:
            console.log(chalk.yellow('Usage:'));
            console.log(chalk.gray('  node demo.js full        # Démonstration complète'));
            console.log(chalk.gray('  node demo.js quick       # Démonstration rapide'));
            console.log(chalk.gray('  node demo.js language    # Test changement de langue'));
            console.log(chalk.gray('  node demo.js responsive  # Test responsivité'));
            console.log(chalk.gray('  node demo.js navigation  # Test navigation'));
            break;
    }
}

module.exports = { runDemo, quickDemo, testFeature };