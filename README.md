# 🚀 صفحات هبوط للجميع - Landing Pages for Everyone

> **Plateforme SaaS moderne pour créer des landing pages professionnelles en quelques minutes**

Une solution complète et multilingue (Arabe, Français, Anglais) pour générer des pages de destination optimisées avec l'intelligence artificielle.

[![Version](https://img.shields.io/badge/version-2.0.0-blue.svg)](https://github.com/votre-repo/releases)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![PHP](https://img.shields.io/badge/PHP-7.4+-777BB4.svg)](https://php.net)
[![MySQL](https://img.shields.io/badge/MySQL-8.0+-4479A1.svg)](https://mysql.com)
[![Firebase](https://img.shields.io/badge/Firebase-Ready-FFA000.svg)](https://firebase.google.com)

## 🎯 Vue d'ensemble

Cette plateforme SaaS permet aux entrepreneurs et entreprises de créer rapidement des landing pages performantes sans compétences techniques. Avec l'intégration de l'IA, la génération de contenu devient automatique et optimisée pour la conversion.

### 🌟 Points Forts

- 🤖 **IA Intégrée** : Génération automatique de contenu avec OpenAI, Anthropic, Gemini
- 🌍 **Multilingue** : Support natif Arabe (RTL), Français, Anglais
- 📱 **Responsive** : Design adaptatif pour tous les appareils
- ⚡ **Performance** : Optimisé pour la vitesse et le SEO
- 🔒 **Sécurisé** : Authentification Firebase et protection des données
- 📊 **Analytics** : Suivi détaillé des conversions et performances
- 📧 **Notifications SMTP** : Templates email multilingues avec support RTL
- 🔔 **Notifications Temps Réel** : Intégration Firebase Cloud Messaging
- 💳 **Paiements Algériens** : Support CCP, BaridiMob, Virement bancaire, COD
- 🛡️ **Sécurité Paiements** : Validation fichiers, détection fraude, audit trails

## ✨ Fonctionnalités Principales

### 🎨 **Création de Landing Pages**

- ✅ Templates modernes et responsives
- ✅ Éditeur visuel intuitif
- ✅ Génération automatique avec IA (OpenAI, Anthropic, Gemini)
- ✅ Support RTL/LTR complet
- ✅ Optimisation SEO automatique
- ✅ A/B Testing intégré

### 🌍 **Multilingue**

- ✅ Arabe (langue principale)
- ✅ Français
- ✅ Anglais
- ✅ Détection automatique de la langue
- ✅ Commutation dynamique

### 📊 **Analytics & Conversion**

- ✅ Suivi des conversions en temps réel
- ✅ Analytics détaillées (vues, clics, formulaires)
- ✅ Tableaux de bord interactifs
- ✅ Rapports d'optimisation

### 📧 **Gestion Newsletter**

- ✅ Inscription/désabonnement automatique
- ✅ Emails de bienvenue personnalisés
- ✅ Statistiques d'engagement
- ✅ Protection anti-spam

### 🔧 **Intégrations**

- ✅ Firebase (authentification, stockage)
- ✅ Google Analytics
- ✅ Mailchimp/SendGrid
- ✅ Webhooks personnalisés

## 🛠️ Stack Technique

### **Frontend**

- HTML5 sémantique
- CSS3 moderne (Grid, Flexbox, Variables CSS)
- JavaScript ES6+ (modules, async/await)
- Swiper.js pour les carrousels
- Animations CSS/JS optimisées

### **Backend**

- PHP 7.4+ (POO, namespaces)
- MySQL 8.0+ avec JSON natif
- Architecture MVC modulaire
- API RESTful

### **Base de Données**

- MySQL avec charset UTF8MB4
- Tables optimisées pour les performances
- Index composites
- Vues pour les statistiques
- Procédures stockées

### **IA & APIs**

- OpenAI GPT (génération de contenu)
- Anthropic Claude (optimisation)
- Google Gemini (analyse)
- Rate limiting intelligent

### **DevOps & Testing**

- Tests automatisés avec Puppeteer
- CI/CD avec GitHub Actions
- Monitoring des performances
- Déploiement automatisé

## 🏗️ Architecture SaaS

### **Multi-Tenant Architecture**

La plateforme supporte plusieurs modèles d'utilisation :

```
┌─────────────────────────────────────────────────────────────┐
│                    🌐 Frontend (Multi-langue)               │
├─────────────────────────────────────────────────────────────┤
│  🔐 Firebase Auth  │  📊 Analytics  │  🤖 AI Services      │
├─────────────────────────────────────────────────────────────┤
│                    ⚡ API Gateway (PHP)                     │
├─────────────────────────────────────────────────────────────┤
│  📄 Landing Pages  │  👥 Users      │  📧 Newsletter       │
├─────────────────────────────────────────────────────────────┤
│                    🗄️ MySQL Database                       │
└─────────────────────────────────────────────────────────────┘
```

### **Modèles d'Utilisation**

#### 🏢 **Mode Entreprise**

- Dashboard administrateur complet
- Gestion multi-utilisateurs
- Analytics avancées
- Support prioritaire

#### 👤 **Mode Individuel**

- Interface simplifiée
- Templates prêts à l'emploi
- Génération IA assistée
- Analytics de base

#### 🛍️ **Mode E-commerce**

- Intégration produits
- Gestion commandes
- Paiements en ligne
- Suivi des stocks

### **Scalabilité**

- **Horizontal** : Load balancing multi-serveurs
- **Vertical** : Optimisation des ressources
- **Database** : Sharding et réplication
- **CDN** : Distribution globale des assets

## 🧪 Tests Automatisés

### **Suite de Tests Puppeteer**

La plateforme inclut un système de tests automatisés complet :

```bash
# Tests rapides
npm run test:quick

# Tests complets
npm run test:full

# Tests de performance
npm run test:performance

# Tests d'accessibilité
npm run test:accessibility
```

### **Types de Tests**

#### 🔍 **Tests Fonctionnels**

- Navigation entre pages
- Changement de langue
- Soumission de formulaires
- Authentification utilisateur

#### 📱 **Tests Responsives**

- Affichage mobile/tablette/desktop
- Orientation portrait/paysage
- Touch gestures
- Menu mobile

#### ♿ **Tests d'Accessibilité**

- Contraste des couleurs
- Navigation au clavier
- Lecteurs d'écran
- Attributs ARIA

#### ⚡ **Tests de Performance**

- Temps de chargement
- Core Web Vitals
- Optimisation images
- Cache efficacité

### **Rapports de Tests**

```bash
# Génération de rapports
node automation/run-tests.js --report

# Rapports disponibles :
# - HTML : reports/test-report.html
# - JSON : reports/test-results.json
# - Screenshots : reports/screenshots/
```

## 🏗️ Infrastructure & Services

### 📧 SMTP Email System

**Multi-language email templates with RTL support:**

```php
// EmailService usage
$emailService = new EmailService();

// Send new order notification
$emailService->sendNewOrderEmail(
    $customerEmail,
    $customerName,
    $orderId,
    $orderData,
    $paymentData
);

// Send order status update
$emailService->sendOrderStatusUpdate(
    $customerEmail,
    $customerName,
    $orderNumber,
    'confirmed'
);
```

**Email Templates:**

- `templates/emails/new_order_ar.html` - Arabic (RTL)
- `templates/emails/new_order_fr.html` - French
- `templates/emails/new_order_en.html` - English

### 🤖 AI Content Generation

**Multi-provider AI integration:**

```php
// AIService usage
$aiService = new AIService($database);

// Generate product title
$title = $aiService->generateProductTitle(
    $productName,
    $category,
    $benefits,
    'ar' // language
);

// Generate product description
$description = $aiService->generateProductDescription(
    $productData,
    'fr'
);

// Generate landing page content
$content = $aiService->generateLandingPageContent(
    $productData,
    'en'
);
```

**AI Prompt Templates:**

- `templates/ai_prompts/product_title_[lang].txt`
- `templates/ai_prompts/product_description_[lang].txt`
- `templates/ai_prompts/landing_page_content_[lang].txt`

### 🔔 Real-time Notifications

**Firebase Cloud Messaging integration:**

```php
// NotificationService usage
$notificationService = new NotificationService($database);

// Send push notification
$notificationService->sendNotification([
    'type' => 'new_order',
    'title' => 'New Order Received',
    'message' => 'Order #12345 from John Doe',
    'data' => [
        'order_id' => 12345,
        'amount' => 15000
    ]
]);

// Send to specific user
$notificationService->sendToUser($userId, $notification);

// Send to topic
$notificationService->sendToTopic('merchants', $notification);
```

### 💳 Algerian Payment Methods

**Comprehensive payment system with local methods:**

```php
// PaymentService usage
$paymentService = new PaymentService($database);

// Get available payment methods
$methods = $paymentService->getAvailablePaymentMethods('ar');

// Process payment
$result = $paymentService->processPayment(
    $orderData,
    $paymentData,
    $_FILES // uploaded receipts
);

// Update payment status
$paymentService->updatePaymentStatus($orderId, 'confirmed', 'Payment verified');
```

**Supported Payment Methods:**

1. **CCP (Compte Chèques Postaux)**

   - CCP number validation
   - RIP key verification
   - Receipt upload requirement

2. **BaridiMob**

   - Transaction ID tracking
   - Phone number validation
   - Screenshot verification

3. **Bank Transfer**

   - Multi-bank support (BEA, BADR, CNEP, CPA, BNA)
   - Transfer reference tracking
   - Proof document upload

4. **Cash on Delivery (COD)**
   - City-based availability
   - Delivery fee calculation
   - Address validation

**Payment Configuration:**

```json
// config/payment_methods.json
{
  "payment_methods": {
    "ccp": {
      "enabled": true,
      "requires_proof": true,
      "fields": [
        {
          "name": "ccp_number",
          "type": "text",
          "validation": {
            "pattern": "^[0-9]{10,12}$"
          }
        }
      ]
    }
  }
}
```

### 🛡️ Payment Security

**Multi-layer security system:**

- **File Validation**: Type, size, and content verification
- **Fraud Detection**: Pattern analysis and risk scoring
- **Audit Trails**: Complete payment history logging
- **Rate Limiting**: Protection against abuse
- **Encryption**: Sensitive data protection

## 📚 Help - Landing Page Templates Management

### 🎨 Adding New Landing Page Templates

This section explains how to add, modify, and manage landing page templates in the system.

#### **Template File Structure**

Templates are stored in the `templates/` directory with the following structure:

```
templates/
├── ecommerce.html          # E-commerce template
├── saas.html              # SaaS/Software template
├── portfolio.html         # Portfolio/Creative template
├── service.html           # Professional services template
├── app.html               # Mobile app template
├── restaurant.html        # Restaurant/Food template
├── agency.html            # Digital agency template
├── education.html         # Education/Course template
├── healthcare.html        # Healthcare/Medical template
└── real-estate.html       # Real estate template
```

#### **Creating a New Template**

**Step 1: Create the HTML Template File**

Create a new file in the `templates/` directory (e.g., `templates/restaurant.html`):

```html
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>{{meta_title}}</title>
    <meta name="description" content="{{meta_description}}" />

    <!-- Bootstrap CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <!-- Font Awesome -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />

    <style>
      /* Template-specific styles */
      :root {
        --primary-color: #d4a574;
        --secondary-color: #8b4513;
        /* Add more custom variables */
      }

      /* Your custom CSS here */
    </style>
  </head>
  <body>
    <!-- Template content with placeholder variables -->
    <section class="hero-section">
      <div class="container">
        <h1>{{hero_title}}</h1>
        <p>{{hero_subtitle}}</p>
        <a href="#menu" class="btn btn-primary">{{cta_button}}</a>
      </div>
    </section>

    <!-- More sections... -->

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  </body>
</html>
```

**Step 2: Update the API Templates List**

Edit `api/landing-pages.php` and add your new template to the `getTemplates()` function:

```php
function getTemplates($db)
{
    try {
        $templates = [
            // ... existing templates ...
            [
                'id' => 'restaurant',
                'name' => 'Restaurant',
                'description' => 'Template for restaurants and food businesses',
                'preview' => '/templates/previews/restaurant.jpg',
                'category' => 'business'
            ]
        ];

        echo json_encode([
            'success' => true,
            'data' => $templates
        ]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Error retrieving templates: ' . $e->getMessage()]);
    }
}
```

**Step 3: Create Preview Image**

Create a preview image for your template:

- Size: 400x300 pixels
- Format: JPG or PNG
- Save as: `templates/previews/restaurant.jpg`

**Step 4: Add Demo Data**

Update the `createLandingPagesTableIfNotExists()` function in `api/landing-pages.php` to include demo data:

```php
if ($count == 0) {
    $demoPages = [
        // ... existing demo pages ...
        ['Restaurant Delizioso', 'restaurant-delizioso', 'restaurant', 'published', 890, 45]
    ];

    // ... rest of the function
}
```

#### **Template Variable System**

Templates use a placeholder variable system with double curly braces: `{{variable_name}}`

**Common Variables:**

- `{{meta_title}}` - Page title for SEO
- `{{meta_description}}` - Page description for SEO
- `{{hero_title}}` - Main headline
- `{{hero_subtitle}}` - Subheadline
- `{{hero_image}}` - Hero section image
- `{{cta_button}}` - Call-to-action button text
- `{{company_name}}` - Business name
- `{{contact_email}}` - Contact email
- `{{contact_phone}}` - Contact phone

**Template-Specific Variables:**

For Restaurant template:

- `{{menu_title}}` - Menu section title
- `{{chef_name}}` - Chef's name
- `{{restaurant_address}}` - Restaurant address
- `{{opening_hours}}` - Business hours

For SaaS template:

- `{{app_name}}` - Application name
- `{{pricing_title}}` - Pricing section title
- `{{plan1_name}}` - First pricing plan name
- `{{plan1_price}}` - First plan price

#### **Modifying Existing Templates**

**Step 1: Locate the Template File**

Find the template file in the `templates/` directory (e.g., `templates/ecommerce.html`)

**Step 2: Edit the HTML Structure**

Make your changes to the HTML structure, CSS styles, or JavaScript functionality.

**Step 3: Update Variables (if needed)**

If you add new placeholder variables, document them and ensure they're handled in the backend.

**Step 4: Test the Changes**

1. Access the dashboard: `http://localhost:8000/dashboard.html`
2. Go to Landing Pages section
3. Create a new page using your modified template
4. Verify all elements display correctly

#### **Template Categories**

Organize templates by category for better user experience:

- **business** - General business templates
- **ecommerce** - Online store templates
- **technology** - Tech/SaaS templates
- **creative** - Portfolio/Agency templates
- **healthcare** - Medical/Health templates
- **education** - School/Course templates
- **food** - Restaurant/Food templates
- **real-estate** - Property/Real estate templates

#### **Best Practices for Template Development**

**1. Responsive Design**

```css
/* Mobile-first approach */
@media (max-width: 768px) {
  .hero-section {
    padding: 60px 0;
    text-align: center;
  }
}
```

**2. Performance Optimization**

- Use modern CSS (Grid, Flexbox)
- Optimize images and use WebP format when possible
- Minimize external dependencies
- Use CSS variables for theming

**3. Accessibility**

- Include proper ARIA labels
- Ensure good color contrast
- Use semantic HTML elements
- Test with screen readers

**4. SEO Optimization**

- Include proper meta tags
- Use structured data markup
- Optimize heading hierarchy (H1, H2, H3)
- Include alt text for images

**5. Cross-browser Compatibility**

- Test on major browsers (Chrome, Firefox, Safari, Edge)
- Use CSS prefixes when needed
- Provide fallbacks for modern CSS features

#### **Template Testing Checklist**

Before deploying a new template:

- [ ] Responsive design works on mobile, tablet, desktop
- [ ] All placeholder variables render correctly
- [ ] Forms submit properly
- [ ] Images load and display correctly
- [ ] CSS animations work smoothly
- [ ] JavaScript functionality works
- [ ] SEO meta tags are present
- [ ] Accessibility standards are met
- [ ] Cross-browser compatibility verified
- [ ] Page load speed is optimized

#### **Troubleshooting Common Issues**

**Template Not Showing in Dashboard:**

1. Check if template is added to `getTemplates()` function
2. Verify template file exists in `templates/` directory
3. Check for PHP syntax errors in API file

**Variables Not Rendering:**

1. Ensure variables use correct syntax: `{{variable_name}}`
2. Check for typos in variable names
3. Verify backend processes the variables correctly

**Styling Issues:**

1. Check CSS syntax and selectors
2. Verify Bootstrap classes are used correctly
3. Test CSS specificity conflicts
4. Validate CSS with online tools

**JavaScript Errors:**

1. Check browser console for errors
2. Verify external script URLs are accessible
3. Test JavaScript syntax
4. Ensure DOM elements exist before manipulation

#### **Advanced Template Features**

**Dynamic Content Loading:**

```javascript
// Load content dynamically
async function loadTemplateContent(templateId) {
  try {
    const response = await fetch(`/api/templates/${templateId}/content`);
    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error loading template content:", error);
  }
}
```

**Template Inheritance:**
Create base templates that other templates can extend:

```html
<!-- base-template.html -->
<!DOCTYPE html>
<html>
  <head>
    <!-- Common head elements -->
  </head>
  <body>
    <header>{{header_content}}</header>
    <main>{{main_content}}</main>
    <footer>{{footer_content}}</footer>
  </body>
</html>
```

**Custom CSS Variables:**

```css
:root {
  --template-primary: #007bff;
  --template-secondary: #6c757d;
  --template-font-family: "Inter", sans-serif;
  --template-border-radius: 8px;
  --template-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}
```

This help documentation provides comprehensive guidance for developers and administrators to effectively manage and extend the landing page template system.

## 📋 Prérequis

### **Serveur**

- PHP 7.4+ avec extensions :
  - `pdo_mysql`
  - `json`
  - `curl`
  - `mbstring`
  - `gd` ou `imagick`
- MySQL 8.0+ ou MariaDB 10.4+
- Apache/Nginx avec mod_rewrite
- SSL/TLS (recommandé)

### **Développement**

- Composer (gestion des dépendances)
- Node.js 16+ (outils de build)
- Git

## 🚀 Installation

### **1. Clonage du Projet**

```bash
git clone https://github.com/votre-repo/landingpage-new.git
cd landingpage-new
```

### **2. Configuration de la Base de Données**

```bash
# Créer la base de données
mysql -u root -p
CREATE DATABASE `mossab-landing-page` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# Importer le schéma
mysql -u root -p mossab-landing-page < sql/init.sql
```

### **3. Configuration Environnement**

```bash
# Copier le fichier de configuration
cp .env.example .env

# Éditer les paramètres
nano .env
```

**Configuration `.env` :**

```env
# Base de données
DB_HOST=localhost
DB_PORT=3307
DB_USERNAME=root
DB_PASSWORD=votre_mot_de_passe
DB_DATABASE=mossab-landing-page
DB_CHARSET=utf8mb4

# APIs IA
OPENAI_API_KEY=sk-proj-...
ANTHROPIC_API_KEY=sk-ant-...
GEMINI_API_KEY=AIza...
DEFAULT_AI_PROVIDER=openai

# Firebase
FIREBASE_API_KEY=AIza...
FIREBASE_AUTH_DOMAIN=votre-projet.firebaseapp.com
FIREBASE_PROJECT_ID=votre-projet
# ... autres paramètres Firebase

# Email
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=votre_mot_de_passe
MAIL_FROM_NAME="صفحات هبوط للجميع"
```

### **4. Permissions Fichiers**

```bash
# Permissions pour les uploads et cache
chmod 755 uploads/
chmod 755 cache/
chmod 644 .env

# Sécuriser les fichiers sensibles
chmod 600 php/config/database.php
```

### **5. Configuration Serveur Web**

**Apache (.htaccess) :**

```apache
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]

# Sécurité
<Files ".env">
    Order allow,deny
    Deny from all
</Files>

# Compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Cache
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
</IfModule>
```

**Nginx :**

```nginx
server {
    listen 80;
    server_name votre-domaine.com;
    root /path/to/landingpage-new;
    index index.php;

    # Gestion des URLs
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    # PHP
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }

    # Sécurité
    location ~ /\. {
        deny all;
    }

    location ~* \.(env|sql|md)$ {
        deny all;
    }

    # Cache statique
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

## 🎯 Utilisation

### **1. Accès à la Plateforme**

```
http://votre-domaine.com/
```

### **2. Création d'une Landing Page**

1. Choisir un template
2. Personnaliser le contenu
3. Configurer les paramètres SEO
4. Publier la page

### **3. Gestion Newsletter**

```php
// Inscription
POST /php/newsletter.php
{
    "email": "<EMAIL>",
    "language": "ar",
    "source": "homepage"
}

// Désabonnement
GET /php/unsubscribe.php?email=<EMAIL>&lang=ar
```

### **4. API Analytics**

```php
// Tracking des événements
POST /php/analytics.php
{
    "event": "page_view",
    "page": "/landing/product-123",
    "user_id": "user_456",
    "metadata": {
        "source": "facebook",
        "campaign": "summer_sale"
    }
}
```

## 🏗️ Utilisation des Nouvelles Fonctionnalités

### **📧 Système d'Email SMTP**

**Configuration et utilisation :**

```php
// Initialisation du service email
require_once 'services/EmailService.php';
$emailService = new EmailService();

// Envoi d'email de nouvelle commande
$orderData = [
    'order_number' => 'ORD-2024-001',
    'customer_name' => 'Ahmed Benali',
    'total_amount' => 15000,
    'products' => [
        ['name' => 'Smartphone Samsung', 'quantity' => 1, 'price' => 15000]
    ]
];

$paymentData = [
    'method' => 'ccp',
    'ccp_number' => '1234567890',
    'transaction_id' => 'TXN123456'
];

$result = $emailService->sendNewOrderEmail(
    '<EMAIL>',
    'Ahmed Benali',
    'ORD-2024-001',
    $orderData,
    $paymentData,
    'ar' // langue
);

if ($result['success']) {
    echo "Email envoyé avec succès";
} else {
    echo "Erreur: " . $result['error'];
}
```

**Templates d'email disponibles :**

- Nouvelle commande : `templates/emails/new_order_{lang}.html`
- Mise à jour statut : `templates/emails/order_status_{lang}.html`
- Activation marchand : `templates/emails/merchant_activation_{lang}.html`

### **🤖 Génération de Contenu IA**

**Utilisation du service IA :**

```php
// Initialisation du service IA
require_once 'services/AIService.php';
$aiService = new AIService($database);

// Génération de titre produit
$productData = [
    'name' => 'Smartphone Samsung Galaxy',
    'category' => 'Electronics',
    'benefits' => ['Écran AMOLED', 'Batterie longue durée', 'Appareil photo 108MP']
];

$title = $aiService->generateProductTitle(
    $productData['name'],
    $productData['category'],
    $productData['benefits'],
    'ar' // langue
);

echo "Titre généré: " . $title;

// Génération de description produit
$description = $aiService->generateProductDescription($productData, 'fr');

// Génération de contenu landing page
$landingContent = $aiService->generateLandingPageContent($productData, 'en');

// Vérification des limites d'utilisation
$usage = $aiService->getUsageStats($merchantId);
echo "Tokens utilisés ce mois: " . $usage['monthly_tokens'];
echo "Limite: " . $usage['monthly_limit'];
```

**Gestion des prompts :**

- Modifier les templates dans `templates/ai_prompts/`
- Personnaliser par langue et type de contenu
- Optimiser pour le marché algérien

### **🔔 Notifications en Temps Réel**

**Configuration Firebase :**

```javascript
// Frontend - Initialisation Firebase
import { initializeApp } from "firebase/app";
import { getMessaging, getToken, onMessage } from "firebase/messaging";

const firebaseConfig = {
  apiKey: "your-api-key",
  authDomain: "your-project.firebaseapp.com",
  projectId: "your-project-id",
  messagingSenderId: "123456789",
};

const app = initializeApp(firebaseConfig);
const messaging = getMessaging(app);

// Demander permission et obtenir token
getToken(messaging, { vapidKey: "your-vapid-key" }).then((currentToken) => {
  if (currentToken) {
    console.log("Token FCM:", currentToken);
    // Envoyer le token au serveur
    saveTokenToServer(currentToken);
  }
});

// Écouter les messages
onMessage(messaging, (payload) => {
  console.log("Message reçu:", payload);
  showNotification(payload.notification.title, payload.notification.body);
});
```

**Backend - Envoi de notifications :**

```php
// Initialisation du service notifications
require_once 'services/NotificationService.php';
$notificationService = new NotificationService($database);

// Notification de nouvelle commande
$notification = [
    'type' => 'new_order',
    'title' => 'Nouvelle Commande',
    'message' => 'Commande #ORD-2024-001 reçue',
    'data' => [
        'order_id' => 'ORD-2024-001',
        'amount' => 15000,
        'customer' => 'Ahmed Benali'
    ]
];

// Envoyer à un utilisateur spécifique
$result = $notificationService->sendToUser($merchantId, $notification);

// Envoyer à un topic (tous les marchands)
$result = $notificationService->sendToTopic('merchants', $notification);

// Notification IA terminée
$aiNotification = [
    'type' => 'ai_generation_done',
    'title' => 'Contenu IA Prêt',
    'message' => 'La description de votre produit a été générée',
    'data' => [
        'product_id' => 123,
        'content_type' => 'description'
    ]
];

$notificationService->sendToUser($merchantId, $aiNotification);
```

### **💳 Méthodes de Paiement Algériennes**

**Interface de sélection de paiement :**

```html
<!-- Inclure dans votre page checkout -->
<link rel="stylesheet" href="templates/checkout/payment_selection.css" />
<div id="payment-container"></div>
<script src="templates/checkout/payment_selection.js"></script>

<script>
  // Initialiser l'interface de paiement
  const paymentUI = new PaymentSelection({
    container: "#payment-container",
    language: "ar", // ar, fr, en
    onPaymentSelect: function (method, data) {
      console.log("Méthode sélectionnée:", method, data);
    },
    onSubmit: function (orderData) {
      // Traiter la commande
      processOrder(orderData);
    },
  });

  paymentUI.init();
</script>
```

**Backend - Traitement des paiements :**

```php
// Initialisation du service paiement
require_once 'services/PaymentService.php';
$paymentService = new PaymentService($database);

// Données de commande
$orderData = [
    'customer_name' => 'Ahmed Benali',
    'customer_email' => '<EMAIL>',
    'customer_phone' => '+213555123456',
    'products' => [
        ['id' => 1, 'name' => 'Smartphone', 'price' => 15000, 'quantity' => 1]
    ],
    'total_amount' => 15000,
    'shipping_address' => 'Alger, Algérie'
];

// Données de paiement CCP
$paymentData = [
    'method' => 'ccp',
    'ccp_number' => '1234567890',
    'ccp_rip' => '12',
    'sender_name' => 'Ahmed Benali',
    'amount_sent' => 15000
];

// Fichiers uploadés (reçu de paiement)
$uploadedFiles = $_FILES;

// Traiter le paiement
$result = $paymentService->processPayment($orderData, $paymentData, $uploadedFiles);

if ($result['success']) {
    echo "Commande créée: " . $result['order_id'];

    // Envoyer email de confirmation
    $emailService->sendNewOrderEmail(
        $orderData['customer_email'],
        $orderData['customer_name'],
        $result['order_number'],
        $orderData,
        $paymentData
    );

    // Envoyer notification
    $notificationService->sendNewOrderNotification($result['order_id']);
} else {
    echo "Erreur: " . $result['error'];
}
```

**Validation et mise à jour des paiements :**

```php
// Valider un paiement (admin)
$paymentService->updatePaymentStatus(
    $orderId,
    'confirmed',
    'Reçu CCP vérifié et validé'
);

// Rejeter un paiement
$paymentService->updatePaymentStatus(
    $orderId,
    'rejected',
    'Reçu illisible, veuillez renvoyer'
);

// Obtenir statistiques des paiements
$stats = $paymentService->getPaymentStats();
echo "Total commandes: " . $stats['total_orders'];
echo "Paiements confirmés: " . $stats['confirmed_payments'];
echo "En attente: " . $stats['pending_payments'];
```

### **🛡️ Sécurité et Validation**

**Validation des fichiers uploadés :**

```php
// Le PaymentService inclut une validation automatique
$validationRules = [
    'max_size' => 5 * 1024 * 1024, // 5MB
    'allowed_types' => ['image/jpeg', 'image/png', 'application/pdf'],
    'required_fields' => ['ccp_number', 'amount_sent']
];

// Validation manuelle
$isValid = $paymentService->validatePaymentData($paymentData, $uploadedFiles);
if (!$isValid['valid']) {
    foreach ($isValid['errors'] as $error) {
        echo "Erreur: " . $error . "\n";
    }
}
```

**Audit et logs :**

````php
// Tous les paiements sont automatiquement loggés
// Consulter les logs dans la table payment_logs

$logs = $paymentService->getPaymentLogs($orderId);
foreach ($logs as $log) {
    echo $log['created_at'] . " - " . $log['action'] . ": " . $log['details'] . "\n";
}
```// Enregistrer un événement
POST /php/analytics.php
{
    "event_type": "page_view",
    "page_slug": "ma-landing-page",
    "event_data": {
        "section": "hero",
        "action": "cta_click"
    }
}
````

## 📁 Structure du Projet

```
landingpage-new/
├── 📁 css/                    # Styles CSS
│   ├── main.css              # Styles principaux
│   ├── components.css        # Composants UI
│   └── rtl.css              # Styles RTL
├── 📁 js/                     # Scripts JavaScript
│   ├── main.js               # Script principal
│   ├── firebase-config.js    # Configuration Firebase
│   └── components/           # Scripts des composants
├── 📁 php/                    # Backend PHP
│   ├── 📁 config/            # Configuration
│   │   └── database.php      # Connexion BDD
│   ├── 📁 includes/          # Fonctions utilitaires
│   │   └── functions.php     # Fonctions globales
│   ├── 📁 components/        # Composants PHP
│   │   ├── header.php        # En-tête
│   │   ├── hero.php          # Section hero
│   │   └── footer.php        # Pied de page
│   ├── 📁 lang/              # Traductions
│   │   ├── ar.php            # Arabe
│   │   ├── fr.php            # Français
│   │   └── en.php            # Anglais
│   ├── newsletter.php        # Gestion newsletter
│   ├── unsubscribe.php       # Désabonnement
│   └── change-language.php   # Changement langue
├── 📁 sql/                    # Base de données
│   └── init.sql              # Script d'initialisation
├── 📁 uploads/                # Fichiers uploadés
├── 📁 cache/                  # Cache système
├── index.php                 # Page principale
├── .env                      # Configuration
├── .htaccess                 # Configuration Apache
└── README.md                 # Documentation
```

## 🔌 API & Webhooks

### **API RESTful**

La plateforme expose une API complète pour l'intégration externe :

```bash
# Authentification
POST /api/auth/login
POST /api/auth/register
POST /api/auth/refresh

# Landing Pages
GET    /api/pages              # Liste des pages
POST   /api/pages              # Créer une page
GET    /api/pages/{id}         # Détails d'une page
PUT    /api/pages/{id}         # Modifier une page
DELETE /api/pages/{id}         # Supprimer une page

# Analytics
GET    /api/analytics/{pageId} # Statistiques d'une page
POST   /api/analytics/event    # Enregistrer un événement

# IA Content Generation
POST   /api/ai/generate        # Générer du contenu
POST   /api/ai/optimize        # Optimiser du contenu
```

### **Webhooks**

```php
// Configuration webhook
$webhook = [
    'url' => 'https://votre-app.com/webhook',
    'events' => ['page.created', 'page.published', 'conversion.tracked'],
    'secret' => 'your-webhook-secret'
];

// Exemple de payload
{
    "event": "page.published",
    "timestamp": "2024-01-15T10:30:00Z",
    "data": {
        "page_id": "page_123",
        "page_slug": "ma-landing-page",
        "user_id": "user_456"
    },
    "signature": "sha256=..."
}
```

### **SDK JavaScript**

```javascript
// Installation
npm install @landingpages/sdk

// Utilisation
import LandingPagesSDK from '@landingpages/sdk';

const sdk = new LandingPagesSDK({
    apiKey: 'your-api-key',
    baseUrl: 'https://api.landingpages.com'
});

// Créer une page
const page = await sdk.pages.create({
    title: 'Ma nouvelle page',
    template: 'business-pro',
    language: 'fr'
});

// Tracker des événements
sdk.analytics.track('conversion', {
    pageId: page.id,
    value: 99.99,
    currency: 'EUR'
});
```

## 🔧 Développement

### **Configuration de l'Environnement de Développement**

```bash
# Cloner le projet
git clone https://github.com/votre-repo/landingpage-new.git
cd landingpage-new

# Installer les dépendances
composer install
npm install

# Configuration
cp .env.example .env
php artisan key:generate

# Base de données
php artisan migrate
php artisan db:seed

# Démarrer le serveur de développement
php artisan serve
npm run dev
```

### **Ajout d'un Nouveau Template**

```php
// 1. Créer le fichier template
// templates/mon-template.php
<?php
class MonTemplate extends BaseTemplate {
    public function render($data = []) {
        return $this->view('templates.mon-template', $data);
    }

    public function getDefaultData() {
        return [
            'hero_title' => 'Titre par défaut',
            'hero_subtitle' => 'Sous-titre par défaut',
            'cta_text' => 'Commencer maintenant'
        ];
    }
}

// 2. Enregistrer le template
Template::create([
    'name' => 'Mon Template',
    'slug' => 'mon-template',
    'description' => 'Template pour les entreprises',
    'category' => 'business',
    'class' => 'MonTemplate',
    'preview_image' => 'templates/mon-template-preview.jpg',
    'status' => 'active'
]);

// 3. Créer les styles associés
// css/templates/mon-template.css
.mon-template {
    --template-primary: #667eea;
    --template-secondary: #764ba2;
}

.mon-template .hero {
    background: linear-gradient(135deg, var(--template-primary), var(--template-secondary));
    padding: 100px 0;
}
```

### **Ajout d'une Nouvelle Langue**

```php
// 1. Créer le fichier de traduction
// php/lang/es.php (pour l'espagnol)
<?php
return [
    'nav' => [
        'home' => 'Inicio',
        'features' => 'Características',
        'pricing' => 'Precios',
        'contact' => 'Contacto'
    ],
    'hero' => [
        'title' => 'Crea páginas de aterrizaje profesionales',
        'subtitle' => 'En minutos, sin conocimientos técnicos',
        'cta' => 'Empezar ahora'
    ],
    // ... autres traductions
];

// 2. Ajouter dans la configuration
// config/app.php
'supported_locales' => ['ar', 'fr', 'en', 'es'],
'rtl_locales' => ['ar'],

// 3. Mettre à jour le middleware de langue
// app/Http/Middleware/SetLocale.php
public function handle($request, Closure $next) {
    $locale = $request->segment(1);

    if (in_array($locale, config('app.supported_locales'))) {
        app()->setLocale($locale);
    }

    return $next($request);
}
```

### **Création d'un Composant Personnalisé**

```php
// php/components/CustomComponent.php
<?php
class CustomComponent extends BaseComponent {
    protected $name = 'custom-component';
    protected $category = 'content';

    public function render($data = []) {
        $defaults = [
            'title' => '',
            'content' => '',
            'style' => 'default',
            'animation' => 'fade-in'
        ];

        $data = array_merge($defaults, $data);

        return view('components.custom-component', $data);
    }

    public function getEditableFields() {
        return [
            'title' => ['type' => 'text', 'label' => 'Titre'],
            'content' => ['type' => 'textarea', 'label' => 'Contenu'],
            'style' => ['type' => 'select', 'options' => ['default', 'modern', 'minimal']],
            'animation' => ['type' => 'select', 'options' => ['none', 'fade-in', 'slide-up']]
        ];
    }
}
```

### **Personnalisation CSS Avancée**

```css
/* Variables CSS personnalisables */
:root {
  /* Couleurs principales */
  --primary-color: #667eea;
  --primary-dark: #5a67d8;
  --primary-light: #7c8aed;

  --secondary-color: #764ba2;
  --accent-color: #f093fb;

  /* Couleurs système */
  --success-color: #48bb78;
  --warning-color: #ed8936;
  --error-color: #f56565;
  --info-color: #4299e1;

  /* Typographie */
  --font-family-primary: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
  --font-family-heading: "Poppins", var(--font-family-primary);
  --font-family-mono: "Fira Code", monospace;

  /* Espacements */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;

  /* Bordures */
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;
  --border-radius-xl: 16px;

  /* Ombres */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.15);

  /* Transitions */
  --transition-fast: 150ms ease;
  --transition-normal: 300ms ease;
  --transition-slow: 500ms ease;
}

/* Thème sombre */
[data-theme="dark"] {
  --primary-color: #7c8aed;
  --text-color: #f7fafc;
  --background-color: #1a202c;
  --surface-color: #2d3748;
  --border-color: #4a5568;
}

/* Support RTL */
[dir="rtl"] {
  --text-align: right;
  --margin-start: margin-right;
  --margin-end: margin-left;
  --padding-start: padding-right;
  --padding-end: padding-left;
}

/* Responsive breakpoints */
@media (max-width: 640px) {
  :root {
    --spacing-lg: 1rem;
    --spacing-xl: 1.5rem;
    --spacing-2xl: 2rem;
  }
}
```

## 📊 Monitoring & Analytics

### **Métriques Clés**

- Taux de conversion par page
- Temps passé sur la page
- Sources de trafic
- Performances des CTA
- Taux d'ouverture newsletter

### **Logs Système**

```php
// Consultation des logs
SELECT * FROM system_logs
WHERE level IN ('error', 'critical')
AND created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR);

// Statistiques newsletter
SELECT * FROM newsletter_overview;

// Analytics pages
SELECT page_slug, COUNT(*) as views
FROM page_analytics
WHERE event_type = 'page_view'
GROUP BY page_slug
ORDER BY views DESC;
```

## 🔒 Sécurité Avancée

### **Architecture de Sécurité Multi-Couches**

```php
// SecurityManager.php - Gestionnaire de sécurité centralisé
class SecurityManager {
    private $config;
    private $logger;

    public function __construct() {
        $this->config = require 'config/security.php';
        $this->logger = new SecurityLogger();
    }

    // Authentification renforcée
    public function authenticateUser($email, $password, $ip, $userAgent) {
        // 1. Vérification rate limiting
        if ($this->isRateLimited($ip, 'login')) {
            $this->logger->logSuspiciousActivity($ip, 'Rate limit exceeded');
            throw new SecurityException('Trop de tentatives de connexion');
        }

        // 2. Validation de l'email
        if (!$this->validateEmail($email)) {
            $this->incrementFailedAttempts($ip);
            throw new ValidationException('Email invalide');
        }

        // 3. Vérification du mot de passe
        $user = $this->getUserByEmail($email);
        if (!$user || !password_verify($password, $user['password_hash'])) {
            $this->incrementFailedAttempts($ip);
            $this->logger->logFailedLogin($email, $ip, $userAgent);
            throw new AuthenticationException('Identifiants incorrects');
        }

        // 4. Vérification 2FA si activé
        if ($user['two_factor_enabled']) {
            return $this->initiateTwoFactorAuth($user);
        }

        // 5. Création de session sécurisée
        return $this->createSecureSession($user, $ip, $userAgent);
    }

    // Protection CSRF avancée
    public function generateCSRFToken($action = 'default') {
        $token = bin2hex(random_bytes(32));
        $expiry = time() + 3600; // 1 heure

        $_SESSION['csrf_tokens'][$action] = [
            'token' => hash('sha256', $token),
            'expiry' => $expiry
        ];

        return $token;
    }

    public function validateCSRFToken($token, $action = 'default') {
        if (!isset($_SESSION['csrf_tokens'][$action])) {
            return false;
        }

        $stored = $_SESSION['csrf_tokens'][$action];

        if (time() > $stored['expiry']) {
            unset($_SESSION['csrf_tokens'][$action]);
            return false;
        }

        return hash_equals($stored['token'], hash('sha256', $token));
    }

    // Détection d'intrusion
    public function detectSuspiciousActivity($request) {
        $suspiciousPatterns = [
            '/\b(union|select|insert|delete|drop|create|alter)\b/i', // SQL Injection
            '/<script[^>]*>.*?<\/script>/i', // XSS
            '/\.\.\//', // Path Traversal
            '/\b(eval|exec|system|shell_exec)\b/i' // Code Injection
        ];

        foreach ($request as $key => $value) {
            foreach ($suspiciousPatterns as $pattern) {
                if (preg_match($pattern, $value)) {
                    $this->logger->logSecurityThreat($key, $value, $_SERVER['REMOTE_ADDR']);
                    return true;
                }
            }
        }

        return false;
    }
}
```

### **Validation et Sanitisation Avancées**

```php
// InputValidator.php
class InputValidator {
    private $rules = [
        'email' => [
            'filter' => FILTER_VALIDATE_EMAIL,
            'max_length' => 255,
            'required' => true
        ],
        'password' => [
            'min_length' => 8,
            'max_length' => 128,
            'require_uppercase' => true,
            'require_lowercase' => true,
            'require_numbers' => true,
            'require_special' => true
        ],
        'phone' => [
            'pattern' => '/^[+]?[0-9\s\-\(\)]{10,20}$/',
            'sanitize' => true
        ],
        'url' => [
            'filter' => FILTER_VALIDATE_URL,
            'allowed_schemes' => ['http', 'https']
        ]
    ];

    public function validate($data, $type, $customRules = []) {
        $rules = array_merge($this->rules[$type] ?? [], $customRules);

        // Validation de base
        if ($rules['required'] ?? false && empty($data)) {
            throw new ValidationException("Le champ $type est requis");
        }

        // Validation de longueur
        if (isset($rules['max_length']) && strlen($data) > $rules['max_length']) {
            throw new ValidationException("Le champ $type est trop long");
        }

        if (isset($rules['min_length']) && strlen($data) < $rules['min_length']) {
            throw new ValidationException("Le champ $type est trop court");
        }

        // Validation par filtre
        if (isset($rules['filter'])) {
            if (!filter_var($data, $rules['filter'])) {
                throw new ValidationException("Le format du champ $type est invalide");
            }
        }

        // Validation par pattern
        if (isset($rules['pattern'])) {
            if (!preg_match($rules['pattern'], $data)) {
                throw new ValidationException("Le format du champ $type est invalide");
            }
        }

        // Validation spécifique pour les mots de passe
        if ($type === 'password') {
            $this->validatePassword($data, $rules);
        }

        return $this->sanitize($data, $rules);
    }

    private function validatePassword($password, $rules) {
        if ($rules['require_uppercase'] && !preg_match('/[A-Z]/', $password)) {
            throw new ValidationException('Le mot de passe doit contenir au moins une majuscule');
        }

        if ($rules['require_lowercase'] && !preg_match('/[a-z]/', $password)) {
            throw new ValidationException('Le mot de passe doit contenir au moins une minuscule');
        }

        if ($rules['require_numbers'] && !preg_match('/[0-9]/', $password)) {
            throw new ValidationException('Le mot de passe doit contenir au moins un chiffre');
        }

        if ($rules['require_special'] && !preg_match('/[^A-Za-z0-9]/', $password)) {
            throw new ValidationException('Le mot de passe doit contenir au moins un caractère spécial');
        }
    }

    private function sanitize($data, $rules) {
        if ($rules['sanitize'] ?? false) {
            $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
            $data = strip_tags($data);
        }

        return trim($data);
    }
}
```

### **Headers de Sécurité et CSP**

```php
// SecurityHeaders.php
class SecurityHeaders {
    public static function setSecurityHeaders() {
        // Protection XSS
        header('X-XSS-Protection: 1; mode=block');

        // Protection contre le sniffing MIME
        header('X-Content-Type-Options: nosniff');

        // Protection contre le clickjacking
        header('X-Frame-Options: DENY');

        // HSTS (HTTPS Strict Transport Security)
        header('Strict-Transport-Security: max-age=31536000; includeSubDomains; preload');

        // Politique de référent
        header('Referrer-Policy: strict-origin-when-cross-origin');

        // Permissions Policy
        header('Permissions-Policy: geolocation=(), microphone=(), camera=()');

        // Content Security Policy
        $csp = [
            "default-src 'self'",
            "script-src 'self' 'unsafe-inline' https://apis.google.com https://cdn.jsdelivr.net",
            "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
            "font-src 'self' https://fonts.gstatic.com",
            "img-src 'self' data: https:",
            "connect-src 'self' https://api.openai.com https://sheets.googleapis.com",
            "frame-src 'none'",
            "object-src 'none'",
            "base-uri 'self'",
            "form-action 'self'"
        ];

        header('Content-Security-Policy: ' . implode('; ', $csp));
    }
}
```

### **Audit et Logging de Sécurité**

```php
// SecurityLogger.php
class SecurityLogger {
    private $logFile;

    public function __construct() {
        $this->logFile = '/var/log/landingpage/security.log';
    }

    public function logSecurityEvent($level, $event, $details = []) {
        $logEntry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'level' => $level,
            'event' => $event,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'session_id' => session_id(),
            'details' => $details
        ];

        // Log vers fichier
        file_put_contents($this->logFile, json_encode($logEntry) . "\n", FILE_APPEND | LOCK_EX);

        // Log vers base de données pour les événements critiques
        if (in_array($level, ['CRITICAL', 'HIGH'])) {
            $this->logToDatabase($logEntry);
        }

        // Alertes en temps réel pour les menaces
        if ($level === 'CRITICAL') {
            $this->sendSecurityAlert($logEntry);
        }
    }

    public function logFailedLogin($email, $ip, $userAgent) {
        $this->logSecurityEvent('MEDIUM', 'FAILED_LOGIN', [
            'email' => $email,
            'ip' => $ip,
            'user_agent' => $userAgent
        ]);
    }

    public function logSuspiciousActivity($ip, $reason) {
        $this->logSecurityEvent('HIGH', 'SUSPICIOUS_ACTIVITY', [
            'ip' => $ip,
            'reason' => $reason,
            'request_uri' => $_SERVER['REQUEST_URI'] ?? 'unknown'
        ]);
    }

    public function logSecurityThreat($field, $value, $ip) {
        $this->logSecurityEvent('CRITICAL', 'SECURITY_THREAT', [
            'field' => $field,
            'value' => substr($value, 0, 100), // Limiter la taille
            'ip' => $ip,
            'request_method' => $_SERVER['REQUEST_METHOD'] ?? 'unknown'
        ]);
    }

    private function sendSecurityAlert($logEntry) {
        // Envoi d'alerte par email/Slack
        $message = "🚨 ALERTE SÉCURITÉ CRITIQUE\n";
        $message .= "Événement: {$logEntry['event']}\n";
        $message .= "IP: {$logEntry['ip']}\n";
        $message .= "Détails: " . json_encode($logEntry['details']);

        // Webhook Slack
        $this->sendSlackAlert($message);
    }

    private function sendSlackAlert($message) {
        $webhookUrl = $_ENV['SLACK_SECURITY_WEBHOOK'];
        if ($webhookUrl) {
            $payload = json_encode(['text' => $message]);

            $ch = curl_init($webhookUrl);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);
            curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_exec($ch);
            curl_close($ch);
        }
    }
}
```

### **Configuration de Sécurité**

```php
// config/security.php
return [
    'rate_limiting' => [
        'login' => [
            'max_attempts' => 5,
            'window' => 900, // 15 minutes
            'lockout_duration' => 1800 // 30 minutes
        ],
        'api' => [
            'max_requests' => 100,
            'window' => 3600 // 1 heure
        ]
    ],

    'session' => [
        'lifetime' => 7200, // 2 heures
        'regenerate_interval' => 300, // 5 minutes
        'secure' => true,
        'httponly' => true,
        'samesite' => 'Strict'
    ],

    'password' => [
        'min_length' => 12,
        'require_complexity' => true,
        'history_check' => 5, // Derniers 5 mots de passe
        'expiry_days' => 90
    ],

    'file_upload' => [
        'max_size' => 10485760, // 10MB
        'allowed_types' => ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx'],
        'scan_for_malware' => true,
        'quarantine_suspicious' => true
    ],

    'encryption' => [
        'algorithm' => 'AES-256-GCM',
        'key_rotation_days' => 30
    ]
];
```

### **Recommandations de Déploiement Sécurisé**

```bash
# Mise à jour régulière
sudo apt update && sudo apt upgrade

# Firewall avancé
sudo ufw enable
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow 22/tcp

# Fail2ban pour la protection contre les attaques par force brute
sudo apt install fail2ban
sudo systemctl enable fail2ban

# SSL/TLS avec certificats Let's Encrypt
certbot --apache -d votre-domaine.com

# Sauvegarde chiffrée automatique
0 2 * * * gpg --cipher-algo AES256 --compress-algo 1 --s2k-mode 3 --s2k-digest-algo SHA512 --s2k-count 65536 --symmetric --output /backup/db_$(date +%Y%m%d).sql.gpg <(mysqldump -u root -p mossab-landing-page)

# Monitoring des logs de sécurité
*/5 * * * * /usr/local/bin/security-monitor.sh
```

## 🚀 Déploiement & Production

### **Déploiement Automatisé**

```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: "8.1"
          extensions: pdo_mysql, gd, curl

      - name: Install dependencies
        run: |
          composer install --no-dev --optimize-autoloader
          npm ci
          npm run build

      - name: Run tests
        run: |
          php artisan test
          npm run test

      - name: Deploy to server
        uses: appleboy/ssh-action@v0.1.5
        with:
          host: ${{ secrets.HOST }}
          username: ${{ secrets.USERNAME }}
          key: ${{ secrets.PRIVATE_KEY }}
          script: |
            cd /var/www/landingpage
            git pull origin main
            composer install --no-dev
            php artisan migrate --force
            php artisan config:cache
            php artisan route:cache
            php artisan view:cache
            sudo systemctl reload nginx
```

### **Configuration Production**

```bash
# Optimisations serveur
sudo apt update && sudo apt upgrade -y

# PHP-FPM optimisé
sudo nano /etc/php/8.1/fpm/pool.d/www.conf
# pm = dynamic
# pm.max_children = 50
# pm.start_servers = 5
# pm.min_spare_servers = 5
# pm.max_spare_servers = 35
# pm.max_requests = 500

# MySQL optimisé
sudo nano /etc/mysql/mysql.conf.d/mysqld.cnf
# innodb_buffer_pool_size = 1G
# innodb_log_file_size = 256M
# query_cache_size = 128M
# max_connections = 200

# Redis pour le cache
sudo apt install redis-server
sudo systemctl enable redis-server
```

### **Monitoring & Alertes**

```bash
# Installation de monitoring
sudo apt install prometheus node-exporter

# Grafana pour les dashboards
sudo apt install grafana

# Logs centralisés
sudo apt install elasticsearch logstash kibana

# Alertes Slack/Email
curl -X POST -H 'Content-type: application/json' \
  --data '{"text":"🚨 Landing Page Platform Alert: High CPU usage detected"}' \
  $SLACK_WEBHOOK_URL
```

### **Sauvegarde Automatique**

```bash
#!/bin/bash
# backup.sh
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/landingpage"

# Base de données
mysqldump -u root -p$DB_PASSWORD landingpage_db > "$BACKUP_DIR/db_$DATE.sql"

# Fichiers
tar -czf "$BACKUP_DIR/files_$DATE.tar.gz" /var/www/landingpage/uploads

# Upload vers S3
aws s3 cp "$BACKUP_DIR/" s3://landingpage-backups/ --recursive

# Nettoyage (garder 30 jours)
find $BACKUP_DIR -name "*.sql" -mtime +30 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete

# Crontab: 0 2 * * * /path/to/backup.sh
```

## ⚡ Optimisation Performances

### **Frontend Optimisé**

```javascript
// Lazy loading avancé
const observer = new IntersectionObserver((entries) => {
  entries.forEach((entry) => {
    if (entry.isIntersecting) {
      const img = entry.target;
      img.src = img.dataset.src;
      img.classList.remove("lazy");
      observer.unobserve(img);
    }
  });
});

document.querySelectorAll("img[data-src]").forEach((img) => {
  observer.observe(img);
});

// Service Worker pour le cache
if ("serviceWorker" in navigator) {
  navigator.serviceWorker
    .register("/sw.js")
    .then((registration) => console.log("SW registered"))
    .catch((error) => console.log("SW registration failed"));
}

// Préchargement des ressources critiques
const preloadLink = document.createElement("link");
preloadLink.rel = "preload";
preloadLink.as = "font";
preloadLink.href = "/fonts/inter-var.woff2";
preloadLink.crossOrigin = "anonymous";
document.head.appendChild(preloadLink);
```

### **Backend Optimisé**

```php
// Cache Redis
class CacheManager {
    private $redis;

    public function __construct() {
        $this->redis = new Redis();
        $this->redis->connect('127.0.0.1', 6379);
    }

    public function remember($key, $ttl, $callback) {
        $cached = $this->redis->get($key);

        if ($cached !== false) {
            return json_decode($cached, true);
        }

        $result = $callback();
        $this->redis->setex($key, $ttl, json_encode($result));

        return $result;
    }
}

// Optimisation des requêtes
class PageRepository {
    public function getPublishedPages($limit = 10) {
        return Cache::remember('published_pages_' . $limit, 3600, function() use ($limit) {
            return DB::table('landing_pages')
                ->select(['id', 'title', 'slug', 'created_at'])
                ->where('status', 'published')
                ->orderBy('created_at', 'desc')
                ->limit($limit)
                ->get();
        });
    }
}

// Pool de connexions
class DatabasePool {
    private static $connections = [];
    private static $maxConnections = 10;

    public static function getConnection() {
        if (count(self::$connections) < self::$maxConnections) {
            $pdo = new PDO($dsn, $username, $password, [
                PDO::ATTR_PERSISTENT => true,
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => false
            ]);

            self::$connections[] = $pdo;
            return $pdo;
        }

        return self::$connections[array_rand(self::$connections)];
    }
}
```

### **Base de Données Optimisée**

```sql
-- Index composites pour les requêtes fréquentes
CREATE INDEX idx_pages_status_created ON landing_pages(status, created_at DESC);
CREATE INDEX idx_analytics_page_date ON page_analytics(page_id, created_at);
CREATE INDEX idx_newsletter_email_status ON newsletter_subscribers(email, status);

-- Partitioning pour les grandes tables
ALTER TABLE page_analytics
PARTITION BY RANGE (YEAR(created_at)) (
    PARTITION p2023 VALUES LESS THAN (2024),
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p2025 VALUES LESS THAN (2026)
);

-- Procédures stockées pour les statistiques
DELIMITER //
CREATE PROCEDURE GetPageStats(IN page_id INT, IN date_from DATE, IN date_to DATE)
BEGIN
    SELECT
        DATE(created_at) as date,
        COUNT(*) as views,
        COUNT(DISTINCT session_id) as unique_visitors,
        SUM(CASE WHEN event_type = 'conversion' THEN 1 ELSE 0 END) as conversions
    FROM page_analytics
    WHERE page_id = page_id
        AND created_at BETWEEN date_from AND date_to
    GROUP BY DATE(created_at)
    ORDER BY date;
END //
DELIMITER ;

-- Nettoyage automatique des données anciennes
CREATE EVENT CleanOldAnalytics
ON SCHEDULE EVERY 1 DAY
STARTS CURRENT_TIMESTAMP
DO
  DELETE FROM page_analytics
  WHERE created_at < DATE_SUB(NOW(), INTERVAL 2 YEAR);

-- Optimisation des requêtes lentes
SET long_query_time = 2;
SET slow_query_log = 1;
SET slow_query_log_file = '/var/log/mysql/slow-query.log';

-- Analyse des performances
SELECT
    SCHEMA_NAME as 'Database',
    TABLE_NAME as 'Table',
    ROUND(((DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024), 2) as 'Size (MB)'
FROM information_schema.TABLES
WHERE SCHEMA_NAME = 'landingpage_db'
ORDER BY (DATA_LENGTH + INDEX_LENGTH) DESC;
```

## 🔧 Dépannage Avancé

### **Diagnostic Système**

```bash
#!/bin/bash
# diagnostic.sh - Script de diagnostic complet

echo "=== DIAGNOSTIC LANDING PAGE PLATFORM ==="
echo "Date: $(date)"
echo "Serveur: $(hostname)"
echo "Uptime: $(uptime)"
echo ""

# Vérification des services
echo "=== SERVICES ==="
services=("nginx" "php8.1-fpm" "mysql" "redis-server")
for service in "${services[@]}"; do
    if systemctl is-active --quiet $service; then
        echo "✅ $service: ACTIF"
    else
        echo "❌ $service: INACTIF"
        systemctl status $service --no-pager -l
    fi
done

# Vérification des ports
echo "\n=== PORTS ==="
ports=("80:nginx" "443:nginx" "3306:mysql" "6379:redis" "9000:php-fpm")
for port_service in "${ports[@]}"; do
    port=$(echo $port_service | cut -d: -f1)
    service=$(echo $port_service | cut -d: -f2)
    if netstat -tuln | grep -q ":$port "; then
        echo "✅ Port $port ($service): OUVERT"
    else
        echo "❌ Port $port ($service): FERMÉ"
    fi
done

# Vérification de l'espace disque
echo "\n=== ESPACE DISQUE ==="
df -h | grep -E '(Filesystem|/dev/)'

# Vérification de la mémoire
echo "\n=== MÉMOIRE ==="
free -h

# Vérification des logs d'erreur
echo "\n=== LOGS D'ERREUR RÉCENTS ==="
echo "--- Nginx ---"
tail -5 /var/log/nginx/error.log 2>/dev/null || echo "Aucun log nginx"
echo "--- PHP ---"
tail -5 /var/log/php8.1-fpm.log 2>/dev/null || echo "Aucun log PHP"
echo "--- MySQL ---"
tail -5 /var/log/mysql/error.log 2>/dev/null || echo "Aucun log MySQL"

# Test de connectivité base de données
echo "\n=== TEST BASE DE DONNÉES ==="
mysql -u root -p$DB_PASSWORD -e "SELECT 'Connexion OK' as status;" 2>/dev/null && echo "✅ MySQL: Connexion réussie" || echo "❌ MySQL: Échec de connexion"

# Test Redis
echo "\n=== TEST REDIS ==="
redis-cli ping 2>/dev/null && echo "✅ Redis: Connexion réussie" || echo "❌ Redis: Échec de connexion"

echo "\n=== FIN DU DIAGNOSTIC ==="
```

### **Solutions par Problème**

#### **🔴 Erreur 500 - Erreur Serveur Interne**

```bash
# 1. Vérifier les logs PHP
tail -f /var/log/php8.1-fpm.log
tail -f /var/log/nginx/error.log

# 2. Vérifier les permissions
sudo chown -R www-data:www-data /var/www/landingpage
sudo chmod -R 755 /var/www/landingpage
sudo chmod -R 775 /var/www/landingpage/uploads
sudo chmod -R 775 /var/www/landingpage/cache

# 3. Vérifier la configuration PHP
php -m | grep -E '(pdo_mysql|gd|curl|mbstring|xml)'

# 4. Test de syntaxe PHP
php -l /var/www/landingpage/index.php

# 5. Redémarrer les services
sudo systemctl restart php8.1-fpm nginx
```

#### **🔴 Erreur de Base de Données**

```bash
# 1. Vérifier la connexion
mysql -u $DB_USERNAME -p$DB_PASSWORD -h $DB_HOST -P $DB_PORT -e "SHOW DATABASES;"

# 2. Vérifier l'espace disque MySQL
SELECT
    table_schema AS 'Database',
    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'Size (MB)'
FROM information_schema.tables
GROUP BY table_schema;

# 3. Vérifier les processus MySQL
SHOW PROCESSLIST;

# 4. Optimiser les tables
OPTIMIZE TABLE landing_pages, page_analytics, users;

# 5. Réparer si nécessaire
REPAIR TABLE landing_pages;

# 6. Redémarrer MySQL
sudo systemctl restart mysql
```

#### **🔴 Problème de Performance**

```bash
# 1. Analyser les requêtes lentes
mysql -u root -p -e "SELECT * FROM mysql.slow_log ORDER BY start_time DESC LIMIT 10;"

# 2. Vérifier l'utilisation CPU/RAM
top -p $(pgrep -d',' php-fpm)
htop

# 3. Analyser les logs d'accès
tail -f /var/log/nginx/access.log | awk '{print $7}' | sort | uniq -c | sort -nr

# 4. Vérifier le cache Redis
redis-cli info memory
redis-cli info stats

# 5. Optimiser PHP-FPM
# Éditer /etc/php/8.1/fpm/pool.d/www.conf
pm.max_children = 50
pm.start_servers = 10
pm.min_spare_servers = 5
pm.max_spare_servers = 20
pm.max_requests = 500

# 6. Redémarrer PHP-FPM
sudo systemctl restart php8.1-fpm
```

#### **🔴 Problème SSL/HTTPS**

```bash
# 1. Vérifier le certificat
openssl x509 -in /etc/ssl/certs/landingpage.crt -text -noout

# 2. Tester la configuration SSL
ssl-cert-check -c /etc/ssl/certs/landingpage.crt

# 3. Renouveler Let's Encrypt
sudo certbot renew --dry-run
sudo certbot renew

# 4. Vérifier la configuration Nginx
sudo nginx -t

# 5. Redémarrer Nginx
sudo systemctl restart nginx
```

#### **🔴 Problème d'Upload de Fichiers**

```bash
# 1. Vérifier les limites PHP
php -i | grep -E '(upload_max_filesize|post_max_size|max_execution_time)'

# 2. Modifier php.ini
sudo nano /etc/php/8.1/fpm/php.ini
# upload_max_filesize = 50M
# post_max_size = 50M
# max_execution_time = 300
# memory_limit = 256M

# 3. Vérifier les permissions du dossier uploads
ls -la /var/www/landingpage/uploads/
sudo chown -R www-data:www-data /var/www/landingpage/uploads/
sudo chmod -R 775 /var/www/landingpage/uploads/

# 4. Redémarrer PHP-FPM
sudo systemctl restart php8.1-fpm
```

### **🔍 Outils de Monitoring**

```bash
# Installation d'outils de monitoring
sudo apt install htop iotop nethogs ncdu

# Monitoring en temps réel
# CPU et RAM
htop

# I/O disque
sudo iotop

# Trafic réseau
sudo nethogs

# Utilisation disque
ncdu /var/www/landingpage

# Logs en temps réel
sudo tail -f /var/log/nginx/access.log /var/log/nginx/error.log /var/log/php8.1-fpm.log

# Statistiques MySQL
mysql -u root -p -e "SHOW STATUS LIKE 'Threads%'; SHOW STATUS LIKE 'Questions'; SHOW STATUS LIKE 'Uptime';"

# Statistiques Redis
redis-cli info all
```

## 📞 Support

### **Documentation**

- 📖 [Wiki du projet](https://github.com/votre-repo/wiki)
- 🎥 [Tutoriels vidéo](https://youtube.com/playlist)
- 💬 [Forum communauté](https://forum.example.com)

### **Contact**

- 📧 Email: <EMAIL>
- 💬 Discord: [Serveur communauté](https://discord.gg/)
- 🐛 Issues: [GitHub Issues](https://github.com/votre-repo/issues)

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier [LICENSE](LICENSE) pour plus de détails.

## 🤝 Contribution

Les contributions sont les bienvenues ! Consultez notre [guide de contribution](CONTRIBUTING.md).

### **Processus de Contribution**

1. Fork le projet
2. Créer une branche feature (`git checkout -b feature/AmazingFeature`)
3. Commit les changements (`git commit -m 'Add AmazingFeature'`)
4. Push vers la branche (`git push origin feature/AmazingFeature`)
5. Ouvrir une Pull Request

## 🎉 Remerciements

- [Swiper.js](https://swiperjs.com/) pour les carrousels
- [Firebase](https://firebase.google.com/) pour l'infrastructure
- [OpenAI](https://openai.com/) pour l'IA générative
- La communauté open source

---

**Fait avec ❤️ pour la communauté arabophone et francophone**

_صفحات هبوط للجميع - Landing Pages for Everyone_
