<?php
require_once 'php/config/database.php';

try {
    echo "🔍 Correction des catégories pour le store 3...\n\n";
    
    // Trouver le merchant_id du store 3
    $storeQuery = "SELECT id, merchant_id, store_name FROM stores WHERE id = 3";
    $storeStmt = $pdo->prepare($storeQuery);
    $storeStmt->execute();
    $store = $storeStmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$store) {
        echo "❌ Store ID 3 non trouvé\n";
        exit;
    }
    
    echo "✅ Store trouvé: {$store['store_name']} (Merchant ID: {$store['merchant_id']})\n";
    $merchantId = $store['merchant_id'];
    
    // Vérifier si des catégories existent déjà pour ce merchant
    $existingQuery = "SELECT COUNT(*) as count FROM categories WHERE user_id = ?";
    $existingStmt = $pdo->prepare($existingQuery);
    $existingStmt->execute([$merchantId]);
    $existingCount = $existingStmt->fetch()['count'];
    
    echo "📊 Catégories existantes pour merchant {$merchantId}: {$existingCount}\n";
    
    if ($existingCount == 0) {
        echo "\n🔧 Création des catégories pour le merchant {$merchantId}...\n";
        
        // Catégories à créer
        $categories = [
            [
                'name' => 'Électronique',
                'name_ar' => 'إلكترونيات',
                'name_fr' => 'Électronique',
                'name_en' => 'Electronics',
                'slug' => 'electronique',
                'color' => '#007bff',
                'icon' => 'fas fa-microchip'
            ],
            [
                'name' => 'Informatique',
                'name_ar' => 'معلوماتية',
                'name_fr' => 'Informatique',
                'name_en' => 'Computing',
                'slug' => 'informatique',
                'color' => '#28a745',
                'icon' => 'fas fa-laptop'
            ],
            [
                'name' => 'Smartphones',
                'name_ar' => 'هواتف ذكية',
                'name_fr' => 'Smartphones',
                'name_en' => 'Smartphones',
                'slug' => 'smartphones',
                'color' => '#17a2b8',
                'icon' => 'fas fa-mobile-alt'
            ],
            [
                'name' => 'Audio/Vidéo',
                'name_ar' => 'صوت/فيديو',
                'name_fr' => 'Audio/Vidéo',
                'name_en' => 'Audio/Video',
                'slug' => 'audio-video',
                'color' => '#ffc107',
                'icon' => 'fas fa-headphones'
            ],
            [
                'name' => 'Gaming',
                'name_ar' => 'ألعاب',
                'name_fr' => 'Gaming',
                'name_en' => 'Gaming',
                'slug' => 'gaming',
                'color' => '#dc3545',
                'icon' => 'fas fa-gamepad'
            ]
        ];
        
        foreach ($categories as $index => $cat) {
            $insertQuery = "
                INSERT INTO categories (
                    user_id, name, name_ar, name_fr, name_en, slug, description,
                    description_ar, description_fr, description_en, color, icon,
                    category_type, sort_order, status, created_at, updated_at
                ) VALUES (
                    ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'category', ?, 'active', NOW(), NOW()
                )
            ";
            
            $description = "Catégorie " . $cat['name'];
            $description_ar = "فئة " . $cat['name_ar'];
            
            $insertStmt = $pdo->prepare($insertQuery);
            $result = $insertStmt->execute([
                $merchantId,
                $cat['name'],
                $cat['name_ar'],
                $cat['name_fr'],
                $cat['name_en'],
                $cat['slug'],
                $description,
                $description_ar,
                $description,
                $description,
                $cat['color'],
                $cat['icon'],
                $index + 1
            ]);
            
            if ($result) {
                $categoryId = $pdo->lastInsertId();
                echo "  ✅ Catégorie '{$cat['name']}' créée (ID: {$categoryId})\n";
            } else {
                echo "  ❌ Erreur lors de la création de '{$cat['name']}'\n";
            }
        }
    } else {
        echo "ℹ️ Des catégories existent déjà pour ce merchant\n";
    }
    
    // Vérifier le résultat final
    echo "\n📊 Résumé final:\n";
    $finalQuery = "SELECT id, name, name_ar, slug FROM categories WHERE user_id = ?";
    $finalStmt = $pdo->prepare($finalQuery);
    $finalStmt->execute([$merchantId]);
    $finalCategories = $finalStmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "  - Catégories pour merchant {$merchantId}: " . count($finalCategories) . "\n";
    foreach ($finalCategories as $cat) {
        echo "    * {$cat['name']} ({$cat['name_ar']}) - Slug: {$cat['slug']}\n";
    }
    
    echo "\n✅ Correction terminée!\n";
    
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
}
?>
