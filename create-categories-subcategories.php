<?php
require_once 'php/config/database.php';

try {
    echo "🔍 Création des catégories et sous-catégories pour le merchant demo...\n\n";
    
    // Trouver le merchant_id du store 3 (demo merchant)
    $storeQuery = "SELECT id, merchant_id, store_name FROM stores WHERE id = 3";
    $storeStmt = $pdo->prepare($storeQuery);
    $storeStmt->execute();
    $store = $storeStmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$store) {
        echo "❌ Store ID 3 non trouvé\n";
        exit;
    }
    
    echo "✅ Store trouvé: {$store['store_name']} (Merchant ID: {$store['merchant_id']})\n";
    $merchantId = $store['merchant_id'];
    
    // Supprimer les anciennes catégories pour recommencer proprement
    echo "\n🧹 Nettoyage des anciennes catégories...\n";
    $deleteQuery = "DELETE FROM categories WHERE user_id = ?";
    $deleteStmt = $pdo->prepare($deleteQuery);
    $deleteStmt->execute([$merchantId]);
    echo "  ✅ Anciennes catégories supprimées\n";
    
    // Définir les 10 catégories avec leurs sous-catégories
    $categoriesData = [
        [
            'name' => 'Électronique',
            'name_ar' => 'إلكترونيات',
            'slug' => 'electronique',
            'color' => '#007bff',
            'icon' => 'fas fa-microchip',
            'subcategories' => [
                ['name' => 'Composants', 'name_ar' => 'مكونات', 'slug' => 'composants'],
                ['name' => 'Circuits', 'name_ar' => 'دوائر', 'slug' => 'circuits']
            ]
        ],
        [
            'name' => 'Informatique',
            'name_ar' => 'معلوماتية',
            'slug' => 'informatique',
            'color' => '#28a745',
            'icon' => 'fas fa-laptop',
            'subcategories' => [
                ['name' => 'Ordinateurs', 'name_ar' => 'حاسوب', 'slug' => 'ordinateurs'],
                ['name' => 'Périphériques', 'name_ar' => 'ملحقات', 'slug' => 'peripheriques']
            ]
        ],
        [
            'name' => 'Smartphones',
            'name_ar' => 'هواتف ذكية',
            'slug' => 'smartphones',
            'color' => '#17a2b8',
            'icon' => 'fas fa-mobile-alt',
            'subcategories' => [
                ['name' => 'Android', 'name_ar' => 'أندرويد', 'slug' => 'android'],
                ['name' => 'iPhone', 'name_ar' => 'آيفون', 'slug' => 'iphone']
            ]
        ],
        [
            'name' => 'Audio/Vidéo',
            'name_ar' => 'صوت/فيديو',
            'slug' => 'audio-video',
            'color' => '#ffc107',
            'icon' => 'fas fa-headphones',
            'subcategories' => [
                ['name' => 'Casques', 'name_ar' => 'سماعات', 'slug' => 'casques'],
                ['name' => 'Haut-parleurs', 'name_ar' => 'مكبرات صوت', 'slug' => 'haut-parleurs']
            ]
        ],
        [
            'name' => 'Gaming',
            'name_ar' => 'ألعاب',
            'slug' => 'gaming',
            'color' => '#dc3545',
            'icon' => 'fas fa-gamepad',
            'subcategories' => [
                ['name' => 'Consoles', 'name_ar' => 'أجهزة ألعاب', 'slug' => 'consoles'],
                ['name' => 'Jeux', 'name_ar' => 'ألعاب', 'slug' => 'jeux']
            ]
        ],
        [
            'name' => 'Accessoires',
            'name_ar' => 'إكسسوارات',
            'slug' => 'accessoires',
            'color' => '#6f42c1',
            'icon' => 'fas fa-plug',
            'subcategories' => [
                ['name' => 'Câbles', 'name_ar' => 'كابلات', 'slug' => 'cables'],
                ['name' => 'Chargeurs', 'name_ar' => 'شواحن', 'slug' => 'chargeurs']
            ]
        ],
        [
            'name' => 'Maison Intelligente',
            'name_ar' => 'منزل ذكي',
            'slug' => 'maison-intelligente',
            'color' => '#fd7e14',
            'icon' => 'fas fa-home',
            'subcategories' => [
                ['name' => 'Éclairage', 'name_ar' => 'إضاءة', 'slug' => 'eclairage'],
                ['name' => 'Sécurité', 'name_ar' => 'أمان', 'slug' => 'securite']
            ]
        ],
        [
            'name' => 'Wearables',
            'name_ar' => 'أجهزة قابلة للارتداء',
            'slug' => 'wearables',
            'color' => '#20c997',
            'icon' => 'fas fa-watch',
            'subcategories' => [
                ['name' => 'Montres connectées', 'name_ar' => 'ساعات ذكية', 'slug' => 'montres-connectees'],
                ['name' => 'Fitness trackers', 'name_ar' => 'متتبعات اللياقة', 'slug' => 'fitness-trackers']
            ]
        ],
        [
            'name' => 'Stockage',
            'name_ar' => 'تخزين',
            'slug' => 'stockage',
            'color' => '#6610f2',
            'icon' => 'fas fa-hdd',
            'subcategories' => [
                ['name' => 'Disques durs', 'name_ar' => 'أقراص صلبة', 'slug' => 'disques-durs'],
                ['name' => 'Clés USB', 'name_ar' => 'فلاش ميموري', 'slug' => 'cles-usb']
            ]
        ],
        [
            'name' => 'Réseaux',
            'name_ar' => 'شبكات',
            'slug' => 'reseaux',
            'color' => '#e83e8c',
            'icon' => 'fas fa-wifi',
            'subcategories' => [
                ['name' => 'Routeurs', 'name_ar' => 'راوترات', 'slug' => 'routeurs'],
                ['name' => 'Switches', 'name_ar' => 'مفاتيح شبكة', 'slug' => 'switches']
            ]
        ]
    ];
    
    echo "\n📂 Création des catégories et sous-catégories...\n";
    
    foreach ($categoriesData as $index => $categoryData) {
        // Créer la catégorie principale
        $insertCategoryQuery = "
            INSERT INTO categories (
                user_id, name, name_ar, name_fr, name_en, slug, description,
                description_ar, description_fr, description_en, color, icon,
                category_type, status, sort_order, created_at, updated_at
            ) VALUES (
                ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'category', 'active', ?, NOW(), NOW()
            )
        ";
        
        $description = "Catégorie " . $categoryData['name'];
        $description_ar = "فئة " . $categoryData['name_ar'];
        
        $insertStmt = $pdo->prepare($insertCategoryQuery);
        $result = $insertStmt->execute([
            $merchantId,
            $categoryData['name'],
            $categoryData['name_ar'],
            $categoryData['name'],
            $categoryData['name'],
            $categoryData['slug'],
            $description,
            $description_ar,
            $description,
            $description,
            $categoryData['color'],
            $categoryData['icon'],
            $index + 1
        ]);
        
        if ($result) {
            $categoryId = $pdo->lastInsertId();
            echo "  ✅ Catégorie '{$categoryData['name']}' créée (ID: {$categoryId})\n";
            
            // Créer les sous-catégories
            foreach ($categoryData['subcategories'] as $subIndex => $subcat) {
                $insertSubcategoryQuery = "
                    INSERT INTO categories (
                        user_id, parent_id, name, name_ar, name_fr, name_en, slug, description,
                        description_ar, description_fr, description_en, color, icon,
                        category_type, status, sort_order, created_at, updated_at
                    ) VALUES (
                        ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'subcategory', 'active', ?, NOW(), NOW()
                    )
                ";
                
                $subDescription = "Sous-catégorie " . $subcat['name'];
                $subDescription_ar = "فئة فرعية " . $subcat['name_ar'];
                
                $insertSubStmt = $pdo->prepare($insertSubcategoryQuery);
                $subResult = $insertSubStmt->execute([
                    $merchantId,
                    $categoryId,
                    $subcat['name'],
                    $subcat['name_ar'],
                    $subcat['name'],
                    $subcat['name'],
                    $subcat['slug'],
                    $subDescription,
                    $subDescription_ar,
                    $subDescription,
                    $subDescription,
                    $categoryData['color'],
                    $categoryData['icon'],
                    $subIndex + 1
                ]);
                
                if ($subResult) {
                    $subcategoryId = $pdo->lastInsertId();
                    echo "    ✅ Sous-catégorie '{$subcat['name']}' créée (ID: {$subcategoryId})\n";
                } else {
                    echo "    ❌ Erreur lors de la création de la sous-catégorie '{$subcat['name']}'\n";
                }
            }
        } else {
            echo "  ❌ Erreur lors de la création de '{$categoryData['name']}'\n";
        }
    }
    
    // Vérification finale
    echo "\n📊 Résumé final:\n";
    $finalQuery = "SELECT COUNT(*) as count FROM categories WHERE user_id = ? AND category_type = 'category'";
    $finalStmt = $pdo->prepare($finalQuery);
    $finalStmt->execute([$merchantId]);
    $categoryCount = $finalStmt->fetch()['count'];
    
    $subFinalQuery = "SELECT COUNT(*) as count FROM categories WHERE user_id = ? AND category_type = 'subcategory'";
    $subFinalStmt = $pdo->prepare($subFinalQuery);
    $subFinalStmt->execute([$merchantId]);
    $subcategoryCount = $subFinalStmt->fetch()['count'];
    
    echo "  - Catégories principales: {$categoryCount}\n";
    echo "  - Sous-catégories: {$subcategoryCount}\n";
    echo "  - Total: " . ($categoryCount + $subcategoryCount) . "\n";
    
    echo "\n✅ Création terminée avec succès!\n";
    
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
    echo "📍 Fichier: " . $e->getFile() . " ligne " . $e->getLine() . "\n";
}
?>
