<?php
/**
 * Test script to verify dashboard data
 */

header('Content-Type: text/html; charset=utf-8');

// Configuration de la base de données
$host = 'localhost';
$port = 3307;
$dbname = 'landingpage_new';
$username = 'root';
$password = '';

try {
    // Connexion à la base de données
    $pdo = new PDO(
        "mysql:host={$host};port={$port};dbname={$dbname};charset=utf8mb4",
        $username,
        $password,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]
    );

    echo "<h1>🔍 Test des données du dashboard</h1>";
    echo "<style>body{font-family:Arial,sans-serif;margin:20px;} table{border-collapse:collapse;width:100%;margin:20px 0;} th,td{border:1px solid #ddd;padding:8px;text-align:left;} th{background-color:#f2f2f2;} .success{color:green;} .error{color:red;} .warning{color:orange;}</style>";

    // Test 1: Vérifier la structure de la table stores
    echo "<h2>📊 Structure de la table stores</h2>";
    $stmt = $pdo->query("DESCRIBE stores");
    $columns = $stmt->fetchAll();
    
    echo "<table>";
    echo "<tr><th>Colonne</th><th>Type</th><th>Null</th><th>Clé</th><th>Défaut</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>{$column['Field']}</td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "</tr>";
    }
    echo "</table>";

    // Test 2: Données dans la table stores
    echo "<h2>🏪 Données dans la table stores</h2>";
    $stmt = $pdo->query("SELECT * FROM stores ORDER BY id");
    $stores = $stmt->fetchAll();
    
    if (empty($stores)) {
        echo "<p class='warning'>⚠️ Aucun store trouvé dans la base de données</p>";
    } else {
        echo "<p class='success'>✅ " . count($stores) . " store(s) trouvé(s)</p>";
        echo "<table>";
        echo "<tr><th>ID</th><th>User ID</th><th>Store Name</th><th>Store Name AR</th><th>Description</th><th>Status</th><th>Created At</th></tr>";
        foreach ($stores as $store) {
            echo "<tr>";
            echo "<td>{$store['id']}</td>";
            echo "<td>{$store['user_id']}</td>";
            echo "<td>" . htmlspecialchars($store['store_name'] ?? '') . "</td>";
            echo "<td>" . htmlspecialchars($store['store_name_ar'] ?? '') . "</td>";
            echo "<td>" . htmlspecialchars(substr($store['description'] ?? '', 0, 50)) . "...</td>";
            echo "<td>{$store['status']}</td>";
            echo "<td>{$store['created_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }

    // Test 3: Structure de la table products
    echo "<h2>📦 Structure de la table products</h2>";
    $stmt = $pdo->query("DESCRIBE products");
    $columns = $stmt->fetchAll();
    
    echo "<table>";
    echo "<tr><th>Colonne</th><th>Type</th><th>Null</th><th>Clé</th><th>Défaut</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>{$column['Field']}</td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "</tr>";
    }
    echo "</table>";

    // Test 4: Données dans la table products
    echo "<h2>📱 Données dans la table products</h2>";
    $stmt = $pdo->query("SELECT * FROM products ORDER BY id");
    $products = $stmt->fetchAll();
    
    if (empty($products)) {
        echo "<p class='warning'>⚠️ Aucun produit trouvé dans la base de données</p>";
    } else {
        echo "<p class='success'>✅ " . count($products) . " produit(s) trouvé(s)</p>";
        echo "<table>";
        echo "<tr><th>ID</th><th>Merchant ID</th><th>Store ID</th><th>SKU</th><th>Name</th><th>Name AR</th><th>Price</th><th>Status</th><th>Stock</th></tr>";
        foreach ($products as $product) {
            echo "<tr>";
            echo "<td>{$product['id']}</td>";
            echo "<td>{$product['merchant_id']}</td>";
            echo "<td>{$product['store_id']}</td>";
            echo "<td>" . htmlspecialchars($product['sku'] ?? '') . "</td>";
            echo "<td>" . htmlspecialchars($product['name'] ?? '') . "</td>";
            echo "<td>" . htmlspecialchars($product['name_ar'] ?? '') . "</td>";
            echo "<td>{$product['price']} {$product['currency']}</td>";
            echo "<td>{$product['status']}</td>";
            echo "<td>{$product['stock_quantity']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }

    // Test 5: Test de l'API stores-simple.php
    echo "<h2>🔌 Test de l'API stores-simple.php</h2>";
    
    // Simuler une requête GET
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://localhost:8000/api/stores-simple.php');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Authorization: Bearer demo_token'
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "<p><strong>Code HTTP:</strong> $httpCode</p>";
    echo "<p><strong>Réponse:</strong></p>";
    echo "<pre>" . htmlspecialchars($response) . "</pre>";

    // Test 6: Test de l'API products.php
    echo "<h2>🔌 Test de l'API products.php</h2>";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://localhost:8000/api/products.php');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Authorization: Bearer demo_token'
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "<p><strong>Code HTTP:</strong> $httpCode</p>";
    echo "<p><strong>Réponse:</strong></p>";
    echo "<pre>" . htmlspecialchars($response) . "</pre>";

} catch (PDOException $e) {
    echo "<p class='error'>❌ Erreur de base de données: " . htmlspecialchars($e->getMessage()) . "</p>";
} catch (Exception $e) {
    echo "<p class='error'>❌ Erreur: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>
