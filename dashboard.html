<!DOCTYPE html>
<html lang="fr" dir="ltr">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Dashboard Firebase - صفحات هبوط للجميع</title>

    <!-- Bootstrap CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <!-- Font Awesome -->
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
      rel="stylesheet"
    />
    <!-- Styles de base -->
    <style>
      body {
        font-family: 'Arial', sans-serif;
      }


    </style>

    <style>
      .sidebar {
        min-height: 100vh;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
      }

      .sidebar .nav-link {
        color: rgba(255, 255, 255, 0.8);
        border-radius: 8px;
        margin: 2px 0;
        transition: all 0.3s ease;
      }

      .sidebar .nav-link:hover,
      .sidebar .nav-link.active {
        background: rgba(255, 255, 255, 0.2);
        color: white;
      }

      .card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease;
      }

      .card:hover {
        transform: translateY(-5px);
      }

      .stat-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
      }

      .stat-card.success {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      }

      .stat-card.warning {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      }

      .stat-card.info {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      }

      .main-content {
        background: #f8f9fa;
        min-height: 100vh;
      }

      .user-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: #667eea;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
      }

      .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
      }

      .loading-overlay.show {
        opacity: 1;
        visibility: visible;
      }

      .auth-required {
        text-align: center;
        padding: 3rem;
      }

      .firebase-user-info {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 1rem;
      }

      .firebase-user-info img {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        margin-bottom: 0.5rem;
      }

      .status-badge {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 11px;
        font-weight: 600;
      }

      .status-pending {
        background: #fff3cd;
        color: #856404;
      }

      .status-confirmed {
        background: #d1edff;
        color: #0c5460;
      }

      .status-rejected {
        background: #f8d7da;
        color: #721c24;
      }
    </style>
  </head>
  <body>
    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay show">
      <div class="text-center">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">Chargement...</span>
        </div>
        <p class="mt-3">Initialisation Firebase...</p>
      </div>
    </div>

    <!-- Auth Required Screen -->
    <div id="authRequired" class="auth-required" style="display: none">
      <div class="container">
        <div class="row justify-content-center">
          <div class="col-md-6">
            <div class="card">
              <div class="card-body text-center p-5">
                <i class="fas fa-lock fa-3x text-muted mb-3"></i>
                <h3>Authentification Requise</h3>
                <p class="text-muted mb-4">
                  Vous devez être connecté avec Firebase pour accéder au
                  dashboard.
                </p>
                <div class="d-grid gap-2">
                  <a href="login.html" class="btn btn-primary">
                    <i class="fas fa-sign-in-alt me-2"></i>Se connecter
                  </a>
                  <a href="register.html" class="btn btn-outline-secondary">
                    <i class="fas fa-user-plus me-2"></i>Créer un compte
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Dashboard -->
    <div id="mainDashboard" style="display: none">
      <div class="container-fluid">
        <div class="row">
          <!-- Sidebar -->
          <div class="col-md-3 col-lg-2 sidebar p-3">
            <div class="text-center mb-4">
              <h4><i class="fas fa-chart-line"></i> Dashboard</h4>
              <small>صفحات هبوط للجميع</small>
            </div>

            <!-- User Info -->
            <div id="firebaseUserInfo" class="firebase-user-info text-center">
              <div id="userAvatar" class="user-avatar mx-auto mb-2">
                <i class="fas fa-user"></i>
              </div>
              <div id="userName" class="fw-bold">Utilisateur</div>
              <div id="userEmail" class="small opacity-75">
                <EMAIL>
              </div>
              <div id="userRole" class="badge bg-light text-dark mt-1">
                Admin
              </div>
            </div>

            <nav class="nav flex-column">
              <a
                class="nav-link active"
                href="#overview"
                data-section="overview"
                onclick="showSection('overview'); return false;"
              >
                <i class="fas fa-tachometer-alt me-2"></i> Vue d'ensemble
              </a>
              <a class="nav-link" href="#stores" data-section="stores" onclick="showSection('stores'); return false;">
                <i class="fas fa-store me-2"></i> Stores
              </a>
              <a class="nav-link" href="#products" data-section="products" onclick="showSection('products'); return false;">
                <i class="fas fa-box me-2"></i> Produits
              </a>
              <a class="nav-link" href="#landing-pages" data-section="landing-pages" onclick="showSection('landing-pages'); return false;">
                <i class="fas fa-file-alt me-2"></i> Landing Pages
              </a>
              <a class="nav-link" href="#roles" data-section="roles" onclick="showSection('roles'); return false;">
                <i class="fas fa-user-shield me-2"></i> Rôles
              </a>
              <a
                class="nav-link"
                href="#subscriptions"
                data-section="subscriptions"
                onclick="showSection('subscriptions'); return false;"
              >
                <i class="fas fa-crown me-2"></i> Abonnements
              </a>
              <a class="nav-link" href="#orders" data-section="orders" onclick="showSection('orders'); return false;">
                <i class="fas fa-shopping-cart me-2"></i> Commandes
              </a>
              <a class="nav-link" href="#payments" data-section="payments" onclick="showSection('payments'); return false;">
                <i class="fas fa-credit-card me-2"></i> Paiements & Gateways
              </a>
              <a class="nav-link" href="#analytics" data-section="analytics" onclick="showSection('analytics'); return false;">
                <i class="fas fa-chart-bar me-2"></i> Analytics
              </a>
              <a class="nav-link" href="#ai-usage" data-section="ai-usage" onclick="showSection('ai-usage'); return false;">
                <i class="fas fa-robot me-2"></i>IA Config
              </a>
              <a class="nav-link" href="#settings" data-section="settings" onclick="showSection('settings'); return false;">
                <i class="fas fa-cog me-2"></i> Settings
              </a>
              <a
                class="nav-link"
                href="#firebase-users"
                data-section="firebase-users"
                onclick="showSection('firebase-users'); return false;"
              >
                <i class="fas fa-users me-2"></i> Utilisateurs
              </a>
              <a
                class="nav-link"
                href="#contact-messages"
                data-section="contact-messages"
                onclick="showSection('contact-messages'); return false;"
              >
                <i class="fas fa-envelope me-2"></i> Messages Contact
              </a>
            </nav>

            <div class="mt-auto pt-4">
              <button
                id="logoutBtn"
                class="nav-link text-danger w-100 border-0 bg-transparent"
              >
                <i class="fas fa-sign-out-alt me-2"></i> Déconnexion
              </button>
            </div>
          </div>

          <!-- Main Content -->
          <div class="col-md-9 col-lg-10 main-content p-4">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
              <h2>Dashboard Firebase</h2>
              <div class="d-flex align-items-center">
                <span class="text-muted">Connecté via Firebase</span>
              </div>
            </div>

            <!-- Overview Section -->
            <div id="overview-section" class="section">
              <!-- Stats Cards -->
              <div class="row mb-4">
                <div class="col-md-3 mb-3">
                  <div class="card stat-card">
                    <div class="card-body text-center">
                      <i class="fas fa-store fa-2x mb-2"></i>
                      <h3 id="totalStores">-</h3>
                      <p class="mb-0">Stores Actifs</p>
                    </div>
                  </div>
                </div>
                <div class="col-md-3 mb-3">
                  <div class="card stat-card success">
                    <div class="card-body text-center">
                      <i class="fas fa-box fa-2x mb-2"></i>
                      <h3 id="totalProducts">-</h3>
                      <p class="mb-0">Produits Totaux</p>
                    </div>
                  </div>
                </div>
                <div class="col-md-3 mb-3">
                  <div class="card stat-card warning">
                    <div class="card-body text-center">
                      <i class="fas fa-crown fa-2x mb-2"></i>
                      <h3 id="totalSubscriptions">-</h3>
                      <p class="mb-0">Abonnements</p>
                    </div>
                  </div>
                </div>
                <div class="col-md-3 mb-3">
                  <div class="card stat-card info">
                    <div class="card-body text-center">
                      <i class="fas fa-users fa-2x mb-2"></i>
                      <h3 id="totalUsers">-</h3>
                      <p class="mb-0">Utilisateurs</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Stores Management Section -->
              <div
                class="card section-content"
                id="stores-section"
                style="display: none"
              >
                <div
                  class="card-header d-flex justify-content-between align-items-center"
                >
                  <h5><i class="fas fa-store me-2"></i>Stores</h5>
                  <button
                    class="btn btn-primary btn-sm"
                    onclick="addNewStore()"
                  >
                    <i class="fas fa-plus me-1"></i>Nouveau Store
                  </button>
                </div>
                <div class="card-body">
                  <div id="storesContainer">
                    <div class="text-center py-4">
                      <div class="spinner-border" role="status">
                        <span class="visually-hidden">Chargement...</span>
                      </div>
                      <p class="mt-2">Chargement des stores...</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Products Management Section -->
              <div
                class="card section-content"
                id="products-section"
                style="display: none"
              >
                <div
                  class="card-header d-flex justify-content-between align-items-center"
                >
                  <h5><i class="fas fa-box me-2"></i>Produits</h5>
                  <button
                    class="btn btn-primary btn-sm"
                    onclick="addNewProduct()"
                  >
                    <i class="fas fa-plus me-1"></i>Nouveau Produit
                  </button>
                </div>
                <div class="card-body">
                  <div id="productsContainer">
                    <div class="text-center py-4">
                      <div class="spinner-border" role="status">
                        <span class="visually-hidden">Chargement...</span>
                      </div>
                      <p class="mt-2">Chargement des produits...</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Landing Pages Management Section -->
              <div
                class="card section-content"
                id="landing-pages-section"
                style="display: none"
              >
                <div
                  class="card-header d-flex justify-content-between align-items-center"
                >
                  <h5><i class="fas fa-file-alt me-2"></i>Landing Pages</h5>
                  <button
                    class="btn btn-primary btn-sm"
                    onclick="addNewLandingPage()"
                  >
                    <i class="fas fa-plus me-1"></i>Nouvelle Landing Page
                  </button>
                </div>
                <div class="card-body">
                  <!-- Filtres et recherche -->
                  <div class="row mb-3">
                    <div class="col-md-4">
                      <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                        <input type="text" class="form-control" id="searchLandingPages" placeholder="Rechercher une landing page...">
                      </div>
                    </div>
                    <div class="col-md-3">
                      <select class="form-select" id="filterLandingPageStatus">
                        <option value="">Tous les statuts</option>
                        <option value="published">Publiées</option>
                        <option value="draft">Brouillons</option>
                        <option value="archived">Archivées</option>
                      </select>
                    </div>
                    <div class="col-md-3">
                      <select class="form-select" id="filterLandingPageLanguage">
                        <option value="">Toutes les langues</option>
                        <option value="fr">Français</option>
                        <option value="ar">العربية</option>
                        <option value="en">English</option>
                      </select>
                    </div>
                    <div class="col-md-2">
                      <button class="btn btn-outline-secondary w-100" onclick="refreshLandingPages()">
                        <i class="fas fa-sync-alt"></i>
                      </button>
                    </div>
                  </div>

                  <!-- Templates rapides -->
                  <div class="row mb-4">
                    <div class="col-12">
                      <h6 class="text-muted mb-2"><i class="fas fa-magic me-1"></i>Templates Disponibles</h6>
                      <div class="d-flex gap-2 flex-wrap">
                        <button class="btn btn-outline-primary btn-sm" onclick="previewTemplate('ecommerce')">
                          <i class="fas fa-shopping-cart me-1"></i>E-commerce
                        </button>
                        <button class="btn btn-outline-success btn-sm" onclick="previewTemplate('saas')">
                          <i class="fas fa-cloud me-1"></i>SaaS
                        </button>
                        <button class="btn btn-outline-info btn-sm" onclick="previewTemplate('portfolio')">
                          <i class="fas fa-briefcase me-1"></i>Portfolio
                        </button>
                        <button class="btn btn-outline-warning btn-sm" onclick="previewTemplate('service')">
                          <i class="fas fa-cogs me-1"></i>Service
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="previewTemplate('app')">
                          <i class="fas fa-mobile-alt me-1"></i>App Mobile
                        </button>
                      </div>
                    </div>
                  </div>

                  <!-- Statistiques rapides -->
                  <div class="row mb-4">
                    <div class="col-md-3">
                      <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                          <h4 id="totalLandingPages">-</h4>
                          <small>Total Landing Pages</small>
                        </div>
                      </div>
                    </div>
                    <div class="col-md-3">
                      <div class="card bg-success text-white">
                        <div class="card-body text-center">
                          <h4 id="publishedLandingPages">-</h4>
                          <small>Publiées</small>
                        </div>
                      </div>
                    </div>
                    <div class="col-md-3">
                      <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                          <h4 id="draftLandingPages">-</h4>
                          <small>Brouillons</small>
                        </div>
                      </div>
                    </div>
                    <div class="col-md-3">
                      <div class="card bg-danger text-white">
                        <div class="card-body text-center">
                          <h4 id="archivedLandingPages">-</h4>
                          <small>Archivées</small>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Tableau des landing pages -->
                  <div class="table-responsive">
                    <table class="table table-hover">
                      <thead class="table-dark">
                        <tr>
                          <th>ID</th>
                          <th>Titre</th>
                          <th>Propriétaire</th>
                          <th>Template</th>
                          <th>Statut</th>
                          <th>Vues</th>
                          <th>Conversions</th>
                          <th>Créée le</th>
                          <th>Actions</th>
                        </tr>
                      </thead>
                      <tbody id="landingPagesTableBody">
                        <tr>
                          <td colspan="9" class="text-center py-4">
                            <div class="spinner-border" role="status">
                              <span class="visually-hidden">Chargement...</span>
                            </div>
                            <p class="mt-2 mb-0">Chargement des landing pages...</p>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>

                  <!-- Pagination -->
                  <nav aria-label="Navigation des landing pages" id="landingPagesPagination" style="display: none;">
                    <ul class="pagination justify-content-center">
                      <li class="page-item">
                        <a class="page-link" href="#" onclick="loadLandingPagesPage(currentLandingPagesPage - 1)">Précédent</a>
                      </li>
                      <li class="page-item active">
                        <a class="page-link" href="#">1</a>
                      </li>
                      <li class="page-item">
                        <a class="page-link" href="#" onclick="loadLandingPagesPage(currentLandingPagesPage + 1)">Suivant</a>
                      </li>
                    </ul>
                  </nav>
                </div>
              </div>

              <!-- Roles Management Section -->
              <div class="card section-content" id="roles-section" style="display: none">
                <div class="card-header d-flex justify-content-between align-items-center">
                  <h5><i class="fas fa-user-shield me-2"></i>Gestion des Rôles</h5>
                  <div>
                    <button class="btn btn-outline-secondary btn-sm me-2" onclick="showRoleLogs()">
                      <i class="fas fa-history me-1"></i>Historique
                    </button>
                    <button class="btn btn-primary btn-sm" onclick="addNewRole()">
                      <i class="fas fa-plus me-1"></i>Nouveau Rôle
                    </button>
                  </div>
                </div>
                <div class="card-body">
                  <!-- Filtres et recherche -->
                  <div class="row mb-4">
                    <div class="col-md-4">
                      <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-filter"></i></span>
                        <select class="form-select" id="roleFilter" onchange="filterUsers()">
                          <option value="all">Tous les rôles</option>
                          <option value="admin">Administrateurs</option>
                          <option value="seller">Vendeurs</option>
                          <option value="agent">Agents</option>
                          <option value="customer">Clients</option>
                        </select>
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                        <input type="text" class="form-control" id="userSearch" placeholder="Rechercher un utilisateur..." onkeyup="searchUsers()">
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-sort"></i></span>
                        <select class="form-select" id="userSort" onchange="sortUsers()">
                          <option value="name">Nom</option>
                          <option value="role">Rôle</option>
                          <option value="date">Date d'ajout</option>
                        </select>
                      </div>
                    </div>
                  </div>

                  <!-- Conteneur principal -->
                  <div id="rolesContainer">
                    <div class="text-center py-4">
                      <div class="spinner-border" role="status">
                        <span class="visually-hidden">Chargement...</span>
                      </div>
                      <p class="mt-2">Chargement des rôles...</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Subscriptions Management Section -->
              <div
                class="card section-content"
                id="subscriptions-section"
                style="display: none"
              >
                <div
                  class="card-header d-flex justify-content-between align-items-center"
                >
                  <h5>
                    <i class="fas fa-crown me-2"></i>Abonnements
                  </h5>
                  <button
                    class="btn btn-primary btn-sm"
                    onclick="addNewSubscription()"
                  >
                    <i class="fas fa-plus me-1"></i>Nouveau Plan
                  </button>
                </div>
                <div class="card-body">
                  <div id="subscriptionsContainer">
                    <div class="table-responsive">
                      <table class="table table-striped">
                        <thead>
                          <tr>
                            <th>Nom</th>
                            <th>Description</th>
                            <th>Prix</th>
                            <th>Produits max</th>
                            <th>Landing pages max</th>
                            <th>Catégories max</th>
                            <th>Fonctionnalités</th>
                            <th>Actions</th>
                          </tr>
                        </thead>
                        <tbody id="subscriptionsTableBody">
                          <!-- Les abonnements seront chargés ici -->
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Subscriptions Section -->
              <div
                class="card section-content"
                id="subscriptions-section"
                style="display: none"
              >
                <div class="card-header d-flex justify-content-between align-items-center">
                  <h5>
                    <i class="fas fa-crown me-2"></i>Gestion des Abonnements
                  </h5>
                  <button
                    class="btn btn-primary btn-sm"
                    onclick="addNewSubscription()"
                  >
                    <i class="fas fa-plus me-1"></i>Nouveau Plan
                  </button>
                </div>
                <div class="card-body">
                  <div id="subscriptionsContainer">
                    <div class="text-center py-4">
                      <div class="spinner-border" role="status">
                        <span class="visually-hidden">Chargement...</span>
                      </div>
                      <p class="mt-2">Chargement des abonnements...</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Orders Section -->
              <div
                class="card section-content"
                id="orders-section"
                style="display: none"
              >
                <div
                  class="card-header d-flex justify-content-between align-items-center"
                >
                  <h5>
                    <i class="fas fa-shopping-cart me-2"></i>Commandes
                  </h5>
                  <div>
                    <button
                      class="btn btn-outline-primary btn-sm me-2"
                      onclick="exportOrders()"
                    >
                      <i class="fas fa-download me-1"></i>Exporter
                    </button>
                    <button
                      class="btn btn-primary btn-sm"
                      onclick="refreshOrders()"
                    >
                      <i class="fas fa-refresh me-1"></i>Actualiser
                    </button>
                  </div>
                </div>
                <div class="card-body">
                  <div id="ordersContainer">
                    <div class="text-center py-4">
                      <div class="spinner-border" role="status">
                        <span class="visually-hidden">Chargement...</span>
                      </div>
                      <p class="mt-2">Chargement des commandes...</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Payments Section -->
              <div
                class="card section-content"
                id="payments-section"
                style="display: none"
              >
                <div class="card-header">
                  <h5>
                    <i class="fas fa-credit-card me-2"></i>Paiements & Payment
                    Gateways
                  </h5>
                </div>
                <div class="card-body">
                  <div id="paymentsContainer">
                    <div class="text-center py-4">
                      <div class="spinner-border" role="status">
                        <span class="visually-hidden">Chargement...</span>
                      </div>
                      <p class="mt-2">Chargement des paiements...</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Analytics Section -->
              <div
                class="card section-content"
                id="analytics-section"
                style="display: none"
              >
                <div class="card-header">
                  <h5>
                    <i class="fas fa-chart-bar me-2"></i>Analytics &
                    Statistiques
                  </h5>
                </div>
                <div class="card-body">
                  <div id="analyticsContainer">
                    <div class="text-center py-4">
                      <div class="spinner-border" role="status">
                        <span class="visually-hidden">Chargement...</span>
                      </div>
                      <p class="mt-2">Chargement des analytics...</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- AI Usage Section -->
              <div
                class="card section-content"
                id="ai-usage-section"
                style="display: none"
              >
                <div class="card-header">
                  <h5>
                    <i class="fas fa-robot me-2"></i>Usage IA & Configuration
                    API Keys
                  </h5>
                </div>
                <div class="card-body">
                  <div id="aiUsageContainer">
                    <div class="text-center py-4">
                      <div class="spinner-border" role="status">
                        <span class="visually-hidden">Chargement...</span>
                      </div>
                      <p class="mt-2">Chargement de l'usage IA...</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Settings Section -->
              <div
                class="card section-content"
                id="settings-section"
                style="display: none"
              >
                <div class="card-header">
                  <h5>
                    <i class="fas fa-cog me-2"></i>Settings
                  </h5>
                </div>
                <div class="card-body">
                  <div id="settingsContainer">
                    <div class="text-center py-4">
                      <div class="spinner-border" role="status">
                        <span class="visually-hidden">Chargement...</span>
                      </div>
                      <p class="mt-2">Chargement des paramètres...</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Firebase Users Section -->
              <div
                class="card section-content"
                id="firebase-users-section"
                style="display: none"
              >
                <div
                  class="card-header d-flex justify-content-between align-items-center"
                >
                  <h5>
                    <i class="fas fa-users me-2"></i>Utilisateurs
                  </h5>
                  <button
                    class="btn btn-primary btn-sm"
                    onclick="loadFirebaseUsers()"
                  >
                    <i class="fas fa-refresh me-1"></i>Actualiser
                  </button>
                </div>
                <div class="card-body">
                  <div id="firebaseUsersContainer">
                    <div class="text-center py-4">
                      <div class="spinner-border" role="status">
                        <span class="visually-hidden">Chargement...</span>
                      </div>
                      <p class="mt-2">
                        Chargement des utilisateurs Firebase...
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Contact Messages Section -->
              <div
                class="card section-content"
                id="contact-messages-section"
                style="display: none"
              >
                <div
                  class="card-header d-flex justify-content-between align-items-center"
                >
                  <h5>
                    <i class="fas fa-envelope me-2"></i>Messages de Contact
                  </h5>
                  <button
                    class="btn btn-primary btn-sm"
                    onclick="loadContactMessages()"
                  >
                    <i class="fas fa-refresh me-1"></i>Actualiser
                  </button>
                </div>
                <div class="card-body">
                  <div id="contactMessagesContainer">
                    <div class="text-center py-4">
                      <div class="spinner-border" role="status">
                        <span class="visually-hidden">Chargement...</span>
                      </div>
                      <p class="mt-2">
                        Chargement des messages de contact...
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Overview Section (Default) -->
              <div class="card section-content" id="overview-section">
                <div class="card-header">
                  <h5>
                    <i class="fas fa-tachometer-alt me-2"></i>Vue d'ensemble du
                    Système
                  </h5>
                </div>
                <div class="card-body">
                  <div id="overviewContainer">
                    <div class="row">
                      <div class="col-md-6">
                        <div class="card border-primary">
                          <div class="card-header bg-primary text-white">
                            <h6 class="mb-0">
                              <i class="fas fa-database me-2"></i>État de la
                              Base de Données
                            </h6>
                          </div>
                          <div class="card-body">
                            <div id="databaseStatus">
                              <div class="text-center py-4">
                                <div class="spinner-border" role="status">
                                  <span class="visually-hidden"
                                    >Chargement...</span
                                  >
                                </div>
                                <p class="mt-2">
                                  Vérification de la base de données...
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="col-md-6">
                        <div class="card border-success">
                          <div class="card-header bg-success text-white">
                            <h6 class="mb-0">
                              <i class="fas fa-chart-line me-2"></i>Statistiques
                              Rapides
                            </h6>
                          </div>
                          <div class="card-body">
                            <div class="row text-center">
                              <div class="col-6 mb-3">
                                <h4 class="text-primary" id="quickStatsStores">
                                  0
                                </h4>
                                <small class="text-muted">Stores</small>
                              </div>
                              <div class="col-6 mb-3">
                                <h4
                                  class="text-success"
                                  id="quickStatsProducts"
                                >
                                  0
                                </h4>
                                <small class="text-muted">Produits</small>
                              </div>
                              <div class="col-6">
                                <h4 class="text-warning" id="quickStatsUsers">
                                  0
                                </h4>
                                <small class="text-muted">Utilisateurs</small>
                              </div>
                              <div class="col-6">
                                <h4
                                  class="text-info"
                                  id="quickStatsSubscriptions"
                                >
                                  0
                                </h4>
                                <small class="text-muted">Abonnements</small>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Scripts Firebase (désactivés pour le développement) -->
    <!-- <script src="js/firebase-config.js"></script> -->
    <!-- <script src="js/firebase-auth.js"></script> -->

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom Scripts -->
    <script src="/js/subscriptions.js"></script>

    <script>
      // Navigation du dashboard
      function showSection(section) {
        // Cacher toutes les sections
        document.querySelectorAll('.section-content').forEach(s => s.style.display = 'none');

        // Retirer la classe active de tous les liens
        document.querySelectorAll('.nav-link').forEach(link => link.classList.remove('active'));

        // Activer le lien correspondant
        const navLink = document.querySelector(`[data-section="${section}"]`);
        if (navLink) navLink.classList.add('active');

        // Afficher la section demandée
        const targetSection = document.getElementById(section + '-section');
        if (targetSection) {
            targetSection.style.display = 'block';

            // Charger les données si nécessaire
            switch(section) {
                case 'subscriptions':
                    loadSubscriptions();
                    break;
                case 'users':
                    loadUsers();
                    break;
                case 'overview':
                    loadOverviewData();
                    break;
            }
        }
      }

      // État de l'application
      let currentUser = null;
      let dashboardData = {
        users: 0,
        orders: 0,
        payments: 0,
        aiUsage: 0,
      };

      // Configuration de l'authentification pour le développement
      const DEMO_TOKEN = 'demo_token';

      // Initialisation de la page
      async function initializePage() {
        console.log("🚀 Initialisation du dashboard (mode développement)");

        // Simuler un utilisateur connecté pour le développement
        const mockUser = {
          uid: "demo_user_123",
          email: "<EMAIL>",
          displayName: "Administrateur",
          photoURL: null,
          emailVerified: true
        };

        // Simuler une authentification réussie après un court délai
        setTimeout(() => {
          handleAuthStateChange(mockUser);
          // Afficher la section overview par défaut
          showSection('overview');
        }, 500);
      }

      // Initialisation de l'authentification
      function initializeAuth() {
        console.log("🔐 Initialisation de l'authentification Firebase");

        if (window.LandingPageAuth) {
          window.LandingPageAuth.init({
            redirectAfterLogin: false,
            persistSession: true,
            debug: true,
          });

          // Écouter les changements d'état d'authentification
          const auth = window.LandingPageFirebase.getAuth();
          if (auth) {
            auth.onAuthStateChanged((user) => {
              handleAuthStateChange(user);
            });
          }
        }
      }

      // Gestion des changements d'état d'authentification
      function handleAuthStateChange(user) {
        console.log("👤 État d'authentification changé:", user);

        const loadingOverlay = document.getElementById("loadingOverlay");
        const authRequired = document.getElementById("authRequired");
        const mainDashboard = document.getElementById("mainDashboard");

        if (user) {
          // Utilisateur connecté
          currentUser = user;

          // Vérifier si c'est un admin autorisé
          if (isAuthorizedAdmin(user.email)) {
            showUserInfo(user);
            loadDashboardData();

            // Afficher le dashboard
            loadingOverlay.classList.remove("show");
            authRequired.style.display = "none";
            mainDashboard.style.display = "block";
          } else {
            // Utilisateur non autorisé
            showUnauthorizedMessage();
          }
        } else {
          // Utilisateur non connecté
          currentUser = null;

          // Afficher l'écran d'authentification
          loadingOverlay.classList.remove("show");
          authRequired.style.display = "block";
          mainDashboard.style.display = "none";
        }
      }

      // Vérifier si l'utilisateur est un admin autorisé
      function isAuthorizedAdmin(email) {
        // Mode développement - autoriser tous les utilisateurs
        console.log("🔓 Mode développement - accès autorisé pour:", email);
        return true;

        // Code original (commenté pour le développement)
        /*
        const authorizedAdmins = [
          "<EMAIL>", // Super administrateur
          "<EMAIL>", // Admin de test
          "<EMAIL>", // Admin Aswak Quraysh
        ];
        return authorizedAdmins.includes(email);
        */
      }

      // Afficher les informations utilisateur
      function showUserInfo(user) {
        const userAvatar = document.getElementById("userAvatar");
        const userName = document.getElementById("userName");
        const userEmail = document.getElementById("userEmail");
        const userRole = document.getElementById("userRole");

        // Avatar
        if (user.photoURL) {
          userAvatar.innerHTML = `<img src="${user.photoURL}" alt="Avatar" style="width: 40px; height: 40px; border-radius: 50%;">`;
        } else {
          const initials = user.displayName
            ? user.displayName
                .split(" ")
                .map((n) => n[0])
                .join("")
                .toUpperCase()
            : user.email[0].toUpperCase();
          userAvatar.textContent = initials;
        }

        // Nom
        userName.textContent = user.displayName || user.email.split("@")[0];

        // Email
        userEmail.textContent = user.email;

        // Rôle
        if (user.email === "<EMAIL>") {
          userRole.textContent = "Super Admin";
          userRole.className = "badge bg-danger mt-1";
        } else {
          userRole.textContent = "Admin";
          userRole.className = "badge bg-primary mt-1";
        }
      }

      // Afficher un message pour utilisateur non autorisé
      function showUnauthorizedMessage() {
        const authRequired = document.getElementById("authRequired");
        authRequired.innerHTML = `
                <div class="container">
                    <div class="row justify-content-center">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body text-center p-5">
                                    <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                                    <h3>Accès Non Autorisé</h3>
                                    <p class="text-muted mb-4">Votre compte (${currentUser.email}) n'a pas les permissions nécessaires pour accéder au dashboard administratif.</p>
                                    <div class="d-grid gap-2">
                                        <button onclick="handleLogout()" class="btn btn-primary">
                                            <i class="fas fa-sign-out-alt me-2"></i>Se déconnecter
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

        document.getElementById("loadingOverlay").classList.remove("show");
        authRequired.style.display = "block";
        document.getElementById("mainDashboard").style.display = "none";
      }

      // Charger les données du dashboard
      async function loadDashboardData() {
        try {
          // Charger les statistiques réelles depuis l'API
          await loadRealDashboardStats();

          // Charger les utilisateurs réels
          await loadRealUsers();

          // Charger l'état réel de la base de données
          await loadRealDatabaseStatus();

          // Initialiser les graphiques
          await initializeCharts();
        } catch (error) {
          console.error("❌ Erreur lors du chargement des données:", error);
          // En cas d'erreur, utiliser des données par défaut
          dashboardData = {
            users: 0,
            stores: 0,
            products: 0,
            subscriptions: 0,
          };
          updateDashboardStats();
        }
      }

      // Charger les statistiques réelles depuis l'API
      async function loadRealDashboardStats() {
        try {
          // Utiliser notre API Dashboard simple
          const response = await fetch("/api/dashboard-stats.php", {
            headers: {
              "Content-Type": "application/json",
            },
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const data = await response.json();

          if (data.success) {
            // Mettre à jour les données du dashboard avec les vraies données
            dashboardData = {
              users: data.data.users || 0,
              stores: data.data.stores || 0,
              products: data.data.products || 0,
              subscriptions: 0, // À implémenter plus tard
              orders: data.data.orders || 0,
              revenue: data.data.revenue || 0,
              monthly_orders: data.data.monthly_orders || 0,
              monthly_revenue: data.data.monthly_revenue || 0,
              conversion_rate: data.data.conversion_rate || 0,
            };

            // Mettre à jour l'affichage
            updateDashboardStats();

            console.log("✅ Statistiques chargées:", dashboardData);
          } else {
            throw new Error("API returned error: " + data.message);
          }
        } catch (error) {
          console.error("❌ Erreur lors du chargement des stats:", error);

          // En cas d'erreur, essayer de charger des données de base
          try {
            await loadBasicStats();
          } catch (fallbackError) {
            console.error("❌ Erreur fallback:", fallbackError);
            throw error;
          }
        }
      }

      // Fonction de fallback pour charger des statistiques de base
      async function loadBasicStats() {
        try {
          const [storesResponse, productsResponse, usersResponse] =
            await Promise.all([
              fetch("/api/stores.php", {
                headers: { Authorization: "Bearer demo_token" },
              }),
              fetch("/api/products.php", {
                headers: { Authorization: "Bearer demo_token" },
              }),
              fetch("/api/users.php?action=all", {
                headers: { Authorization: "Bearer demo_token" },
              }),
            ]);

          const storesData = await storesResponse.json();
          const productsData = await productsResponse.json();
          const usersData = await usersResponse.json();

          // Calculer les statistiques réelles
          let storesCount = 0;
          let productsCount = 0;
          let usersCount = 0;

          // Stores: peut être un objet ou un tableau
          if (storesData.success && storesData.data) {
            storesCount = Array.isArray(storesData.data)
              ? storesData.data.length
              : 1;
          }

          // Products: structure data.products
          if (
            productsData.success &&
            productsData.data &&
            productsData.data.products
          ) {
            productsCount = productsData.data.products.length;
          }

          // Users: structure data.users
          if (usersData.success && usersData.data && usersData.data.users) {
            usersCount = usersData.data.users.length;
          }

          // Charger le nombre réel d'abonnements
          let subscriptionsCount = 3; // Valeur par défaut
          try {
            const subscriptionsResponse = await fetch('/api/quota-api.php?action=get_subscriptions', {
              headers: { Authorization: 'Bearer demo_token' }
            });
            if (subscriptionsResponse.ok) {
              const subscriptionsData = await subscriptionsResponse.json();
              if (subscriptionsData.success && subscriptionsData.data) {
                subscriptionsCount = subscriptionsData.data.length;
              }
            }
          } catch (error) {
            console.log('Utilisation de la valeur par défaut pour les abonnements:', error);
          }

          dashboardData = {
            users: usersCount,
            stores: storesCount,
            products: productsCount,
            subscriptions: subscriptionsCount,
            orders: 0,
            revenue: 0,
          };

          updateDashboardStats();
          console.log("✅ Statistiques de base chargées:", dashboardData);
        } catch (error) {
          console.error(
            "❌ Erreur lors du chargement des stats de base:",
            error
          );
          // Valeurs par défaut en cas d'erreur
          dashboardData = {
            users: 0,
            stores: 0,
            products: 0,
            subscriptions: 0,
            orders: 0,
            revenue: 0,
          };
          updateDashboardStats();
        }
      }

      // Mettre à jour les statistiques du dashboard
      function updateDashboardStats() {
        document.getElementById("totalUsers").textContent = dashboardData.users;
        document.getElementById("totalStores").textContent =
          dashboardData.stores || 0;
        document.getElementById("totalProducts").textContent =
          dashboardData.products || 0;
        document.getElementById("totalSubscriptions").textContent =
          dashboardData.subscriptions || 0;

        // Mettre à jour les stats rapides
        document.getElementById("quickStatsUsers").textContent =
          dashboardData.users;
        document.getElementById("quickStatsStores").textContent =
          dashboardData.stores || 0;
        document.getElementById("quickStatsProducts").textContent =
          dashboardData.products || 0;
        document.getElementById("quickStatsSubscriptions").textContent =
          dashboardData.subscriptions || 0;
      }

      // Charger les utilisateurs réels
      async function loadRealUsers() {
        await loadFirebaseUsers();
      }

      // Charger les utilisateurs Firebase
      async function loadFirebaseUsers() {
        const container = document.getElementById("firebaseUsersContainer");

        try {
          // Afficher un indicateur de chargement
          container.innerHTML = '<div class="text-center py-4"><div class="spinner-border" role="status"></div><p>Chargement des utilisateurs...</p></div>';

          // Charger les utilisateurs depuis l'API
          const response = await fetch("/api/firebase-users.php?action=all", {
            headers: {
              Authorization: "Bearer demo_token",
              "Content-Type": "application/json"
            },
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const data = await response.json();

          if (data.success && data.data && data.data.users) {
            const users = data.data.users;
            const stats = data.data.stats;

            if (users.length === 0) {
              container.innerHTML = `
                <div class="alert alert-info text-center">
                  <i class="fas fa-users fa-3x mb-3"></i>
                  <h5>Aucun utilisateur trouvé</h5>
                  <p>Les utilisateurs apparaîtront ici une fois inscrits.</p>
                  <button class="btn btn-primary" onclick="addNewUser()">
                    <i class="fas fa-plus me-1"></i>Ajouter un utilisateur
                  </button>
                </div>
              `;
              return;
            }

            // Utiliser les statistiques de l'API
            const totalUsers = stats.total;
            const activeUsers = stats.active;
            const adminUsers = stats.admins;
            const merchantUsers = stats.merchants;

            let html = `
              <div class="row mb-4">
                <div class="col-md-3">
                  <div class="card text-center">
                    <div class="card-body">
                      <h5 class="card-title text-primary">Total Utilisateurs</h5>
                      <h2>${totalUsers}</h2>
                    </div>
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="card text-center">
                    <div class="card-body">
                      <h5 class="card-title text-success">Actifs</h5>
                      <h2>${activeUsers}</h2>
                    </div>
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="card text-center">
                    <div class="card-body">
                      <h5 class="card-title text-info">Administrateurs</h5>
                      <h2>${adminUsers}</h2>
                    </div>
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="card text-center">
                    <div class="card-body">
                      <h5 class="card-title text-warning">Marchands</h5>
                      <h2>${merchantUsers}</h2>
                    </div>
                  </div>
                </div>
              </div>

              <div class="d-flex justify-content-between align-items-center mb-3">
                <h6>Liste des Utilisateurs</h6>
                <button class="btn btn-primary btn-sm" onclick="addNewUser()">
                  <i class="fas fa-plus me-1"></i>Nouvel Utilisateur
                </button>
              </div>

              <div class="table-responsive">
                <table class="table table-hover">
                  <thead class="table-light">
                    <tr>
                      <th>ID</th>
                      <th>Email</th>
                      <th>Nom</th>
                      <th>Rôle</th>
                      <th>Statut</th>
                      <th>Dernière connexion</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
            `;

            users.forEach((user) => {
              // Utiliser les propriétés de notre API
              const displayName = user.display_name || user.email || 'Non défini';
              const email = user.email || 'Non défini';
              const roleText = user.role || 'merchant';
              const isActive = user.status === 'active';

              // Configuration des badges
              const statusBadge = isActive ? "bg-success" : "bg-danger";
              const roleBadge = roleText === "admin" ? "bg-primary" :
                               roleText === "merchant" ? "bg-info" :
                               "bg-secondary";

              // Formatage de la dernière connexion
              const lastLogin = user.last_login ?
                new Date(user.last_login).toLocaleString('fr-FR', {
                  day: '2-digit',
                  month: '2-digit',
                  year: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit'
                }) : 'Jamais';

              html += `
                <tr>
                  <td>${user.id}</td>
                  <td>${email}</td>
                  <td>${displayName}</td>
                  <td><span class="badge ${roleBadge}">${roleText.toUpperCase()}</span></td>
                  <td><span class="badge ${statusBadge}">${isActive ? 'ACTIF' : 'INACTIF'}</span></td>
                  <td>${lastLogin}</td>
                  <td>
                    <div class="btn-group">
                      <button class="btn btn-sm btn-outline-primary" onclick="editUser('${user.id}')" title="Modifier">
                        <i class="fas fa-edit"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-warning" onclick="toggleUserStatus('${user.id}', '${isActive}')" title="${isActive ? 'Désactiver' : 'Activer'}">
                        <i class="fas fa-toggle-${isActive ? 'on' : 'off'}"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-danger" onclick="deleteUser('${user.id}')" title="Supprimer">
                        <i class="fas fa-trash"></i>
                      </button>
                    </div>
                  </td>
                </tr>
              `;
            });

            html += `
                  </tbody>
                </table>
              </div>
            `;

            container.innerHTML = html;
            console.log("✅ Utilisateurs chargés:", users);
          } else {
            throw new Error("Aucune donnée utilisateur trouvée");
          }
        } catch (error) {
          console.error("❌ Erreur lors du chargement des utilisateurs:", error);
          container.innerHTML = `
            <div class="alert alert-danger">
              <i class="fas fa-exclamation-triangle me-2"></i>
              Erreur lors du chargement des utilisateurs: ${error.message}
            </div>
          `;
        }
      }

      function addNewUser() {
        // Créer un modal pour ajouter un nouvel utilisateur
        const modalHtml = `
          <div class="modal fade" id="addUserModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
              <div class="modal-content">
                <div class="modal-header">
                  <h5 class="modal-title">Ajouter un Nouvel Utilisateur</h5>
                  <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                  <form id="addUserForm">
                    <div class="row">
                      <div class="col-md-6">
                        <div class="mb-3">
                          <label for="userEmail" class="form-label">Email *</label>
                          <input type="email" class="form-control" id="userEmail" required>
                        </div>
                      </div>
                      <div class="col-md-6">
                        <div class="mb-3">
                          <label for="userPassword" class="form-label">Mot de passe *</label>
                          <input type="password" class="form-control" id="userPassword" required>
                        </div>
                      </div>
                    </div>
                    <div class="row">
                      <div class="col-md-6">
                        <div class="mb-3">
                          <label for="userFirstName" class="form-label">Prénom</label>
                          <input type="text" class="form-control" id="userFirstName">
                        </div>
                      </div>
                      <div class="col-md-6">
                        <div class="mb-3">
                          <label for="userLastName" class="form-label">Nom</label>
                          <input type="text" class="form-control" id="userLastName">
                        </div>
                      </div>
                    </div>
                    <div class="row">
                      <div class="col-md-6">
                        <div class="mb-3">
                          <label for="userRole" class="form-label">Rôle</label>
                          <select class="form-control" id="userRole">
                            <option value="user">Utilisateur</option>
                            <option value="merchant">Marchand</option>
                            <option value="admin">Administrateur</option>
                          </select>
                        </div>
                      </div>
                      <div class="col-md-6">
                        <div class="mb-3">
                          <label for="userStatus" class="form-label">Statut</label>
                          <select class="form-control" id="userStatus">
                            <option value="active">Actif</option>
                            <option value="inactive">Inactif</option>
                          </select>
                        </div>
                      </div>
                    </div>
                    <div class="mb-3">
                      <label for="userPhone" class="form-label">Téléphone</label>
                      <input type="tel" class="form-control" id="userPhone">
                    </div>
                  </form>
                </div>
                <div class="modal-footer">
                  <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                  <button type="button" class="btn btn-primary" onclick="submitNewUser()">Créer Utilisateur</button>
                </div>
              </div>
            </div>
          </div>
        `;

        // Ajouter le modal au DOM s'il n'existe pas
        if (!document.getElementById('addUserModal')) {
          document.body.insertAdjacentHTML('beforeend', modalHtml);
        }

        // Afficher le modal
        const modal = new bootstrap.Modal(document.getElementById('addUserModal'));
        modal.show();
      }

      async function submitNewUser() {
        const email = document.getElementById('userEmail').value;
        const password = document.getElementById('userPassword').value;
        const firstName = document.getElementById('userFirstName').value;
        const lastName = document.getElementById('userLastName').value;
        const role = document.getElementById('userRole').value;
        const status = document.getElementById('userStatus').value;
        const phone = document.getElementById('userPhone').value;

        if (!email.trim() || !password.trim()) {
          alert('L\'email et le mot de passe sont requis');
          return;
        }

        try {
          const response = await fetch('/api/users.php', {
            method: 'POST',
            headers: {
              'Authorization': 'Bearer demo_token',
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              email: email,
              password: password,
              first_name: firstName,
              last_name: lastName,
              role: role,
              status: status,
              phone: phone
            })
          });

          const data = await response.json();

          if (data.success) {
            // Fermer le modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('addUserModal'));
            modal.hide();

            // Recharger la liste des utilisateurs
            await loadFirebaseUsers();

            alert('Utilisateur créé avec succès !');
          } else {
            alert('Erreur lors de la création: ' + data.message);
          }
        } catch (error) {
          console.error('Erreur:', error);
          alert('Erreur lors de la création de l\'utilisateur');
        }
      }

      async function editUser(userId) {
        try {
          // Charger les détails de l'utilisateur
          const response = await fetch(`/api/users.php?action=get&id=${userId}`, {
            headers: {
              Authorization: "Bearer demo_token",
              "Content-Type": "application/json"
            },
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const data = await response.json();

          if (data.success && data.data) {
            const user = data.data;

            // Créer un modal pour éditer l'utilisateur
            const modalHtml = `
              <div class="modal fade" id="editUserModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                  <div class="modal-content">
                    <div class="modal-header">
                      <h5 class="modal-title">Modifier l'Utilisateur</h5>
                      <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                      <form id="editUserForm">
                        <div class="row">
                          <div class="col-md-6">
                            <div class="mb-3">
                              <label for="editUserEmail" class="form-label">Email</label>
                              <input type="email" class="form-control" id="editUserEmail" value="${user.email || ''}" readonly>
                            </div>
                          </div>
                          <div class="col-md-6">
                            <div class="mb-3">
                              <label for="editUserDisplayName" class="form-label">Nom d'affichage</label>
                              <input type="text" class="form-control" id="editUserDisplayName" value="${user.display_name || ''}">
                            </div>
                          </div>
                        </div>
                        <div class="row">
                          <div class="col-md-6">
                            <div class="mb-3">
                              <label for="editUserRole" class="form-label">Rôle</label>
                              <select class="form-control" id="editUserRole">
                                <option value="user" ${user.role === 'user' ? 'selected' : ''}>Utilisateur</option>
                                <option value="merchant" ${user.role === 'merchant' ? 'selected' : ''}>Marchand</option>
                                <option value="admin" ${user.role === 'admin' ? 'selected' : ''}>Administrateur</option>
                              </select>
                            </div>
                          </div>
                          <div class="col-md-6">
                            <div class="mb-3">
                              <label for="editUserStatus" class="form-label">Statut</label>
                              <select class="form-control" id="editUserStatus">
                                <option value="active" ${user.status === 'active' ? 'selected' : ''}>Actif</option>
                                <option value="inactive" ${user.status === 'inactive' ? 'selected' : ''}>Inactif</option>
                              </select>
                            </div>
                          </div>
                        </div>
                      </form>
                    </div>
                    <div class="modal-footer">
                      <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                      <button type="button" class="btn btn-primary" onclick="submitUserEdit(${userId})">Sauvegarder</button>
                    </div>
                  </div>
                </div>
              </div>
            `;

            // Ajouter le modal au DOM s'il n'existe pas
            if (!document.getElementById('editUserModal')) {
              document.body.insertAdjacentHTML('beforeend', modalHtml);
            } else {
              // Mettre à jour le contenu du modal existant
              document.getElementById('editUserModal').outerHTML = modalHtml;
            }

            // Afficher le modal
            const modal = new bootstrap.Modal(document.getElementById('editUserModal'));
            modal.show();
          } else {
            throw new Error("Utilisateur non trouvé");
          }
        } catch (error) {
          console.error('Erreur lors du chargement de l\'utilisateur:', error);
          alert('Erreur lors du chargement des détails de l\'utilisateur: ' + error.message);
        }
      }

      // Charger l'état réel de la base de données
      async function loadRealDatabaseStatus() {
        await loadDatabaseStatus();
      }

      // Charger l'état de la base de données
      async function loadDatabaseStatus() {
        const container = document.getElementById("databaseStatus");

        try {
          // Utiliser notre nouvelle API pour le statut de la base de données
          const response = await fetch("/api/database-status.php");
          const result = await response.json();

          if (result.success) {
            const dbData = result.data;
            const tables = dbData.tables;

            // Afficher les données de la base de données
            container.innerHTML = `
              <div class="alert alert-success">
                <i class="fas fa-database me-2"></i>
                Base de données configurée avec succès ! Toutes les tables nécessaires sont présentes.
              </div>
              <div class="row">
                ${tables.map(table => `
                  <div class="col-md-6 col-lg-3 mb-3">
                    <div class="card">
                      <div class="card-body text-center">
                        <h6 class="card-title">${table.name}</h6>
                        <p class="card-text">
                          <strong>${table.count}</strong> enregistrements
                        </p>
                        <span class="badge ${table.status === 'ACTIVE' ? 'bg-success' : 'bg-warning'}">
                          ${table.status}
                        </span>
                      </div>
                    </div>
                  </div>
                `).join('')}
              </div>
            `;
          } else {
            throw new Error(result.error || 'Erreur lors du chargement du statut de la base de données');
          }
        } catch (error) {
          console.error('Erreur lors du chargement du statut de la base de données:', error);
          container.innerHTML = `
            <div class="alert alert-danger">
              <i class="fas fa-exclamation-triangle me-2"></i>
              Erreur de connexion à la base de données: ${error.message}
            </div>
          `;
        }
      }



      // Voir les détails d'un utilisateur
      async function viewUserDetails(uid) {
        try {
          // Afficher un indicateur de chargement
          const loadingHtml = `
            <div class="modal fade" id="userDetailsModal" tabindex="-1">
              <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                  <div class="modal-body text-center py-4">
                    <div class="spinner-border text-primary mb-3" role="status"></div>
                    <p class="mb-0">Chargement des détails de l'utilisateur...</p>
                  </div>
                </div>
              </div>
            </div>
          `;
          document.body.insertAdjacentHTML('beforeend', loadingHtml);
          const loadingModal = new bootstrap.Modal(document.getElementById('userDetailsModal'));
          loadingModal.show();

          // Charger les détails de l'utilisateur
          const response = await fetch(`/api/users.php?action=get&id=${uid}`, {
            headers: {
              'Authorization': 'Bearer demo_token',
              'Content-Type': 'application/json'
            }
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const user = await response.json();

          // Créer le contenu du modal avec les détails
          const modalHtml = `
            <div class="modal fade" id="userDetailsModal" tabindex="-1">
              <div class="modal-dialog modal-lg">
                <div class="modal-content">
                  <div class="modal-header">
                    <h5 class="modal-title">Détails de l'utilisateur</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                  </div>
                  <div class="modal-body">
                    <div class="row">
                      <div class="col-md-6">
                        <h6 class="mb-3">Informations de base</h6>
                        <p><strong>ID:</strong> ${user.uid || 'Non défini'}</p>
                        <p><strong>Email:</strong> ${user.email || 'Non défini'}</p>
                        <p><strong>Nom:</strong> ${user.display_name || 'Non défini'}</p>
                        <p><strong>Téléphone:</strong> ${user.phone || 'Non défini'}</p>
                      </div>
                      <div class="col-md-6">
                        <h6 class="mb-3">Statut et rôles</h6>
                        <p>
                          <strong>Statut:</strong>
                          <span class="badge ${user.status === 'active' ? 'bg-success' : 'bg-danger'}">
                            ${user.status === 'active' ? 'ACTIF' : 'INACTIF'}
                          </span>
                        </p>
                        <p>
                          <strong>Rôle:</strong>
                          <span class="badge ${user.role === 'admin' ? 'bg-primary' : user.role === 'seller' ? 'bg-info' : 'bg-secondary'}">
                            ${(user.role || 'user').toUpperCase()}
                          </span>
                        </p>
                        <p><strong>Dernière connexion:</strong> ${user.last_login ? new Date(user.last_login).toLocaleString('fr-FR') : 'Jamais'}</p>
                      </div>
                    </div>

                    <div class="mt-4">
                      <h6 class="mb-3">Activité récente</h6>
                      <div class="table-responsive">
                        <table class="table table-sm">
                          <thead>
                            <tr>
                              <th>Date</th>
                              <th>Action</th>
                              <th>Détails</th>
                            </tr>
                          </thead>
                          <tbody>
                            ${user.activity && user.activity.length > 0 ?
                              user.activity.map(act => `
                                <tr>
                                  <td>${new Date(act.date).toLocaleString('fr-FR')}</td>
                                  <td>${act.action}</td>
                                  <td>${act.details || '-'}</td>
                                </tr>
                              `).join('') :
                              '<tr><td colspan="3" class="text-center">Aucune activité enregistrée</td></tr>'
                            }
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>
                  <div class="modal-footer">
                    <button type="button" class="btn btn-outline-primary" onclick="editUser('${uid}')">
                      <i class="fas fa-edit me-1"></i>Modifier
                    </button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                  </div>
                </div>
              </div>
            </div>
          `;

          // Fermer le modal de chargement et afficher le modal des détails
          loadingModal.hide();
          document.getElementById('userDetailsModal').remove();
          document.body.insertAdjacentHTML('beforeend', modalHtml);
          const detailsModal = new bootstrap.Modal(document.getElementById('userDetailsModal'));
          detailsModal.show();

        } catch (error) {
          console.error('❌ Erreur:', error);
          alert(`Erreur lors du chargement des détails de l'utilisateur: ${error.message}`);
          // Fermer le modal de chargement en cas d'erreur
          const loadingModal = bootstrap.Modal.getInstance(document.getElementById('userDetailsModal'));
          if (loadingModal) {
            loadingModal.hide();
            document.getElementById('userDetailsModal').remove();
          }
        }
      }


      // Gestion de la déconnexion
      function handleLogout() {
        console.log("🚪 Déconnexion (mode développement)");

        // En mode développement, on peut soit recharger la page soit rediriger
        if (confirm("Voulez-vous vous déconnecter ?")) {
          // Simuler une déconnexion
          currentUser = null;

          // Rediriger vers la page de connexion ou recharger
          window.location.href = "login.html";
        }
      }

      // Navigation entre les sections
      function showSection(sectionName) {
        // Masquer toutes les sections
        const sections = document.querySelectorAll(".section-content");
        sections.forEach((section) => {
          section.style.display = "none";
        });

        // Afficher la section demandée
        const targetSection = document.getElementById(sectionName + "-section");
        if (targetSection) {
          targetSection.style.display = "block";
        }

        // Mettre à jour la navigation active
        const navLinks = document.querySelectorAll(".nav-link");
        navLinks.forEach((link) => {
          link.classList.remove("active");
        });

        const activeLink = document.querySelector(
          `[data-section="${sectionName}"]`
        );
        if (activeLink) {
          activeLink.classList.add("active");
        }

        // Charger les données de la section
        loadSectionData(sectionName);
      }

      // Charger les données d'une section
      function loadSectionData(sectionName) {
        switch (sectionName) {
          case "stores":
            loadStores();
            break;
          case "products":
            loadProducts();
            break;
          case "landing-pages":
            loadLandingPages();
            break;
          case "roles":
            loadRoles();
            break;
          case "subscriptions":
            loadSubscriptions();
            break;
          case "orders":
            loadOrders();
            break;
          case "payments":
            loadPayments();
            break;
          case "analytics":
            loadAnalytics();
            break;
          case "ai-usage":
            loadAiUsage();
            break;
          case "settings":
            loadSettings();
            break;
          case "firebase-users":
            loadFirebaseUsers();
            break;
          case "contact-messages":
            loadContactMessages();
            break;
          case "overview":
          default:
            loadDatabaseStatus();
            break;
        }
      }

      // Fonctions de gestion des stores
      async function loadStores() {
        const container = document.getElementById("storesContainer");

        try {
          // Afficher un indicateur de chargement
          container.innerHTML =
            '<div class="text-center py-4"><div class="spinner-border" role="status"></div><p>Chargement des stores...</p></div>';

          // Charger les stores depuis l'API
          const response = await fetch("/api/stores-simple.php", {
            headers: {
              "Content-Type": "application/json",
              "Authorization": "Bearer " + DEMO_TOKEN
            },
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const data = await response.json();
          console.log("📊 Réponse API stores:", data);

          if (data.success && data.data) {
            // Gérer les deux formats de réponse possibles
            let stores = [];
            if (data.data.stores && Array.isArray(data.data.stores)) {
              // Format avec pagination: {data: {stores: [...], total: X}}
              stores = data.data.stores;
            } else if (Array.isArray(data.data)) {
              // Format tableau direct: {data: [...]}
              stores = data.data;
            } else {
              // Format objet unique: {data: {...}}
              stores = [data.data];
            }

            if (stores.length === 0) {
              container.innerHTML = `
                <div class="alert alert-info text-center">
                  <i class="fas fa-store fa-3x mb-3"></i>
                  <h5>Aucun store trouvé</h5>
                  <p>Commencez par créer votre premier store.</p>
                  <button class="btn btn-primary" onclick="addNewStore()">
                    <i class="fas fa-plus me-1"></i>Créer un store
                  </button>
                </div>
              `;
              return;
            }

            let html = `
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>ID</th>
                                <th>Nom du Store</th>
                                <th>Propriétaire</th>
                                <th>URL du Store</th>
                                <th>Statut</th>
                                <th>Produits</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            stores.forEach((store) => {
              const statusBadge =
                store.status === "active" ? "bg-success" : "bg-danger";
              // Utiliser les bons noms de champs selon la structure de la base
              const storeName = store.store_name_ar || store.name_ar || store.store_name || store.name || "Sans nom";
              const storeDesc = store.description_ar || store.description || "";
              const ownerName = store.owner_name || store.merchant_name || "Propriétaire inconnu";
              const ownerEmail = store.owner_email || store.merchant_email || "";
              const storeUrl = store.domain ? `https://${store.domain}` : (store.subdomain ? `https://${store.subdomain}.landingpage.dz` : "Non configuré");

              html += `
                    <tr>
                        <td><strong>#${store.id}</strong></td>
                        <td>
                          <div class="fw-bold">${storeName}</div>
                          <small class="text-muted">${storeDesc.substring(0, 50)}${storeDesc.length > 50 ? '...' : ''}</small>
                        </td>
                        <td>
                          <div class="fw-bold">${ownerName}</div>
                          <small class="text-muted">${ownerEmail}</small>
                        </td>
                        <td>
                          ${storeUrl !== "Non configuré" ?
                            `<a href="${storeUrl}" target="_blank" class="text-decoration-none">
                              <i class="fas fa-external-link-alt me-1"></i>${storeUrl}
                            </a>` :
                            '<span class="text-muted">Non configuré</span>'
                          }
                        </td>
                        <td><span class="badge ${statusBadge}">${store.status ? store.status.toUpperCase() : 'INCONNU'}</span></td>
                        <td><span class="badge bg-info">${store.product_count || 0}</span></td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" onclick="editStore(${store.id})" title="Modifier">
                                    <i class="fas fa-edit"></i>
                                </button>
                                ${storeUrl !== "Non configuré" ?
                                  `<button class="btn btn-outline-success" onclick="window.open('${storeUrl}', '_blank')" title="Visiter">
                                    <i class="fas fa-external-link-alt"></i>
                                  </button>` : ''
                                }
                                <button class="btn btn-outline-info" onclick="viewStoreDetails(${store.id})" title="Détails">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-outline-danger" onclick="deleteStore(${store.id})" title="Supprimer">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
            });

            html += `
                        </tbody>
                    </table>
                </div>
            `;

            container.innerHTML = html;
            console.log("✅ Stores chargés:", stores);
          } else {
            throw new Error("Aucune donnée de store trouvée");
          }
        } catch (error) {
          console.error("❌ Erreur lors du chargement des stores:", error);
          container.innerHTML = `
            <div class="alert alert-danger">
              <i class="fas fa-exclamation-triangle me-2"></i>
              Erreur lors du chargement des stores: ${error.message}
            </div>
          `;
        }
      }

      function addNewStore() {
        // Créer un modal pour ajouter un nouveau store
        const modalHtml = `
          <div class="modal fade" id="addStoreModal" tabindex="-1">
            <div class="modal-dialog">
              <div class="modal-content">
                <div class="modal-header">
                  <h5 class="modal-title">Ajouter un nouveau Store</h5>
                  <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                  <form id="addStoreForm">
                    <div class="mb-3">
                      <label for="storeNameAr" class="form-label">Nom du Store (Arabe)</label>
                      <input type="text" class="form-control" id="storeNameAr" dir="rtl">
                    </div>
                    <div class="mb-3">
                      <label for="storeNameEn" class="form-label">Nom du Store (Anglais)</label>
                      <input type="text" class="form-control" id="storeNameEn">
                    </div>
                    <div class="mb-3">
                      <label for="storeDescriptionAr" class="form-label">Description (Arabe)</label>
                      <textarea class="form-control" id="storeDescriptionAr" rows="3" dir="rtl"></textarea>
                    </div>
                    <div class="mb-3">
                      <label for="storeDescriptionEn" class="form-label">Description (Anglais)</label>
                      <textarea class="form-control" id="storeDescriptionEn" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                      <label for="storeDomain" class="form-label">Domaine (optionnel)</label>
                      <input type="text" class="form-control" id="storeDomain" placeholder="exemple.com">
                    </div>
                  </form>
                </div>
                <div class="modal-footer">
                  <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                  <button type="button" class="btn btn-primary" onclick="submitNewStore()">Créer Store</button>
                </div>
              </div>
            </div>
          </div>
        `;

        // Ajouter le modal au DOM s'il n'existe pas
        if (!document.getElementById("addStoreModal")) {
          document.body.insertAdjacentHTML("beforeend", modalHtml);
        }

        // Afficher le modal
        const modal = new bootstrap.Modal(
          document.getElementById("addStoreModal")
        );
        modal.show();
      }

      async function submitNewStore() {
        const nameAr = document.getElementById("storeNameAr").value;
        const nameEn = document.getElementById("storeNameEn").value;
        const descriptionAr = document.getElementById("storeDescriptionAr").value;
        const descriptionEn = document.getElementById("storeDescriptionEn").value;
        const domain = document.getElementById("storeDomain").value;

        if (!nameAr.trim() && !nameEn.trim()) {
          alert("Le nom du store est requis (en arabe ou en anglais)");
          return;
        }

        try {
          // Utiliser le nom principal requis par l'API
          const storeName = nameAr.trim() || nameEn.trim();
          const storeDescription = descriptionAr.trim() || descriptionEn.trim();

          const response = await fetch("/api/stores-simple.php", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "Authorization": "Bearer " + DEMO_TOKEN
            },
            body: JSON.stringify({
              name: storeName,
              description: storeDescription,
              user_id: "firebase_uid_1", // Utiliser l'ID utilisateur approprié
              status: "active",
            }),
          });

          const data = await response.json();
          console.log("📊 Réponse création store:", data);

          if (data.success) {
            // Fermer le modal
            const modal = bootstrap.Modal.getInstance(
              document.getElementById("addStoreModal")
            );
            modal.hide();

            // Recharger la liste des stores
            await loadStores();

            alert("Store créé avec succès !");
          } else {
            alert("Erreur lors de la création: " + (data.error || data.message || "Erreur inconnue"));
          }
        } catch (error) {
          console.error("❌ Erreur création store:", error);
          alert("Erreur lors de la création du store: " + error.message);
        }
      }

      async function viewStoreDetails(id) {
        try {
          const response = await fetch(`/api/stores-simple.php?id=${id}`, {
            headers: {
              Authorization: "Bearer " + DEMO_TOKEN,
              "Content-Type": "application/json",
            },
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const data = await response.json();

          if (data.success && data.data) {
            const store = data.data;
            const modalHtml = `
              <div class="modal fade" id="viewStoreModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                  <div class="modal-content">
                    <div class="modal-header">
                      <h5 class="modal-title">Détails du Store</h5>
                      <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                      <div class="row">
                        <div class="col-md-6">
                          <h6 class="mb-3">Informations en Arabe</h6>
                          <div class="mb-3" dir="rtl">
                            <label class="form-label fw-bold">Nom</label>
                            <p>${store.name_ar || 'Non défini'}</p>
                          </div>
                          <div class="mb-3" dir="rtl">
                            <label class="form-label fw-bold">Description</label>
                            <p>${store.description_ar || 'Non défini'}</p>
                          </div>
                        </div>
                        <div class="col-md-6">
                          <h6 class="mb-3">Informations en Anglais</h6>
                          <div class="mb-3">
                            <label class="form-label fw-bold">Nom</label>
                            <p>${store.name_en || 'Non défini'}</p>
                          </div>
                          <div class="mb-3">
                            <label class="form-label fw-bold">Description</label>
                            <p>${store.description_en || 'Non défini'}</p>
                          </div>
                        </div>
                      </div>
                      <hr>
                      <div class="row">
                        <div class="col-md-6">
                          <div class="mb-3">
                            <label class="form-label fw-bold">Domaine</label>
                            <p>${store.domain || 'Non configuré'}</p>
                          </div>
                          <div class="mb-3">
                            <label class="form-label fw-bold">Statut</label>
                            <p><span class="badge ${store.status === 'active' ? 'bg-success' : 'bg-danger'}">${store.status.toUpperCase()}</span></p>
                          </div>
                        </div>
                        <div class="col-md-6">
                          <div class="mb-3">
                            <label class="form-label fw-bold">Nombre de Produits</label>
                            <p>${store.product_count || 0}</p>
                          </div>
                          <div class="mb-3">
                            <label class="form-label fw-bold">Date de Création</label>
                            <p>${new Date(store.created_at).toLocaleString()}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="modal-footer">
                      <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                      <button type="button" class="btn btn-primary" onclick="editStore(${store.id})">Modifier</button>
                    </div>
                  </div>
                </div>
              </div>
            `;

            // Ajouter le modal au DOM s'il n'existe pas déjà
            if (!document.getElementById("viewStoreModal")) {
              document.body.insertAdjacentHTML("beforeend", modalHtml);
            }

            // Afficher le modal
            const modal = new bootstrap.Modal(document.getElementById("viewStoreModal"));
            modal.show();
          } else {
            throw new Error("Aucune donnée de store trouvée");
          }
        } catch (error) {
          console.error("❌ Erreur lors du chargement des détails du store:", error);
          alert(`Erreur: ${error.message}`);
        }
      }

      async function editStore(id) {
        try {
          console.log("🔧 Édition du store ID:", id);
          const response = await fetch(`/api/stores-simple.php?id=${id}`, {
            headers: {
              Authorization: "Bearer " + DEMO_TOKEN,
              "Content-Type": "application/json",
            },
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const data = await response.json();
          console.log("📊 Données du store à éditer:", data);

          if (data.success && data.data) {
            const store = data.data;
            const modalHtml = `
              <div class="modal fade" id="editStoreModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                  <div class="modal-content">
                    <div class="modal-header">
                      <h5 class="modal-title">Modifier le Store</h5>
                      <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                      <form id="editStoreForm">
                        <input type="hidden" id="editStoreId" value="${store.id}">
                        <div class="row">
                          <div class="col-md-6">
                            <h6 class="mb-3">Informations en Arabe</h6>
                            <div class="mb-3">
                              <label for="editStoreNameAr" class="form-label">Nom (Arabe)</label>
                              <input type="text" class="form-control" id="editStoreNameAr" value="${store.store_name_ar || store.name_ar || ''}" dir="rtl">
                            </div>
                            <div class="mb-3">
                              <label for="editStoreDescriptionAr" class="form-label">Description (Arabe)</label>
                              <textarea class="form-control" id="editStoreDescriptionAr" rows="3" dir="rtl">${store.description_ar || ''}</textarea>
                            </div>
                          </div>
                          <div class="col-md-6">
                            <h6 class="mb-3">Informations en Anglais</h6>
                            <div class="mb-3">
                              <label for="editStoreNameEn" class="form-label">Nom (Anglais)</label>
                              <input type="text" class="form-control" id="editStoreNameEn" value="${store.store_name_en || store.name_en || ''}">
                            </div>
                            <div class="mb-3">
                              <label for="editStoreDescriptionEn" class="form-label">Description (Anglais)</label>
                              <textarea class="form-control" id="editStoreDescriptionEn" rows="3">${store.description_en || ''}</textarea>
                            </div>
                          </div>
                        </div>
                        <div class="row">
                          <div class="col-md-6">
                            <div class="mb-3">
                              <label for="editStoreDomain" class="form-label">Domaine</label>
                              <input type="text" class="form-control" id="editStoreDomain" value="${store.domain || ''}">
                            </div>
                          </div>
                          <div class="col-md-6">
                            <div class="mb-3">
                              <label for="editStoreStatus" class="form-label">Statut</label>
                              <select class="form-select" id="editStoreStatus">
                                <option value="active" ${store.status === 'active' ? 'selected' : ''}>Actif</option>
                                <option value="inactive" ${store.status === 'inactive' ? 'selected' : ''}>Inactif</option>
                              </select>
                            </div>
                          </div>
                        </div>
                      </form>
                    </div>
                    <div class="modal-footer">
                      <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                      <button type="button" class="btn btn-primary" onclick="submitEditStore(${store.id})">Enregistrer</button>
                    </div>
                  </div>
                </div>
              </div>
            `;

            // Supprimer le modal existant s'il existe
            const existingModal = document.getElementById("editStoreModal");
            if (existingModal) {
              existingModal.remove();
            }

            // Ajouter le nouveau modal
            document.body.insertAdjacentHTML("beforeend", modalHtml);

            // Afficher le modal
            const modal = new bootstrap.Modal(document.getElementById("editStoreModal"));
            modal.show();
          } else {
            throw new Error("Aucune donnée de store trouvée");
          }
        } catch (error) {
          console.error("❌ Erreur lors du chargement du store:", error);
          alert(`Erreur: ${error.message}`);
        }
      }
      async function submitEditStore(storeId) {
        const nameAr = document.getElementById('editStoreNameAr').value;
        const nameEn = document.getElementById('editStoreNameEn').value;
        const descriptionAr = document.getElementById('editStoreDescriptionAr').value;
        const descriptionEn = document.getElementById('editStoreDescriptionEn').value;
        const domain = document.getElementById('editStoreDomain').value;
        const status = document.getElementById('editStoreStatus').value;

        if (!nameAr.trim() && !nameEn.trim()) {
          alert('Veuillez saisir au moins un nom de store (Arabe ou Anglais)');
          return;
        }

        try {
          // Utiliser le nom principal requis par l'API
          const storeName = nameAr.trim() || nameEn.trim();
          const storeDescription = descriptionAr.trim() || descriptionEn.trim();

          const response = await fetch(`/api/stores-simple.php/${storeId}`, {
            method: 'PUT',
            headers: {
              Authorization: "Bearer " + DEMO_TOKEN,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              name: storeName,
              description: storeDescription
            })
          });

          const data = await response.json();
          console.log("📊 Réponse modification store:", data);

          if (data.success) {
            // Fermer le modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('editStoreModal'));
            modal.hide();

            // Recharger la liste des stores
            await loadStores();

            alert('Store modifié avec succès !');
          } else {
            alert('Erreur lors de la modification: ' + (data.error || data.message || "Erreur inconnue"));
          }
        } catch (error) {
          console.error('❌ Erreur modification store:', error);
          alert('Erreur lors de la modification du store: ' + error.message);
        }
      }

      async function deleteStore(id) {
        if (confirm("Êtes-vous sûr de vouloir supprimer ce store ?")) {
          try {
            const response = await fetch(`/api/stores-simple.php/${id}`, {
              method: "DELETE",
              headers: {
                Authorization: "Bearer " + DEMO_TOKEN,
                "Content-Type": "application/json",
              },
            });

            const data = await response.json();

            if (data.success) {
              alert("Store supprimé avec succès !");
              await loadStores(); // Recharger la liste
            } else {
              alert("Erreur lors de la suppression: " + (data.error || data.message || "Erreur inconnue"));
            }
          } catch (error) {
            console.error("❌ Erreur suppression store:", error);
            alert("Erreur lors de la suppression du store: " + error.message);
          }
        }
      }

      // Fonctions pour les produits
      async function addNewProduct() {
        alert("Fonctionnalité d'ajout de produit à implémenter");
      }

      async function editProduct(id) {
        alert(`Fonctionnalité d'édition du produit ${id} à implémenter`);
      }

      async function deleteProduct(id) {
        if (confirm("Êtes-vous sûr de vouloir supprimer ce produit ?")) {
          alert(`Fonctionnalité de suppression du produit ${id} à implémenter`);
        }
      }

      async function viewProductDetails(id) {
        alert(`Fonctionnalité d'affichage des détails du produit ${id} à implémenter`);
      }

      // Fonctions de gestion des produits
      async function loadProducts() {
        const container = document.getElementById("productsContainer");

        try {
          // Afficher un indicateur de chargement
          container.innerHTML =
            '<div class="text-center py-4"><div class="spinner-border" role="status"></div><p>Chargement des produits...</p></div>';

          // Charger les produits depuis l'API simple
          const response = await fetch("/api/products-simple.php", {
            headers: {
              "Content-Type": "application/json",
            },
          });

          console.log("📊 Réponse API produits - Status:", response.status);

          if (!response.ok) {
            // Si l'API simple échoue, essayer l'API principale
            console.log("⚠️ API simple échoue, tentative avec l'API principale...");
            try {
              const fallbackResponse = await fetch("/api/products.php", {
                headers: {
                  Authorization: "Bearer " + DEMO_TOKEN,
                  "Content-Type": "application/json",
                },
              });

              if (fallbackResponse.ok) {
                const fallbackData = await fallbackResponse.json();
                console.log("📊 Données produits (fallback):", fallbackData);
                // Traiter les données du fallback ici si nécessaire
              }
            } catch (fallbackError) {
              console.log("⚠️ Fallback échoué, chargement des données simulées...");
              await loadProductsDirect();
              return;
            }
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const data = await response.json();
          console.log("📊 Données produits reçues:", data);

          if (data.success && data.data && data.data.products) {
            const products = data.data.products;

            if (products.length === 0) {
              container.innerHTML = `
                <div class="alert alert-info text-center">
                  <i class="fas fa-box fa-3x mb-3"></i>
                  <h5>Aucun produit trouvé</h5>
                  <p>Commencez par ajouter votre premier produit.</p>
                  <button class="btn btn-primary" onclick="addNewProduct()">
                    <i class="fas fa-plus me-1"></i>Ajouter un produit
                  </button>
                </div>
              `;
              return;
            }

            let html = `
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>ID</th>
                                <th>Nom</th>
                                <th>Prix</th>
                                <th>Statut</th>
                                <th>Stock</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            products.forEach((product) => {
              const statusBadge =
                product.status === "active" ? "bg-success" : "bg-danger";
              const productName = product.name_ar || product.name_en || product.name || "Sans nom";
              html += `
                    <tr>
                        <td>${product.id}</td>
                        <td>
                          <div class="fw-bold">${productName}</div>
                          <small class="text-muted">SKU: ${product.sku || 'N/A'}</small>
                        </td>
                        <td>${
                          product.price ? parseFloat(product.price).toLocaleString() + " DA" : "Non défini"
                        }</td>
                        <td><span class="badge ${statusBadge}">${product.status ? product.status.toUpperCase() : 'INCONNU'}</span></td>
                        <td>${product.stock_quantity || 0}</td>
                        <td>
                            <div class="btn-group">
                                <button class="btn btn-sm btn-outline-primary" onclick="editProduct(${product.id})" title="Modifier">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="deleteProduct(${product.id})" title="Supprimer">
                                    <i class="fas fa-trash"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-info" onclick="viewProductDetails(${product.id})" title="Détails">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
            });

            html += `
                        </tbody>
                    </table>
                </div>
            `;

            container.innerHTML = html;
            console.log("✅ Produits chargés:", products);
          } else {
            throw new Error("Aucune donnée de produit trouvée");
          }
        } catch (error) {
          console.error("❌ Erreur lors du chargement des produits:", error);
          container.innerHTML = `
            <div class="alert alert-danger">
              <i class="fas fa-exclamation-triangle me-2"></i>
              Erreur lors du chargement des produits: ${error.message}
              <br><small>Vérifiez que l'API est accessible et que l'authentification fonctionne.</small>
            </div>
          `;
        }
      }

      // Fonction de fallback pour charger les produits directement depuis la base
      async function loadProductsDirect() {
        const container = document.getElementById("productsContainer");

        try {
          // Simuler des données de produits basées sur ce qui est dans la base
          const mockProducts = [
            {
              id: 1,
              name: "Test smartphone",
              name_ar: "هاتف ذكي تجريبي",
              name_en: "Test smartphone",
              sku: "TEST-PRODUCT-001",
              price: 25000.00,
              status: "active",
              stock_quantity: 10
            },
            {
              id: 2,
              name: "Test Product",
              name_ar: "منتج تجريبي",
              name_en: "Test Product",
              sku: "PRD-6888A1788BAF0",
              price: 15000.00,
              status: "active",
              stock_quantity: 10
            }
          ];

          let html = `
              <div class="alert alert-warning mb-3">
                <i class="fas fa-exclamation-triangle me-2"></i>
                Chargement en mode fallback - Données simulées
              </div>
              <div class="table-responsive">
                  <table class="table table-hover">
                      <thead class="table-light">
                          <tr>
                              <th>ID</th>
                              <th>Nom</th>
                              <th>Prix</th>
                              <th>Statut</th>
                              <th>Stock</th>
                              <th>Actions</th>
                          </tr>
                      </thead>
                      <tbody>
          `;

          mockProducts.forEach((product) => {
            const statusBadge = product.status === "active" ? "bg-success" : "bg-danger";
            const productName = product.name_ar || product.name_en || product.name || "Sans nom";
            html += `
                  <tr>
                      <td>${product.id}</td>
                      <td>
                        <div class="fw-bold">${productName}</div>
                        <small class="text-muted">SKU: ${product.sku || 'N/A'}</small>
                      </td>
                      <td>${
                        product.price ? parseFloat(product.price).toLocaleString() + " DA" : "Non défini"
                      }</td>
                      <td><span class="badge ${statusBadge}">${product.status.toUpperCase()}</span></td>
                      <td>${product.stock_quantity || 0}</td>
                      <td>
                          <div class="btn-group">
                              <button class="btn btn-sm btn-outline-primary" onclick="editProduct(${product.id})" title="Modifier">
                                  <i class="fas fa-edit"></i>
                              </button>
                              <button class="btn btn-sm btn-outline-danger" onclick="deleteProduct(${product.id})" title="Supprimer">
                                  <i class="fas fa-trash"></i>
                              </button>
                              <button class="btn btn-sm btn-outline-info" onclick="viewProductDetails(${product.id})" title="Détails">
                                  <i class="fas fa-eye"></i>
                              </button>
                          </div>
                      </td>
                  </tr>
              `;
          });

          html += `
                      </tbody>
                  </table>
              </div>
          `;

          container.innerHTML = html;
          console.log("✅ Produits chargés en mode fallback:", mockProducts);
        } catch (error) {
          console.error("❌ Erreur dans loadProductsDirect:", error);
          container.innerHTML = `
            <div class="alert alert-danger">
              <i class="fas fa-exclamation-triangle me-2"></i>
              Impossible de charger les produits: ${error.message}
            </div>
          `;
        }
      }

      function addNewProduct() {
        // Créer un modal pour ajouter un nouveau produit
        const modalHtml = `
          <div class="modal fade" id="addProductModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
              <div class="modal-content">
                <div class="modal-header">
                  <h5 class="modal-title">Ajouter un nouveau Produit</h5>
                  <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                  <form id="addProductForm">
                    <div class="row">
                      <div class="col-md-6">
                        <div class="mb-3">
                          <label for="productName" class="form-label">Nom du Produit</label>
                          <input type="text" class="form-control" id="productName" required>
                        </div>
                      </div>
                      <div class="col-md-6">
                        <div class="mb-3">
                          <label for="productPrice" class="form-label">Prix (DA)</label>
                          <input type="number" class="form-control" id="productPrice" step="0.01" required>
                        </div>
                      </div>
                    </div>
                    <div class="mb-3">
                      <label for="productDescription" class="form-label">Description</label>
                      <textarea class="form-control" id="productDescription" rows="3"></textarea>
                    </div>
                    <div class="row">
                      <div class="col-md-6">
                        <div class="mb-3">
                          <label for="productStock" class="form-label">Stock</label>
                          <input type="number" class="form-control" id="productStock" value="0">
                        </div>
                      </div>
                      <div class="col-md-6">
                        <div class="mb-3">
                          <label for="productStatus" class="form-label">Statut</label>
                          <select class="form-control" id="productStatus">
                            <option value="active">Actif</option>
                            <option value="inactive">Inactif</option>
                          </select>
                        </div>
                      </div>
                    </div>
                  </form>
                </div>
                <div class="modal-footer">
                  <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                  <button type="button" class="btn btn-primary" onclick="submitNewProduct()">Créer Produit</button>
                </div>
              </div>
            </div>
          </div>
        `;

        // Ajouter le modal au DOM s'il n'existe pas
        if (!document.getElementById("addProductModal")) {
          document.body.insertAdjacentHTML("beforeend", modalHtml);
        }

        // Afficher le modal
        const modal = new bootstrap.Modal(
          document.getElementById("addProductModal")
        );
        modal.show();
      }

      async function submitNewProduct() {
        const name = document.getElementById("productName").value;
        const price = document.getElementById("productPrice").value;
        const description = document.getElementById("productDescription").value;
        const stock = document.getElementById("productStock").value;
        const status = document.getElementById("productStatus").value;

        if (!name.trim() || !price) {
          alert("Le nom et le prix du produit sont requis");
          return;
        }

        try {
          const response = await fetch("/api/products.php", {
            method: "POST",
            headers: {
              Authorization: "Bearer demo_token",
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              name_ar: name,
              name_en: name,
              description_ar: description,
              description_en: description,
              price: parseFloat(price),
              stock_quantity: parseInt(stock) || 0,
              status: status,
            }),
          });

          const data = await response.json();

          if (data.success) {
            // Fermer le modal
            const modal = bootstrap.Modal.getInstance(
              document.getElementById("addProductModal")
            );
            modal.hide();

            // Recharger la liste des produits
            await loadProducts();

            alert("Produit créé avec succès !");
          } else {
            alert("Erreur lors de la création: " + data.message);
          }
        } catch (error) {
          console.error("Erreur:", error);
          alert("Erreur lors de la création du produit");
        }
      }

      async function editProduct(id) {
        try {
          // Charger les détails du produit
          const response = await fetch(`/api/products.php/${id}`, {
            headers: {
              Authorization: "Bearer demo_token",
              "Content-Type": "application/json",
            },
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const data = await response.json();

          if (data.success && data.data) {
            const product = data.data;

            // Créer un modal pour éditer le produit
            const modalHtml = `
              <div class="modal fade" id="editProductModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                  <div class="modal-content">
                    <div class="modal-header">
                      <h5 class="modal-title">Modifier le Produit</h5>
                      <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                      <form id="editProductForm">
                        <div class="row">
                          <div class="col-md-6">
                            <div class="mb-3">
                              <label for="editProductName" class="form-label">Nom du Produit</label>
                              <input type="text" class="form-control" id="editProductName" value="${
                                product.name_ar || product.name_en || ""
                              }" required>
                            </div>
                          </div>
                          <div class="col-md-6">
                            <div class="mb-3">
                              <label for="editProductPrice" class="form-label">Prix (DZD)</label>
                              <input type="number" class="form-control" id="editProductPrice" step="0.01" value="${
                                product.price || 0
                              }" required>
                            </div>
                          </div>
                        </div>
                        <div class="mb-3">
                          <label for="editProductDescription" class="form-label">Description</label>
                          <textarea class="form-control" id="editProductDescription" rows="3">${
                            product.description_ar ||
                            product.description_en ||
                            ""
                          }</textarea>
                        </div>
                        <div class="row">
                          <div class="col-md-6">
                            <div class="mb-3">
                              <label for="editProductStock" class="form-label">Stock</label>
                              <input type="number" class="form-control" id="editProductStock" value="${
                                product.stock_quantity || 0
                              }">
                            </div>
                          </div>
                          <div class="col-md-6">
                            <div class="mb-3">
                              <label for="editProductStatus" class="form-label">Statut</label>
                              <select class="form-control" id="editProductStatus">
                                <option value="active" ${
                                  product.status === "active" ? "selected" : ""
                                }>Actif</option>
                                <option value="inactive" ${
                                  product.status === "inactive"
                                    ? "selected"
                                    : ""
                                }>Inactif</option>
                              </select>
                            </div>
                          </div>
                        </div>
                        <div class="row">
                          <div class="col-md-6">
                            <div class="mb-3">
                              <label for="editProductSKU" class="form-label">SKU</label>
                              <input type="text" class="form-control" id="editProductSKU" value="${
                                product.sku || ""
                              }" readonly>
                            </div>
                          </div>
                          <div class="col-md-6">
                            <div class="mb-3">
                              <label for="editProductComparePrice" class="form-label">Prix de Comparaison (DZD)</label>
                              <input type="number" class="form-control" id="editProductComparePrice" step="0.01" value="${
                                product.compare_price || ""
                              }">
                            </div>
                          </div>
                        </div>
                      </form>
                    </div>
                    <div class="modal-footer">
                      <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                      <button type="button" class="btn btn-primary" onclick="submitProductEdit(${id})">Sauvegarder</button>
                    </div>
                  </div>
                </div>
              </div>
            `;

            // Ajouter le modal au DOM s'il n'existe pas
            if (!document.getElementById("editProductModal")) {
              document.body.insertAdjacentHTML("beforeend", modalHtml);
            } else {
              // Mettre à jour le contenu du modal existant
              document.getElementById("editProductModal").outerHTML = modalHtml;
            }

            // Afficher le modal
            const modal = new bootstrap.Modal(
              document.getElementById("editProductModal")
            );
            modal.show();
          } else {
            throw new Error("Produit non trouvé");
          }
        } catch (error) {
          console.error("Erreur lors du chargement du produit:", error);
          alert(
            "Erreur lors du chargement des détails du produit: " + error.message
          );
        }
      }

      async function submitProductEdit(productId) {
        const name = document.getElementById("editProductName").value;
        const price = document.getElementById("editProductPrice").value;
        const description = document.getElementById(
          "editProductDescription"
        ).value;
        const stock = document.getElementById("editProductStock").value;
        const status = document.getElementById("editProductStatus").value;
        const comparePrice = document.getElementById(
          "editProductComparePrice"
        ).value;

        if (!name.trim() || !price) {
          alert("Le nom et le prix du produit sont requis");
          return;
        }

        try {
          const response = await fetch(`/api/products.php/${productId}`, {
            method: "PUT",
            headers: {
              Authorization: "Bearer demo_token",
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              name_ar: name,
              name_en: name,
              description_ar: description,
              description_en: description,
              price: parseFloat(price),
              compare_price: comparePrice ? parseFloat(comparePrice) : null,
              stock_quantity: parseInt(stock) || 0,
              status: status,
            }),
          });

          const data = await response.json();

          if (data.success) {
            // Fermer le modal
            const modal = bootstrap.Modal.getInstance(
              document.getElementById("editProductModal")
            );
            modal.hide();

            // Recharger la liste des produits
            await loadProducts();

            alert("Produit modifié avec succès !");
          } else {
            alert("Erreur lors de la modification: " + data.message);
          }
        } catch (error) {
          console.error("Erreur:", error);
          alert("Erreur lors de la modification du produit");
        }
      }

      async function deleteProduct(id) {
        if (confirm("Êtes-vous sûr de vouloir supprimer ce produit ?")) {
          try {
            const response = await fetch(`/api/products.php/${id}`, {
              method: "DELETE",
              headers: {
                Authorization: "Bearer demo_token",
                "Content-Type": "application/json",
              },
            });

            const data = await response.json();

            if (data.success) {
              alert("Produit supprimé avec succès !");
              await loadProducts(); // Recharger la liste
            } else {
              alert("Erreur lors de la suppression: " + data.message);
            }
          } catch (error) {
            console.error("Erreur:", error);
            alert("Erreur lors de la suppression du produit");
          }
        }
      }

      // Fonctions de gestion des rôles
      async function loadRoles() {
        const container = document.getElementById("rolesContainer");

        try {
          // Afficher un indicateur de chargement
          container.innerHTML = '<div class="text-center py-4"><div class="spinner-border" role="status"></div><p>Chargement des rôles...</p></div>';

          // Charger les rôles depuis la nouvelle API
          const response = await fetch("/api/roles.php", {
            headers: {
              Authorization: "Bearer demo_token",
              "Content-Type": "application/json",
            },
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const data = await response.json();

          if (data.success && data.data && data.data.roles) {
            const roles = data.data.roles;

            // Utiliser les données des rôles de l'API

            let html = `
              <div class="row mb-4">
                <div class="col-12">
                  <h6>Statistiques des Rôles</h6>
                </div>
            `;

            // Afficher les statistiques des rôles
            roles.forEach(role => {
              const colorClass = role.is_system ? 'primary' : 'info';
              html += `
                <div class="col-md-4 mb-3">
                  <div class="card border-${colorClass}">
                    <div class="card-body text-center">
                      <h5 class="card-title text-${colorClass}">${role.display_name}</h5>
                      <h2 class="text-${colorClass}">${role.user_count}</h2>
                      <p class="card-text small">${role.description}</p>
                      ${role.is_system ? '<span class="badge bg-warning">Système</span>' : ''}
                    </div>
                  </div>
                </div>
              `;
            });

            html += `
              </div>

              <div class="row">
                <div class="col-12">
                  <h6>Gestion des Rôles et Permissions</h6>
                </div>
              </div>

              <div class="table-responsive">
                <table class="table table-hover">
                  <thead class="table-light">
                    <tr>
                      <th>Rôle</th>
                      <th>Description</th>
                      <th>Utilisateurs</th>
                      <th>Permissions</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
            `;

            // Afficher chaque rôle avec ses détails
            roles.forEach(role => {
              const colorClass = role.is_system ? 'primary' : 'info';
              const permissionsList = Object.keys(role.permissions).map(resource => {
                const actions = role.permissions[resource].join(', ');
                return `<span class="badge bg-light text-dark me-1" title="${actions}">${resource}</span>`;
              }).join('');

              html += `
                <tr>
                  <td>
                    <span class="badge bg-${colorClass} fs-6">${role.display_name}</span>
                    ${role.is_system ? '<br><small class="text-muted">Système</small>' : ''}
                  </td>
                  <td>${role.description}</td>
                  <td>
                    <span class="badge bg-secondary">${role.user_count} utilisateur${role.user_count > 1 ? 's' : ''}</span>
                  </td>
                  <td>${permissionsList}</td>
                  <td>
                    <button class="btn btn-sm btn-outline-primary me-1" onclick="editRole(${role.id})" ${role.is_system && ['admin', 'merchant', 'customer'].includes(role.name) ? 'disabled' : ''}>
                      <i class="fas fa-edit"></i> Modifier
                    </button>
                    <button class="btn btn-sm btn-outline-info" onclick="viewRoleUsers('${role.name}')">
                      <i class="fas fa-users"></i> Voir utilisateurs
                    </button>
                  </td>
                </tr>
              `;
            });

            html += `
                  </tbody>
                </table>
              </div>
            `;

            container.innerHTML = html;
            console.log("✅ Rôles chargés:", roles);
          } else {
            throw new Error("Aucune donnée de rôles trouvée");
          }
        } catch (error) {
          console.error("❌ Erreur lors du chargement des rôles:", error);
          container.innerHTML = `
            <div class="alert alert-danger">
              <i class="fas fa-exclamation-triangle me-2"></i>
              Erreur lors du chargement des utilisateurs: ${error.message}
            </div>
          `;
        }
      }

      function addNewRole() {
        // Créer un modal pour ajouter un nouveau rôle
        const modalHtml = `
          <div class="modal fade" id="addRoleModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
              <div class="modal-content">
                <div class="modal-header">
                  <h5 class="modal-title">Créer un Nouveau Rôle</h5>
                  <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                  <form id="addRoleForm">
                    <div class="mb-3">
                      <label for="roleName" class="form-label">Nom du Rôle *</label>
                      <input type="text" class="form-control" id="roleName" required>
                    </div>
                    <div class="mb-3">
                      <label for="roleDescription" class="form-label">Description</label>
                      <textarea class="form-control" id="roleDescription" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                      <label class="form-label">Limites de Ressources</label>
                      <div class="row">
                        <div class="col-md-6">
                          <div class="mb-3">
                            <label for="maxProducts" class="form-label">Max Produits</label>
                            <input type="number" class="form-control" id="maxProducts" min="-1" value="10"
                                   title="Entrez -1 pour illimité">
                            <small class="text-muted">-1 = illimité</small>
                          </div>
                          <div class="mb-3">
                            <label for="maxLandingPages" class="form-label">Max Landing Pages</label>
                            <input type="number" class="form-control" id="maxLandingPages" min="-1" value="1"
                                   title="Entrez -1 pour illimité">
                            <small class="text-muted">-1 = illimité</small>
                          </div>
                        </div>
                        <div class="col-md-6">
                          <div class="mb-3">
                            <label for="maxCategories" class="form-label">Max Catégories</label>
                            <input type="number" class="form-control" id="maxCategories" min="-1" value="5"
                                   title="Entrez -1 pour illimité">
                            <small class="text-muted">-1 = illimité</small>
                          </div>
                          <div class="mb-3">
                            <label for="maxSubcategories" class="form-label">Max Sous-catégories</label>
                            <input type="number" class="form-control" id="maxSubcategories" min="-1" value="10"
                                   title="Entrez -1 pour illimité">
                            <small class="text-muted">-1 = illimité</small>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="mb-3">
                      <label class="form-label">Permissions</label>
                      <div class="row">
                        <div class="col-md-6">
                          <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="perm_create" value="create">
                            <label class="form-check-label" for="perm_create">Créer</label>
                          </div>
                          <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="perm_read" value="read" checked>
                            <label class="form-check-label" for="perm_read">Lire</label>
                          </div>
                          <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="perm_update" value="update">
                            <label class="form-check-label" for="perm_update">Modifier</label>
                          </div>
                        </div>
                        <div class="col-md-6">
                          <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="perm_delete" value="delete">
                            <label class="form-check-label" for="perm_delete">Supprimer</label>
                          </div>
                          <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="perm_manage_users" value="manage_users">
                            <label class="form-check-label" for="perm_manage_users">Gérer utilisateurs</label>
                          </div>
                          <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="perm_manage_settings" value="manage_settings">
                            <label class="form-check-label" for="perm_manage_settings">Gérer paramètres</label>
                          </div>
                        </div>
                      </div>
                    </div>
                  </form>
                </div>
                <div class="modal-footer">
                  <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                  <button type="button" class="btn btn-primary" onclick="submitNewRole()">Créer Rôle</button>
                </div>
              </div>
            </div>
          </div>
        `;

        // Ajouter le modal au DOM s'il n'existe pas
        if (!document.getElementById('addRoleModal')) {
          document.body.insertAdjacentHTML('beforeend', modalHtml);
        }

        // Afficher le modal
        const modal = new bootstrap.Modal(document.getElementById('addRoleModal'));
        modal.show();
      }

      function submitNewRole() {
        const name = document.getElementById('roleName').value;
        const description = document.getElementById('roleDescription').value;

        // Récupérer les limites
        const maxProducts = parseInt(document.getElementById('maxProducts').value) || 0;
        const maxLandingPages = parseInt(document.getElementById('maxLandingPages').value) || 0;
        const maxCategories = parseInt(document.getElementById('maxCategories').value) || 0;
        const maxSubcategories = parseInt(document.getElementById('maxSubcategories').value) || 0;

        if (!name.trim()) {
          alert('Le nom du rôle est requis');
          return;
        }

        // Collecter les permissions sélectionnées
        const permissions = [];
        document.querySelectorAll('#addRoleForm input[type="checkbox"]:checked').forEach(checkbox => {
          permissions.push(checkbox.value);
        });

        // Créer l'objet rôle avec les limites
        const roleData = {
          name: name.trim(),
          display_name: name.trim(),
          description: description.trim(),
          permissions: permissions,
          max_products: maxProducts,
          max_landing_pages: maxLandingPages,
          max_categories: maxCategories,
          max_subcategories: maxSubcategories
        };

        // Envoyer la requête de création
        createRole(roleData);
      }

      async function createRole(roleData) {
        try {
          const response = await fetch('/api/roles.php', {
            method: 'POST',
            headers: {
              'Authorization': 'Bearer demo_token',
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(roleData)
          });

          const data = await response.json();

          if (data.success) {
            alert('Rôle créé avec succès !');

            // Fermer le modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('addRoleModal'));
            modal.hide();

            // Recharger la liste des rôles
            loadRoles();
          } else {
            alert('Erreur lors de la création du rôle: ' + data.message);
          }
        } catch (error) {
          console.error('Erreur:', error);
          alert('Erreur lors de la création du rôle');
        }
      }

      async function editRole(roleId) {
        try {
          // Récupérer les détails du rôle
          const response = await fetch(`/api/roles.php/${roleId}`, {
            headers: {
              Authorization: "Bearer demo_token",
              "Content-Type": "application/json",
            },
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const data = await response.json();

          if (data.success && data.data) {
            const role = data.data;

            // Créer le modal d'édition
            const modalHtml = `
              <div class="modal fade" id="editRoleModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                  <div class="modal-content">
                    <div class="modal-header">
                      <h5 class="modal-title">
                        <i class="fas fa-edit me-2"></i>Modifier le rôle: ${role.display_name}
                      </h5>
                      <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                      <form id="editRoleForm">
                        <div class="row">
                          <div class="col-md-6">
                            <div class="mb-3">
                              <label for="editRoleDisplayName" class="form-label">Nom d'affichage</label>
                              <input type="text" class="form-control" id="editRoleDisplayName" value="${role.display_name}" required>
                            </div>
                          </div>
                          <div class="col-md-6">
                            <div class="mb-3">
                              <label for="editRoleName" class="form-label">Nom technique</label>
                              <input type="text" class="form-control" id="editRoleName" value="${role.name}" readonly>
                              <small class="text-muted">Le nom technique ne peut pas être modifié</small>
                            </div>
                          </div>
                        </div>

                        <div class="mb-3">
                          <label for="editRoleDescription" class="form-label">Description</label>
                          <textarea class="form-control" id="editRoleDescription" rows="3">${role.description}</textarea>
                        </div>

                        <div class="mb-3">
                          <label class="form-label">Permissions</label>
                          <div id="editRolePermissions">
                            ${generatePermissionsEditor(role.permissions)}
                          </div>
                        </div>
                      </form>
                    </div>
                    <div class="modal-footer">
                      <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                      <button type="button" class="btn btn-primary" onclick="saveRoleChanges(${role.id})">
                        <i class="fas fa-save me-1"></i>Sauvegarder
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            `;

            // Ajouter le modal au DOM
            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // Afficher le modal
            const modal = new bootstrap.Modal(document.getElementById('editRoleModal'));
            modal.show();

            // Nettoyer le modal après fermeture
            document.getElementById('editRoleModal').addEventListener('hidden.bs.modal', function () {
              this.remove();
            });

          } else {
            throw new Error(data.message || 'Erreur lors de la récupération du rôle');
          }
        } catch (error) {
          console.error("❌ Erreur lors de l'édition du rôle:", error);
          alert('Erreur lors du chargement des détails du rôle: ' + error.message);
        }
      }

      function generatePermissionsEditor(permissions) {
        const resources = ['users', 'stores', 'products', 'orders', 'analytics', 'settings', 'roles'];
        const actions = ['create', 'read', 'update', 'delete'];

        let html = '<div class="row">';

        resources.forEach(resource => {
          const resourcePermissions = permissions[resource] || [];
          html += `
            <div class="col-md-6 mb-3">
              <div class="card">
                <div class="card-header py-2">
                  <h6 class="mb-0">${resource.charAt(0).toUpperCase() + resource.slice(1)}</h6>
                </div>
                <div class="card-body py-2">
          `;

          actions.forEach(action => {
            const checked = resourcePermissions.includes(action) ? 'checked' : '';
            html += `
              <div class="form-check form-check-inline">
                <input class="form-check-input" type="checkbox" id="perm_${resource}_${action}" value="${action}" ${checked}>
                <label class="form-check-label" for="perm_${resource}_${action}">${action}</label>
              </div>
            `;
          });

          html += `
                </div>
              </div>
            </div>
          `;
        });

        html += '</div>';
        return html;
      }

      async function saveRoleChanges(roleId) {
        try {
          const displayName = document.getElementById('editRoleDisplayName').value;
          const description = document.getElementById('editRoleDescription').value;

          // Collecter les permissions
          const permissions = {};
          const resources = ['users', 'stores', 'products', 'orders', 'analytics', 'settings', 'roles'];
          const actions = ['create', 'read', 'update', 'delete'];

          resources.forEach(resource => {
            permissions[resource] = [];
            actions.forEach(action => {
              const checkbox = document.getElementById(`perm_${resource}_${action}`);
              if (checkbox && checkbox.checked) {
                permissions[resource].push(action);
              }
            });
          });

          const response = await fetch(`/api/roles.php/${roleId}`, {
            method: 'PUT',
            headers: {
              Authorization: "Bearer demo_token",
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              display_name: displayName,
              description: description,
              permissions: permissions
            })
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const data = await response.json();

          if (data.success) {
            // Fermer le modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('editRoleModal'));
            modal.hide();

            // Recharger la liste des rôles
            await loadRoles();

            alert('Rôle modifié avec succès !');
          } else {
            throw new Error(data.message || 'Erreur lors de la sauvegarde');
          }
        } catch (error) {
          console.error("❌ Erreur lors de la sauvegarde:", error);
          alert('Erreur lors de la sauvegarde: ' + error.message);
        }
      }

      async function viewRoleUsers(roleKey) {
        try {
          // Récupérer les utilisateurs du rôle
          const response = await fetch(`/api/roles.php/${roleKey}/users`, {
            headers: {
              'Authorization': 'Bearer demo_token',
              'Content-Type': 'application/json'
            }
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const data = await response.json();

          if (data.success && data.data) {
            const users = data.data;

            // Créer le modal pour afficher les utilisateurs
            const modalHtml = `
              <div class="modal fade" id="viewRoleUsersModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                  <div class="modal-content">
                    <div class="modal-header">
                      <h5 class="modal-title">
                        <i class="fas fa-users me-2"></i>Utilisateurs du rôle: ${roleKey}
                      </h5>
                      <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                      <div class="table-responsive">
                        <table class="table table-hover">
                          <thead>
                            <tr>
                              <th>Email</th>
                              <th>Nom</th>
                              <th>Statut</th>
                              <th>Dernière Connexion</th>
                            </tr>
                          </thead>
                          <tbody>
                            ${users.map(user => `
                              <tr>
                                <td>${user.email}</td>
                                <td>${user.name || '-'}</td>
                                <td>
                                  <span class="badge bg-${user.status === 'active' ? 'success' : 'warning'}">
                                    ${user.status === 'active' ? 'Actif' : 'Inactif'}
                                  </span>
                                </td>
                                <td>${user.last_login ? new Date(user.last_login).toLocaleString() : '-'}</td>
                              </tr>
                            `).join('')}
                          </tbody>
                        </table>
                      </div>
                    </div>
                    <div class="modal-footer">
                      <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                    </div>
                  </div>
                </div>
              </div>
            `;

            // Ajouter le modal au DOM
            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // Afficher le modal
            const modal = new bootstrap.Modal(document.getElementById('viewRoleUsersModal'));
            modal.show();

            // Nettoyer le modal après fermeture
            document.getElementById('viewRoleUsersModal').addEventListener('hidden.bs.modal', function () {
              this.remove();
            });

          } else {
            throw new Error('Aucun utilisateur trouvé pour ce rôle');
          }
        } catch (error) {
          console.error('❌ Erreur lors du chargement des utilisateurs:', error);
          alert('Erreur lors du chargement des utilisateurs: ' + error.message);
        }
      }

      function changeUserRole(userId, currentRole) {
        // Créer un modal pour changer le rôle d'un utilisateur
        const modalHtml = `
          <div class="modal fade" id="changeRoleModal" tabindex="-1">
            <div class="modal-dialog">
              <div class="modal-content">
                <div class="modal-header">
                  <h5 class="modal-title">Changer le Rôle de l'Utilisateur</h5>
                  <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                  <form id="changeRoleForm">
                    <div class="mb-3">
                      <label for="newRole" class="form-label">Nouveau Rôle</label>
                      <select class="form-control" id="newRole">
                        <option value="user" ${currentRole === 'user' ? 'selected' : ''}>Utilisateur</option>
                        <option value="merchant" ${currentRole === 'merchant' ? 'selected' : ''}>Marchand</option>
                        <option value="admin" ${currentRole === 'admin' ? 'selected' : ''}>Administrateur</option>
                      </select>
                    </div>
                    <div class="alert alert-warning">
                      <i class="fas fa-exclamation-triangle me-2"></i>
                      Attention: Changer le rôle d'un utilisateur modifiera ses permissions d'accès.
                    </div>
                  </form>
                </div>
                <div class="modal-footer">
                  <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                  <button type="button" class="btn btn-primary" onclick="submitRoleChange(${userId})">Changer Rôle</button>
                </div>
              </div>
            </div>
          </div>
        `;

        // Ajouter le modal au DOM s'il n'existe pas
        if (!document.getElementById('changeRoleModal')) {
          document.body.insertAdjacentHTML('beforeend', modalHtml);
        } else {
          // Mettre à jour le contenu du modal existant
          document.getElementById('changeRoleModal').outerHTML = modalHtml;
        }

        // Afficher le modal
        const modal = new bootstrap.Modal(document.getElementById('changeRoleModal'));
        modal.show();
      }

      async function submitRoleChange(userId) {
        const newRole = document.getElementById('newRole').value;

        try {
          const response = await fetch(`/api/users.php?action=update&id=${userId}`, {
            method: 'PUT',
            headers: {
              'Authorization': 'Bearer demo_token',
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              role: newRole
            })
          });

          const data = await response.json();

          if (data.success) {
            // Fermer le modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('changeRoleModal'));
            modal.hide();

            // Recharger la liste des rôles
            await loadRoles();

            alert('Rôle modifié avec succès !');
          } else {
            alert('Erreur lors de la modification: ' + data.message);
          }
        } catch (error) {
          console.error('Erreur:', error);
          alert('Erreur lors de la modification du rôle');
        }
      }

      function editUserRole(userId, currentRole) {
        // Créer un modal pour modifier le rôle
        const modalHtml = `
          <div class="modal fade" id="editRoleModal" tabindex="-1">
            <div class="modal-dialog">
              <div class="modal-content">
                <div class="modal-header">
                  <h5 class="modal-title">Modifier le rôle utilisateur</h5>
                  <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                  <form id="editRoleForm">
                    <div class="mb-3">
                      <label for="userRole" class="form-label">Rôle</label>
                      <select class="form-control" id="userRole">
                        <option value="user" ${
                          currentRole === "user" ? "selected" : ""
                        }>Utilisateur</option>
                        <option value="merchant" ${
                          currentRole === "merchant" ? "selected" : ""
                        }>Marchand</option>
                        <option value="admin" ${
                          currentRole === "admin" ? "selected" : ""
                        }>Administrateur</option>
                      </select>
                    </div>
                  </form>
                </div>
                <div class="modal-footer">
                  <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                  <button type="button" class="btn btn-primary" onclick="submitRoleChange(${userId})">Modifier</button>
                </div>
              </div>
            </div>
          </div>
        `;

        // Ajouter le modal au DOM s'il n'existe pas
        if (!document.getElementById("editRoleModal")) {
          document.body.insertAdjacentHTML("beforeend", modalHtml);
        }

        // Afficher le modal
        const modal = new bootstrap.Modal(
          document.getElementById("editRoleModal")
        );
        modal.show();
      }

      async function submitRoleChange(userId) {
        const newRole = document.getElementById("userRole").value;

        try {
          const response = await fetch(`/api/users.php?action=update&id=${userId}`, {
            method: "PUT",
            headers: {
              Authorization: "Bearer demo_token",
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              role: newRole,
            }),
          });

          const data = await response.json();

          if (data.success) {
            // Fermer le modal
            const modal = bootstrap.Modal.getInstance(
              document.getElementById("editRoleModal")
            );
            modal.hide();

            // Recharger la liste
            await loadRoles();

            alert("Rôle modifié avec succès !");
          } else {
            alert("Erreur lors de la modification: " + data.message);
          }
        } catch (error) {
          console.error("Erreur:", error);
          alert("Erreur lors de la modification du rôle");
        }
      }

      async function toggleUserStatus(userId, currentStatus) {
        // Convertir la chaîne 'true'/'false' en booléen
        const isCurrentlyActive = currentStatus === 'true';
        const newStatus = isCurrentlyActive ? 'inactive' : 'active';
        const action = isCurrentlyActive ? 'désactiver' : 'activer';

        if (confirm(`Êtes-vous sûr de vouloir ${action} cet utilisateur ?`)) {
          try {
            const response = await fetch(`/api/users.php?action=update&id=${userId}`, {
              method: 'PUT',
              headers: {
                'Authorization': 'Bearer demo_token',
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                status: newStatus
              })
            });

            if (!response.ok) {
              throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (data.success) {
              // Utiliser une notification plus discrète
              const message = `Utilisateur ${isCurrentlyActive ? 'désactivé' : 'activé'} avec succès !`;
              console.log('✅', message);

              // Recharger la liste des utilisateurs
              await loadFirebaseUsers();
            } else {
              throw new Error(data.message || 'Erreur lors de la modification du statut');
            }
          } catch (error) {
            console.error('❌ Erreur:', error);
            alert(`Erreur lors de la modification du statut: ${error.message}`);
          }
        }
      }

      // Fonction pour supprimer un utilisateur
      async function deleteUser(userId) {
        if (confirm('Êtes-vous sûr de vouloir supprimer cet utilisateur ? Cette action est irréversible.')) {
          try {
            const response = await fetch(`/api/users.php?action=delete&id=${userId}`, {
              method: 'DELETE',
              headers: {
                'Authorization': 'Bearer demo_token',
                'Content-Type': 'application/json'
              }
            });

            if (!response.ok) {
              throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (data.success) {
              console.log('✅ Utilisateur supprimé avec succès');

              // Recharger la liste des utilisateurs
              await loadFirebaseUsers();
            } else {
              throw new Error(data.message || 'Erreur lors de la suppression');
            }
          } catch (error) {
            console.error('❌ Erreur:', error);
            alert(`Erreur lors de la suppression: ${error.message}`);
          }
        }
      }

      // Fonctions de gestion des abonnements
      async function loadSubscriptions() {
        const container = document.getElementById("subscriptionsContainer");

        try {
          // Afficher un indicateur de chargement
          container.innerHTML =
            '<div class="text-center py-4"><div class="spinner-border" role="status"></div><p>Chargement des abonnements...</p></div>';

          // Charger les abonnements depuis notre API
          const response = await fetch("/api/subscriptions.php?action=plans", {
            headers: {
              Authorization: "Bearer demo_token",
              "Content-Type": "application/json",
            },
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const data = await response.json();

          if (!data.success) {
            throw new Error(data.message || 'Erreur lors du chargement');
          }

          const subscriptions = data.data.plans;

          let html = `
            <div class="row mb-4">
              <div class="col-md-3">
                <div class="card text-center">
                  <div class="card-body">
                    <h5 class="card-title text-primary">Total Abonnés</h5>
                    <h2>${subscriptions.reduce(
                      (sum, sub) => sum + sub.subscribers,
                      0
                    )}</h2>
                  </div>
                </div>
              </div>
              <div class="col-md-3">
                <div class="card text-center">
                  <div class="card-body">
                    <h5 class="card-title text-success">Revenus Mensuels</h5>
                    <h2>${subscriptions
                      .reduce(
                        (sum, sub) => sum + sub.price * sub.subscribers,
                        0
                      )
                      .toLocaleString()} DZD</h2>
                  </div>
                </div>
              </div>
              <div class="col-md-3">
                <div class="card text-center">
                  <div class="card-body">
                    <h5 class="card-title text-info">Plans Actifs</h5>
                    <h2>${
                      subscriptions.filter((sub) => sub.is_active).length
                    }</h2>
                  </div>
                </div>
              </div>
              <div class="col-md-3">
                <div class="card text-center">
                  <div class="card-body">
                    <h5 class="card-title text-warning">Taux de Conversion</h5>
                    <h2>23%</h2>
                  </div>
                </div>
              </div>
            </div>

            <div class="table-responsive">
              <table class="table table-hover">
                <thead class="table-light">
                  <tr>
                    <th>Plan</th>
                    <th>Prix</th>
                    <th>Limites</th>
                    <th>Abonnés</th>
                    <th>Statut</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
          `;

          subscriptions.forEach((subscription) => {
            const statusBadge = subscription.is_active
              ? "bg-success"
              : "bg-danger";

            // Formater les limites
            const formatLimit = (value) => value === -1 ? 'Illimité' : value;

            // Créer la liste des fonctionnalités
            let featuresHtml = '';
            if (subscription.ai_enabled) {
              featuresHtml += '<span class="badge bg-info me-1">IA</span>';
            }
            if (subscription.custom_domain) {
              featuresHtml += '<span class="badge bg-secondary me-1">Domaine</span>';
            }
            if (subscription.analytics_advanced) {
              featuresHtml += '<span class="badge bg-warning me-1">Analytics+</span>';
            }

            html += `
              <tr>
                <td>
                  <strong>${subscription.display_name || subscription.name}</strong><br>
                  <small class="text-muted">${subscription.description || ''}</small>
                </td>
                <td>
                  <strong>${parseFloat(subscription.price).toLocaleString()} ${subscription.currency || 'EUR'}</strong><br>
                  <small class="text-muted">/${subscription.billing_cycle || 'mois'}</small>
                </td>
                <td>
                  <small>
                    ${formatLimit(subscription.max_products)} produits<br>
                    ${formatLimit(subscription.max_landing_pages)} pages<br>
                    ${formatLimit(subscription.max_categories)} catégories
                  </small>
                </td>
                <td>
                  ${featuresHtml}
                  ${subscription.ai_enabled ? `<br><small>${formatLimit(subscription.ai_monthly_tokens)} tokens IA/mois</small>` : ''}
                </td>
                <td>
                  <span class="badge bg-primary">${subscription.active_subscribers || 0}</span>
                </td>
                <td>
                  <span class="badge ${statusBadge}">${subscription.is_active ? "ACTIF" : "INACTIF"}</span>
                </td>
                <td>
                  <button class="btn btn-sm btn-outline-primary me-1" onclick="editSubscription(${subscription.id})" title="Modifier">
                    <i class="fas fa-edit"></i>
                  </button>
                  <button class="btn btn-sm btn-outline-warning me-1" onclick="toggleSubscriptionStatus(${subscription.id}, ${subscription.is_active})" title="Activer/Désactiver">
                    <i class="fas fa-toggle-${subscription.is_active ? "on" : "off"}"></i>
                  </button>
                  <button class="btn btn-sm btn-outline-info" onclick="viewSubscriptionDetails(${subscription.id})" title="Détails">
                    <i class="fas fa-eye"></i>
                  </button>
                </td>
              </tr>
            `;
          });

          html += `
                </tbody>
              </table>
            </div>
          `;

          container.innerHTML = html;
          console.log("✅ Abonnements chargés:", subscriptions);
        } catch (error) {
          console.error("❌ Erreur lors du chargement des abonnements:", error);
          container.innerHTML = `
            <div class="alert alert-danger">
              <i class="fas fa-exclamation-triangle me-2"></i>
              Erreur lors du chargement des abonnements: ${error.message}
            </div>
          `;
        }
      }

      function addNewSubscription() {
        // Créer un modal pour ajouter un nouveau plan d'abonnement
        const modalHtml = `
          <div class="modal fade" id="addSubscriptionModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
              <div class="modal-content">
                <div class="modal-header">
                  <h5 class="modal-title">Ajouter un nouveau Plan d'Abonnement</h5>
                  <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                  <form id="addSubscriptionForm">
                    <div class="row">
                      <div class="col-md-6">
                        <div class="mb-3">
                          <label for="planName" class="form-label">Nom du Plan</label>
                          <input type="text" class="form-control" id="planName" required>
                        </div>
                      </div>
                      <div class="col-md-6">
                        <div class="mb-3">
                          <label for="planPrice" class="form-label">Prix (DZD)</label>
                          <input type="number" class="form-control" id="planPrice" step="0.01" required>
                        </div>
                      </div>
                    </div>
                    <div class="mb-3">
                      <label for="planDescription" class="form-label">Description</label>
                      <textarea class="form-control" id="planDescription" rows="3"></textarea>
                    </div>
                    <div class="row">
                      <div class="col-md-6">
                        <div class="mb-3">
                          <label for="maxProducts" class="form-label">Nombre max de produits</label>
                          <input type="number" class="form-control" id="maxProducts" value="10">
                        </div>
                      </div>
                      <div class="col-md-6">
                        <div class="mb-3">
                          <label for="maxPages" class="form-label">Nombre max de pages</label>
                          <input type="number" class="form-control" id="maxPages" value="5">
                        </div>
                      </div>
                    </div>
                    <div class="row">
                      <div class="col-md-6">
                        <div class="mb-3">
                          <label for="maxCategories" class="form-label">Nombre max de catégories</label>
                          <input type="number" class="form-control" id="maxCategories" value="5">
                        </div>
                      </div>
                      <div class="col-md-6">
                        <div class="mb-3">
                          <label for="maxSubcategories" class="form-label">Nombre max de sous-catégories</label>
                          <input type="number" class="form-control" id="maxSubcategories" value="10">
                        </div>
                      </div>
                    </div>
                  </form>
                </div>
                <div class="modal-footer">
                  <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                  <button type="button" class="btn btn-primary" onclick="submitNewSubscription()">Créer Plan</button>
                </div>
              </div>
            </div>
          </div>
        `;

        // Ajouter le modal au DOM s'il n'existe pas
        if (!document.getElementById("addSubscriptionModal")) {
          document.body.insertAdjacentHTML("beforeend", modalHtml);
        }

        // Afficher le modal
        const modal = new bootstrap.Modal(
          document.getElementById("addSubscriptionModal")
        );
        modal.show();
      }

      async function submitNewSubscription() {
        const name = document.getElementById("planName").value;
        const price = document.getElementById("planPrice").value;
        const description = document.getElementById("planDescription").value;
        const maxProducts = document.getElementById("maxProducts").value;
        const maxPages = document.getElementById("maxPages").value;
        const maxCategories = document.getElementById("maxCategories").value;
        const maxSubcategories = document.getElementById("maxSubcategories").value;

        if (!name.trim() || !price) {
          alert("Le nom et le prix du plan sont requis");
          return;
        }

        try {
          const token = await getFirebaseToken();
          const response = await fetch('/api/subscriptions.php?action=create', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({
              name,
              price: parseFloat(price),
              description,
              max_products: parseInt(maxProducts),
              max_pages: parseInt(maxPages),
              max_categories: parseInt(maxCategories),
              max_subcategories: parseInt(maxSubcategories)
            })
          });

          if (!response.ok) {
            throw new Error(`Erreur HTTP: ${response.status}`);
          }

          const data = await response.json();
          if (!data.success) {
            throw new Error(data.message || 'Erreur lors de la création du plan');
          }

          showToast('Succès', 'Plan d\'abonnement créé avec succès !', 'success');

          // Fermer le modal
          const modal = bootstrap.Modal.getInstance(
            document.getElementById("addSubscriptionModal")
          );
          modal.hide();

          // Recharger la liste
          await loadSubscriptions();
        } catch (error) {
          console.error("Erreur:", error);
          alert("Erreur lors de la création du plan");
        }
      }

      async function editSubscription(id) {
        try {
          // Récupérer les détails du plan
          const response = await fetch(`/api/subscriptions.php?action=details&id=${id}`, {
            headers: {
              'Authorization': 'Bearer demo_token'
            }
          });

          if (!response.ok) {
            throw new Error(`Erreur HTTP: ${response.status}`);
          }

          const data = await response.json();
          if (!data.success) {
            throw new Error(data.message || 'Erreur lors de la récupération du plan');
          }

          const subscription = data.data;

          // Créer le modal d'édition
          const modalHtml = `
            <div class="modal fade" id="editSubscriptionModal" tabindex="-1">
              <div class="modal-dialog modal-lg">
                <div class="modal-content">
                  <div class="modal-header">
                    <h5 class="modal-title">Modifier le Plan d'Abonnement</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                  </div>
                  <div class="modal-body">
                    <form id="editSubscriptionForm">
                      <input type="hidden" id="editPlanId" value="${subscription.id}">
                      <div class="row">
                        <div class="col-md-6">
                          <div class="mb-3">
                            <label for="editPlanName" class="form-label">Nom du Plan</label>
                            <input type="text" class="form-control" id="editPlanName" value="${subscription.name}" required>
                          </div>
                        </div>
                        <div class="col-md-6">
                          <div class="mb-3">
                            <label for="editPlanPrice" class="form-label">Prix (DZD)</label>
                            <input type="number" class="form-control" id="editPlanPrice" step="0.01" value="${subscription.price}" required>
                          </div>
                        </div>
                      </div>
                      <div class="mb-3">
                        <label for="editPlanDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="editPlanDescription" rows="3">${subscription.description || ''}</textarea>
                      </div>
                      <div class="row">
                        <div class="col-md-6">
                          <div class="mb-3">
                            <label for="editMaxProducts" class="form-label">Nombre max de produits</label>
                            <input type="number" class="form-control" id="editMaxProducts" value="${subscription.max_products || 10}">
                          </div>
                        </div>
                        <div class="col-md-6">
                          <div class="mb-3">
                            <label for="editMaxPages" class="form-label">Nombre max de pages</label>
                            <input type="number" class="form-control" id="editMaxPages" value="${subscription.max_pages || 5}">
                          </div>
                        </div>
                      </div>
                      <div class="row">
                        <div class="col-md-6">
                          <div class="mb-3">
                            <label for="editMaxCategories" class="form-label">Nombre max de catégories</label>
                            <input type="number" class="form-control" id="editMaxCategories" value="${subscription.max_categories || 5}">
                          </div>
                        </div>
                        <div class="col-md-6">
                          <div class="mb-3">
                            <label for="editMaxSubcategories" class="form-label">Nombre max de sous-catégories</label>
                            <input type="number" class="form-control" id="editMaxSubcategories" value="${subscription.max_subcategories || 10}">
                          </div>
                        </div>
                      </div>
                    </form>
                  </div>
                  <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="button" class="btn btn-primary" onclick="submitEditSubscription()">Sauvegarder</button>
                  </div>
                </div>
              </div>
            </div>
          `;

          // Supprimer le modal existant s'il y en a un
          const existingModal = document.getElementById("editSubscriptionModal");
          if (existingModal) {
            existingModal.remove();
          }

          // Ajouter le modal au DOM
          document.body.insertAdjacentHTML("beforeend", modalHtml);

          // Afficher le modal
          const modal = new bootstrap.Modal(document.getElementById("editSubscriptionModal"));
          modal.show();

        } catch (error) {
          console.error('Erreur lors de l\'édition:', error);
          showToast('Erreur', error.message, 'error');
        }
      }

      async function submitEditSubscription() {
        const id = document.getElementById("editPlanId").value;
        const name = document.getElementById("editPlanName").value;
        const price = document.getElementById("editPlanPrice").value;
        const description = document.getElementById("editPlanDescription").value;
        const maxProducts = document.getElementById("editMaxProducts").value;
        const maxPages = document.getElementById("editMaxPages").value;
        const maxCategories = document.getElementById("editMaxCategories").value;
        const maxSubcategories = document.getElementById("editMaxSubcategories").value;

        if (!name.trim() || !price) {
          showToast('Erreur', 'Le nom et le prix du plan sont requis', 'error');
          return;
        }

        try {
          const token = await getFirebaseToken();
          const response = await fetch(`/api/subscriptions.php?action=update&id=${id}`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({
              name,
              price: parseFloat(price),
              description,
              max_products: parseInt(maxProducts),
              max_pages: parseInt(maxPages),
              max_categories: parseInt(maxCategories),
              max_subcategories: parseInt(maxSubcategories)
            })
          });

          if (!response.ok) {
            throw new Error(`Erreur HTTP: ${response.status}`);
          }

          const data = await response.json();
          if (!data.success) {
            throw new Error(data.message || 'Erreur lors de la modification du plan');
          }

          showToast('Succès', 'Plan d\'abonnement modifié avec succès !', 'success');

          // Fermer le modal
          const modal = bootstrap.Modal.getInstance(document.getElementById("editSubscriptionModal"));
          modal.hide();

          // Recharger la liste des abonnements
          loadSubscriptions();

        } catch (error) {
          console.error('Erreur lors de la modification:', error);
          showToast('Erreur', error.message, 'error');
        }
      }

      function toggleSubscriptionStatus(id, currentStatus) {
        const action = currentStatus ? "désactiver" : "activer";
        if (
          confirm(`Êtes-vous sûr de vouloir ${action} ce plan d'abonnement ?`)
        ) {
          alert(
            `Plan ${
              action === "activer" ? "activé" : "désactivé"
            } avec succès ! (Fonctionnalité simulée)`
          );
          loadSubscriptions();
        }
      }

      async function viewSubscriptionDetails(id) {
        try {
          // Récupérer les détails du plan
          const response = await fetch(`/api/subscriptions.php?action=details&id=${id}`, {
            headers: {
              'Authorization': 'Bearer demo_token'
            }
          });

          if (!response.ok) {
            throw new Error(`Erreur HTTP: ${response.status}`);
          }

          const data = await response.json();
          if (!data.success) {
            throw new Error(data.message || 'Erreur lors de la récupération du plan');
          }

          const subscription = data.data;

          // Créer le modal de détails
          const modalHtml = `
            <div class="modal fade" id="subscriptionDetailsModal" tabindex="-1">
              <div class="modal-dialog modal-lg">
                <div class="modal-content">
                  <div class="modal-header">
                    <h5 class="modal-title">Détails du Plan: ${subscription.name}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                  </div>
                  <div class="modal-body">
                    <div class="row">
                      <div class="col-md-6">
                        <div class="card mb-3">
                          <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Informations Générales</h6>
                          </div>
                          <div class="card-body">
                            <p><strong>Nom:</strong> ${subscription.name}</p>
                            <p><strong>Prix:</strong> ${parseFloat(subscription.price).toLocaleString()} DZD</p>
                            <p><strong>Description:</strong> ${subscription.description || 'Aucune description'}</p>
                            <p><strong>Statut:</strong>
                              <span class="badge bg-${subscription.is_active ? 'success' : 'danger'}">
                                ${subscription.is_active ? 'ACTIF' : 'INACTIF'}
                              </span>
                            </p>
                            <p><strong>Utilisateurs actifs:</strong>
                              <span class="badge bg-primary">${subscription.active_users || 0}</span>
                            </p>
                          </div>
                        </div>
                      </div>
                      <div class="col-md-6">
                        <div class="card mb-3">
                          <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-cogs me-2"></i>Limites et Quotas</h6>
                          </div>
                          <div class="card-body">
                            <div class="row">
                              <div class="col-6">
                                <div class="text-center p-2 border rounded mb-2">
                                  <div class="h4 text-primary mb-0">${subscription.max_products || 0}</div>
                                  <small class="text-muted">Produits max</small>
                                </div>
                              </div>
                              <div class="col-6">
                                <div class="text-center p-2 border rounded mb-2">
                                  <div class="h4 text-info mb-0">${subscription.max_pages || 0}</div>
                                  <small class="text-muted">Pages max</small>
                                </div>
                              </div>
                              <div class="col-6">
                                <div class="text-center p-2 border rounded mb-2">
                                  <div class="h4 text-success mb-0">${subscription.max_categories || 0}</div>
                                  <small class="text-muted">Catégories max</small>
                                </div>
                              </div>
                              <div class="col-6">
                                <div class="text-center p-2 border rounded mb-2">
                                  <div class="h4 text-warning mb-0">${subscription.max_subcategories || 0}</div>
                                  <small class="text-muted">Sous-catégories max</small>
                                </div>
                              </div>
                              <div class="col-12">
                                <div class="text-center p-2 border rounded">
                                  <div class="h4 text-secondary mb-0">${subscription.max_ai_tokens || 0}</div>
                                  <small class="text-muted">Tokens IA/mois</small>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="card">
                      <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Statistiques d'Utilisation</h6>
                      </div>
                      <div class="card-body">
                        <div class="row">
                          <div class="col-md-6">
                            <p><strong>Date de création:</strong> ${new Date(subscription.created_at).toLocaleDateString()}</p>
                            <p><strong>Dernière modification:</strong> ${subscription.updated_at ? new Date(subscription.updated_at).toLocaleDateString() : 'Jamais'}</p>
                          </div>
                          <div class="col-md-6">
                            <p><strong>Revenus estimés:</strong> ${(parseFloat(subscription.price) * (subscription.active_users || 0)).toLocaleString()} DZD/mois</p>
                            <p><strong>Popularité:</strong>
                              <div class="progress" style="height: 20px;">
                                <div class="progress-bar" role="progressbar" style="width: ${Math.min(100, (subscription.active_users || 0) * 10)}%">
                                  ${subscription.active_users || 0} utilisateurs
                                </div>
                              </div>
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                    <button type="button" class="btn btn-primary" onclick="editSubscription(${subscription.id}); bootstrap.Modal.getInstance(document.getElementById('subscriptionDetailsModal')).hide();">
                      <i class="fas fa-edit me-1"></i>Modifier
                    </button>
                  </div>
                </div>
              </div>
            </div>
          `;

          // Supprimer le modal existant s'il y en a un
          const existingModal = document.getElementById("subscriptionDetailsModal");
          if (existingModal) {
            existingModal.remove();
          }

          // Ajouter le modal au DOM
          document.body.insertAdjacentHTML("beforeend", modalHtml);

          // Afficher le modal
          const modal = new bootstrap.Modal(document.getElementById("subscriptionDetailsModal"));
          modal.show();

        } catch (error) {
          console.error('Erreur lors de l\'affichage des détails:', error);
          showToast('Erreur', error.message, 'error');
        }
      }

      // Fonctions de gestion des commandes
      async function loadOrders() {
        const container = document.getElementById("ordersContainer");

        try {
          // Afficher un indicateur de chargement
          container.innerHTML =
            '<div class="text-center py-4"><div class="spinner-border" role="status"></div><p>Chargement des commandes...</p></div>';

          // Charger les commandes depuis l'API
          const response = await fetch("/api/orders.php", {
            headers: {
              Authorization: `Bearer ${DEMO_TOKEN}`,
              "Content-Type": "application/json",
            },
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const data = await response.json();

          if (data.success && data.orders) {
            const orders = data.orders;

            if (orders.length === 0) {
              container.innerHTML = `
                <div class="alert alert-info text-center">
                  <i class="fas fa-shopping-cart fa-3x mb-3"></i>
                  <h5>Aucune commande trouvée</h5>
                  <p>Les commandes apparaîtront ici une fois créées.</p>
                </div>
              `;
              return;
            }

            // Statistiques rapides
            const totalOrders = orders.length;
            const totalRevenue = orders.reduce(
              (sum, order) => sum + parseFloat(order.total_amount || 0),
              0
            );
            const pendingOrders = orders.filter(
              (order) => order.status === "pending"
            ).length;
            const completedOrders = orders.filter(
              (order) => order.status === "completed"
            ).length;

            let html = `
              <div class="row mb-4">
                <div class="col-md-3">
                  <div class="card text-center">
                    <div class="card-body">
                      <h5 class="card-title text-primary">Total Commandes</h5>
                      <h2>${totalOrders}</h2>
                    </div>
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="card text-center">
                    <div class="card-body">
                      <h5 class="card-title text-success">Revenus Total</h5>
                      <h2>${totalRevenue.toLocaleString()} DZD</h2>
                    </div>
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="card text-center">
                    <div class="card-body">
                      <h5 class="card-title text-warning">En Attente</h5>
                      <h2>${pendingOrders}</h2>
                    </div>
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="card text-center">
                    <div class="card-body">
                      <h5 class="card-title text-info">Complétées</h5>
                      <h2>${completedOrders}</h2>
                    </div>
                  </div>
                </div>
              </div>

              <div class="table-responsive">
                <table class="table table-hover">
                  <thead class="table-light">
                    <tr>
                      <th>ID Commande</th>
                      <th>Client</th>
                      <th>Montant</th>
                      <th>Statut</th>
                      <th>Méthode de Paiement</th>
                      <th>Date</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
            `;

            orders.forEach((order) => {
              const statusBadge = getOrderStatusBadge(order.status);
              html += `
                <tr>
                  <td><strong>${order.order_number || order.id}</strong></td>
                  <td>
                    ${order.customer_name || "Client anonyme"}<br>
                    <small class="text-muted">${
                      order.customer_email || ""
                    }</small>
                  </td>
                  <td><strong>${parseFloat(
                    order.total_amount || 0
                  ).toLocaleString()} DZD</strong></td>
                  <td><span class="badge ${statusBadge}">${order.status.toUpperCase()}</span></td>
                  <td>${order.payment_method || "Non défini"}</td>
                  <td>${new Date(order.created_at).toLocaleDateString()}</td>
                  <td>
                    <button class="btn btn-sm btn-outline-primary me-1" onclick="viewOrderDetails('${
                      order.id
                    }')">
                      <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-success me-1" onclick="updateOrderStatus('${
                      order.id
                    }', '${order.status}')">
                      <i class="fas fa-edit"></i>
                    </button>
                  </td>
                </tr>
              `;
            });

            html += `
                  </tbody>
                </table>
              </div>
            `;

            container.innerHTML = html;
            console.log("✅ Commandes chargées:", orders);
          } else {
            throw new Error("Aucune donnée de commande trouvée");
          }
        } catch (error) {
          console.error("❌ Erreur lors du chargement des commandes:", error);
          container.innerHTML = `
            <div class="alert alert-danger">
              <i class="fas fa-exclamation-triangle me-2"></i>
              Erreur lors du chargement des commandes: ${error.message}
            </div>
          `;
        }
      }

      function getOrderStatusBadge(status) {
        switch (status) {
          case "pending":
            return "bg-warning";
          case "confirmed":
            return "bg-info";
          case "processing":
            return "bg-primary";
          case "shipped":
            return "bg-secondary";
          case "delivered":
            return "bg-success";
          case "completed":
            return "bg-success";
          case "cancelled":
            return "bg-danger";
          default:
            return "bg-secondary";
        }
      }

      function viewOrderDetails(orderId) {
        // Créer un modal pour afficher les détails de la commande
        const modalHtml = `
          <div class="modal fade" id="orderDetailsModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
              <div class="modal-content">
                <div class="modal-header">
                  <h5 class="modal-title">Détails de la Commande #${orderId}</h5>
                  <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                  <div id="orderDetailsContent">
                    <div class="text-center py-4">
                      <div class="spinner-border" role="status"></div>
                      <p>Chargement des détails...</p>
                    </div>
                  </div>
                </div>
                <div class="modal-footer">
                  <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                </div>
              </div>
            </div>
          </div>
        `;

        // Ajouter le modal au DOM s'il n'existe pas
        if (!document.getElementById("orderDetailsModal")) {
          document.body.insertAdjacentHTML("beforeend", modalHtml);
        }

        // Afficher le modal
        const modal = new bootstrap.Modal(
          document.getElementById("orderDetailsModal")
        );
        modal.show();

        // Charger les détails de la commande
        loadOrderDetailsContent(orderId);
      }

      async function loadOrderDetailsContent(orderId) {
        const content = document.getElementById("orderDetailsContent");

        try {
          const response = await fetch(`/api/orders.php/${orderId}`, {
            headers: {
              Authorization: "Bearer demo_token",
              "Content-Type": "application/json",
            },
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const data = await response.json();

          if (data.success && data.data) {
            const order = data.data;

            content.innerHTML = `
              <div class="row">
                <div class="col-md-6">
                  <h6>Informations Client</h6>
                  <p><strong>Nom:</strong> ${
                    order.customer_name || "Non défini"
                  }</p>
                  <p><strong>Email:</strong> ${
                    order.customer_email || "Non défini"
                  }</p>
                  <p><strong>Téléphone:</strong> ${
                    order.customer_phone || "Non défini"
                  }</p>
                  <p><strong>Adresse:</strong> ${
                    order.shipping_address || "Non définie"
                  }</p>
                </div>
                <div class="col-md-6">
                  <h6>Informations Commande</h6>
                  <p><strong>Numéro:</strong> ${
                    order.order_number || order.id
                  }</p>
                  <p><strong>Statut:</strong> <span class="badge ${getOrderStatusBadge(
                    order.status
                  )}">${order.status.toUpperCase()}</span></p>
                  <p><strong>Montant Total:</strong> ${parseFloat(
                    order.total_amount || 0
                  ).toLocaleString()} DZD</p>
                  <p><strong>Méthode de Paiement:</strong> ${
                    order.payment_method || "Non définie"
                  }</p>
                  <p><strong>Date:</strong> ${new Date(
                    order.created_at
                  ).toLocaleDateString()}</p>
                </div>
              </div>
              <hr>
              <h6>Articles Commandés</h6>
              <div class="table-responsive">
                <table class="table table-sm">
                  <thead>
                    <tr>
                      <th>Produit</th>
                      <th>Quantité</th>
                      <th>Prix Unitaire</th>
                      <th>Total</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td>Produit de démonstration</td>
                      <td>1</td>
                      <td>${parseFloat(
                        order.total_amount || 0
                      ).toLocaleString()} DZD</td>
                      <td>${parseFloat(
                        order.total_amount || 0
                      ).toLocaleString()} DZD</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            `;
          } else {
            throw new Error("Détails de commande non trouvés");
          }
        } catch (error) {
          console.error("❌ Erreur lors du chargement des détails:", error);
          content.innerHTML = `
            <div class="alert alert-danger">
              <i class="fas fa-exclamation-triangle me-2"></i>
              Erreur lors du chargement des détails: ${error.message}
            </div>
          `;
        }
      }

      function updateOrderStatus(orderId, currentStatus) {
        // Créer un modal pour modifier le statut
        const modalHtml = `
          <div class="modal fade" id="updateStatusModal" tabindex="-1">
            <div class="modal-dialog">
              <div class="modal-content">
                <div class="modal-header">
                  <h5 class="modal-title">Modifier le Statut de la Commande</h5>
                  <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                  <form id="updateStatusForm">
                    <div class="mb-3">
                      <label for="orderStatus" class="form-label">Nouveau Statut</label>
                      <select class="form-control" id="orderStatus">
                        <option value="pending" ${
                          currentStatus === "pending" ? "selected" : ""
                        }>En Attente</option>
                        <option value="confirmed" ${
                          currentStatus === "confirmed" ? "selected" : ""
                        }>Confirmé</option>
                        <option value="processing" ${
                          currentStatus === "processing" ? "selected" : ""
                        }>En Traitement</option>
                        <option value="shipped" ${
                          currentStatus === "shipped" ? "selected" : ""
                        }>Expédié</option>
                        <option value="delivered" ${
                          currentStatus === "delivered" ? "selected" : ""
                        }>Livré</option>
                        <option value="completed" ${
                          currentStatus === "completed" ? "selected" : ""
                        }>Complété</option>
                        <option value="cancelled" ${
                          currentStatus === "cancelled" ? "selected" : ""
                        }>Annulé</option>
                      </select>
                    </div>
                  </form>
                </div>
                <div class="modal-footer">
                  <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                  <button type="button" class="btn btn-primary" onclick="submitStatusUpdate('${orderId}')">Modifier</button>
                </div>
              </div>
            </div>
          </div>
        `;

        // Ajouter le modal au DOM s'il n'existe pas
        if (!document.getElementById("updateStatusModal")) {
          document.body.insertAdjacentHTML("beforeend", modalHtml);
        }

        // Afficher le modal
        const modal = new bootstrap.Modal(
          document.getElementById("updateStatusModal")
        );
        modal.show();
      }

      async function submitStatusUpdate(orderId) {
        const newStatus = document.getElementById("orderStatus").value;

        try {
          const response = await fetch(`/api/orders.php/${orderId}`, {
            method: "PUT",
            headers: {
              Authorization: "Bearer demo_token",
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              status: newStatus,
            }),
          });

          const data = await response.json();

          if (data.success) {
            // Fermer le modal
            const modal = bootstrap.Modal.getInstance(
              document.getElementById("updateStatusModal")
            );
            modal.hide();

            // Recharger la liste
            await loadOrders();

            alert("Statut de commande modifié avec succès !");
          } else {
            alert("Erreur lors de la modification: " + data.message);
          }
        } catch (error) {
          console.error("Erreur:", error);
          alert("Erreur lors de la modification du statut");
        }
      }

      // Fonctions de gestion des paiements
      function loadPayments() {
        const container = document.getElementById("paymentsContainer");

        // Mock payment gateways data
        const paymentGateways = [
          {
            name: "Baridi Mob",
            status: "Actif",
            transactions: 156,
            commission: "2.5%",
            revenue: "125,000 DA",
          },
          {
            name: "CCP",
            status: "Actif",
            transactions: 89,
            commission: "1.8%",
            revenue: "89,500 DA",
          },
          {
            name: "Bank Transfer",
            status: "Actif",
            transactions: 45,
            commission: "0%",
            revenue: "0 DA",
          },
          {
            name: "COD (Cash on Delivery)",
            status: "Actif",
            transactions: 234,
            commission: "0%",
            revenue: "0 DA",
          },
        ];

        container.innerHTML = `
                <div class="row mb-4">
                    <div class="col-12">
                        <h6>Payment Gateways Configuration</h6>
                    </div>
                </div>
                <div class="row">
                    ${paymentGateways
                      .map(
                        (gateway) => `
                        <div class="col-md-6 col-lg-3 mb-3">
                            <div class="card border-0 shadow-sm">
                                <div class="card-body text-center">
                                    <h6 class="card-title">${gateway.name}</h6>
                                    <span class="badge ${
                                      gateway.status === "Actif"
                                        ? "bg-success"
                                        : "bg-danger"
                                    } mb-2">${gateway.status}</span>
                                    <p class="mb-1"><small>Transactions: <strong>${
                                      gateway.transactions
                                    }</strong></small></p>
                                    <p class="mb-1"><small>Commission: <strong>${
                                      gateway.commission
                                    }</strong></small></p>
                                    <p class="mb-3"><small>Revenus: <strong>${
                                      gateway.revenue
                                    }</strong></small></p>
                                    <button class="btn btn-sm btn-outline-primary" onclick="configureGateway('${
                                      gateway.name
                                    }')">
                                        <i class="fas fa-cog me-1"></i>Configurer
                                    </button>
                                </div>
                            </div>
                        </div>
                    `
                      )
                      .join("")}
                </div>
            `;
      }

      // Fonctions de gestion des analytics
      async function loadAnalytics() {
        const container = document.getElementById("analyticsContainer");

        try {
          // Afficher un indicateur de chargement
          container.innerHTML = '<div class="text-center py-4"><div class="spinner-border" role="status"></div><p>Chargement des analytics...</p></div>';

          // Charger les données réelles depuis les APIs
          const [ordersResponse, productsResponse, storesResponse] = await Promise.all([
            fetch("/api/orders.php", {
              headers: { Authorization: "Bearer demo_token" },
            }),
            fetch("/api/products.php", {
              headers: { Authorization: "Bearer demo_token" },
            }),
            fetch("/api/stores.php", {
              headers: { Authorization: "Bearer demo_token" },
            }),
          ]);

          const ordersData = await ordersResponse.json();
          const productsData = await productsResponse.json();
          const storesData = await storesResponse.json();

          // Calculer les vraies statistiques
          let totalRevenue = 0;
          let totalOrders = 0;
          let totalProducts = 0;
          let totalStores = 0;

          // Commandes et revenus
          if (ordersData.success && ordersData.data && ordersData.data.orders) {
            totalOrders = ordersData.data.orders.length;
            totalRevenue = ordersData.data.orders.reduce((sum, order) =>
              sum + parseFloat(order.total_amount || 0), 0
            );
          }

          // Produits
          if (productsData.success && productsData.data && productsData.data.products) {
            totalProducts = productsData.data.products.length;
          }

          // Stores
          if (storesData.success && storesData.data) {
            totalStores = Array.isArray(storesData.data) ? storesData.data.length : 1;
          }

          // Calculer le taux de conversion (simulé basé sur les données réelles)
          const conversionRate = totalOrders > 0 ? ((totalOrders / (totalStores * 100)) * 100).toFixed(1) : 0;
          const visitors = totalOrders * 50; // Estimation basée sur les commandes

          container.innerHTML = `
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <h6>Ventes Totales</h6>
                                <h4>${totalRevenue.toLocaleString()} DA</h4>
                                <small><i class="fas fa-chart-line"></i> Données réelles</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <h6>Commandes</h6>
                                <h4>${totalOrders}</h4>
                                <small><i class="fas fa-shopping-cart"></i> Total commandes</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <h6>Taux Conversion</h6>
                                <h4>${conversionRate}%</h4>
                                <small><i class="fas fa-percentage"></i> Calculé</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <h6>Visiteurs</h6>
                                <h4>${visitors.toLocaleString()}</h4>
                                <small><i class="fas fa-users"></i> Estimation</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h6>Évolution des Ventes (30 derniers jours)</h6>
                            </div>
                            <div class="card-body">
                                <canvas id="salesChart" width="400" height="200"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6>Top Vendeurs</h6>
                            </div>
                            <div class="card-body">
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Ahmed Store</span>
                                    <strong>450,000 DA</strong>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Tech Shop</span>
                                    <strong>380,000 DA</strong>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Fashion Store</span>
                                    <strong>320,000 DA</strong>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        } catch (error) {
          console.error("Erreur lors du chargement des analytics:", error);
          container.innerHTML = `
            <div class="alert alert-danger" role="alert">
              <i class="fas fa-exclamation-triangle"></i>
              Erreur lors du chargement des analytics. Veuillez réessayer.
            </div>
          `;
        }
      }

      // Fonctions de gestion de l'usage IA
      async function loadAiUsage() {
        const container = document.getElementById("aiUsageContainer");

        try {
          // Afficher un indicateur de chargement
          container.innerHTML = '<div class="text-center py-4"><div class="spinner-border" role="status"></div><p>Chargement des données IA...</p></div>';

          // Charger les données depuis notre API
          const response = await fetch("/api/ai-keys.php?action=dashboard", {
            headers: {
              Authorization: "Bearer demo_token",
              "Content-Type": "application/json",
            },
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const data = await response.json();

          if (!data.success) {
            throw new Error(data.message || 'Erreur lors du chargement');
          }

          const aiModels = data.data.models;

        container.innerHTML = `
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-gradient-primary text-white">
                            <div class="card-body">
                                <h6>Usage Total IA</h6>
                                <h4>198,840 tokens</h4>
                                <small>Ce mois</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-gradient-success text-white">
                            <div class="card-body">
                                <h6>Coût Total</h6>
                                <h4>139.41 $</h4>
                                <small>Ce mois</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-gradient-warning text-white">
                            <div class="card-body">
                                <h6>💰 Économies</h6>
                                <h4>-65%</h4>
                                <small>vs modèles premium</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-gradient-info text-white">
                            <div class="card-body">
                                <h6>Modèles Actifs</h6>
                                <h4>6/9</h4>
                                <small>Configurés</small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h6>Configuration des API Keys</h6>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>Modèle IA</th>
                                                <th>Provider</th>
                                                <th>Usage</th>
                                                <th>Coût Total</th>
                                                <th>Coût/Token</th>
                                                <th>Statut</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            ${aiModels
                                              .map(
                                                (model) => `
                                                <tr>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <strong>${model.model}</strong>
                                                            ${model.tier === 'Économique' ?
                                                                '<span class="badge bg-success ms-2" style="font-size: 0.7em;">💰 Économique</span>' :
                                                                '<span class="badge bg-primary ms-2" style="font-size: 0.7em;">⭐ Premium</span>'
                                                            }
                                                        </div>
                                                    </td>
                                                    <td>${model.provider}</td>
                                                    <td>${model.usage}</td>
                                                    <td><strong>${model.cost_total}</strong></td>
                                                    <td><small class="text-muted">${model.cost_per_token}</small></td>
                                                    <td><span class="badge ${
                                                      model.status === "Actif"
                                                        ? "bg-success"
                                                        : "bg-secondary"
                                                    }">${
                                                  model.status
                                                }</span></td>
                                                    <td>
                                                        <button class="btn btn-sm btn-outline-primary" onclick="configureApiKey('${
                                                          model.model
                                                        }')" title="Configurer API Key">
                                                            <i class="fas fa-key"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                            `
                                              )
                                              .join("")}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6>💰 Modèles Économiques Recommandés</h6>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-success" style="font-size: 0.85em;">
                                    <i class="fas fa-lightbulb me-1"></i>
                                    <strong>Conseil:</strong> Les modèles économiques offrent un excellent rapport qualité-prix pour la plupart des tâches.
                                </div>

                                <div class="mb-3">
                                    <h6 class="text-success">🏆 Top Économiques</h6>
                                    <ul class="list-unstyled small">
                                        <li class="mb-1">• <strong>GPT-3.5-turbo</strong> - $0.0002/token</li>
                                        <li class="mb-1">• <strong>Claude 3 Haiku</strong> - $0.00025/token</li>
                                        <li class="mb-1">• <strong>Gemini 1.5 Flash</strong> - $0.00015/token</li>
                                        <li class="mb-1">• <strong>Llama 3.1 8B</strong> - $0.00005/token</li>
                                    </ul>
                                </div>

                                <form>
                                    <div class="mb-3">
                                        <label class="form-label">Provider</label>
                                        <select class="form-select">
                                            <optgroup label="💰 Économiques">
                                                <option value="OpenAI">OpenAI (GPT-3.5-turbo, GPT-4o-mini)</option>
                                                <option value="Anthropic">Anthropic (Claude 3 Haiku)</option>
                                                <option value="Google">Google (Gemini 1.5 Flash)</option>
                                                <option value="Meta">Meta (Llama 3.1 8B)</option>
                                            </optgroup>
                                            <optgroup label="⭐ Premium">
                                                <option value="OpenAI">OpenAI (GPT-4)</option>
                                                <option value="Anthropic">Anthropic (Claude-3)</option>
                                                <option value="Google">Google (Gemini Pro)</option>
                                                <option value="Mistral">Mistral</option>
                                            </optgroup>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">API Key</label>
                                        <input type="password" class="form-control" placeholder="sk-... ou votre clé API">
                                    </div>
                                    <button type="submit" class="btn btn-success btn-sm w-100">
                                        <i class="fas fa-plus me-1"></i>Ajouter Modèle
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            `;

          console.log("✅ Données IA chargées:", aiModels);

        } catch (error) {
          console.error("❌ Erreur lors du chargement des données IA:", error);
          container.innerHTML = `
            <div class="alert alert-danger">
              <i class="fas fa-exclamation-triangle me-2"></i>
              Erreur lors du chargement des données IA: ${error.message}
            </div>
          `;
        }
      }

      // Fonctions utilitaires
      function getStatusBadge(status) {
        switch (status) {
          case "Livré":
            return "bg-success";
          case "En cours":
            return "bg-warning";
          case "Confirmé":
            return "bg-info";
          case "Annulé":
            return "bg-danger";
          default:
            return "bg-secondary";
        }
      }

      function viewOrderDetails(orderId) {
        alert(`Détails de la commande ${orderId} - À implémenter`);
      }

      function exportOrders() {
        alert("Export des commandes - À implémenter");
      }

      function refreshOrders() {
        loadOrders();
      }

      function configureGateway(gatewayName) {
        // Créer un modal pour configurer la passerelle de paiement
        const modalHtml = `
          <div class="modal fade" id="configureGatewayModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
              <div class="modal-content">
                <div class="modal-header">
                  <h5 class="modal-title">Configuration - ${gatewayName}</h5>
                  <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                  <form id="configureGatewayForm">
                    <div class="mb-3">
                      <label for="gatewayApiKey" class="form-label">Clé API</label>
                      <div class="input-group">
                        <input type="password" class="form-control" id="gatewayApiKey" placeholder="Entrez votre clé API ${gatewayName}">
                        <button class="btn btn-outline-secondary" type="button" onclick="toggleGatewayKeyVisibility()">
                          <i class="fas fa-eye" id="gatewayToggleIcon"></i>
                        </button>
                      </div>
                      <div class="form-text">Votre clé API sera stockée de manière sécurisée.</div>
                    </div>

                    <div class="mb-3">
                      <label for="gatewaySecretKey" class="form-label">Clé Secrète</label>
                      <input type="password" class="form-control" id="gatewaySecretKey" placeholder="Clé secrète (si applicable)">
                    </div>

                    <div class="mb-3">
                      <label for="gatewayEndpoint" class="form-label">URL de l'API</label>
                      <input type="url" class="form-control" id="gatewayEndpoint" placeholder="https://api.${gatewayName.toLowerCase()}.com" value="${getGatewayDefaultEndpoint(gatewayName)}">
                    </div>

                    <div class="row">
                      <div class="col-md-6">
                        <div class="mb-3">
                          <label for="gatewayCurrency" class="form-label">Devise par défaut</label>
                          <select class="form-control" id="gatewayCurrency">
                            <option value="DZD">DZD - Dinar Algérien</option>
                            <option value="EUR">EUR - Euro</option>
                            <option value="USD">USD - Dollar US</option>
                          </select>
                        </div>
                      </div>
                      <div class="col-md-6">
                        <div class="mb-3">
                          <label for="gatewayMode" class="form-label">Mode</label>
                          <select class="form-control" id="gatewayMode">
                            <option value="sandbox">Test (Sandbox)</option>
                            <option value="live">Production (Live)</option>
                          </select>
                        </div>
                      </div>
                    </div>

                    <div class="mb-3">
                      <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="gatewayEnabled" checked>
                        <label class="form-check-label" for="gatewayEnabled">
                          Activer cette passerelle de paiement
                        </label>
                      </div>
                    </div>
                  </form>
                </div>
                <div class="modal-footer">
                  <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                  <button type="button" class="btn btn-primary" onclick="saveGatewayConfiguration('${gatewayName}')">Sauvegarder</button>
                </div>
              </div>
            </div>
          </div>
        `;

        // Ajouter le modal au DOM
        if (!document.getElementById('configureGatewayModal')) {
          document.body.insertAdjacentHTML('beforeend', modalHtml);
        } else {
          document.getElementById('configureGatewayModal').outerHTML = modalHtml;
        }

        // Charger la configuration existante
        loadGatewayConfiguration(gatewayName);

        // Afficher le modal
        const modal = new bootstrap.Modal(document.getElementById('configureGatewayModal'));
        modal.show();
      }

      // Fonction pour obtenir l'endpoint par défaut selon la passerelle
      function getGatewayDefaultEndpoint(gatewayName) {
        const endpoints = {
          'PayPal': 'https://api.paypal.com',
          'Stripe': 'https://api.stripe.com',
          'CCP': 'https://api.ccp.dz',
          'Chargily': 'https://api.chargily.com',
          'Baridimob': 'https://api.baridimob.dz'
        };
        return endpoints[gatewayName] || 'https://api.example.com';
      }

      // Basculer la visibilité de la clé API
      function toggleGatewayKeyVisibility() {
        const input = document.getElementById('gatewayApiKey');
        const icon = document.getElementById('gatewayToggleIcon');

        if (input.type === 'password') {
          input.type = 'text';
          icon.className = 'fas fa-eye-slash';
        } else {
          input.type = 'password';
          icon.className = 'fas fa-eye';
        }
      }

      // Charger la configuration existante d'une passerelle
      async function loadGatewayConfiguration(gatewayName) {
        try {
          const response = await fetch(`/api/payments.php?action=get_gateway_config&gateway=${gatewayName}`, {
            headers: {
              'Authorization': 'Bearer demo_token',
              'Content-Type': 'application/json'
            }
          });

          if (response.ok) {
            const data = await response.json();
            if (data.success && data.data) {
              const config = data.data;
              document.getElementById('gatewayApiKey').value = config.api_key || '';
              document.getElementById('gatewaySecretKey').value = config.secret_key || '';
              document.getElementById('gatewayEndpoint').value = config.endpoint || getGatewayDefaultEndpoint(gatewayName);
              document.getElementById('gatewayCurrency').value = config.currency || 'DZD';
              document.getElementById('gatewayMode').value = config.mode || 'sandbox';
              document.getElementById('gatewayEnabled').checked = config.enabled || false;
            }
          }
        } catch (error) {
          console.log('Aucune configuration existante trouvée pour', gatewayName);
        }
      }

      // Sauvegarder la configuration d'une passerelle
      async function saveGatewayConfiguration(gatewayName) {
        try {
          const config = {
            gateway: gatewayName,
            api_key: document.getElementById('gatewayApiKey').value,
            secret_key: document.getElementById('gatewaySecretKey').value,
            endpoint: document.getElementById('gatewayEndpoint').value,
            currency: document.getElementById('gatewayCurrency').value,
            mode: document.getElementById('gatewayMode').value,
            enabled: document.getElementById('gatewayEnabled').checked
          };

          const response = await fetch('/api/payments.php?action=save_gateway_config', {
            method: 'POST',
            headers: {
              'Authorization': 'Bearer demo_token',
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(config)
          });

          const data = await response.json();

          if (data.success) {
            // Fermer le modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('configureGatewayModal'));
            modal.hide();

            // Afficher un message de succès
            showNotification(`Configuration de ${gatewayName} sauvegardée avec succès!`, 'success');

            // Recharger la section des paiements
            loadPaymentGateways();
          } else {
            throw new Error(data.message || 'Erreur lors de la sauvegarde');
          }
        } catch (error) {
          console.error('Erreur lors de la sauvegarde:', error);
          showNotification(`Erreur lors de la sauvegarde: ${error.message}`, 'error');
        }
      }

      // Fonction pour afficher des notifications
      function showNotification(message, type = 'info') {
        const alertClass = type === 'success' ? 'alert-success' : type === 'error' ? 'alert-danger' : 'alert-info';
        const notification = `
          <div class="alert ${alertClass} alert-dismissible fade show position-fixed" style="top: 20px; right: 20px; z-index: 9999;" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
          </div>
        `;
        document.body.insertAdjacentHTML('beforeend', notification);

        // Auto-remove after 5 seconds
        setTimeout(() => {
          const alerts = document.querySelectorAll('.alert');
          alerts.forEach(alert => {
            if (alert.textContent.includes(message)) {
              alert.remove();
            }
          });
        }, 5000);
      }

      function configureApiKey(modelName) {
        // Créer un modal pour configurer l'API Key
        const modalHtml = `
          <div class="modal fade" id="configureApiKeyModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
              <div class="modal-content">
                <div class="modal-header">
                  <h5 class="modal-title">Configuration API Key - ${modelName}</h5>
                  <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                  <form id="configureApiKeyForm">
                    <div class="mb-3">
                      <label for="apiKeyValue" class="form-label">API Key</label>
                      <div class="input-group">
                        <input type="password" class="form-control" id="apiKeyValue" placeholder="Entrez votre API Key ${modelName}">
                        <button class="btn btn-outline-secondary" type="button" onclick="toggleApiKeyVisibility()">
                          <i class="fas fa-eye" id="toggleIcon"></i>
                        </button>
                      </div>
                      <div class="form-text">Votre API Key sera stockée de manière sécurisée.</div>
                    </div>

                    <div class="mb-3">
                      <label for="apiEndpoint" class="form-label">Endpoint API (optionnel)</label>
                      <input type="url" class="form-control" id="apiEndpoint" placeholder="https://api.openai.com/v1" value="${getDefaultEndpoint(
                        modelName
                      )}">
                    </div>

                    <div class="row">
                      <div class="col-md-6">
                        <div class="mb-3">
                          <label for="maxTokens" class="form-label">Max Tokens</label>
                          <input type="number" class="form-control" id="maxTokens" value="4000">
                        </div>
                      </div>
                      <div class="col-md-6">
                        <div class="mb-3">
                          <label for="temperature" class="form-label">Temperature</label>
                          <input type="number" class="form-control" id="temperature" step="0.1" min="0" max="2" value="0.7">
                        </div>
                      </div>
                    </div>

                    <div class="mb-3">
                      <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="enableModel" checked>
                        <label class="form-check-label" for="enableModel">
                          Activer ce modèle
                        </label>
                      </div>
                    </div>

                    <!-- Informations spécifiques au modèle -->
                    <div id="modelSpecificInfo"></div>

                    <div class="alert alert-info">
                      <i class="fas fa-info-circle me-2"></i>
                      <strong>Note:</strong> Cette configuration sera utilisée pour la génération automatique de contenu et les fonctionnalités IA de la plateforme.
                    </div>
                  </form>
                </div>
                <div class="modal-footer">
                  <button type="button" class="btn btn-outline-primary me-2" onclick="testApiKey('${modelName}')">
                    <i class="fas fa-vial me-1"></i>Tester
                  </button>
                  <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                  <button type="button" class="btn btn-primary" onclick="saveApiKey('${modelName}')">
                    <i class="fas fa-save me-1"></i>Sauvegarder
                  </button>
                </div>
              </div>
            </div>
          </div>
        `;

        // Ajouter le modal au DOM s'il n'existe pas
        if (!document.getElementById("configureApiKeyModal")) {
          document.body.insertAdjacentHTML("beforeend", modalHtml);
        } else {
          // Mettre à jour le contenu du modal existant
          document.getElementById("configureApiKeyModal").outerHTML = modalHtml;
        }

        // Charger la configuration existante si disponible
        loadExistingApiKeyConfig(modelName);

        // Ajouter les informations spécifiques au modèle
        updateModelSpecificInfo(modelName);

        // Afficher le modal
        const modal = new bootstrap.Modal(
          document.getElementById("configureApiKeyModal")
        );
        modal.show();
      }

      function updateModelSpecificInfo(modelName) {
        const infoContainer = document.getElementById('modelSpecificInfo');

        const modelInfo = {
          // Modèles économiques OpenAI
          'GPT-3.5-turbo': {
            category: 'budget',
            costPerToken: '$0.0002',
            description: 'Le modèle le plus économique d\'OpenAI, parfait pour la plupart des tâches de génération de texte.',
            useCases: ['Génération de contenu', 'Résumés', 'Traductions', 'Questions-réponses'],
            savings: '90% moins cher que GPT-4'
          },
          'GPT-4o-mini': {
            category: 'budget',
            costPerToken: '$0.0005',
            description: 'Version allégée de GPT-4 avec un excellent rapport qualité-prix.',
            useCases: ['Analyse de texte', 'Rédaction créative', 'Code simple', 'Assistance client'],
            savings: '75% moins cher que GPT-4'
          },
          'GPT-4-turbo': {
            category: 'budget',
            costPerToken: '$0.002',
            description: 'Version optimisée de GPT-4 pour un meilleur équilibre coût-performance.',
            useCases: ['Tâches complexes', 'Analyse approfondie', 'Code avancé', 'Recherche'],
            savings: '33% moins cher que GPT-4'
          },
          // Modèles économiques Anthropic
          'Claude 3 Haiku': {
            category: 'budget',
            costPerToken: '$0.00025',
            description: 'Le modèle le plus rapide et économique d\'Anthropic, idéal pour les tâches simples.',
            useCases: ['Modération de contenu', 'Classification', 'Extraction d\'informations', 'Résumés courts'],
            savings: '92% moins cher que Claude-3 Opus'
          },
          // Modèles économiques Google
          'Gemini 1.5 Flash': {
            category: 'budget',
            costPerToken: '$0.00015',
            description: 'Modèle ultra-rapide de Google avec des coûts très compétitifs.',
            useCases: ['Génération rapide', 'Chatbots', 'Analyse de sentiment', 'Traduction'],
            savings: '85% moins cher que Gemini Pro'
          },
          // Modèles économiques Meta
          'Llama 3.1 8B': {
            category: 'budget',
            costPerToken: '$0.00005',
            description: 'Modèle open-source de Meta, le plus économique du marché.',
            useCases: ['Expérimentation', 'Prototypage', 'Tâches simples', 'Formation'],
            savings: '98% moins cher que les modèles premium'
          }
        };

        const info = modelInfo[modelName];

        if (info && info.category === 'budget') {
          infoContainer.innerHTML = `
            <div class="alert alert-success border-success">
              <div class="d-flex align-items-center mb-2">
                <i class="fas fa-coins text-success me-2"></i>
                <strong class="text-success">Modèle Économique - ${info.savings}</strong>
              </div>
              <p class="mb-2 small">${info.description}</p>
              <div class="row">
                <div class="col-md-6">
                  <strong class="small">💰 Coût:</strong> <span class="text-success">${info.costPerToken}/token</span>
                </div>
                <div class="col-md-6">
                  <strong class="small">⚡ Économies:</strong> <span class="text-success">${info.savings}</span>
                </div>
              </div>
              <div class="mt-2">
                <strong class="small">🎯 Cas d'usage recommandés:</strong>
                <div class="mt-1">
                  ${info.useCases.map(useCase => `<span class="badge bg-light text-dark me-1 mb-1">${useCase}</span>`).join('')}
                </div>
              </div>
            </div>
          `;
        } else if (info) {
          infoContainer.innerHTML = `
            <div class="alert alert-primary border-primary">
              <div class="d-flex align-items-center mb-2">
                <i class="fas fa-star text-primary me-2"></i>
                <strong class="text-primary">Modèle Premium</strong>
              </div>
              <p class="mb-2 small">${info.description || 'Modèle haute performance pour les tâches complexes.'}</p>
              <div class="row">
                <div class="col-md-6">
                  <strong class="small">💎 Coût:</strong> <span class="text-primary">${info.costPerToken || 'Variable'}/token</span>
                </div>
                <div class="col-md-6">
                  <strong class="small">⭐ Qualité:</strong> <span class="text-primary">Premium</span>
                </div>
              </div>
            </div>
          `;
        } else {
          infoContainer.innerHTML = '';
        }
      }

      function getDefaultEndpoint(modelName) {
        const endpoints = {
          // Modèles OpenAI
          "GPT-4": "https://api.openai.com/v1",
          "GPT-3.5": "https://api.openai.com/v1",
          "GPT-3.5-turbo": "https://api.openai.com/v1",
          "GPT-4o-mini": "https://api.openai.com/v1",
          "GPT-4-turbo": "https://api.openai.com/v1",
          // Modèles Anthropic
          "Claude": "https://api.anthropic.com/v1",
          "Claude-3": "https://api.anthropic.com/v1",
          "Claude 3 Haiku": "https://api.anthropic.com/v1",
          // Modèles Google
          "Gemini": "https://generativelanguage.googleapis.com/v1",
          "Gemini Pro": "https://generativelanguage.googleapis.com/v1",
          "Gemini 1.5 Flash": "https://generativelanguage.googleapis.com/v1",
          // Modèles Meta
          "Llama 3.1 8B": "https://api.together.xyz/v1",
          // Autres
          "Mistral": "https://api.mistral.ai/v1",
        };
        return endpoints[modelName] || "";
      }

      function toggleApiKeyVisibility() {
        const input = document.getElementById("apiKeyValue");
        const icon = document.getElementById("toggleIcon");

        if (input.type === "password") {
          input.type = "text";
          icon.className = "fas fa-eye-slash";
        } else {
          input.type = "password";
          icon.className = "fas fa-eye";
        }
      }

      async function loadExistingApiKeyConfig(modelName) {
        try {
          // Simuler le chargement de la configuration existante
          // En production, ceci ferait un appel à l'API pour récupérer la config
          const savedConfig = localStorage.getItem(
            `apikey_${modelName.toLowerCase()}`
          );
          if (savedConfig) {
            const config = JSON.parse(savedConfig);
            document.getElementById("apiKeyValue").value = config.apiKey || "";
            document.getElementById("apiEndpoint").value =
              config.endpoint || getDefaultEndpoint(modelName);
            document.getElementById("maxTokens").value =
              config.maxTokens || 4000;
            document.getElementById("temperature").value =
              config.temperature || 0.7;
            document.getElementById("enableModel").checked =
              config.enabled !== false;
          }
        } catch (error) {
          console.error(
            "Erreur lors du chargement de la configuration:",
            error
          );
        }
      }

      async function testApiKey(modelName) {
        const apiKey = document.getElementById("apiKeyValue").value;
        const endpoint = document.getElementById("apiEndpoint").value;
        const maxTokens = document.getElementById("maxTokens").value;
        const temperature = document.getElementById("temperature").value;

        if (!apiKey.trim()) {
          showNotification("Veuillez entrer une API Key avant de tester.", "error");
          return;
        }

        // Afficher un indicateur de test
        const testButton = event.target;
        const originalText = testButton.innerHTML;
        testButton.innerHTML =
          '<i class="fas fa-spinner fa-spin me-1"></i>Test en cours...';
        testButton.disabled = true;

        // Messages de test spécifiques aux modèles économiques
        const testPrompts = {
          'GPT-3.5-turbo': 'Écris un titre accrocheur pour une landing page de produit tech.',
          'GPT-4o-mini': 'Génère une description courte et persuasive pour un service SaaS.',
          'GPT-4-turbo': 'Analyse les avantages concurrentiels d\'un produit innovant.',
          'Claude 3 Haiku': 'Résume en 2 phrases les bénéfices d\'un outil de productivité.',
          'Gemini 1.5 Flash': 'Crée un slogan percutant pour une startup.',
          'Llama 3.1 8B': 'Génère 3 points clés pour une présentation produit.',
          'GPT-4': 'Analyse complexe de stratégie marketing.',
          'Claude-3': 'Rédaction de contenu premium.',
          'Gemini Pro': 'Analyse de données avancée.'
        };

        const testPrompt = testPrompts[modelName] || 'Test de génération de contenu simple.';

        try {
          // Simuler un test d'API avec paramètres spécifiques
          await new Promise((resolve) => setTimeout(resolve, 2000));

          // Meilleur taux de succès pour les modèles économiques (80% vs 70%)
          const budgetModels = ['GPT-3.5-turbo', 'GPT-4o-mini', 'GPT-4-turbo', 'Claude 3 Haiku', 'Gemini 1.5 Flash', 'Llama 3.1 8B'];
          const successRate = budgetModels.includes(modelName) ? 0.2 : 0.3;
          const isValid = Math.random() > successRate;

          if (isValid) {
            const responseTime = Math.floor(Math.random() * 1500) + 500; // 500-2000ms
            const estimatedCost = calculateEstimatedCost(modelName, testPrompt);
            const isBudget = budgetModels.includes(modelName);

            const message = `✅ Test réussi pour ${modelName}!\n` +
              `⚡ Temps de réponse: ${responseTime}ms\n` +
              `💰 Coût estimé: ${estimatedCost}\n` +
              `🎯 Prompt testé: "${testPrompt}"\n` +
              `⚙️ Paramètres: Max tokens: ${maxTokens}, Temperature: ${temperature}` +
              (isBudget ? '\n🏆 Modèle économique - Excellent rapport qualité/prix!' : '');

            showNotification(message, "success");
          } else {
            const message = `❌ Test échoué pour ${modelName}.\n` +
              `Vérifiez votre clé API et l'endpoint.\n` +
              `Paramètres testés: Max tokens: ${maxTokens}, Temperature: ${temperature}\n` +
              `Endpoint: ${endpoint}`;

            showNotification(message, "error");
          }
        } catch (error) {
          showNotification(`❌ Erreur lors du test : ${error.message}`, "error");
        } finally {
          // Restaurer le bouton
          testButton.innerHTML = originalText;
          testButton.disabled = false;
        }
      }

      function calculateEstimatedCost(modelName, prompt) {
        const costs = {
          'GPT-3.5-turbo': 0.0002,
          'GPT-4o-mini': 0.0005,
          'GPT-4-turbo': 0.002,
          'Claude 3 Haiku': 0.00025,
          'Gemini 1.5 Flash': 0.00015,
          'Llama 3.1 8B': 0.00005,
          'GPT-4': 0.006,
          'Claude-3': 0.008,
          'Gemini Pro': 0.004
        };

        const estimatedTokens = Math.ceil(prompt.length / 4) + 50; // Estimation simple
        const cost = (costs[modelName] || 0.001) * estimatedTokens;

        return cost < 0.001 ? '<$0.001' : `$${cost.toFixed(4)}`;
      }

      async function saveApiKey(modelName) {
        const apiKey = document.getElementById("apiKeyValue").value;
        const endpoint = document.getElementById("apiEndpoint").value;
        const maxTokens = document.getElementById("maxTokens").value;
        const temperature = document.getElementById("temperature").value;
        const enabled = document.getElementById("enableModel").checked;

        if (!apiKey.trim()) {
          alert("Veuillez entrer une API Key.");
          return;
        }

        try {
          // Configuration à sauvegarder
          const config = {
            apiKey: apiKey,
            endpoint: endpoint,
            maxTokens: parseInt(maxTokens),
            temperature: parseFloat(temperature),
            enabled: enabled,
            lastUpdated: new Date().toISOString(),
          };

          // Simuler la sauvegarde (en production, ceci ferait un appel API sécurisé)
          localStorage.setItem(
            `apikey_${modelName.toLowerCase()}`,
            JSON.stringify(config)
          );

          // Fermer le modal
          const modal = bootstrap.Modal.getInstance(
            document.getElementById("configureApiKeyModal")
          );
          modal.hide();

          // Recharger la section AI Usage pour refléter les changements
          loadAiUsage();

          alert(`✅ Configuration ${modelName} sauvegardée avec succès !`);
        } catch (error) {
          console.error("Erreur lors de la sauvegarde:", error);
          alert("❌ Erreur lors de la sauvegarde de la configuration.");
        }
      }

      // Fonctions de gestion des settings
      async function loadSettings() {
        const container = document.getElementById("settingsContainer");

        try {
          // Afficher un indicateur de chargement
          container.innerHTML = '<div class="text-center py-4"><div class="spinner-border" role="status"></div><p>Chargement des paramètres...</p></div>';

          // Charger les paramètres existants depuis l'API
          const response = await fetch("/api/settings.php", {
            headers: {
              Authorization: "Bearer demo_token",
              "Content-Type": "application/json",
            },
          });

          let settings = {};
          if (response.ok) {
            const data = await response.json();
            if (data.success && data.data) {
              settings = data.data;
            }
          }

          const html = `
            <div class="row">
              <div class="col-md-6">
                <div class="card">
                  <div class="card-header d-flex justify-content-between align-items-center">
                    <h6><i class="fas fa-envelope me-2"></i>Configuration SMTP</h6>
                    <span class="badge ${settings.smtp_enabled ? 'bg-success' : 'bg-danger'}">
                      ${settings.smtp_enabled ? 'Activé' : 'Désactivé'}
                    </span>
                  </div>
                  <div class="card-body">
                    <form id="smtpForm">
                      <div class="mb-3">
                        <label class="form-label">Serveur SMTP *</label>
                        <input type="text" class="form-control" id="smtpHost"
                               value="${settings.smtp_host || ''}"
                               placeholder="smtp.gmail.com" required>
                      </div>
                      <div class="row">
                        <div class="col-md-6">
                          <div class="mb-3">
                            <label class="form-label">Port *</label>
                            <input type="number" class="form-control" id="smtpPort"
                                   value="${settings.smtp_port || '587'}"
                                   placeholder="587" required>
                          </div>
                        </div>
                        <div class="col-md-6">
                          <div class="mb-3">
                            <label class="form-label">Sécurité</label>
                            <select class="form-control" id="smtpSecurity">
                              <option value="tls" ${settings.smtp_security === 'tls' ? 'selected' : ''}>TLS</option>
                              <option value="ssl" ${settings.smtp_security === 'ssl' ? 'selected' : ''}>SSL</option>
                              <option value="none" ${settings.smtp_security === 'none' ? 'selected' : ''}>Aucune</option>
                            </select>
                          </div>
                        </div>
                      </div>
                      <div class="mb-3">
                        <label class="form-label">Email d'expédition *</label>
                        <input type="email" class="form-control" id="smtpEmail"
                               value="${settings.smtp_email || ''}"
                               placeholder="<EMAIL>" required>
                      </div>
                      <div class="mb-3">
                        <label class="form-label">Mot de passe *</label>
                        <input type="password" class="form-control" id="smtpPassword"
                               placeholder="••••••••••••" required>
                        <small class="form-text text-muted">Laissez vide pour conserver le mot de passe actuel</small>
                      </div>
                      <div class="mb-3">
                        <label class="form-label">Nom d'expéditeur</label>
                        <input type="text" class="form-control" id="smtpFromName"
                               value="${settings.smtp_from_name || ''}"
                               placeholder="Landing Page System">
                      </div>
                      <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="smtpEnabled"
                               ${settings.smtp_enabled ? 'checked' : ''}>
                        <label class="form-check-label" for="smtpEnabled">
                          Activer l'envoi d'emails SMTP
                        </label>
                      </div>
                      <div class="d-flex gap-2">
                        <button type="button" class="btn btn-primary" onclick="saveSmtpSettings()">
                          <i class="fas fa-save me-1"></i>Sauvegarder
                        </button>
                        <button type="button" class="btn btn-outline-info" onclick="testSmtpConnection()">
                          <i class="fas fa-paper-plane me-1"></i>Tester
                        </button>
                      </div>
                    </form>
                  </div>
                </div>
              </div>

              <div class="col-md-6">
                <div class="card">
                  <div class="card-header">
                    <h6><i class="fas fa-cogs me-2"></i>Paramètres Système</h6>
                  </div>
                  <div class="card-body">
                    <form id="systemForm">
                      <div class="mb-3">
                        <label class="form-label">Nom du site</label>
                        <input type="text" class="form-control" id="siteName"
                               value="${settings.site_name || 'Landing Page System'}"
                               placeholder="Landing Page System">
                      </div>
                      <div class="mb-3">
                        <label class="form-label">URL du site</label>
                        <input type="url" class="form-control" id="siteUrl"
                               value="${settings.site_url || ''}"
                               placeholder="https://example.com">
                      </div>
                      <div class="mb-3">
                        <label class="form-label">Email administrateur</label>
                        <input type="email" class="form-control" id="adminEmail"
                               value="${settings.admin_email || ''}"
                               placeholder="<EMAIL>">
                      </div>
                      <div class="mb-3">
                        <label class="form-label">Fuseau horaire</label>
                        <select class="form-control" id="timezone">
                          <option value="Africa/Algiers" ${settings.timezone === 'Africa/Algiers' ? 'selected' : ''}>Algérie (UTC+1)</option>
                          <option value="Europe/Paris" ${settings.timezone === 'Europe/Paris' ? 'selected' : ''}>Paris (UTC+1)</option>
                          <option value="UTC" ${settings.timezone === 'UTC' ? 'selected' : ''}>UTC</option>
                        </select>
                      </div>
                      <div class="mb-3">
                        <label class="form-label">Langue par défaut</label>
                        <select class="form-control" id="defaultLanguage">
                          <option value="fr" ${settings.default_language === 'fr' ? 'selected' : ''}>Français</option>
                          <option value="ar" ${settings.default_language === 'ar' ? 'selected' : ''}>العربية</option>
                          <option value="en" ${settings.default_language === 'en' ? 'selected' : ''}>English</option>
                        </select>
                      </div>
                      <button type="button" class="btn btn-primary" onclick="saveSystemSettings()">
                        <i class="fas fa-save me-1"></i>Sauvegarder
                      </button>
                    </form>
                  </div>
                </div>

                <div class="card mt-3">
                  <div class="card-header">
                    <h6><i class="fas fa-shield-alt me-2"></i>Sécurité</h6>
                  </div>
                  <div class="card-body">
                    <div class="mb-3 form-check">
                      <input type="checkbox" class="form-check-input" id="maintenanceMode"
                             ${settings.maintenance_mode ? 'checked' : ''}>
                      <label class="form-check-label" for="maintenanceMode">
                        Mode maintenance
                      </label>
                    </div>
                    <div class="mb-3 form-check">
                      <input type="checkbox" class="form-check-input" id="registrationEnabled"
                             ${settings.registration_enabled !== false ? 'checked' : ''}>
                      <label class="form-check-label" for="registrationEnabled">
                        Autoriser les nouvelles inscriptions
                      </label>
                    </div>
                    <button type="button" class="btn btn-warning" onclick="saveSecuritySettings()">
                      <i class="fas fa-shield-alt me-1"></i>Sauvegarder
                    </button>
                  </div>
                </div>
              </div>
            </div>
          `;

          container.innerHTML = html;
          console.log("✅ Paramètres chargés:", settings);
        } catch (error) {
          console.error("❌ Erreur lors du chargement des paramètres:", error);
          container.innerHTML = `
            <div class="alert alert-danger">
              <i class="fas fa-exclamation-triangle me-2"></i>
              Erreur lors du chargement des paramètres: ${error.message}
            </div>
          `;
        }
      }

      async function saveSmtpSettings() {
        const smtpData = {
          smtp_host: document.getElementById('smtpHost').value,
          smtp_port: parseInt(document.getElementById('smtpPort').value),
          smtp_security: document.getElementById('smtpSecurity').value,
          smtp_email: document.getElementById('smtpEmail').value,
          smtp_password: document.getElementById('smtpPassword').value,
          smtp_from_name: document.getElementById('smtpFromName').value,
          smtp_enabled: document.getElementById('smtpEnabled').checked
        };

        // Validation
        if (!smtpData.smtp_host || !smtpData.smtp_email || !smtpData.smtp_port) {
          alert('Veuillez remplir tous les champs obligatoires');
          return;
        }

        try {
          const response = await fetch('/api/settings.php', {
            method: 'POST',
            headers: {
              'Authorization': 'Bearer demo_token',
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              type: 'smtp',
              settings: smtpData
            })
          });

          const data = await response.json();

          if (data.success) {
            alert('✅ Configuration SMTP sauvegardée avec succès !');
            // Recharger les paramètres
            await loadSettings();
          } else {
            alert('❌ Erreur lors de la sauvegarde: ' + data.message);
          }
        } catch (error) {
          console.error('Erreur:', error);
          alert('❌ Erreur lors de la sauvegarde des paramètres SMTP');
        }
      }

      async function testSmtpConnection() {
        const testEmail = prompt('Entrez une adresse email pour tester l\'envoi:');
        if (!testEmail) return;

        try {
          const response = await fetch('/api/settings.php', {
            method: 'POST',
            headers: {
              'Authorization': 'Bearer demo_token',
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              type: 'test_smtp',
              test_email: testEmail
            })
          });

          const data = await response.json();

          if (data.success) {
            alert('✅ Email de test envoyé avec succès !');
          } else {
            alert('❌ Erreur lors du test: ' + data.message);
          }
        } catch (error) {
          console.error('Erreur:', error);
          alert('❌ Erreur lors du test SMTP');
        }
      }

      async function saveSystemSettings() {
        const systemData = {
          site_name: document.getElementById('siteName').value,
          site_url: document.getElementById('siteUrl').value,
          admin_email: document.getElementById('adminEmail').value,
          timezone: document.getElementById('timezone').value,
          default_language: document.getElementById('defaultLanguage').value
        };

        try {
          const response = await fetch('/api/settings.php', {
            method: 'POST',
            headers: {
              'Authorization': 'Bearer demo_token',
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              type: 'system',
              settings: systemData
            })
          });

          const data = await response.json();

          if (data.success) {
            alert('✅ Paramètres système sauvegardés avec succès !');
          } else {
            alert('❌ Erreur lors de la sauvegarde: ' + data.message);
          }
        } catch (error) {
          console.error('Erreur:', error);
          alert('❌ Erreur lors de la sauvegarde des paramètres système');
        }
      }

      async function saveSecuritySettings() {
        const securityData = {
          maintenance_mode: document.getElementById('maintenanceMode').checked,
          registration_enabled: document.getElementById('registrationEnabled').checked
        };

        try {
          const response = await fetch('/api/settings.php', {
            method: 'POST',
            headers: {
              'Authorization': 'Bearer demo_token',
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              type: 'security',
              settings: securityData
            })
          });

          const data = await response.json();

          if (data.success) {
            alert('✅ Paramètres de sécurité sauvegardés avec succès !');
          } else {
            alert('❌ Erreur lors de la sauvegarde: ' + data.message);
          }
        } catch (error) {
          console.error('Erreur:', error);
          alert('❌ Erreur lors de la sauvegarde des paramètres de sécurité');
        }
      }

      // Gestionnaires d'événements
      document.addEventListener("DOMContentLoaded", function () {
        initializePage();

        // Gestionnaire de navigation
        document.querySelectorAll(".nav-link").forEach((link) => {
          link.addEventListener("click", function (e) {
            e.preventDefault();
            const section = this.getAttribute("data-section");
            showSection(section);
          });
        });
      });

      // Fonction pour créer une landing page à partir d'un template
      async function createFromTemplate(templateId) {
        try {
          // Afficher un indicateur de chargement
          showNotification('Création de la landing page en cours...', 'info');

          // Appeler l'API pour créer une landing page à partir du template
          const response = await fetch('/api/landing-pages.php?action=create', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer demo_token'
            },
            body: JSON.stringify({
              title: `Nouvelle Landing Page ${templateId}`,
              template_id: templateId,
              status: 'draft',
              user_id: 1
            })
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const data = await response.json();

          if (data.success) {
            showNotification('Landing page créée avec succès !', 'success');
            // Ouvrir l'éditeur avec la nouvelle landing page
            openLandingPageBuilder(data.page_id);
          } else {
            throw new Error(data.message || 'Erreur lors de la création de la landing page');
          }
        } catch (error) {
          console.error('❌ Erreur:', error);
          showNotification(`Erreur: ${error.message}`, 'error');
        }
      }

      // Fonction pour ajouter une nouvelle landing page
      function addNewLandingPage() {
        // Créer et afficher le modal de sélection de template
        const modalHtml = `
          <div class="modal fade" id="templateSelectorModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
              <div class="modal-content">
                <div class="modal-header">
                  <h5 class="modal-title">Choisir un template</h5>
                  <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                  <div class="row">
                    <div class="col-md-4 mb-3">
                      <div class="card h-100">
                        <div class="card-body text-center">
                          <h5 class="card-title">Page vide</h5>
                          <p class="card-text">Commencer avec une page vide</p>
                          <button class="btn btn-primary" onclick="openLandingPageBuilder()">
                            Sélectionner
                          </button>
                        </div>
                      </div>
                    </div>
                    <div class="col-md-4 mb-3">
                      <div class="card h-100">
                        <div class="card-body text-center">
                          <h5 class="card-title">Template Produit</h5>
                          <p class="card-text">Idéal pour présenter un produit</p>
                          <button class="btn btn-primary" onclick="createFromTemplate('product')">
                            Sélectionner
                          </button>
                        </div>
                      </div>
                    </div>
                    <div class="col-md-4 mb-3">
                      <div class="card h-100">
                        <div class="card-body text-center">
                          <h5 class="card-title">Template Service</h5>
                          <p class="card-text">Parfait pour promouvoir un service</p>
                          <button class="btn btn-primary" onclick="createFromTemplate('service')">
                            Sélectionner
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        `;

        // Ajouter le modal au DOM s'il n'existe pas déjà
        if (!document.getElementById('templateSelectorModal')) {
          document.body.insertAdjacentHTML('beforeend', modalHtml);
        }

        // Afficher le modal
        const modal = new bootstrap.Modal(document.getElementById('templateSelectorModal'));
        modal.show();
      }

      // Initialiser les graphiques avec Chart.js
      async function initializeCharts() {
        try {
          // Charger les données d'analytics depuis notre API
          const response = await fetch(
            "/firebase-dashboard-api.php?action=stats",
            {
              headers: {
                Authorization: "Bearer demo_token",
                "Content-Type": "application/json",
              },
            }
          );

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const data = await response.json();

          if (data.success) {
            const analyticsData = data.data;

            // Graphique des ventes
            const salesChartElement = document.getElementById("salesChart");
            if (!salesChartElement) {
              console.warn("⚠️ Élément salesChart non trouvé, graphique ignoré");
              return;
            }
            const salesCtx = salesChartElement.getContext("2d");
            new Chart(salesCtx, {
              type: "line",
              data: {
                labels: analyticsData.sales
                  ? analyticsData.sales.map((item) => item.date)
                  : ["Jan", "Fév", "Mar", "Avr", "Mai", "Jun"],
                datasets: [
                  {
                    label: "Ventes (DA)",
                    data: analyticsData.sales
                      ? analyticsData.sales.map((item) => item.revenue)
                      : [12000, 19000, 15000, 25000, 22000, 30000],
                    borderColor: "rgb(75, 192, 192)",
                    backgroundColor: "rgba(75, 192, 192, 0.2)",
                    tension: 0.1,
                  },
                ],
              },
              options: {
                responsive: true,
                plugins: {
                  title: {
                    display: true,
                    text: "Évolution des Ventes",
                  },
                },
                scales: {
                  y: {
                    beginAtZero: true,
                    ticks: {
                      callback: function (value) {
                        return value.toLocaleString() + " DA";
                      },
                    },
                  },
                },
              },
            });

            console.log("✅ Graphiques initialisés avec succès");
          } else {
            throw new Error("Erreur lors du chargement des données analytics");
          }
        } catch (error) {
          console.error(
            "❌ Erreur lors de l'initialisation des graphiques:",
            error
          );

          // En cas d'erreur, créer un graphique avec des données par défaut
          const salesChartElement = document.getElementById("salesChart");
          if (!salesChartElement) {
            console.warn("⚠️ Élément salesChart non trouvé dans le catch, graphique ignoré");
            return;
          }
          const salesCtx = salesChartElement.getContext("2d");
          new Chart(salesCtx, {
            type: "line",
            data: {
              labels: ["Jan", "Fév", "Mar", "Avr", "Mai", "Jun"],
              datasets: [
                {
                  label: "Ventes (DA)",
                  data: [12000, 19000, 15000, 25000, 22000, 30000],
                  borderColor: "rgb(75, 192, 192)",
                  backgroundColor: "rgba(75, 192, 192, 0.2)",
                  tension: 0.1,
                },
              ],
            },
            options: {
              responsive: true,
              plugins: {
                title: {
                  display: true,
                  text: "Évolution des Ventes (Données de démonstration)",
                },
              },
              scales: {
                y: {
                  beginAtZero: true,
                  ticks: {
                    callback: function (value) {
                      return value.toLocaleString() + " DA";
                    },
                  },
                },
              },
            },
          });
        }
      }

      // Gestionnaire de déconnexion
      document
        .getElementById("logoutBtn")
        .addEventListener("click", handleLogout);

      console.log("🔥 Dashboard Firebase initialisé");
    </script>

          </div> <!-- Fermeture main-content -->
        </div> <!-- Fermeture row -->
      </div> <!-- Fermeture container-fluid -->
    </div> <!-- Fermeture mainDashboard -->

    <!-- Landing Page Builder Modal -->
    <div class="modal fade" id="landingPageBuilderModal" tabindex="-1" aria-labelledby="landingPageBuilderModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-fullscreen">
        <div class="modal-content">
          <div class="modal-header bg-primary text-white">
            <h5 class="modal-title" id="landingPageBuilderModalLabel">
              <i class="fas fa-magic me-2"></i>Constructeur de Landing Page
            </h5>
            <div class="d-flex gap-2">
              <button type="button" class="btn btn-light btn-sm" onclick="previewLandingPage()">
                <i class="fas fa-eye me-1"></i>Aperçu
              </button>
              <button type="button" class="btn btn-success btn-sm" onclick="saveLandingPage()">
                <i class="fas fa-save me-1"></i>Sauvegarder
              </button>
              <button type="button" class="btn btn-outline-light btn-sm" data-bs-dismiss="modal">
                <i class="fas fa-times"></i>
              </button>
            </div>
          </div>
          <div class="modal-body p-0">
            <div class="row g-0 h-100">
              <!-- Sidebar avec composants -->
              <div class="col-md-3 bg-light border-end" style="height: 80vh; overflow-y: auto;">
                <div class="p-3">
                  <!-- Onglets -->
                  <ul class="nav nav-tabs mb-3" id="builderTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                      <button class="nav-link active" id="components-tab" data-bs-toggle="tab" data-bs-target="#components" type="button" role="tab">
                        <i class="fas fa-puzzle-piece me-1"></i>Blocs
                      </button>
                    </li>
                    <li class="nav-item" role="presentation">
                      <button class="nav-link" id="settings-tab" data-bs-toggle="tab" data-bs-target="#settings" type="button" role="tab">
                        <i class="fas fa-cog me-1"></i>Config
                      </button>
                    </li>
                    <li class="nav-item" role="presentation">
                      <button class="nav-link" id="ai-tab" data-bs-toggle="tab" data-bs-target="#ai" type="button" role="tab">
                        <i class="fas fa-robot me-1"></i>IA
                      </button>
                    </li>
                  </ul>

                  <!-- Contenu des onglets -->
                  <div class="tab-content" id="builderTabsContent">
                    <!-- Onglet Composants -->
                    <div class="tab-pane fade show active" id="components" role="tabpanel">
                      <h6 class="text-muted mb-3">Glissez les blocs dans l'éditeur</h6>

                      <!-- Blocs de base -->
                      <div class="mb-3">
                        <h6><i class="fas fa-layer-group me-1"></i>Blocs de base</h6>
                        <div class="d-grid gap-2">
                          <div class="component-item" draggable="true" data-component="hero">
                            <i class="fas fa-star me-2"></i>Section Hero
                          </div>
                          <div class="component-item" draggable="true" data-component="text">
                            <i class="fas fa-align-left me-2"></i>Bloc Texte
                          </div>
                          <div class="component-item" draggable="true" data-component="image">
                            <i class="fas fa-image me-2"></i>Image
                          </div>
                          <div class="component-item" draggable="true" data-component="button">
                            <i class="fas fa-mouse-pointer me-2"></i>Bouton CTA
                          </div>
                          <div class="component-item" draggable="true" data-component="form">
                            <i class="fas fa-wpforms me-2"></i>Formulaire
                          </div>
                        </div>
                      </div>

                      <!-- Blocs avancés -->
                      <div class="mb-3">
                        <h6><i class="fas fa-magic me-1"></i>Blocs avancés</h6>
                        <div class="d-grid gap-2">
                          <div class="component-item" draggable="true" data-component="testimonials">
                            <i class="fas fa-quote-left me-2"></i>Témoignages
                          </div>
                          <div class="component-item" draggable="true" data-component="pricing">
                            <i class="fas fa-tags me-2"></i>Tarification
                          </div>
                          <div class="component-item" draggable="true" data-component="features">
                            <i class="fas fa-list-ul me-2"></i>Fonctionnalités
                          </div>
                          <div class="component-item" draggable="true" data-component="gallery">
                            <i class="fas fa-images me-2"></i>Galerie
                          </div>
                          <div class="component-item" draggable="true" data-component="countdown">
                            <i class="fas fa-clock me-2"></i>Compte à rebours
                          </div>
                        </div>
                      </div>

                      <!-- Layouts -->
                      <div class="mb-3">
                        <h6><i class="fas fa-th-large me-1"></i>Layouts</h6>
                        <div class="d-grid gap-2">
                          <div class="component-item" draggable="true" data-component="two-columns">
                            <i class="fas fa-columns me-2"></i>2 Colonnes
                          </div>
                          <div class="component-item" draggable="true" data-component="three-columns">
                            <i class="fas fa-th me-2"></i>3 Colonnes
                          </div>
                          <div class="component-item" draggable="true" data-component="spacer">
                            <i class="fas fa-arrows-alt-v me-2"></i>Espacement
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Onglet Configuration -->
                    <div class="tab-pane fade" id="settings" role="tabpanel">
                      <div class="mb-3">
                        <label class="form-label">Nom de la page</label>
                        <input type="text" class="form-control" id="pageTitle" placeholder="Ma Landing Page">
                      </div>

                      <div class="mb-3">
                        <label class="form-label">Langue</label>
                        <select class="form-select" id="pageLanguage">
                          <option value="fr">Français</option>
                          <option value="ar">العربية</option>
                          <option value="en">English</option>
                        </select>
                      </div>

                      <div class="mb-3">
                        <label class="form-label">Couleur principale</label>
                        <input type="color" class="form-control form-control-color" id="primaryColor" value="#007bff">
                      </div>

                      <div class="mb-3">
                        <label class="form-label">Police</label>
                        <select class="form-select" id="fontFamily">
                          <option value="Inter">Inter (Moderne)</option>
                          <option value="Roboto">Roboto (Classique)</option>
                          <option value="Poppins">Poppins (Élégant)</option>
                          <option value="Cairo">Cairo (Arabe)</option>
                        </select>
                      </div>

                      <div class="mb-3">
                        <div class="form-check">
                          <input class="form-check-input" type="checkbox" id="enableRTL">
                          <label class="form-check-label" for="enableRTL">
                            Support RTL (Arabe)
                          </label>
                        </div>
                      </div>

                      <div class="mb-3">
                        <div class="form-check">
                          <input class="form-check-input" type="checkbox" id="enableAnalytics" checked>
                          <label class="form-check-label" for="enableAnalytics">
                            Analytics intégrés
                          </label>
                        </div>
                      </div>
                    </div>

                    <!-- Onglet IA -->
                    <div class="tab-pane fade" id="ai" role="tabpanel">
                      <div class="mb-3">
                        <h6><i class="fas fa-magic me-1"></i>Génération IA</h6>
                        <p class="small text-muted">Utilisez l'IA pour générer du contenu automatiquement</p>
                      </div>

                      <div class="mb-3">
                        <label class="form-label">Type de produit/service</label>
                        <input type="text" class="form-control" id="aiProductType" placeholder="Ex: Application mobile, Formation en ligne...">
                      </div>

                      <div class="mb-3">
                        <label class="form-label">Public cible</label>
                        <input type="text" class="form-control" id="aiTargetAudience" placeholder="Ex: Entrepreneurs, Étudiants...">
                      </div>

                      <div class="mb-3">
                        <label class="form-label">Ton de communication</label>
                        <select class="form-select" id="aiTone">
                          <option value="professional">Professionnel</option>
                          <option value="friendly">Amical</option>
                          <option value="urgent">Urgent</option>
                          <option value="luxury">Luxe</option>
                          <option value="technical">Technique</option>
                        </select>
                      </div>

                      <div class="d-grid gap-2">
                        <button class="btn btn-primary" onclick="generateAIContent('hero')">
                          <i class="fas fa-robot me-1"></i>Générer Hero
                        </button>
                        <button class="btn btn-outline-primary" onclick="generateAIContent('features')">
                          <i class="fas fa-list me-1"></i>Générer Fonctionnalités
                        </button>
                        <button class="btn btn-outline-primary" onclick="generateAIContent('testimonials')">
                          <i class="fas fa-quote-left me-1"></i>Générer Témoignages
                        </button>
                        <button class="btn btn-outline-primary" onclick="generateAIContent('full')">
                          <i class="fas fa-magic me-1"></i>Page Complète
                        </button>
                      </div>

                      <div class="mt-3">
                        <div class="alert alert-info small">
                          <i class="fas fa-info-circle me-1"></i>
                          <strong>Modèles économiques disponibles:</strong><br>
                          GPT-3.5-turbo, Claude 3 Haiku, Gemini 1.5 Flash
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Zone d'édition principale -->
              <div class="col-md-6" style="height: 80vh; overflow-y: auto;">
                <div class="p-3">
                  <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="mb-0">Éditeur Visuel</h6>
                    <div class="btn-group btn-group-sm" role="group">
                      <button type="button" class="btn btn-outline-secondary active" onclick="setViewMode('desktop')">
                        <i class="fas fa-desktop"></i>
                      </button>
                      <button type="button" class="btn btn-outline-secondary" onclick="setViewMode('tablet')">
                        <i class="fas fa-tablet-alt"></i>
                      </button>
                      <button type="button" class="btn btn-outline-secondary" onclick="setViewMode('mobile')">
                        <i class="fas fa-mobile-alt"></i>
                      </button>
                    </div>
                  </div>

                  <!-- Zone de drop -->
                  <div id="pageBuilder" class="page-builder-canvas border rounded p-3 bg-white" style="min-height: 600px;">
                    <div class="text-center text-muted py-5">
                      <i class="fas fa-mouse-pointer fa-3x mb-3 opacity-50"></i>
                      <h5>Glissez des blocs ici pour commencer</h5>
                      <p>Ou utilisez un template pour démarrer rapidement</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Panneau de propriétés -->
              <div class="col-md-3 bg-light border-start" style="height: 80vh; overflow-y: auto;">
                <div class="p-3">
                  <h6 class="mb-3"><i class="fas fa-sliders-h me-1"></i>Propriétés</h6>

                  <div id="propertiesPanel">
                    <div class="text-center text-muted py-4">
                      <i class="fas fa-hand-pointer fa-2x mb-2 opacity-50"></i>
                      <p class="small">Sélectionnez un élément pour modifier ses propriétés</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Styles pour le constructeur -->
    <style>
      .component-item {
        padding: 8px 12px;
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 6px;
        cursor: grab;
        transition: all 0.2s;
        font-size: 14px;
      }

      .component-item:hover {
        background: #f8f9fa;
        border-color: #007bff;
        transform: translateY(-1px);
      }

      .component-item:active {
        cursor: grabbing;
      }

      .page-builder-canvas {
        position: relative;
      }

      .page-builder-canvas.drag-over {
        border-color: #007bff !important;
        background-color: #f8f9ff !important;
      }

      .builder-element {
        position: relative;
        margin: 10px 0;
        padding: 15px;
        border: 2px dashed transparent;
        border-radius: 6px;
        transition: all 0.2s;
      }

      .builder-element:hover {
        border-color: #007bff;
        background-color: rgba(0, 123, 255, 0.05);
      }

      .builder-element.selected {
        border-color: #007bff;
        background-color: rgba(0, 123, 255, 0.1);
      }

      .element-controls {
        position: absolute;
        top: -15px;
        right: 5px;
        display: none;
      }

      .builder-element:hover .element-controls,
      .builder-element.selected .element-controls {
        display: block;
      }

      .view-mode-desktop .page-builder-canvas {
        max-width: 100%;
      }

      .view-mode-tablet .page-builder-canvas {
        max-width: 768px;
        margin: 0 auto;
      }

      .view-mode-mobile .page-builder-canvas {
        max-width: 375px;
        margin: 0 auto;
      }
    </style>
        </div>
      </div>
    </div>

     <!-- JavaScript pour le constructeur de landing pages -->
     <script>
       // Variables globales pour le constructeur
       let currentSelectedElement = null;
       let pageBuilderData = {
         elements: [],
         settings: {
           title: '',
           language: 'fr',
           primaryColor: '#007bff',
           fontFamily: 'Inter',
           enableRTL: false,
           enableAnalytics: true
         }
       };

       // Templates de composants
       const componentTemplates = {
         hero: {
           html: `<div class="hero-section text-center py-5">
             <div class="container">
               <h1 class="display-4 fw-bold mb-4">Votre Titre Principal</h1>
               <p class="lead mb-4">Une description captivante de votre produit ou service qui convertit vos visiteurs en clients.</p>
               <button class="btn btn-primary btn-lg">Commencer Maintenant</button>
             </div>
           </div>`,
           css: `.hero-section { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }`,
           name: 'Section Hero'
         },
         text: {
           html: `<div class="text-section py-4">
             <div class="container">
               <h2>Titre de Section</h2>
               <p>Votre contenu textuel ici. Vous pouvez modifier ce texte pour présenter vos idées, produits ou services de manière claire et engageante.</p>
             </div>
           </div>`,
           css: `.text-section { background: #f8f9fa; }`,
           name: 'Bloc Texte'
         },
         image: {
           html: `<div class="image-section py-4">
             <div class="container text-center">
               <img src="https://via.placeholder.com/600x400/007bff/ffffff?text=Votre+Image" class="img-fluid rounded" alt="Image descriptive">
             </div>
           </div>`,
           css: `.image-section img { max-width: 100%; height: auto; }`,
           name: 'Image'
         },
         button: {
           html: `<div class="button-section py-3">
             <div class="container text-center">
               <button class="btn btn-primary btn-lg">Bouton d'Action</button>
             </div>
           </div>`,
           css: `.button-section .btn { min-width: 200px; }`,
           name: 'Bouton CTA'
         },
         form: {
           html: `<div class="form-section py-5">
             <div class="container">
               <div class="row justify-content-center">
                 <div class="col-md-6">
                   <h3 class="text-center mb-4">Contactez-nous</h3>
                   <form>
                     <div class="mb-3">
                       <input type="text" class="form-control" placeholder="Votre nom" required>
                     </div>
                     <div class="mb-3">
                       <input type="email" class="form-control" placeholder="Votre email" required>
                     </div>
                     <div class="mb-3">
                       <textarea class="form-control" rows="4" placeholder="Votre message"></textarea>
                     </div>
                     <button type="submit" class="btn btn-primary w-100">Envoyer</button>
                   </form>
                 </div>
               </div>
             </div>
           </div>`,
           css: `.form-section { background: #f8f9fa; }`,
           name: 'Formulaire'
         },
         testimonials: {
           html: `<div class="testimonials-section py-5">
             <div class="container">
               <h2 class="text-center mb-5">Ce que disent nos clients</h2>
               <div class="row">
                 <div class="col-md-4 mb-4">
                   <div class="card h-100">
                     <div class="card-body text-center">
                       <i class="fas fa-quote-left fa-2x text-primary mb-3"></i>
                       <p class="card-text">"Excellent service, je recommande vivement !"</p>
                       <h6 class="card-title">Marie Dupont</h6>
                       <small class="text-muted">Cliente satisfaite</small>
                     </div>
                   </div>
                 </div>
                 <div class="col-md-4 mb-4">
                   <div class="card h-100">
                     <div class="card-body text-center">
                       <i class="fas fa-quote-left fa-2x text-primary mb-3"></i>
                       <p class="card-text">"Une expérience formidable, très professionnel."</p>
                       <h6 class="card-title">Jean Martin</h6>
                       <small class="text-muted">Entrepreneur</small>
                     </div>
                   </div>
                 </div>
                 <div class="col-md-4 mb-4">
                   <div class="card h-100">
                     <div class="card-body text-center">
                       <i class="fas fa-quote-left fa-2x text-primary mb-3"></i>
                       <p class="card-text">"Résultats au-delà de mes attentes !"</p>
                       <h6 class="card-title">Sophie Bernard</h6>
                       <small class="text-muted">Directrice Marketing</small>
                     </div>
                   </div>
                 </div>
               </div>
             </div>
           </div>`,
           css: `.testimonials-section .card { border: none; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }`,
           name: 'Témoignages'
         },
         pricing: {
           html: `<div class="pricing-section py-5">
             <div class="container">
               <h2 class="text-center mb-5">Nos Tarifs</h2>
               <div class="row justify-content-center">
                 <div class="col-md-4 mb-4">
                   <div class="card text-center">
                     <div class="card-header bg-primary text-white">
                       <h4>Basique</h4>
                     </div>
                     <div class="card-body">
                       <h2 class="card-title">29€<small class="text-muted">/mois</small></h2>
                       <ul class="list-unstyled">
                         <li>✓ Fonctionnalité 1</li>
                         <li>✓ Fonctionnalité 2</li>
                         <li>✓ Support email</li>
                       </ul>
                       <button class="btn btn-primary">Choisir</button>
                     </div>
                   </div>
                 </div>
                 <div class="col-md-4 mb-4">
                   <div class="card text-center border-primary">
                     <div class="card-header bg-primary text-white">
                       <h4>Premium <span class="badge bg-warning">Populaire</span></h4>
                     </div>
                     <div class="card-body">
                       <h2 class="card-title">59€<small class="text-muted">/mois</small></h2>
                       <ul class="list-unstyled">
                         <li>✓ Toutes les fonctionnalités Basique</li>
                         <li>✓ Fonctionnalité avancée</li>
                         <li>✓ Support prioritaire</li>
                       </ul>
                       <button class="btn btn-primary">Choisir</button>
                     </div>
                   </div>
                 </div>
               </div>
             </div>
           </div>`,
           css: `.pricing-section .card { transition: transform 0.2s; } .pricing-section .card:hover { transform: translateY(-5px); }`,
           name: 'Tarification'
         },
         features: {
           html: `<div class="features-section py-5">
             <div class="container">
               <h2 class="text-center mb-5">Nos Fonctionnalités</h2>
               <div class="row">
                 <div class="col-md-4 mb-4 text-center">
                   <i class="fas fa-rocket fa-3x text-primary mb-3"></i>
                   <h4>Rapide</h4>
                   <p>Performance optimisée pour une expérience utilisateur exceptionnelle.</p>
                 </div>
                 <div class="col-md-4 mb-4 text-center">
                   <i class="fas fa-shield-alt fa-3x text-primary mb-3"></i>
                   <h4>Sécurisé</h4>
                   <p>Sécurité de niveau entreprise pour protéger vos données.</p>
                 </div>
                 <div class="col-md-4 mb-4 text-center">
                   <i class="fas fa-users fa-3x text-primary mb-3"></i>
                   <h4>Collaboratif</h4>
                   <p>Travaillez en équipe avec des outils de collaboration avancés.</p>
                 </div>
               </div>
             </div>
           </div>`,
           css: `.features-section i { transition: transform 0.2s; } .features-section i:hover { transform: scale(1.1); }`,
           name: 'Fonctionnalités'
         },
         'two-columns': {
           html: `<div class="two-columns-section py-4">
             <div class="container">
               <div class="row">
                 <div class="col-md-6">
                   <h3>Colonne Gauche</h3>
                   <p>Contenu de la première colonne. Vous pouvez ajouter du texte, des images ou d'autres éléments ici.</p>
                 </div>
                 <div class="col-md-6">
                   <h3>Colonne Droite</h3>
                   <p>Contenu de la deuxième colonne. Parfait pour créer des layouts équilibrés et organisés.</p>
                 </div>
               </div>
             </div>
           </div>`,
           css: `.two-columns-section { background: #f8f9fa; }`,
           name: '2 Colonnes'
         },
         spacer: {
           html: `<div class="spacer-section" style="height: 60px;"></div>`,
           css: `.spacer-section { background: transparent; }`,
           name: 'Espacement'
         }
       };

       // Initialisation du constructeur
       function initLandingPageBuilder() {
         const canvas = document.getElementById('pageBuilder');
         const components = document.querySelectorAll('.component-item');

         // Gestion du drag and drop
         components.forEach(component => {
           component.addEventListener('dragstart', handleDragStart);
         });

         canvas.addEventListener('dragover', handleDragOver);
         canvas.addEventListener('drop', handleDrop);
         canvas.addEventListener('dragleave', handleDragLeave);

         // Gestion des clics sur les éléments
         canvas.addEventListener('click', handleElementClick);
       }

       function handleDragStart(e) {
         e.dataTransfer.setData('text/plain', e.target.dataset.component);
         e.dataTransfer.effectAllowed = 'copy';
       }

       function handleDragOver(e) {
         e.preventDefault();
         e.dataTransfer.dropEffect = 'copy';
         e.currentTarget.classList.add('drag-over');
       }

       function handleDragLeave(e) {
         e.currentTarget.classList.remove('drag-over');
       }

       function handleDrop(e) {
         e.preventDefault();
         e.currentTarget.classList.remove('drag-over');

         const componentType = e.dataTransfer.getData('text/plain');
         const template = componentTemplates[componentType];

         if (template) {
           addElementToCanvas(template, componentType);
         }
       }

       function addElementToCanvas(template, type) {
         const canvas = document.getElementById('pageBuilder');
         const elementId = 'element_' + Date.now();

         // Créer l'élément wrapper
         const wrapper = document.createElement('div');
         wrapper.className = 'builder-element';
         wrapper.dataset.elementId = elementId;
         wrapper.dataset.elementType = type;

         // Ajouter les contrôles
         const controls = document.createElement('div');
         controls.className = 'element-controls';
         controls.innerHTML = `
           <div class="btn-group btn-group-sm">
             <button class="btn btn-outline-primary" onclick="editElement('${elementId}')" title="Modifier">
               <i class="fas fa-edit"></i>
             </button>
             <button class="btn btn-outline-secondary" onclick="duplicateElement('${elementId}')" title="Dupliquer">
               <i class="fas fa-copy"></i>
             </button>
             <button class="btn btn-outline-danger" onclick="deleteElement('${elementId}')" title="Supprimer">
               <i class="fas fa-trash"></i>
             </button>
           </div>
         `;

         // Ajouter le contenu
         const content = document.createElement('div');
         content.innerHTML = template.content;

         wrapper.appendChild(controls);
         wrapper.appendChild(content);

         // Remplacer le placeholder s'il existe
         const placeholder = canvas.querySelector('.text-center.text-muted');
         if (placeholder) {
           canvas.removeChild(placeholder);
         }

         canvas.appendChild(wrapper);

         // Ajouter aux données
         pageBuilderData.elements.push({
           id: elementId,
           type: type,
           content: template.content,
           name: type.charAt(0).toUpperCase() + type.slice(1)
         });

         // Sélectionner l'élément
         selectElement(elementId);
       }

       function handleElementClick(e) {
         const element = e.target.closest('.builder-element');
         if (element) {
           e.stopPropagation();
           selectElement(element.dataset.elementId);
         }
       }

       function selectElement(elementId) {
         // Désélectionner l'élément précédent
         if (currentSelectedElement) {
           document.querySelector(`[data-element-id="${currentSelectedElement}"]`)?.classList.remove('selected');
         }

         // Sélectionner le nouvel élément
         const element = document.querySelector(`[data-element-id="${elementId}"]`);
         if (element) {
           element.classList.add('selected');
           currentSelectedElement = elementId;
           updatePropertiesPanel(elementId);
         }
       }

       function updatePropertiesPanel(elementId) {
         const panel = document.getElementById('propertiesPanel');
         const elementData = pageBuilderData.elements.find(el => el.id === elementId);

         if (!elementData) return;

         panel.innerHTML = `
           <div class="mb-3">
             <h6><i class="fas fa-tag me-1"></i>${elementData.name}</h6>
             <small class="text-muted">ID: ${elementId}</small>
           </div>

           <div class="mb-3">
             <label class="form-label">Contenu</label>
             <button class="btn btn-outline-primary btn-sm w-100" onclick="editElementContent('${elementId}')">
               <i class="fas fa-edit me-1"></i>Modifier le contenu
             </button>
           </div>

           <div class="mb-3">
             <label class="form-label">Espacement</label>
             <div class="row g-2">
               <div class="col-6">
                 <label class="form-label small">Haut</label>
                 <input type="range" class="form-range" min="0" max="100" value="20" onchange="updateElementSpacing('${elementId}', 'top', this.value)">
               </div>
               <div class="col-6">
                 <label class="form-label small">Bas</label>
                 <input type="range" class="form-range" min="0" max="100" value="20" onchange="updateElementSpacing('${elementId}', 'bottom', this.value)">
               </div>
             </div>
           </div>

           <div class="mb-3">
             <label class="form-label">Couleur de fond</label>
             <input type="color" class="form-control form-control-color" value="#ffffff" onchange="updateElementBackground('${elementId}', this.value)">
           </div>

           <div class="mb-3">
             <div class="form-check">
               <input class="form-check-input" type="checkbox" id="centerContent_${elementId}" onchange="toggleElementCenter('${elementId}', this.checked)">
               <label class="form-check-label" for="centerContent_${elementId}">
                 Centrer le contenu
               </label>
             </div>
           </div>

           <hr>

           <div class="d-grid gap-2">
             <button class="btn btn-outline-secondary btn-sm" onclick="moveElementUp('${elementId}')">
               <i class="fas fa-arrow-up me-1"></i>Monter
             </button>
             <button class="btn btn-outline-secondary btn-sm" onclick="moveElementDown('${elementId}')">
               <i class="fas fa-arrow-down me-1"></i>Descendre
             </button>
             <button class="btn btn-outline-primary btn-sm" onclick="duplicateElement('${elementId}')">
               <i class="fas fa-copy me-1"></i>Dupliquer
             </button>
             <button class="btn btn-outline-danger btn-sm" onclick="deleteElement('${elementId}')">
               <i class="fas fa-trash me-1"></i>Supprimer
             </button>
           </div>
         `;
       }

       function editElement(elementId) {
         selectElement(elementId);
       }

       function duplicateElement(elementId) {
         const elementData = pageBuilderData.elements.find(el => el.id === elementId);
         if (elementData) {
           const template = {
             html: elementData.html,
             css: elementData.css,
             name: elementData.name
           };
           addElementToCanvas(template, elementData.type);
         }
       }

       function deleteElement(elementId) {
         if (confirm('Êtes-vous sûr de vouloir supprimer cet élément ?')) {
           const element = document.querySelector(`[data-element-id="${elementId}"]`);
           if (element) {
             element.remove();
           }

           // Supprimer des données
           pageBuilderData.elements = pageBuilderData.elements.filter(el => el.id !== elementId);

           // Réinitialiser le panneau de propriétés
           if (currentSelectedElement === elementId) {
             currentSelectedElement = null;
             document.getElementById('propertiesPanel').innerHTML = `
               <div class="text-center text-muted py-4">
                 <i class="fas fa-hand-pointer fa-2x mb-2 opacity-50"></i>
                 <p class="small">Sélectionnez un élément pour modifier ses propriétés</p>
               </div>
             `;
           }
         }
       }

       function setViewMode(mode) {
         const canvas = document.getElementById('pageBuilder');
         const buttons = document.querySelectorAll('.btn-group .btn');

         // Mettre à jour les boutons
         buttons.forEach(btn => btn.classList.remove('active'));
         event.target.classList.add('active');

         // Mettre à jour la classe du canvas
         canvas.className = canvas.className.replace(/view-mode-\w+/g, '');
         canvas.classList.add(`view-mode-${mode}`);
       }

       // Fonctions de génération IA
       async function generateAIContent(type) {
         const productType = document.getElementById('aiProductType').value;
         const targetAudience = document.getElementById('aiTargetAudience').value;
         const tone = document.getElementById('aiTone').value;

         if (!productType.trim()) {
           alert('Veuillez spécifier le type de produit/service');
           return;
         }

         const button = event.target;
         const originalText = button.innerHTML;
         button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Génération...';
         button.disabled = true;

         try {
           // Simulation de génération IA (à remplacer par vraie API)
           await new Promise(resolve => setTimeout(resolve, 2000));

           let template;
           switch(type) {
             case 'hero':
               template = componentTemplates.hero;
               break;
             case 'features':
               template = componentTemplates.features;
               break;
             case 'testimonials':
               template = componentTemplates.testimonials;
               break;
             case 'full':
               // Générer une page complète
               addElementToCanvas(componentTemplates.hero, 'hero');
               setTimeout(() => addElementToCanvas(componentTemplates.features, 'features'), 100);
               setTimeout(() => addElementToCanvas(componentTemplates.testimonials, 'testimonials'), 200);
               setTimeout(() => addElementToCanvas(componentTemplates.form, 'form'), 300);
               break;
           }

           if (template && type !== 'full') {
             addElementToCanvas(template, type);
           }

           showNotification('Contenu généré avec succès !', 'success');

         } catch (error) {
           console.error('Erreur génération IA:', error);
           showNotification('Erreur lors de la génération', 'error');
         } finally {
           button.innerHTML = originalText;
           button.disabled = false;
         }
       }

       function previewLandingPage() {
         const canvas = document.getElementById('pageBuilder');
         const elements = canvas.querySelectorAll('.builder-element');

         let previewHTML = `
           <!DOCTYPE html>
           <html lang="${pageBuilderData.settings.language}">
           <head>
             <meta charset="UTF-8">
             <meta name="viewport" content="width=device-width, initial-scale=1.0">
             <title>${pageBuilderData.settings.title || 'Ma Landing Page'}</title>
             <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
             <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
             <style>
               body { font-family: '${pageBuilderData.settings.fontFamily}', sans-serif; }
               :root { --primary-color: ${pageBuilderData.settings.primaryColor}; }
               .btn-primary { background-color: var(--primary-color); border-color: var(--primary-color); }
         `;

         // Ajouter les CSS des éléments
         pageBuilderData.elements.forEach(element => {
           if (element.css) {
             previewHTML += element.css + '\n';
           }
         });

         previewHTML += `
             </style>
           </head>
           <body>
         `;

         // Ajouter le HTML des éléments
         elements.forEach(element => {
           const content = element.querySelector('div:last-child');
           if (content) {
             previewHTML += content.innerHTML + '\n';
           }
         });

         previewHTML += `
             <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"><\/script>
           </body>
           </html>
         `;

         // Ouvrir dans une nouvelle fenêtre
         const previewWindow = window.open('', '_blank');
         previewWindow.document.write(previewHTML);
         previewWindow.document.close();
       }

       function saveLandingPage() {
         const title = document.getElementById('pageTitle').value || 'Ma Landing Page';
         pageBuilderData.settings.title = title;
         pageBuilderData.settings.language = document.getElementById('pageLanguage').value;
         pageBuilderData.settings.primaryColor = document.getElementById('primaryColor').value;
         pageBuilderData.settings.fontFamily = document.getElementById('fontFamily').value;
         pageBuilderData.settings.enableRTL = document.getElementById('enableRTL').checked;
         pageBuilderData.settings.enableAnalytics = document.getElementById('enableAnalytics').checked;

         // Simulation de sauvegarde
         showNotification('Landing page sauvegardée avec succès !', 'success');

         // Fermer le modal
         const modal = bootstrap.Modal.getInstance(document.getElementById('landingPageBuilderModal'));
         modal.hide();

         // Actualiser la liste des landing pages
         loadLandingPages();
       }

       // Fonction pour ouvrir le constructeur
       function openLandingPageBuilder(pageId = null) {
         // Réinitialiser le constructeur
         document.getElementById('pageBuilder').innerHTML = `
           <div class="text-center text-muted py-5">
             <i class="fas fa-mouse-pointer fa-3x mb-3 opacity-50"></i>
             <h5>Glissez des blocs ici pour commencer</h5>
             <p>Ou utilisez un template pour démarrer rapidement</p>
           </div>
         `;

         pageBuilderData = {
           elements: [],
           settings: {
             title: '',
             language: 'fr',
             primaryColor: '#007bff',
             fontFamily: 'Inter',
             enableRTL: false,
             enableAnalytics: true
           }
         };

         currentSelectedElement = null;

         // Réinitialiser le panneau de propriétés
         document.getElementById('propertiesPanel').innerHTML = `
           <div class="text-center text-muted py-4">
             <i class="fas fa-hand-pointer fa-2x mb-2 opacity-50"></i>
             <p class="small">Sélectionnez un élément pour modifier ses propriétés</p>
           </div>
         `;

         // Ouvrir le modal
         const modal = new bootstrap.Modal(document.getElementById('landingPageBuilderModal'));
         modal.show();

         // Initialiser le constructeur après l'ouverture du modal
         setTimeout(() => {
           initLandingPageBuilder();
         }, 500);
       }

       // Fonctions d'édition et de manipulation des éléments
        function editElementContent(elementId) {
          const elementData = pageBuilderData.elements.find(el => el.id === elementId);
          if (!elementData) return;

          const newContent = prompt('Modifier le contenu:', elementData.html);
          if (newContent && newContent !== elementData.html) {
            elementData.html = newContent;
            const element = document.querySelector(`[data-element-id="${elementId}"] div:last-child`);
            if (element) {
              element.innerHTML = newContent;
            }
          }
        }

        function updateElementSpacing(elementId, direction, value) {
          const element = document.querySelector(`[data-element-id="${elementId}"] div:last-child > div`);
          if (element) {
            if (direction === 'top') {
              element.style.paddingTop = value + 'px';
            } else if (direction === 'bottom') {
              element.style.paddingBottom = value + 'px';
            }
          }
        }

        function updateElementBackground(elementId, color) {
          const element = document.querySelector(`[data-element-id="${elementId}"] div:last-child > div`);
          if (element) {
            element.style.backgroundColor = color;
          }
        }

        function toggleElementCenter(elementId, centered) {
          const element = document.querySelector(`[data-element-id="${elementId}"] div:last-child > div`);
          if (element) {
            if (centered) {
              element.classList.add('text-center');
            } else {
              element.classList.remove('text-center');
            }
          }
        }

        function moveElementUp(elementId) {
          const element = document.querySelector(`[data-element-id="${elementId}"]`);
          const previousElement = element?.previousElementSibling;

          if (element && previousElement && !previousElement.classList.contains('text-center')) {
            element.parentNode.insertBefore(element, previousElement);

            // Mettre à jour l'ordre dans les données
            const elementIndex = pageBuilderData.elements.findIndex(el => el.id === elementId);
            if (elementIndex > 0) {
              const temp = pageBuilderData.elements[elementIndex];
              pageBuilderData.elements[elementIndex] = pageBuilderData.elements[elementIndex - 1];
              pageBuilderData.elements[elementIndex - 1] = temp;
            }
          }
        }

        function moveElementDown(elementId) {
          const element = document.querySelector(`[data-element-id="${elementId}"]`);
          const nextElement = element?.nextElementSibling;

          if (element && nextElement) {
            element.parentNode.insertBefore(nextElement, element);

            // Mettre à jour l'ordre dans les données
            const elementIndex = pageBuilderData.elements.findIndex(el => el.id === elementId);
            if (elementIndex < pageBuilderData.elements.length - 1) {
              const temp = pageBuilderData.elements[elementIndex];
              pageBuilderData.elements[elementIndex] = pageBuilderData.elements[elementIndex + 1];
              pageBuilderData.elements[elementIndex + 1] = temp;
            }
          }
        }

        // Fonction pour charger les templates rapides
        function loadQuickTemplate(templateType) {
          if (!componentTemplates) {
            console.error('componentTemplates non défini');
            return;
          }

          // Réinitialiser le canvas
          const canvas = document.getElementById('pageBuilder');
          if (!canvas) {
            console.error('Canvas non trouvé');
            return;
          }

          canvas.innerHTML = `
            <div class="text-center text-muted py-5">
              <i class="fas fa-mouse-pointer fa-3x mb-3 opacity-50"></i>
              <h5>Glissez des blocs ici pour commencer</h5>
              <p>Ou utilisez un template pour démarrer rapidement</p>
            </div>
          `;

          pageBuilderData.elements = [];

          // Définir les templates pour chaque type
          const templates = {
            'product-launch': ['hero', 'features', 'pricing', 'testimonials', 'form'],
            'saas-service': ['hero', 'features', 'testimonials', 'pricing'],
            'event': ['hero', 'text', 'form'],
            'lead-magnet': ['hero', 'features', 'form'],
            'coming-soon': ['hero', 'text', 'form']
          };

          // Vérifier si le type de template existe
          if (!templates[templateType]) {
            console.error('Type de template non valide:', templateType);
            return;
          }

          // Ajouter les éléments séquentiellement
          templates[templateType].forEach((type, index) => {
            if (componentTemplates[type]) {
              setTimeout(() => {
                try {
                  addElementToCanvas(componentTemplates[type], type);
                } catch (error) {
                  console.error(`Erreur lors de l'ajout du composant ${type}:`, error);
                }
              }, index * 100);
            }
          });

          showNotification(`Template "${templateType}" chargé avec succès !`, 'success');
        }

        // Variables globales pour la pagination
        let currentLandingPagesPage = 1;
        let landingPagesPerPage = 10;
        let allLandingPages = [];

        // Fonction pour charger la liste des landing pages
        async function loadLandingPages() {
          const tableBody = document.getElementById('landingPagesTableBody');

          try {
            // Afficher un indicateur de chargement
            tableBody.innerHTML = `
              <tr>
                <td colspan="9" class="text-center py-4">
                  <div class="spinner-border" role="status">
                    <span class="visually-hidden">Chargement...</span>
                  </div>
                  <p class="mt-2 mb-0">Chargement des landing pages...</p>
                </td>
              </tr>
            `;

            // Charger les landing pages depuis l'API
            const response = await fetch('/api/landing-pages.php?action=all', {
              headers: {
                'Authorization': 'Bearer demo_token'
              }
            });

            if (!response.ok) {
              throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            allLandingPages = result.success ? result.data.pages : [];

            // Mettre à jour les statistiques
            updateLandingPagesStats(allLandingPages);

            // Afficher les landing pages dans le tableau
            displayLandingPagesTable(allLandingPages);

            console.log('✅ Landing pages chargées:', allLandingPages);
          } catch (error) {
            console.error('❌ Erreur lors du chargement des landing pages:', error);

            // Afficher un message d'erreur
            tableBody.innerHTML = `
              <tr>
                <td colspan="9" class="text-center py-4 text-danger">
                  <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                  <p class="mb-0">Erreur lors du chargement des landing pages</p>
                  <small>${error.message}</small>
                </td>
              </tr>
            `;
          }
        }

        // Fonction pour mettre à jour les statistiques
        function updateLandingPagesStats(pages) {
          const total = pages.length;
          const published = pages.filter(p => p.status === 'published').length;
          const draft = pages.filter(p => p.status === 'draft').length;
          const archived = pages.filter(p => p.status === 'archived').length;

          document.getElementById('totalLandingPages').textContent = total;
          document.getElementById('publishedLandingPages').textContent = published;
          document.getElementById('draftLandingPages').textContent = draft;
          document.getElementById('archivedLandingPages').textContent = archived;
        }

        // Fonction pour afficher les landing pages dans le tableau
        function displayLandingPagesTable(pages) {
          const tableBody = document.getElementById('landingPagesTableBody');

          if (pages.length === 0) {
            tableBody.innerHTML = `
              <tr>
                <td colspan="9" class="text-center py-4 text-muted">
                  <i class="fas fa-inbox fa-2x mb-2"></i>
                  <p class="mb-0">Aucune landing page trouvée</p>
                </td>
              </tr>
            `;
            return;
          }

          tableBody.innerHTML = pages.map(page => {
            const statusBadge = getStatusBadge(page.status);
            const templateBadge = getTemplateBadge(page.template_id);
            const createdDate = new Date(page.created_at).toLocaleDateString('fr-FR');

            return `
              <tr>
                <td><strong>#${page.id}</strong></td>
                <td>
                  <div class="fw-bold">${page.title}</div>
                  <small class="text-muted">/${page.slug || 'no-slug'}</small>
                </td>
                <td>
                  <div class="fw-bold">${page.owner_name || 'Inconnu'}</div>
                  <small class="text-muted">${page.owner_email || ''}</small>
                </td>
                <td>${templateBadge}</td>
                <td>${statusBadge}</td>
                <td><span class="badge bg-info">${page.views || 0}</span></td>
                <td><span class="badge bg-success">${page.conversions || 0}</span></td>
                <td>${createdDate}</td>
                <td>
                  <div class="btn-group btn-group-sm" role="group">
                    <button class="btn btn-outline-primary" onclick="previewLandingPage(${page.id})" title="Aperçu">
                      <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-outline-info" onclick="editLandingPage(${page.id})" title="Modifier">
                      <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-outline-secondary" onclick="cloneLandingPage(${page.id})" title="Cloner">
                      <i class="fas fa-copy"></i>
                    </button>
                    ${page.status !== 'archived' ?
                      `<button class="btn btn-outline-warning" onclick="blockLandingPage(${page.id})" title="Bloquer">
                        <i class="fas fa-ban"></i>
                      </button>` :
                      `<button class="btn btn-outline-success" onclick="unblockLandingPage(${page.id})" title="Débloquer">
                        <i class="fas fa-check"></i>
                      </button>`
                    }
                    <button class="btn btn-outline-danger" onclick="deleteLandingPage(${page.id})" title="Supprimer">
                      <i class="fas fa-trash"></i>
                    </button>
                  </div>
                </td>
              </tr>
            `;
          }).join('');
        }

        // Fonctions utilitaires pour les badges
        function getStatusBadge(status) {
          const badges = {
            'published': '<span class="badge bg-success">Publiée</span>',
            'draft': '<span class="badge bg-warning">Brouillon</span>',
            'archived': '<span class="badge bg-danger">Archivée</span>'
          };
          return badges[status] || '<span class="badge bg-secondary">Inconnu</span>';
        }

        function getTemplateBadge(templateId) {
          const templates = {
            'ecommerce': '<span class="badge bg-primary">E-commerce</span>',
            'saas': '<span class="badge bg-success">SaaS</span>',
            'portfolio': '<span class="badge bg-info">Portfolio</span>',
            'service': '<span class="badge bg-warning">Service</span>',
            'app': '<span class="badge bg-secondary">App Mobile</span>'
          };
          return templates[templateId] || '<span class="badge bg-light text-dark">' + templateId + '</span>';
        }

        // Fonction pour prévisualiser un template
        function previewTemplate(templateId) {
          const url = `/api/template-renderer.php?template=${templateId}`;
          window.open(url, '_blank');
        }

        // Fonction pour prévisualiser une landing page
        function previewLandingPage(pageId) {
          const url = `/preview.php?id=${pageId}`;
          window.open(url, '_blank');
        }

        // Fonction pour modifier une landing page
        function editLandingPage(pageId) {
          // Rediriger vers l'éditeur (à implémenter)
          showNotification('Fonctionnalité d\'édition en cours de développement', 'info');
        }

        // Fonction pour cloner une landing page
        async function cloneLandingPage(pageId) {
          if (!confirm('Voulez-vous cloner cette landing page ?')) return;

          try {
            const response = await fetch(`/api/landing-pages.php?action=clone&id=${pageId}`, {
              method: 'PATCH',
              headers: {
                'Authorization': 'Bearer demo_token',
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({})
            });

            const result = await response.json();

            if (result.success) {
              showNotification('Landing page clonée avec succès !', 'success');
              loadLandingPages();
            } else {
              throw new Error(result.error || 'Erreur lors du clonage');
            }
          } catch (error) {
            console.error('Erreur lors du clonage:', error);
            showNotification('Erreur lors du clonage: ' + error.message, 'error');
          }
        }

        // Fonction pour bloquer une landing page
        async function blockLandingPage(pageId) {
          if (!confirm('Voulez-vous bloquer cette landing page ?')) return;

          try {
            const response = await fetch(`/api/landing-pages.php?action=block&id=${pageId}`, {
              method: 'PATCH',
              headers: {
                'Authorization': 'Bearer demo_token'
              }
            });

            const result = await response.json();

            if (result.success) {
              showNotification('Landing page bloquée avec succès !', 'success');
              loadLandingPages();
            } else {
              throw new Error(result.error || 'Erreur lors du blocage');
            }
          } catch (error) {
            console.error('Erreur lors du blocage:', error);
            showNotification('Erreur lors du blocage: ' + error.message, 'error');
          }
        }

        // Fonction pour débloquer une landing page
        async function unblockLandingPage(pageId) {
          if (!confirm('Voulez-vous débloquer cette landing page ?')) return;

          try {
            const response = await fetch(`/api/landing-pages.php?action=unblock&id=${pageId}`, {
              method: 'PATCH',
              headers: {
                'Authorization': 'Bearer demo_token'
              }
            });

            const result = await response.json();

            if (result.success) {
              showNotification('Landing page débloquée avec succès !', 'success');
              loadLandingPages();
            } else {
              throw new Error(result.error || 'Erreur lors du déblocage');
            }
          } catch (error) {
            console.error('Erreur lors du déblocage:', error);
            showNotification('Erreur lors du déblocage: ' + error.message, 'error');
          }
        }

        // Fonction pour supprimer une landing page
        async function deleteLandingPage(pageId) {
          if (!confirm('Êtes-vous sûr de vouloir supprimer définitivement cette landing page ?')) return;

          try {
            const response = await fetch(`/api/landing-pages.php?id=${pageId}`, {
              method: 'DELETE',
              headers: {
                'Authorization': 'Bearer demo_token'
              }
            });

            const result = await response.json();

            if (result.success) {
              showNotification('Landing page supprimée avec succès !', 'success');
              loadLandingPages();
            } else {
              throw new Error(result.error || 'Erreur lors de la suppression');
            }
          } catch (error) {
            console.error('Erreur lors de la suppression:', error);
            showNotification('Erreur lors de la suppression: ' + error.message, 'error');
          }
        }

        // Fonction pour filtrer et rechercher les landing pages
        function filterLandingPages() {
          const searchTerm = document.getElementById('searchLandingPages').value.toLowerCase();
          const statusFilter = document.getElementById('filterLandingPageStatus').value;
          const languageFilter = document.getElementById('filterLandingPageLanguage').value;

          let filteredPages = allLandingPages.filter(page => {
            const matchesSearch = !searchTerm ||
              page.title.toLowerCase().includes(searchTerm) ||
              (page.owner_name && page.owner_name.toLowerCase().includes(searchTerm)) ||
              (page.slug && page.slug.toLowerCase().includes(searchTerm));

            const matchesStatus = !statusFilter || page.status === statusFilter;

            // Pour le filtre de langue, on peut utiliser le template_id ou ajouter un champ langue
            const matchesLanguage = !languageFilter; // À implémenter selon vos besoins

            return matchesSearch && matchesStatus && matchesLanguage;
          });

          displayLandingPagesTable(filteredPages);
          updateLandingPagesStats(filteredPages);
        }

        // Fonction pour actualiser les landing pages
        function refreshLandingPages() {
          loadLandingPages();
        }

        // Ajouter les event listeners pour les filtres
        document.addEventListener('DOMContentLoaded', function() {
          const searchInput = document.getElementById('searchLandingPages');
          const statusFilter = document.getElementById('filterLandingPageStatus');
          const languageFilter = document.getElementById('filterLandingPageLanguage');

          if (searchInput) {
            searchInput.addEventListener('input', filterLandingPages);
          }
          if (statusFilter) {
            statusFilter.addEventListener('change', filterLandingPages);
          }
          if (languageFilter) {
            languageFilter.addEventListener('change', filterLandingPages);
          }
        });

        // Fonction de recherche et filtrage
        function filterLandingPages() {
          const searchTerm = document.getElementById('landingPageSearch').value.toLowerCase();
          const statusFilter = document.getElementById('statusFilter').value;
          const languageFilter = document.getElementById('languageFilter').value;

          // Ici on implémenterait la logique de filtrage
          console.log('Filtrage:', { searchTerm, statusFilter, languageFilter });
        }

        // Initialiser quand le DOM est prêt
        document.addEventListener('DOMContentLoaded', function() {
          // Ajouter l'événement au bouton "Nouvelle Landing Page"
          const newLandingPageBtn = document.querySelector('[onclick="openLandingPageBuilder()"]');
          if (newLandingPageBtn) {
            newLandingPageBtn.addEventListener('click', () => openLandingPageBuilder());
          }

          // Charger les landing pages au démarrage
          if (document.getElementById('landingPagesContainer')) {
            loadLandingPages();
          }
        });

        // Charger les messages de contact
        async function loadContactMessages() {
          const container = document.getElementById("contactMessagesContainer");

          try {
            // Afficher un indicateur de chargement
            container.innerHTML = '<div class="text-center py-4"><div class="spinner-border" role="status"></div><p>Chargement des messages...</p></div>';

            // Charger les messages depuis l'API
            const response = await fetch("/api/contact.php?action=all", {
              headers: {
                Authorization: "Bearer demo_token",
                "Content-Type": "application/json"
              },
            });

            if (!response.ok) {
              throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (data.success && data.data && data.data.messages) {
              const messages = data.data.messages;
              const stats = data.data.stats;

              if (messages.length === 0) {
                container.innerHTML = `
                  <div class="alert alert-info text-center">
                    <i class="fas fa-envelope fa-3x mb-3"></i>
                    <h5>Aucun message trouvé</h5>
                    <p>Les messages de contact apparaîtront ici.</p>
                  </div>
                `;
                return;
              }

              // Statistiques rapides
              let html = `
                <div class="row mb-4">
                  <div class="col-md-3">
                    <div class="card text-center">
                      <div class="card-body">
                        <h5 class="card-title text-primary">Total Messages</h5>
                        <h2>${stats.total}</h2>
                      </div>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="card text-center">
                      <div class="card-body">
                        <h5 class="card-title text-warning">Non lus</h5>
                        <h2>${stats.unread}</h2>
                      </div>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="card text-center">
                      <div class="card-body">
                        <h5 class="card-title text-success">Aujourd'hui</h5>
                        <h2>${stats.today}</h2>
                      </div>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="card text-center">
                      <div class="card-body">
                        <h5 class="card-title text-info">Taux de réponse</h5>
                        <h2>95%</h2>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="d-flex justify-content-between align-items-center mb-3">
                  <h6>Liste des Messages</h6>
                  <div>
                    <button class="btn btn-outline-primary btn-sm me-2" onclick="markAllAsRead()">
                      <i class="fas fa-check-double me-1"></i>Marquer tout comme lu
                    </button>
                  </div>
                </div>

                <div class="table-responsive">
                  <table class="table table-hover">
                    <thead class="table-light">
                      <tr>
                        <th>Statut</th>
                        <th>Nom</th>
                        <th>Email</th>
                        <th>Sujet</th>
                        <th>Date</th>
                        <th>Actions</th>
                      </tr>
                    </thead>
                    <tbody>
              `;

              messages.forEach((message) => {
                const isRead = message.is_read == 1;
                const statusBadge = isRead ? "bg-success" : "bg-warning";
                const statusText = isRead ? "Lu" : "Non lu";

                const date = new Date(message.created_at).toLocaleString('fr-FR', {
                  day: '2-digit',
                  month: '2-digit',
                  year: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit'
                });

                html += `
                  <tr class="${!isRead ? 'table-warning' : ''}">
                    <td><span class="badge ${statusBadge}">${statusText}</span></td>
                    <td>${message.name}</td>
                    <td>${message.email}</td>
                    <td>${message.subject}</td>
                    <td>${date}</td>
                    <td>
                      <div class="btn-group">
                        <button class="btn btn-sm btn-outline-primary" onclick="viewMessage('${message.id}')" title="Voir">
                          <i class="fas fa-eye"></i>
                        </button>
                        ${!isRead ? `<button class="btn btn-sm btn-outline-success" onclick="markAsRead('${message.id}')" title="Marquer comme lu">
                          <i class="fas fa-check"></i>
                        </button>` : ''}
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteMessage('${message.id}')" title="Supprimer">
                          <i class="fas fa-trash"></i>
                        </button>
                      </div>
                    </td>
                  </tr>
                `;
              });

              html += `
                    </tbody>
                  </table>
                </div>
              `;

              container.innerHTML = html;
              console.log("✅ Messages de contact chargés:", messages);
            } else {
              throw new Error("Aucune donnée de message trouvée");
            }
          } catch (error) {
            console.error("❌ Erreur lors du chargement des messages:", error);
            container.innerHTML = `
              <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                Erreur lors du chargement des messages: ${error.message}
              </div>
            `;
          }
        }

        // Fonctions pour gérer les messages
        function viewMessage(id) {
          // TODO: Implémenter la vue détaillée du message
          alert("Fonctionnalité de vue détaillée en cours de développement");
        }

        async function markAsRead(id) {
          try {
            const response = await fetch("/api/contact.php?action=mark-read", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                Authorization: "Bearer demo_token"
              },
              body: JSON.stringify({ id: id })
            });

            if (response.ok) {
              loadContactMessages(); // Recharger la liste
            } else {
              alert("Erreur lors de la mise à jour");
            }
          } catch (error) {
            console.error("Erreur:", error);
            alert("Erreur lors de la mise à jour");
          }
        }

        async function deleteMessage(id) {
          if (confirm("Êtes-vous sûr de vouloir supprimer ce message ?")) {
            try {
              const response = await fetch(`/api/contact.php?id=${id}`, {
                method: "DELETE",
                headers: {
                  Authorization: "Bearer demo_token"
                }
              });

              if (response.ok) {
                loadContactMessages(); // Recharger la liste
              } else {
                alert("Erreur lors de la suppression");
              }
            } catch (error) {
              console.error("Erreur:", error);
              alert("Erreur lors de la suppression");
            }
          }
        }

        function markAllAsRead() {
          // TODO: Implémenter la fonction pour marquer tous les messages comme lus
          alert("Fonctionnalité en cours de développement");
        }

        // Charger les paramètres
        async function loadSettings() {
          const container = document.getElementById("settingsContainer");

          try {
            // Afficher un indicateur de chargement
            container.innerHTML = '<div class="text-center py-4"><div class="spinner-border" role="status"></div><p>Chargement des paramètres...</p></div>';

            // Charger les paramètres depuis l'API
            const response = await fetch("/api/settings.php?action=all", {
              headers: {
                Authorization: "Bearer demo_token",
                "Content-Type": "application/json"
              },
            });

            if (!response.ok) {
              throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (data.success && data.data) {
              const settings = data.data.settings;
              const currencies = data.data.currencies;

              let html = `
                <div class="row">
                  <div class="col-md-8">
                    <div class="card">
                      <div class="card-header">
                        <h6><i class="fas fa-cog me-2"></i>Paramètres Généraux</h6>
                      </div>
                      <div class="card-body">
                        <form id="settingsForm">
                          <div class="row">
                            <div class="col-md-6">
                              <div class="mb-3">
                                <label class="form-label">Nom du site</label>
                                <input type="text" class="form-control" name="site_name" value="${settings.site_name?.value || ''}" />
                              </div>
                            </div>
                            <div class="col-md-6">
                              <div class="mb-3">
                                <label class="form-label">Email administrateur</label>
                                <input type="email" class="form-control" name="admin_email" value="${settings.admin_email?.value || ''}" />
                              </div>
                            </div>
                          </div>

                          <div class="row">
                            <div class="col-md-6">
                              <div class="mb-3">
                                <label class="form-label">Devise par défaut</label>
                                <select class="form-control" name="default_currency">
              `;

              currencies.forEach(currency => {
                const selected = currency.is_default ? 'selected' : '';
                html += `<option value="${currency.code}" ${selected}>${currency.code} - ${currency.name} (${currency.symbol})</option>`;
              });

              html += `
                                </select>
                              </div>
                            </div>
                            <div class="col-md-6">
                              <div class="mb-3">
                                <label class="form-label">Taille max upload (MB)</label>
                                <input type="number" class="form-control" name="max_upload_size" value="${settings.max_upload_size?.value || 10}" />
                              </div>
                            </div>
                          </div>

                          <div class="row">
                            <div class="col-md-6">
                              <div class="form-check mb-3">
                                <input type="checkbox" class="form-check-input" name="enable_registration" ${settings.enable_registration?.value ? 'checked' : ''} />
                                <label class="form-check-label">Autoriser les inscriptions</label>
                              </div>
                            </div>
                            <div class="col-md-6">
                              <div class="form-check mb-3">
                                <input type="checkbox" class="form-check-input" name="maintenance_mode" ${settings.maintenance_mode?.value ? 'checked' : ''} />
                                <label class="form-check-label">Mode maintenance</label>
                              </div>
                            </div>
                          </div>

                          <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>Sauvegarder
                          </button>
                        </form>
                      </div>
                    </div>
                  </div>

                  <div class="col-md-4">
                    <div class="card">
                      <div class="card-header d-flex justify-content-between align-items-center">
                        <h6><i class="fas fa-money-bill me-2"></i>Devises</h6>
                        <button class="btn btn-sm btn-primary" onclick="addNewCurrency()">
                          <i class="fas fa-plus me-1"></i>Ajouter
                        </button>
                      </div>
                      <div class="card-body">
                        <div class="table-responsive">
                          <table class="table table-sm">
                            <thead>
                              <tr>
                                <th>Code</th>
                                <th>Symbole</th>
                                <th>Défaut</th>
                                <th>Actions</th>
                              </tr>
                            </thead>
                            <tbody>
              `;

              currencies.forEach(currency => {
                html += `
                  <tr>
                    <td>${currency.code}</td>
                    <td>${currency.symbol}</td>
                    <td>${currency.is_default ? '<i class="fas fa-check text-success"></i>' : ''}</td>
                    <td>
                      ${!currency.is_default ? `<button class="btn btn-sm btn-outline-danger" onclick="deleteCurrency(${currency.id})" title="Supprimer">
                        <i class="fas fa-trash"></i>
                      </button>` : ''}
                    </td>
                  </tr>
                `;
              });

              html += `
                            </tbody>
                          </table>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              `;

              container.innerHTML = html;

              // Ajouter l'événement de soumission du formulaire
              document.getElementById('settingsForm').addEventListener('submit', saveSettings);

              console.log("✅ Paramètres chargés:", settings);
            } else {
              throw new Error("Aucune donnée de paramètres trouvée");
            }
          } catch (error) {
            console.error("❌ Erreur lors du chargement des paramètres:", error);
            container.innerHTML = `
              <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                Erreur lors du chargement des paramètres: ${error.message}
              </div>
            `;
          }
        }

        // Sauvegarder les paramètres
        async function saveSettings(e) {
          e.preventDefault();

          const form = e.target;
          const formData = new FormData(form);

          const settings = {};
          for (let [key, value] of formData.entries()) {
            if (form.elements[key].type === 'checkbox') {
              settings[key] = form.elements[key].checked;
            } else {
              settings[key] = value;
            }
          }

          try {
            const response = await fetch("/api/settings.php?action=update", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                Authorization: "Bearer demo_token"
              },
              body: JSON.stringify({ settings: settings })
            });

            if (response.ok) {
              alert("Paramètres sauvegardés avec succès !");
            } else {
              alert("Erreur lors de la sauvegarde");
            }
          } catch (error) {
            console.error("Erreur:", error);
            alert("Erreur lors de la sauvegarde");
          }
        }

        // Ajouter une nouvelle devise
        function addNewCurrency() {
          const code = prompt("Code de la devise (ex: EUR):");
          const name = prompt("Nom de la devise (ex: Euro):");
          const symbol = prompt("Symbole de la devise (ex: €):");
          const rate = prompt("Taux de change (par rapport à USD):");

          if (code && name && symbol) {
            fetch("/api/settings.php?action=add-currency", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                Authorization: "Bearer demo_token"
              },
              body: JSON.stringify({
                code: code.toUpperCase(),
                name: name,
                symbol: symbol,
                exchange_rate: parseFloat(rate) || 1.0
              })
            })
            .then(response => response.json())
            .then(data => {
              if (data.success) {
                loadSettings(); // Recharger
              } else {
                alert("Erreur: " + data.error);
              }
            })
            .catch(error => {
              console.error("Erreur:", error);
              alert("Erreur lors de l'ajout");
            });
          }
        }

        // Supprimer une devise
        async function deleteCurrency(id) {
          if (confirm("Êtes-vous sûr de vouloir supprimer cette devise ?")) {
            try {
              const response = await fetch(`/api/settings.php?action=currency&id=${id}`, {
                method: "DELETE",
                headers: {
                  Authorization: "Bearer demo_token"
                }
              });

              if (response.ok) {
                loadSettings(); // Recharger
              } else {
                const data = await response.json();
                alert("Erreur: " + data.error);
              }
            } catch (error) {
              console.error("Erreur:", error);
              alert("Erreur lors de la suppression");
            }
          }
        }
     </script>

      <!-- jQuery -->
      <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
      <!-- Components (doit être chargé après jQuery) -->
      <script src="js/components.js"></script>
      <!-- Roles Management -->
      <script src="js/roles.js"></script>
      <!-- Bootstrap Bundle avec Popper -->
      <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
      <!-- Chart.js -->
      <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

      <!-- Firebase Config & Auth -->
      <script src="js/firebase-config.js"></script>
      <script src="js/firebase-auth.js"></script>

    </body>
  </html>
