<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

try {
    $pdo = new PDO(
        "mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4",
        "root",
        "",
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM products");
    $result = $stmt->fetch();
    echo "Connexion réussie. Nombre de produits : " . $result['count'];
    
} catch (PDOException $e) {
    echo "Erreur de connexion : " . $e->getMessage();
}
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "=== Test de base de données ===\n";

try {
    $pdo = new PDO(
        "mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4",
        'root',
        '',
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    
    echo "✅ Connexion réussie\n";
    
    // Vérifier les tables
    $tables = ['products', 'categories', 'landing_pages', 'stores'];
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
            $result = $stmt->fetch();
            echo "✅ Table $table: " . $result['count'] . " enregistrements\n";
        } catch (Exception $e) {
            echo "❌ Erreur table $table: " . $e->getMessage() . "\n";
        }
    }
    
    // Test spécifique store_id = 3
    echo "\n--- Test pour store_id = 3 ---\n";
    
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM products WHERE store_id = ?");
    $stmt->execute([3]);
    $result = $stmt->fetch();
    echo "Produits store_id 3: " . $result['count'] . "\n";
    
    // Les catégories n'ont pas de store_id, on compte simplement le total
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM product_categories");
    $result = $stmt->fetch();
    echo "Total catégories: " . $result['count'] . "\n";
    
    // Vérifier si le store existe
    $stmt = $pdo->prepare("SELECT * FROM stores WHERE id = ?");
    $stmt->execute([3]);
    $store = $stmt->fetch();
    if ($store) {
        echo "✅ Store 3 existe: " . $store['name'] . "\n";
    } else {
        echo "❌ Store 3 n'existe pas\n";
    }
    
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
}
?>
