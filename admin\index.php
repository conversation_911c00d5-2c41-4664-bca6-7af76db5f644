<?php
/**
 * Interface d'administration principale
 * Gestion des stores, produits, rôles, abonnements et paramètres
 */

require_once '../config/database.php';
require_once '../php/QuotaManager.php';

session_start();

// Vérification de l'authentification admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../auth/login.php');
    exit;
}

$lang = $_GET['lang'] ?? $_SESSION['language'] ?? 'ar';

// Traductions
$translations = [
    'ar' => [
        'title' => 'لوحة الإدارة الرئيسية',
        'dashboard' => 'لوحة التحكم',
        'stores_management' => 'إدارة المتاجر',
        'products_management' => 'إدارة المنتجات',
        'roles_management' => 'إدارة الأدوار',
        'subscriptions_management' => 'إدارة الاشتراكات',
        'settings' => 'الإعدادات',
        'smtp_settings' => 'إعدادات SMTP',
        'statistics' => 'الإحصائيات',
        'total_users' => 'إجمالي المستخدمين',
        'active_stores' => 'المتاجر النشطة',
        'total_products' => 'إجمالي المنتجات',
        'monthly_revenue' => 'الإيرادات الشهرية',
        'recent_activities' => 'الأنشطة الحديثة',
        'quick_actions' => 'إجراءات سريعة',
        'add_user' => 'إضافة مستخدم',
        'add_store' => 'إضافة متجر',
        'view_reports' => 'عرض التقارير',
        'system_health' => 'حالة النظام'
    ],
    'fr' => [
        'title' => 'Panneau d\'Administration Principal',
        'dashboard' => 'Tableau de bord',
        'stores_management' => 'Gestion des Magasins',
        'products_management' => 'Gestion des Produits',
        'roles_management' => 'Gestion des Rôles',
        'subscriptions_management' => 'Gestion des Abonnements',
        'settings' => 'Paramètres',
        'smtp_settings' => 'Paramètres SMTP',
        'statistics' => 'Statistiques',
        'total_users' => 'Total Utilisateurs',
        'active_stores' => 'Magasins Actifs',
        'total_products' => 'Total Produits',
        'monthly_revenue' => 'Revenus Mensuels',
        'recent_activities' => 'Activités Récentes',
        'quick_actions' => 'Actions Rapides',
        'add_user' => 'Ajouter Utilisateur',
        'add_store' => 'Ajouter Magasin',
        'view_reports' => 'Voir Rapports',
        'system_health' => 'État du Système'
    ],
    'en' => [
        'title' => 'Main Administration Panel',
        'dashboard' => 'Dashboard',
        'stores_management' => 'Stores Management',
        'products_management' => 'Products Management',
        'roles_management' => 'Roles Management',
        'subscriptions_management' => 'Subscriptions Management',
        'settings' => 'Settings',
        'smtp_settings' => 'SMTP Settings',
        'statistics' => 'Statistics',
        'total_users' => 'Total Users',
        'active_stores' => 'Active Stores',
        'total_products' => 'Total Products',
        'monthly_revenue' => 'Monthly Revenue',
        'recent_activities' => 'Recent Activities',
        'quick_actions' => 'Quick Actions',
        'add_user' => 'Add User',
        'add_store' => 'Add Store',
        'view_reports' => 'View Reports',
        'system_health' => 'System Health'
    ]
];

$t = $translations[$lang];

// Récupération des statistiques
try {
    $stats = [
        'total_users' => $pdo->query("SELECT COUNT(*) FROM users WHERE status = 'active'")->fetchColumn(),
        'active_stores' => $pdo->query("SELECT COUNT(DISTINCT user_id) FROM products WHERE status = 'published'")->fetchColumn(),
        'total_products' => $pdo->query("SELECT COUNT(*) FROM products")->fetchColumn(),
        'total_landing_pages' => $pdo->query("SELECT COUNT(*) FROM landing_pages")->fetchColumn()
    ];
    
    // Activités récentes
    $recent_activities = $pdo->query("
        SELECT al.*, u.name as user_name 
        FROM activity_logs al 
        JOIN users u ON al.user_id = u.id 
        ORDER BY al.created_at DESC 
        LIMIT 10
    ")->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $stats = ['total_users' => 0, 'active_stores' => 0, 'total_products' => 0, 'total_landing_pages' => 0];
    $recent_activities = [];
}
?>

<!DOCTYPE html>
<html lang="<?= $lang ?>" dir="<?= $lang === 'ar' ? 'rtl' : 'ltr' ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $t['title'] ?> - Landing Pages SaaS</title>
    
    <!-- Bootstrap 5 RTL Support -->
    <?php if ($lang === 'ar'): ?>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <?php else: ?>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <?php endif; ?>
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #64748b;
            --success-color: #059669;
            --warning-color: #d97706;
            --danger-color: #dc2626;
            --dark-color: #1e293b;
        }
        
        body {
            background-color: #f8fafc;
            font-family: <?= $lang === 'ar' ? '\'Cairo\', \'Segoe UI\'' : '\'Inter\', \'Segoe UI\'' ?>, sans-serif;
        }
        
        .sidebar {
            background: linear-gradient(135deg, var(--primary-color), #1e40af);
            min-height: 100vh;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }
        
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            margin: 4px 0;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: rgba(255,255,255,0.1);
            color: white;
            transform: translateX(<?= $lang === 'ar' ? '-5px' : '5px' ?>);
        }
        
        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        
        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        
        .stat-card {
            background: linear-gradient(135deg, #fff, #f8fafc);
            border-left: 4px solid var(--primary-color);
        }
        
        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), #1e40af);
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
            font-weight: 500;
        }
        
        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
        }
        
        .activity-item {
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 8px;
            background: #f8fafc;
            border-left: 3px solid var(--primary-color);
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }
        
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                top: 0;
                left: <?= $lang === 'ar' ? '0' : '-250px' ?>;
                right: <?= $lang === 'ar' ? '-250px' : '0' ?>;
                width: 250px;
                z-index: 1000;
                transition: all 0.3s ease;
            }
            
            .sidebar.show {
                left: <?= $lang === 'ar' ? '0' : '0' ?>;
                right: <?= $lang === 'ar' ? '0' : '0' ?>;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse" id="sidebar">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h4 class="text-white"><?= $t['dashboard'] ?></h4>
                        <small class="text-white-50">Admin Panel</small>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="#dashboard">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                <?= $t['dashboard'] ?>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="stores-management.php">
                                <i class="fas fa-store me-2"></i>
                                <?= $t['stores_management'] ?>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="products-management.php">
                                <i class="fas fa-box me-2"></i>
                                <?= $t['products_management'] ?>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="roles-management.php">
                                <i class="fas fa-users-cog me-2"></i>
                                <?= $t['roles_management'] ?>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="subscriptions-manager.php">
                                <i class="fas fa-credit-card me-2"></i>
                                <?= $t['subscriptions_management'] ?>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="ai-settings.php">
                                <i class="fas fa-robot me-2"></i>
                                Configuration IA
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="settings.php">
                                <i class="fas fa-cog me-2"></i>
                                <?= $t['settings'] ?>
                            </a>
                        </li>
                    </ul>
                    
                    <!-- Language Switcher -->
                    <div class="mt-4 pt-3 border-top border-white-50">
                        <div class="dropdown">
                            <button class="btn btn-outline-light btn-sm dropdown-toggle w-100" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-globe me-1"></i>
                                <?= strtoupper($lang) ?>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="?lang=ar">العربية</a></li>
                                <li><a class="dropdown-item" href="?lang=fr">Français</a></li>
                                <li><a class="dropdown-item" href="?lang=en">English</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </nav>
            
            <!-- Main Content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <!-- Top Navigation -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2"><?= $t['title'] ?></h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button class="btn btn-outline-secondary d-md-none" type="button" data-bs-toggle="collapse" data-bs-target="#sidebar">
                            <i class="fas fa-bars"></i>
                        </button>
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-download"></i> Export
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card h-100">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                            <?= $t['total_users'] ?>
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?= number_format($stats['total_users']) ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <div class="stat-icon" style="background: linear-gradient(135deg, #3b82f6, #1e40af);">
                                            <i class="fas fa-users"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card h-100">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                            <?= $t['active_stores'] ?>
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?= number_format($stats['active_stores']) ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <div class="stat-icon" style="background: linear-gradient(135deg, #10b981, #059669);">
                                            <i class="fas fa-store"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card h-100">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                            <?= $t['total_products'] ?>
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?= number_format($stats['total_products']) ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <div class="stat-icon" style="background: linear-gradient(135deg, #06b6d4, #0891b2);">
                                            <i class="fas fa-box"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card h-100">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                            Landing Pages
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?= number_format($stats['total_landing_pages']) ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <div class="stat-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
                                            <i class="fas fa-file-alt"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Charts and Recent Activities -->
                <div class="row">
                    <!-- Quick Actions -->
                    <div class="col-lg-4 mb-4">
                        <div class="card h-100">
                            <div class="card-header bg-primary text-white">
                                <h6 class="m-0 font-weight-bold">
                                    <i class="fas fa-bolt me-2"></i>
                                    <?= $t['quick_actions'] ?>
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <a href="stores-management.php?action=add" class="btn btn-outline-primary">
                                        <i class="fas fa-plus me-2"></i>
                                        <?= $t['add_store'] ?>
                                    </a>
                                    <a href="roles-management.php?action=add" class="btn btn-outline-success">
                                        <i class="fas fa-user-plus me-2"></i>
                                        <?= $t['add_user'] ?>
                                    </a>
                                    <a href="reports.php" class="btn btn-outline-info">
                                        <i class="fas fa-chart-bar me-2"></i>
                                        <?= $t['view_reports'] ?>
                                    </a>
                                    <a href="settings.php" class="btn btn-outline-warning">
                                        <i class="fas fa-cog me-2"></i>
                                        <?= $t['settings'] ?>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Recent Activities -->
                    <div class="col-lg-8 mb-4">
                        <div class="card h-100">
                            <div class="card-header bg-info text-white">
                                <h6 class="m-0 font-weight-bold">
                                    <i class="fas fa-history me-2"></i>
                                    <?= $t['recent_activities'] ?>
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="activity-timeline">
                                    <?php if (empty($recent_activities)): ?>
                                        <p class="text-muted text-center py-3">
                                            <i class="fas fa-info-circle me-2"></i>
                                            Aucune activité récente
                                        </p>
                                    <?php else: ?>
                                        <?php foreach ($recent_activities as $activity): ?>
                                            <div class="activity-item">
                                                <div class="d-flex justify-content-between align-items-start">
                                                    <div>
                                                        <strong><?= htmlspecialchars($activity['user_name']) ?></strong>
                                                        <span class="text-muted"><?= htmlspecialchars($activity['action']) ?></span>
                                                        <?php if ($activity['resource_type']): ?>
                                                            <small class="badge bg-secondary"><?= htmlspecialchars($activity['resource_type']) ?></small>
                                                        <?php endif; ?>
                                                    </div>
                                                    <small class="text-muted">
                                                        <?= date('d/m/Y H:i', strtotime($activity['created_at'])) ?>
                                                    </small>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- System Health Status -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header bg-success text-white">
                                <h6 class="m-0 font-weight-bold">
                                    <i class="fas fa-heartbeat me-2"></i>
                                    <?= $t['system_health'] ?>
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row text-center">
                                    <div class="col-md-3">
                                        <div class="p-3">
                                            <i class="fas fa-database fa-2x text-success mb-2"></i>
                                            <h6>Base de données</h6>
                                            <span class="badge bg-success">Connectée</span>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="p-3">
                                            <i class="fas fa-server fa-2x text-success mb-2"></i>
                                            <h6>Serveur Web</h6>
                                            <span class="badge bg-success">Opérationnel</span>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="p-3">
                                            <i class="fas fa-envelope fa-2x text-warning mb-2"></i>
                                            <h6>Service Email</h6>
                                            <span class="badge bg-warning">À configurer</span>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="p-3">
                                            <i class="fas fa-robot fa-2x text-info mb-2"></i>
                                            <h6>Service IA</h6>
                                            <span class="badge bg-info">Disponible</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Auto-refresh statistics every 30 seconds
        setInterval(function() {
            // You can implement AJAX refresh here
            console.log('Refreshing statistics...');
        }, 30000);
        
        // Mobile sidebar toggle
        document.addEventListener('DOMContentLoaded', function() {
            const sidebarToggle = document.querySelector('[data-bs-target="#sidebar"]');
            const sidebar = document.getElementById('sidebar');
            
            if (sidebarToggle && sidebar) {
                sidebarToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('show');
                });
            }
        });
    </script>
</body>
</html>