<?php
/**
 * Check table structure
 */

require_once 'api/config/database.php';

echo "<h2>Table Structure Check</h2>\n";
echo "<pre>\n";

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Check users table structure
    echo "=== USERS TABLE STRUCTURE ===\n";
    $columns = $db->query("DESCRIBE users")->fetchAll();
    foreach ($columns as $column) {
        echo "{$column['Field']} - {$column['Type']} - {$column['Null']} - {$column['Key']}\n";
    }
    
    // Check stores table structure
    echo "\n=== STORES TABLE STRUCTURE ===\n";
    $columns = $db->query("DESCRIBE stores")->fetchAll();
    foreach ($columns as $column) {
        echo "{$column['Field']} - {$column['Type']} - {$column['Null']} - {$column['Key']}\n";
    }
    
    // Check products table structure
    echo "\n=== PRODUCTS TABLE STRUCTURE ===\n";
    $columns = $db->query("DESCRIBE products")->fetchAll();
    foreach ($columns as $column) {
        echo "{$column['Field']} - {$column['Type']} - {$column['Null']} - {$column['Key']}\n";
    }
    
    // Check user_roles table structure
    echo "\n=== USER_ROLES TABLE STRUCTURE ===\n";
    $columns = $db->query("DESCRIBE user_roles")->fetchAll();
    foreach ($columns as $column) {
        echo "{$column['Field']} - {$column['Type']} - {$column['Null']} - {$column['Key']}\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

echo "</pre>\n";
?>
