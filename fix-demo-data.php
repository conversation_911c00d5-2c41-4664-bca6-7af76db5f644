<?php
require_once 'php/config/database.php';

try {
    // $pdo est déjà défini dans database.php

    echo "🔍 Diagnostic des données demo...\n\n";

    // Vérifier les stores
    $storeQuery = "SELECT id, merchant_id, store_name FROM stores";
    $stores = $pdo->query($storeQuery)->fetchAll(PDO::FETCH_ASSOC);

    echo "📊 STORES:\n";
    foreach ($stores as $store) {
        echo "  - ID: {$store['id']}, Merchant: {$store['merchant_id']}, Nom: {$store['store_name']}\n";
    }

    // Vérifier les landing pages
    $lpQuery = "SELECT id, merchant_id, title, status FROM landing_pages";
    $landingPages = $pdo->query($lpQuery)->fetchAll(PDO::FETCH_ASSOC);

    echo "\n📄 LANDING PAGES:\n";
    foreach ($landingPages as $lp) {
        echo "  - ID: {$lp['id']}, Merchant: {$lp['merchant_id']}, Titre: {$lp['title']}, Status: {$lp['status']}\n";
    }

    // Vérifier les catégories
    $catQuery = "SELECT id, merchant_id, name FROM categories";
    $categories = $pdo->query($catQuery)->fetchAll(PDO::FETCH_ASSOC);

    echo "\n📂 CATEGORIES:\n";
    if (empty($categories)) {
        echo "  ❌ Aucune catégorie trouvée\n";
    } else {
        foreach ($categories as $cat) {
            echo "  - ID: {$cat['id']}, Merchant: {$cat['merchant_id']}, Nom: {$cat['name']}\n";
        }
    }

    // Créer des catégories pour le merchant demo (ID 1)
    echo "\n🔧 Création des catégories pour le merchant demo...\n";

    $demoCategories = [
        ['Électronique', 'Produits électroniques et high-tech', '#007bff'],
        ['Informatique', 'Ordinateurs, accessoires et logiciels', '#28a745'],
        ['Smartphones', 'Téléphones et accessoires mobiles', '#17a2b8'],
        ['Audio/Vidéo', 'Équipements audio et vidéo', '#ffc107'],
        ['Gaming', 'Consoles et jeux vidéo', '#dc3545'],
        ['Accessoires', 'Accessoires et périphériques', '#6f42c1']
    ];

    foreach ($demoCategories as $index => $catData) {
        $checkQuery = "SELECT id FROM categories WHERE merchant_id = 1 AND name = ?";
        $checkStmt = $pdo->prepare($checkQuery);
        $checkStmt->execute([$catData[0]]);

        if (!$checkStmt->fetch()) {
            $insertQuery = "
                INSERT INTO categories (
                    merchant_id, name, name_fr, name_en, name_ar,
                    description, description_fr, description_en, description_ar,
                    color, icon, category_type, is_active, sort_order, created_at
                ) VALUES (
                    1, ?, ?, ?, ?,
                    ?, ?, ?, ?,
                    ?, 'fas fa-tag', 'main', 1, ?, NOW()
                )
            ";

            $pdo->prepare($insertQuery)->execute([
                $catData[0], // name
                $catData[0], // name_fr
                $catData[0], // name_en
                $catData[0], // name_ar
                $catData[1], // description
                $catData[1], // description_fr
                $catData[1], // description_en
                $catData[1], // description_ar
                $catData[2], // color
                $index + 1   // sort_order
            ]);

            echo "  ✅ Catégorie '{$catData[0]}' créée\n";
        } else {
            echo "  ℹ️ Catégorie '{$catData[0]}' existe déjà\n";
        }
    }

    // Vérifier si les landing pages ont le bon merchant_id
    echo "\n🔧 Vérification des landing pages...\n";
    $updateQuery = "UPDATE landing_pages SET merchant_id = 1 WHERE merchant_id IS NULL OR merchant_id = 0";
    $updated = $pdo->exec($updateQuery);
    if ($updated > 0) {
        echo "  ✅ {$updated} landing pages mises à jour avec merchant_id = 1\n";
    }

    // Résumé final
    echo "\n📊 RÉSUMÉ FINAL:\n";
    $finalCatCount = $pdo->query("SELECT COUNT(*) FROM categories WHERE merchant_id = 1")->fetchColumn();
    $finalLpCount = $pdo->query("SELECT COUNT(*) FROM landing_pages WHERE merchant_id = 1")->fetchColumn();

    echo "  - Catégories pour merchant 1: {$finalCatCount}\n";
    echo "  - Landing pages pour merchant 1: {$finalLpCount}\n";

    echo "\n✅ Données demo corrigées avec succès!\n";
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
}
