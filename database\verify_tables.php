<?php
/**
 * Script simple pour vérifier les tables de la base de données
 */

// Configuration de la base de données
$config = [
    'host' => 'localhost',
    'port' => '3307',
    'username' => 'root',
    'password' => '',
    'database' => 'landingpage_new',
    'charset' => 'utf8mb4'
];

try {
    $dsn = "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}";
    $pdo = new PDO($dsn, $config['username'], $config['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    
    echo "✅ Connexion à la base de données réussie\n\n";
    
    // Lister toutes les tables
    echo "📋 Tables présentes dans la base de données:\n";
    echo "=" . str_repeat("=", 50) . "\n";
    
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    foreach ($tables as $table) {
        echo "  📊 $table\n";
    }
    
    echo "\n📈 Total: " . count($tables) . " tables\n\n";
    
    // Vérifier les tables critiques
    $criticalTables = ['payments', 'payment_logs', 'ai_usage', 'orders', 'users'];
    echo "🔍 Vérification des tables critiques:\n";
    echo "=" . str_repeat("=", 50) . "\n";
    
    foreach ($criticalTables as $table) {
        if (in_array($table, $tables)) {
            // Compter les enregistrements
            try {
                $countStmt = $pdo->prepare("SELECT COUNT(*) as count FROM `$table`");
                $countStmt->execute();
                $count = $countStmt->fetch()['count'];
                echo "  ✅ $table: $count enregistrements\n";
            } catch (Exception $e) {
                echo "  ⚠️ $table: existe mais erreur de comptage\n";
            }
        } else {
            echo "  ❌ $table: MANQUANTE\n";
        }
    }
    
    // Statistiques rapides des paiements si la table existe
    if (in_array('payments', $tables)) {
        echo "\n💳 Statistiques des paiements:\n";
        echo "=" . str_repeat("=", 50) . "\n";
        
        try {
            $stmt = $pdo->query("
                SELECT 
                    method,
                    COUNT(*) as count,
                    SUM(amount) as total
                FROM payments 
                GROUP BY method
            ");
            
            $paymentStats = $stmt->fetchAll();
            
            if (empty($paymentStats)) {
                echo "  ℹ️ Aucun paiement enregistré\n";
            } else {
                foreach ($paymentStats as $stat) {
                    $total = number_format($stat['total'], 2);
                    echo "  💰 {$stat['method']}: {$stat['count']} paiements ({$total} DZD)\n";
                }
            }
        } catch (Exception $e) {
            echo "  ⚠️ Erreur lors de la récupération des statistiques\n";
        }
    }
    
    echo "\n🎉 Vérification terminée avec succès!\n";
    
} catch (PDOException $e) {
    echo "❌ Erreur de connexion: " . $e->getMessage() . "\n";
    exit(1);
} catch (Exception $e) {
    echo "💥 Erreur: " . $e->getMessage() . "\n";
    exit(1);
}
?>