<?php
/**
 * Configuration des Clés API IA - Interface d'administration
 * Gestion des clés API pour GPT-4, Claude-3, Gemini Pro
 */

require_once '../config/database.php';
require_once '../php/Auth.php';

session_start();

// Vérification de l'authentification admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../auth/login.php');
    exit;
}

$lang = $_GET['lang'] ?? $_SESSION['language'] ?? 'ar';

// Traductions
$translations = [
    'ar' => [
        'title' => 'إعدادات مفاتيح الذكاء الاصطناعي',
        'ai_models' => 'نماذج الذكاء الاصطناعي',
        'add_api_key' => 'إضافة مفتاح API جديد',
        'model_name' => 'اسم النموذج',
        'provider' => 'المزود',
        'api_key' => 'مفتاح API',
        'status' => 'الحالة',
        'usage' => 'الاستخدام',
        'cost' => 'التكلفة',
        'actions' => 'الإجراءات',
        'active' => 'نشط',
        'inactive' => 'غير نشط',
        'edit' => 'تعديل',
        'delete' => 'حذف',
        'test' => 'اختبار',
        'save' => 'حفظ',
        'cancel' => 'إلغاء',
        'api_key_added' => 'تم إضافة مفتاح API بنجاح',
        'api_key_updated' => 'تم تحديث مفتاح API بنجاح',
        'api_key_deleted' => 'تم حذف مفتاح API بنجاح',
        'status_updated' => 'تم تحديث الحالة بنجاح',
        'test_successful' => 'اختبار المفتاح نجح',
        'test_failed' => 'فشل اختبار المفتاح',
        'error' => 'حدث خطأ',
        'confirm_delete' => 'هل أنت متأكد من حذف هذا المفتاح؟',
        'back_to_dashboard' => 'العودة إلى لوحة التحكم',
        'add_new_model' => 'إضافة نموذج جديد'
    ],
    'fr' => [
        'title' => 'Configuration des Clés API IA',
        'ai_models' => 'Modèles IA',
        'add_api_key' => 'Ajouter une Nouvelle Clé API',
        'model_name' => 'Nom du Modèle',
        'provider' => 'Fournisseur',
        'api_key' => 'Clé API',
        'status' => 'Statut',
        'usage' => 'Utilisation',
        'cost' => 'Coût',
        'actions' => 'Actions',
        'active' => 'Actif',
        'inactive' => 'Inactif',
        'edit' => 'Modifier',
        'delete' => 'Supprimer',
        'test' => 'Tester',
        'save' => 'Enregistrer',
        'cancel' => 'Annuler',
        'api_key_added' => 'Clé API ajoutée avec succès',
        'api_key_updated' => 'Clé API mise à jour avec succès',
        'api_key_deleted' => 'Clé API supprimée avec succès',
        'status_updated' => 'Statut mis à jour avec succès',
        'test_successful' => 'Test de la clé réussi',
        'test_failed' => 'Échec du test de la clé',
        'error' => 'Une erreur s\'est produite',
        'confirm_delete' => 'Êtes-vous sûr de vouloir supprimer cette clé ?',
        'back_to_dashboard' => 'Retour au Tableau de Bord',
        'add_new_model' => 'Ajouter Nouveau Modèle'
    ],
    'en' => [
        'title' => 'AI API Keys Configuration',
        'ai_models' => 'AI Models',
        'add_api_key' => 'Add New API Key',
        'model_name' => 'Model Name',
        'provider' => 'Provider',
        'api_key' => 'API Key',
        'status' => 'Status',
        'usage' => 'Usage',
        'cost' => 'Cost',
        'actions' => 'Actions',
        'active' => 'Active',
        'inactive' => 'Inactive',
        'edit' => 'Edit',
        'delete' => 'Delete',
        'test' => 'Test',
        'save' => 'Save',
        'cancel' => 'Cancel',
        'api_key_added' => 'API key added successfully',
        'api_key_updated' => 'API key updated successfully',
        'api_key_deleted' => 'API key deleted successfully',
        'status_updated' => 'Status updated successfully',
        'test_successful' => 'Key test successful',
        'test_failed' => 'Key test failed',
        'error' => 'An error occurred',
        'confirm_delete' => 'Are you sure you want to delete this key?',
        'back_to_dashboard' => 'Back to Dashboard',
        'add_new_model' => 'Add New Model'
    ]
];

$t = $translations[$lang];

// Fonction de chiffrement simple pour les clés API
function encryptApiKey($key) {
    return base64_encode(openssl_encrypt($key, 'AES-256-CBC', 'landing_page_secret_key', 0, '1234567890123456'));
}

function decryptApiKey($encryptedKey) {
    return openssl_decrypt(base64_decode($encryptedKey), 'AES-256-CBC', 'landing_page_secret_key', 0, '1234567890123456');
}

// Traitement des actions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'add':
                    $stmt = $pdo->prepare("
                        INSERT INTO ai_keys (provider, model_name, api_key, status, created_at) 
                        VALUES (?, ?, ?, ?, NOW())
                    ");
                    $stmt->execute([
                        $_POST['provider'],
                        $_POST['model_name'],
                        encryptApiKey($_POST['api_key']),
                        $_POST['status'] ?? 'active'
                    ]);
                    $message = $t['api_key_added'];
                    $messageType = 'success';
                    break;
                    
                case 'update':
                    $stmt = $pdo->prepare("
                        UPDATE ai_keys 
                        SET provider = ?, model_name = ?, api_key = ?, status = ?, updated_at = NOW() 
                        WHERE id = ?
                    ");
                    $stmt->execute([
                        $_POST['provider'],
                        $_POST['model_name'],
                        encryptApiKey($_POST['api_key']),
                        $_POST['status'],
                        $_POST['key_id']
                    ]);
                    $message = $t['api_key_updated'];
                    $messageType = 'success';
                    break;
                    
                case 'delete':
                    $stmt = $pdo->prepare("DELETE FROM ai_keys WHERE id = ?");
                    $stmt->execute([$_POST['key_id']]);
                    $message = $t['api_key_deleted'];
                    $messageType = 'success';
                    break;
                    
                case 'toggle_status':
                    $stmt = $pdo->prepare("
                        UPDATE ai_keys 
                        SET status = CASE WHEN status = 'active' THEN 'inactive' ELSE 'active' END,
                            updated_at = NOW()
                        WHERE id = ?
                    ");
                    $stmt->execute([$_POST['key_id']]);
                    $message = $t['status_updated'];
                    $messageType = 'success';
                    break;
                    
                case 'test':
                    // Simulation du test de clé API
                    $testResult = testApiKey($_POST['provider'], $_POST['api_key']);
                    if ($testResult) {
                        $message = $t['test_successful'];
                        $messageType = 'success';
                    } else {
                        $message = $t['test_failed'];
                        $messageType = 'warning';
                    }
                    break;
            }
        }
    } catch (Exception $e) {
        $message = $t['error'] . ': ' . $e->getMessage();
        $messageType = 'danger';
    }
}

// Fonction de test des clés API (simulation)
function testApiKey($provider, $apiKey) {
    // Simulation - dans un vrai projet, vous feriez un appel API réel
    return strlen($apiKey) > 10; // Test simple
}

// Récupération des clés API
try {
    $apiKeys = $pdo->query("
        SELECT ak.*, 
               COALESCE(SUM(au.tokens_used), 0) as total_usage,
               COALESCE(SUM(au.cost), 0) as total_cost
        FROM ai_keys ak
        LEFT JOIN ai_usage au ON ak.id = au.ai_key_id
        GROUP BY ak.id
        ORDER BY ak.created_at DESC
    ")->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $apiKeys = [];
}

// Providers et modèles disponibles
$providers = [
    'OpenAI' => ['GPT-4', 'GPT-4-turbo', 'GPT-3.5-turbo'],
    'Anthropic' => ['Claude-3-opus', 'Claude-3-sonnet', 'Claude-3-haiku'],
    'Google' => ['Gemini-Pro', 'Gemini-Pro-Vision', 'Gemini-Ultra']
];
?>

<!DOCTYPE html>
<html lang="<?= $lang ?>" dir="<?= $lang === 'ar' ? 'rtl' : 'ltr' ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $t['title'] ?> - Landing Pages SaaS</title>
    
    <!-- Bootstrap 5 RTL Support -->
    <?php if ($lang === 'ar'): ?>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <?php else: ?>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <?php endif; ?>
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #64748b;
            --success-color: #059669;
            --warning-color: #d97706;
            --danger-color: #dc2626;
        }
        
        body {
            background-color: #f8fafc;
            font-family: <?= $lang === 'ar' ? '\'Cairo\', \'Segoe UI\'' : '\'Inter\', \'Segoe UI\'' ?>, sans-serif;
        }
        
        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), #1e40af);
            border: none;
            border-radius: 8px;
        }
        
        .table th {
            background-color: #f8fafc;
            border-bottom: 2px solid #e2e8f0;
            font-weight: 600;
        }
        
        .badge {
            font-size: 0.75rem;
            padding: 0.5rem 0.75rem;
        }
        
        .provider-badge {
            font-size: 0.7rem;
            padding: 0.25rem 0.5rem;
        }
        
        .api-key-display {
            font-family: 'Courier New', monospace;
            background-color: #f8fafc;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
        }
        
        .status-toggle {
            cursor: pointer;
        }
        
        .modal-content {
            border-radius: 12px;
        }
        
        .provider-openai { background-color: #10b981; }
        .provider-anthropic { background-color: #8b5cf6; }
        .provider-google { background-color: #f59e0b; }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-0"><?= $t['title'] ?></h1>
                <p class="text-muted mb-0">Gérez les clés API pour GPT-4, Claude-3, Gemini Pro</p>
            </div>
            <div>
                <a href="index.php" class="btn btn-outline-secondary me-2">
                    <i class="fas fa-arrow-left me-1"></i>
                    <?= $t['back_to_dashboard'] ?>
                </a>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addApiKeyModal">
                    <i class="fas fa-plus me-1"></i>
                    <?= $t['add_new_model'] ?>
                </button>
            </div>
        </div>
        
        <!-- Messages -->
        <?php if ($message): ?>
            <div class="alert alert-<?= $messageType ?> alert-dismissible fade show" role="alert">
                <?= htmlspecialchars($message) ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
        
        <!-- API Keys Table -->
        <div class="card">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-robot me-2"></i>
                    <?= $t['ai_models'] ?>
                </h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th><?= $t['model_name'] ?></th>
                                <th><?= $t['provider'] ?></th>
                                <th><?= $t['api_key'] ?></th>
                                <th><?= $t['status'] ?></th>
                                <th><?= $t['usage'] ?></th>
                                <th><?= $t['cost'] ?></th>
                                <th class="text-center"><?= $t['actions'] ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($apiKeys)): ?>
                                <tr>
                                    <td colspan="7" class="text-center py-4 text-muted">
                                        <i class="fas fa-info-circle me-2"></i>
                                        Aucune clé API configurée
                                    </td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($apiKeys as $key): ?>
                                    <tr>
                                        <td>
                                            <strong><?= htmlspecialchars($key['model_name']) ?></strong>
                                        </td>
                                        <td>
                                            <span class="badge provider-badge provider-<?= strtolower($key['provider']) ?>">
                                                <?= htmlspecialchars($key['provider']) ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="api-key-display">
                                                <?= substr(decryptApiKey($key['api_key']), 0, 8) ?>...<?= substr(decryptApiKey($key['api_key']), -4) ?>
                                            </span>
                                        </td>
                                        <td>
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="action" value="toggle_status">
                                                <input type="hidden" name="key_id" value="<?= $key['id'] ?>">
                                                <button type="submit" class="btn btn-sm status-toggle <?= $key['status'] === 'active' ? 'btn-success' : 'btn-secondary' ?>">
                                                    <?= $key['status'] === 'active' ? $t['active'] : $t['inactive'] ?>
                                                </button>
                                            </form>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><?= number_format($key['total_usage']) ?> tokens</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-warning"><?= number_format($key['total_cost'], 2) ?> $</span>
                                        </td>
                                        <td class="text-center">
                                            <button type="button" class="btn btn-sm btn-outline-info me-1" 
                                                    onclick="testApiKey('<?= $key['provider'] ?>', '<?= decryptApiKey($key['api_key']) ?>')">
                                                <i class="fas fa-vial"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-primary me-1" 
                                                    onclick="editApiKey(<?= htmlspecialchars(json_encode(array_merge($key, ['decrypted_key' => decryptApiKey($key['api_key'])]))) ?>)">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="deleteApiKey(<?= $key['id'] ?>, '<?= htmlspecialchars($key['model_name']) ?>')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Add API Key Modal -->
    <div class="modal fade" id="addApiKeyModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <form method="POST">
                    <div class="modal-header">
                        <h5 class="modal-title"><?= $t['add_api_key'] ?></h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <input type="hidden" name="action" value="add">
                        
                        <div class="mb-3">
                            <label for="provider" class="form-label"><?= $t['provider'] ?></label>
                            <select class="form-select" id="provider" name="provider" required onchange="updateModels()">
                                <option value="">Sélectionner un fournisseur</option>
                                <?php foreach ($providers as $provider => $models): ?>
                                    <option value="<?= $provider ?>"><?= $provider ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="model_name" class="form-label"><?= $t['model_name'] ?></label>
                            <select class="form-select" id="model_name" name="model_name" required>
                                <option value="">Sélectionner un modèle</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="api_key" class="form-label"><?= $t['api_key'] ?></label>
                            <input type="password" class="form-control" id="api_key" name="api_key" required 
                                   placeholder="sk-..." style="font-family: 'Courier New', monospace;">
                            <div class="form-text">La clé sera chiffrée automatiquement</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="status" class="form-label"><?= $t['status'] ?></label>
                            <select class="form-select" id="status" name="status">
                                <option value="active"><?= $t['active'] ?></option>
                                <option value="inactive"><?= $t['inactive'] ?></option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?= $t['cancel'] ?></button>
                        <button type="submit" class="btn btn-primary"><?= $t['save'] ?></button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Edit API Key Modal -->
    <div class="modal fade" id="editApiKeyModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <form method="POST" id="editApiKeyForm">
                    <div class="modal-header">
                        <h5 class="modal-title">Modifier la Clé API</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <input type="hidden" name="action" value="update">
                        <input type="hidden" name="key_id" id="edit_key_id">
                        
                        <div class="mb-3">
                            <label for="edit_provider" class="form-label"><?= $t['provider'] ?></label>
                            <select class="form-select" id="edit_provider" name="provider" required onchange="updateEditModels()">
                                <?php foreach ($providers as $provider => $models): ?>
                                    <option value="<?= $provider ?>"><?= $provider ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="edit_model_name" class="form-label"><?= $t['model_name'] ?></label>
                            <select class="form-select" id="edit_model_name" name="model_name" required>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="edit_api_key" class="form-label"><?= $t['api_key'] ?></label>
                            <input type="password" class="form-control" id="edit_api_key" name="api_key" required 
                                   style="font-family: 'Courier New', monospace;">
                        </div>
                        
                        <div class="mb-3">
                            <label for="edit_status" class="form-label"><?= $t['status'] ?></label>
                            <select class="form-select" id="edit_status" name="status">
                                <option value="active"><?= $t['active'] ?></option>
                                <option value="inactive"><?= $t['inactive'] ?></option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?= $t['cancel'] ?></button>
                        <button type="submit" class="btn btn-primary"><?= $t['save'] ?></button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteApiKeyModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <form method="POST" id="deleteApiKeyForm">
                    <div class="modal-header">
                        <h5 class="modal-title">Confirmer la Suppression</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <input type="hidden" name="action" value="delete">
                        <input type="hidden" name="key_id" id="delete_key_id">
                        <p><?= $t['confirm_delete'] ?></p>
                        <p><strong id="delete_model_name"></strong></p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?= $t['cancel'] ?></button>
                        <button type="submit" class="btn btn-danger"><?= $t['delete'] ?></button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Test API Key Modal -->
    <div class="modal fade" id="testApiKeyModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <form method="POST" id="testApiKeyForm">
                    <div class="modal-header">
                        <h5 class="modal-title">Tester la Clé API</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <input type="hidden" name="action" value="test">
                        <input type="hidden" name="provider" id="test_provider">
                        <input type="hidden" name="api_key" id="test_api_key">
                        <p>Voulez-vous tester cette clé API ?</p>
                        <div id="test_result" class="mt-3"></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?= $t['cancel'] ?></button>
                        <button type="submit" class="btn btn-info"><?= $t['test'] ?></button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        const providers = <?= json_encode($providers) ?>;
        
        function updateModels() {
            const providerSelect = document.getElementById('provider');
            const modelSelect = document.getElementById('model_name');
            const selectedProvider = providerSelect.value;
            
            modelSelect.innerHTML = '<option value="">Sélectionner un modèle</option>';
            
            if (selectedProvider && providers[selectedProvider]) {
                providers[selectedProvider].forEach(model => {
                    const option = document.createElement('option');
                    option.value = model;
                    option.textContent = model;
                    modelSelect.appendChild(option);
                });
            }
        }
        
        function updateEditModels() {
            const providerSelect = document.getElementById('edit_provider');
            const modelSelect = document.getElementById('edit_model_name');
            const selectedProvider = providerSelect.value;
            
            modelSelect.innerHTML = '';
            
            if (selectedProvider && providers[selectedProvider]) {
                providers[selectedProvider].forEach(model => {
                    const option = document.createElement('option');
                    option.value = model;
                    option.textContent = model;
                    modelSelect.appendChild(option);
                });
            }
        }
        
        function editApiKey(apiKey) {
            document.getElementById('edit_key_id').value = apiKey.id;
            document.getElementById('edit_provider').value = apiKey.provider;
            document.getElementById('edit_api_key').value = apiKey.decrypted_key;
            document.getElementById('edit_status').value = apiKey.status;
            
            updateEditModels();
            
            setTimeout(() => {
                document.getElementById('edit_model_name').value = apiKey.model_name;
            }, 100);
            
            new bootstrap.Modal(document.getElementById('editApiKeyModal')).show();
        }
        
        function deleteApiKey(keyId, modelName) {
            document.getElementById('delete_key_id').value = keyId;
            document.getElementById('delete_model_name').textContent = modelName;
            new bootstrap.Modal(document.getElementById('deleteApiKeyModal')).show();
        }
        
        function testApiKey(provider, apiKey) {
            document.getElementById('test_provider').value = provider;
            document.getElementById('test_api_key').value = apiKey;
            new bootstrap.Modal(document.getElementById('testApiKeyModal')).show();
        }
    </script>
</body>
</html>