<?php
require_once 'api/config/database.php';

try {
    $database = new Database();
    $pdo = $database->getConnection();
    
    echo "Création des tables d'abonnements...\n";
    
    // Lire et exécuter le script SQL
    $sql = file_get_contents('api/sql/create_subscriptions.sql');
    
    // Diviser en requêtes individuelles
    $queries = array_filter(array_map('trim', explode(';', $sql)));
    
    foreach ($queries as $query) {
        if (!empty($query) && !str_starts_with(trim($query), '--')) {
            try {
                $pdo->exec($query);
                echo "✅ Requête exécutée avec succès\n";
            } catch (Exception $e) {
                echo "⚠️ Erreur ou table déjà existante: " . $e->getMessage() . "\n";
            }
        }
    }
    
    // Vérifier les résultats
    echo "\n📊 Plans d'abonnement créés :\n";
    $stmt = $pdo->query("SELECT name, display_name, price, max_products, max_landing_pages, ai_enabled, ai_monthly_tokens FROM subscriptions ORDER BY sort_order");
    $subscriptions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($subscriptions as $sub) {
        echo "- {$sub['display_name']} ({$sub['name']}):\n";
        echo "  Prix: {$sub['price']}€/mois\n";
        echo "  Produits: " . ($sub['max_products'] == -1 ? 'Illimité' : $sub['max_products']) . "\n";
        echo "  Landing Pages: " . ($sub['max_landing_pages'] == -1 ? 'Illimité' : $sub['max_landing_pages']) . "\n";
        echo "  IA: " . ($sub['ai_enabled'] ? 'Oui' : 'Non');
        if ($sub['ai_enabled']) {
            echo " (" . ($sub['ai_monthly_tokens'] == -1 ? 'Illimité' : number_format($sub['ai_monthly_tokens'])) . " tokens/mois)";
        }
        echo "\n\n";
    }
    
    echo "✅ Structure des abonnements créée avec succès !\n";
    
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
}
?>
