<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "=== Debug simple ===\n";

// Test de connexion DB directe
try {
    $pdo = new PDO(
        "mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4",
        'root',
        '',
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    
    echo "✅ Connexion DB OK\n";
    
    // Test store_id = 3
    $stmt = $pdo->prepare("SELECT merchant_id FROM stores WHERE id = ?");
    $stmt->execute([3]);
    $store = $stmt->fetch();
    
    if ($store) {
        $merchantId = $store['merchant_id'];
        echo "✅ Store 3 -> Merchant ID: $merchantId\n";
        
        // Test produits
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM products WHERE store_id = ?");
        $stmt->execute([3]);
        $result = $stmt->fetch();
        echo "✅ Produits store_id 3: " . $result['count'] . "\n";
        
        // Test catégories
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM categories WHERE merchant_id = ?");
        $stmt->execute([$merchantId]);
        $result = $stmt->fetch();
        echo "✅ Catégories merchant_id $merchantId: " . $result['count'] . "\n";
        
        // Test landing pages
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM landing_pages WHERE merchant_id = ?");
        $stmt->execute([$merchantId]);
        $result = $stmt->fetch();
        echo "✅ Landing pages merchant_id $merchantId: " . $result['count'] . "\n";
        
    } else {
        echo "❌ Store 3 non trouvé\n";
    }
    
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
}

// Test simple de l'API products
echo "\n--- Test API Products ---\n";

// Simuler les variables
$_GET['store_id'] = '3';
$_SERVER['REQUEST_METHOD'] = 'GET';

// Capturer les erreurs
ob_start();
$errorOutput = '';

try {
    // Rediriger les erreurs vers une variable
    set_error_handler(function($severity, $message, $file, $line) use (&$errorOutput) {
        $errorOutput .= "Erreur: $message dans $file ligne $line\n";
    });
    
    // Tester juste la partie connexion de l'API
    $host = 'localhost';
    $port = 3307;
    $dbname = 'landingpage_new';
    $username = 'root';
    $password = '';
    
    $pdo = new PDO(
        "mysql:host={$host};port={$port};dbname={$dbname};charset=utf8mb4",
        $username,
        $password,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]
    );
    
    echo "✅ Connexion API OK\n";
    
    // Test requête simple
    $storeId = 3;
    $query = "SELECT * FROM products WHERE store_id = ? ORDER BY created_at DESC LIMIT 5";
    $stmt = $pdo->prepare($query);
    $stmt->execute([$storeId]);
    $products = $stmt->fetchAll();
    
    echo "✅ Requête produits OK: " . count($products) . " produits\n";
    
    // Créer une réponse JSON
    $response = [
        'success' => true,
        'data' => $products
    ];
    
    $json = json_encode($response);
    if ($json) {
        echo "✅ JSON OK: " . strlen($json) . " caractères\n";
    } else {
        echo "❌ Erreur JSON: " . json_last_error_msg() . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Exception API: " . $e->getMessage() . "\n";
}

$output = ob_get_contents();
ob_end_clean();

if ($errorOutput) {
    echo "❌ Erreurs PHP:\n$errorOutput\n";
}

if ($output) {
    echo "Sortie capturée: $output\n";
}

echo "\n=== Fin debug ===\n";
?>
