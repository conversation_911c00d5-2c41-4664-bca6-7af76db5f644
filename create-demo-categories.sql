-- Créer des catégories pour le merchant demo (ID 1)
USE landingpage_new;
-- Vérifier d'abord les données existantes
SELECT 'STORES EXISTANTS:' as info;
SELECT id,
    merchant_id,
    store_name
FROM stores;
SELECT 'CATEGORIES EXISTANTES:' as info;
SELECT id,
    merchant_id,
    name
FROM categories;
SELECT 'LANDING PAGES EXISTANTES:' as info;
SELECT id,
    merchant_id,
    title
FROM landing_pages;
-- Insérer des catégories pour le merchant 1
INSERT INTO categories (
        merchant_id,
        user_id,
        name,
        name_fr,
        name_en,
        name_ar,
        description,
        description_fr,
        description_en,
        description_ar,
        color,
        icon,
        category_type,
        is_active,
        sort_order,
        created_at,
        slug
    )
VALUES (
        1,
        1,
        'Électronique',
        'Électronique',
        'Electronics',
        'إلكترونيات',
        'Produits électroniques et high-tech',
        'Produits électroniques et high-tech',
        'Electronic and high-tech products',
        'منتجات إلكترونية وتقنية عالية',
        '#007bff',
        'fas fa-laptop',
        'category',
        1,
        1,
        NOW(),
        'electronique'
    ),
    (
        1,
        1,
        'Informatique',
        'Informatique',
        'Computing',
        'حاسوب',
        'Ordinateurs, accessoires et logiciels',
        'Ordinateurs, accessoires et logiciels',
        'Computers, accessories and software',
        'أجهزة كمبيوتر وملحقات وبرامج',
        '#28a745',
        'fas fa-desktop',
        'category',
        1,
        2,
        NOW(),
        'informatique'
    ),
    (
        1,
        1,
        'Smartphones',
        'Smartphones',
        'Smartphones',
        'هواتف ذكية',
        'Téléphones et accessoires mobiles',
        'Téléphones et accessoires mobiles',
        'Phones and mobile accessories',
        'هواتف وملحقات محمولة',
        '#17a2b8',
        'fas fa-mobile-alt',
        'category',
        1,
        3,
        NOW(),
        'smartphones'
    ),
    (
        1,
        1,
        'Audio/Vidéo',
        'Audio/Vidéo',
        'Audio/Video',
        'صوت/فيديو',
        'Équipements audio et vidéo',
        'Équipements audio et vidéo',
        'Audio and video equipment',
        'معدات صوتية ومرئية',
        '#ffc107',
        'fas fa-headphones',
        'category',
        1,
        4,
        NOW(),
        'audio-video'
    ),
    (
        1,
        1,
        'Gaming',
        'Gaming',
        'Gaming',
        'ألعاب',
        'Consoles et jeux vidéo',
        'Consoles et jeux vidéo',
        'Consoles and video games',
        'أجهزة ألعاب وألعاب فيديو',
        '#dc3545',
        'fas fa-gamepad',
        'category',
        1,
        5,
        NOW(),
        'gaming'
    ),
    (
        1,
        1,
        'Accessoires',
        'Accessoires',
        'Accessories',
        'ملحقات',
        'Accessoires et périphériques',
        'Accessoires et périphériques',
        'Accessories and peripherals',
        'ملحقات وأجهزة طرفية',
        '#6f42c1',
        'fas fa-plug',
        'category',
        1,
        6,
        NOW(),
        'accessoires'
    ) ON DUPLICATE KEY
UPDATE name =
VALUES(name);
-- Mettre à jour les landing pages pour qu'elles appartiennent au merchant 1
UPDATE landing_pages
SET merchant_id = 3
WHERE merchant_id IS NULL
    OR merchant_id = 0;
-- Vérifier les résultats
SELECT 'RÉSULTATS FINAUX:' as info;
SELECT COUNT(*) as total_categories
FROM categories
WHERE merchant_id = 3;
SELECT COUNT(*) as total_landing_pages
FROM landing_pages
WHERE merchant_id = 3;
SELECT 'CATÉGORIES CRÉÉES:' as info;
SELECT id,
    name,
    slug,
    color
FROM categories
WHERE merchant_id = 3
ORDER BY sort_order;
