<?php
/**
 * Test Dashboard API
 */

echo "<h2>Dashboard API Test</h2>\n";
echo "<pre>\n";

// Test 1: Dashboard Stats
echo "=== Test 1: Dashboard Stats ===\n";
$_SERVER['REQUEST_METHOD'] = 'GET';
$_SERVER['REQUEST_URI'] = '/firebase-dashboard-api.php/stats';
$_SERVER['HTTP_AUTHORIZATION'] = 'Bearer demo_token';

ob_start();
try {
    include 'firebase-dashboard-api.php';
} catch (Exception $e) {
    echo "Exception: " . $e->getMessage() . "\n";
}
$output = ob_get_clean();
echo $output . "\n";

// Test 2: System Overview
echo "=== Test 2: System Overview ===\n";
$_SERVER['REQUEST_URI'] = '/firebase-dashboard-api.php/overview';

ob_start();
try {
    include 'firebase-dashboard-api.php';
} catch (Exception $e) {
    echo "Exception: " . $e->getMessage() . "\n";
}
$output = ob_get_clean();
echo $output . "\n";

// Test 3: Analytics
echo "=== Test 3: Analytics ===\n";
$_SERVER['REQUEST_URI'] = '/firebase-dashboard-api.php/analytics';

ob_start();
try {
    include 'firebase-dashboard-api.php';
} catch (Exception $e) {
    echo "Exception: " . $e->getMessage() . "\n";
}
$output = ob_get_clean();
echo $output . "\n";

// Test 4: Recent Activity
echo "=== Test 4: Recent Activity ===\n";
$_SERVER['REQUEST_URI'] = '/firebase-dashboard-api.php/recent-activity';

ob_start();
try {
    include 'firebase-dashboard-api.php';
} catch (Exception $e) {
    echo "Exception: " . $e->getMessage() . "\n";
}
$output = ob_get_clean();
echo $output . "\n";

echo "</pre>\n";
?>
