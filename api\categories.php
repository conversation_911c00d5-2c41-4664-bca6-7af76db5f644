<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once 'config/database.php';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $method = $_SERVER['REQUEST_METHOD'];
    $action = isset($_GET['action']) ? $_GET['action'] : '';
    $merchant_id = isset($_GET['merchant_id']) ? (int)$_GET['merchant_id'] : null;
    $lang = isset($_GET['lang']) ? $_GET['lang'] : 'en';
    
    // Simple auth check
    $headers = function_exists('getallheaders') ? getallheaders() : [];
    if (empty($headers) && isset($_SERVER['HTTP_AUTHORIZATION'])) {
        $headers['Authorization'] = $_SERVER['HTTP_AUTHORIZATION'];
    }
    
    $token = isset($headers['Authorization']) ? str_replace('Bearer ', '', $headers['Authorization']) : null;
    
    switch ($method) {
        case 'GET':
            if ($action === 'all') {
                getAllCategories($db, $merchant_id, $lang);
            } elseif ($action === 'limits') {
                getCategoryLimits($db, $merchant_id);
            } elseif ($action === 'count') {
                getCategoryCount($db, $merchant_id);
            } else {
                http_response_code(404);
                echo json_encode(['error' => 'Action non supportée']);
            }
            break;
            
        case 'POST':
            $input = json_decode(file_get_contents('php://input'), true);
            if ($action === 'create') {
                createCategory($db, $merchant_id, $input, $lang);
            } else {
                http_response_code(404);
                echo json_encode(['error' => 'Action non supportée']);
            }
            break;
            
        case 'PUT':
            $input = json_decode(file_get_contents('php://input'), true);
            $id = isset($_GET['id']) ? (int)$_GET['id'] : null;
            if ($action === 'update' && $id) {
                updateCategory($db, $id, $merchant_id, $input, $lang);
            } else {
                http_response_code(404);
                echo json_encode(['error' => 'Action non supportée']);
            }
            break;
            
        case 'DELETE':
            $id = isset($_GET['id']) ? (int)$_GET['id'] : null;
            if ($action === 'delete' && $id) {
                deleteCategory($db, $id, $merchant_id);
            } else {
                http_response_code(404);
                echo json_encode(['error' => 'Action non supportée']);
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Méthode non supportée']);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Erreur serveur: ' . $e->getMessage()]);
}

/**
 * Get all categories with translations
 */
function getAllCategories($db, $merchant_id, $lang)
{
    try {
        $nameField = "name_$lang";
        $descField = "description_$lang";
        
        $query = "
            SELECT 
                c.id,
                c.name,
                COALESCE(c.$nameField, c.name) as translated_name,
                COALESCE(c.$descField, c.description) as translated_description,
                c.slug,
                c.color,
                c.icon,
                c.parent_id,
                c.category_type,
                c.is_active,
                c.is_default,
                c.sort_order,
                c.created_at,
                COUNT(pc.product_id) as product_count
            FROM categories c
            LEFT JOIN product_categories pc ON c.id = pc.category_id
            WHERE (c.merchant_id = ? OR c.is_default = 1)
            GROUP BY c.id
            ORDER BY c.sort_order, c.translated_name
        ";
        
        $stmt = $db->prepare($query);
        $stmt->execute([$merchant_id]);
        $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Organiser en arbre (catégories et sous-catégories)
        $tree = [];
        $subcategories = [];
        
        foreach ($categories as $category) {
            if ($category['parent_id'] === null) {
                $category['subcategories'] = [];
                $tree[$category['id']] = $category;
            } else {
                $subcategories[] = $category;
            }
        }
        
        // Ajouter les sous-catégories à leurs parents
        foreach ($subcategories as $subcategory) {
            if (isset($tree[$subcategory['parent_id']])) {
                $tree[$subcategory['parent_id']]['subcategories'][] = $subcategory;
            }
        }
        
        echo json_encode([
            'success' => true,
            'data' => [
                'categories' => array_values($tree),
                'total' => count($categories),
                'language' => $lang
            ]
        ]);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la récupération des catégories: ' . $e->getMessage()]);
    }
}

/**
 * Get category limits for merchant based on subscription
 */
function getCategoryLimits($db, $merchant_id)
{
    try {
        // Get merchant's subscription limits
        $query = "
            SELECT 
                s.max_categories,
                s.max_subcategories,
                us.current_categories,
                us.current_subcategories
            FROM user_subscriptions us
            JOIN subscriptions s ON us.subscription_id = s.id
            WHERE us.user_id = ? AND us.status = 'active'
            ORDER BY us.created_at DESC
            LIMIT 1
        ";
        
        $stmt = $db->prepare($query);
        $stmt->execute([$merchant_id]);
        $limits = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$limits) {
            // Default limits for free plan
            $limits = [
                'max_categories' => 5,
                'max_subcategories' => 10,
                'current_categories' => 0,
                'current_subcategories' => 0
            ];
        }
        
        // Get current usage
        $countQuery = "
            SELECT 
                COUNT(CASE WHEN parent_id IS NULL THEN 1 END) as current_categories,
                COUNT(CASE WHEN parent_id IS NOT NULL THEN 1 END) as current_subcategories
            FROM categories 
            WHERE merchant_id = ? AND is_active = 1
        ";
        
        $stmt = $db->prepare($countQuery);
        $stmt->execute([$merchant_id]);
        $usage = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $limits['current_categories'] = (int)$usage['current_categories'];
        $limits['current_subcategories'] = (int)$usage['current_subcategories'];
        $limits['can_create_category'] = $limits['current_categories'] < $limits['max_categories'];
        $limits['can_create_subcategory'] = $limits['current_subcategories'] < $limits['max_subcategories'];
        
        echo json_encode([
            'success' => true,
            'data' => $limits
        ]);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la récupération des limites: ' . $e->getMessage()]);
    }
}

/**
 * Create new category with limit check
 */
function createCategory($db, $merchant_id, $data, $lang)
{
    try {
        // Check limits first
        $limitsQuery = "
            SELECT 
                s.max_categories,
                s.max_subcategories
            FROM user_subscriptions us
            JOIN subscriptions s ON us.subscription_id = s.id
            WHERE us.user_id = ? AND us.status = 'active'
            ORDER BY us.created_at DESC
            LIMIT 1
        ";
        
        $stmt = $db->prepare($limitsQuery);
        $stmt->execute([$merchant_id]);
        $limits = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$limits) {
            $limits = ['max_categories' => 5, 'max_subcategories' => 10];
        }
        
        // Check current usage
        $countQuery = "
            SELECT 
                COUNT(CASE WHEN parent_id IS NULL THEN 1 END) as current_categories,
                COUNT(CASE WHEN parent_id IS NOT NULL THEN 1 END) as current_subcategories
            FROM categories 
            WHERE merchant_id = ? AND is_active = 1
        ";
        
        $stmt = $db->prepare($countQuery);
        $stmt->execute([$merchant_id]);
        $usage = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $isSubcategory = !empty($data['parent_id']);
        
        if ($isSubcategory && $usage['current_subcategories'] >= $limits['max_subcategories']) {
            http_response_code(400);
            echo json_encode([
                'error' => 'Limite de sous-catégories atteinte',
                'limit' => $limits['max_subcategories'],
                'current' => $usage['current_subcategories']
            ]);
            return;
        }
        
        if (!$isSubcategory && $usage['current_categories'] >= $limits['max_categories']) {
            http_response_code(400);
            echo json_encode([
                'error' => 'Limite de catégories atteinte',
                'limit' => $limits['max_categories'],
                'current' => $usage['current_categories']
            ]);
            return;
        }
        
        // Create category
        $insertQuery = "
            INSERT INTO categories (
                merchant_id, parent_id, name, name_ar, name_en, name_fr,
                slug, description, description_ar, description_en, description_fr,
                color, icon, category_type, is_active, sort_order
            ) VALUES (
                ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1, 
                (SELECT COALESCE(MAX(sort_order), 0) + 1 FROM categories c2 WHERE c2.merchant_id = ?)
            )
        ";
        
        $slug = strtolower(str_replace(' ', '-', $data['name']));
        $categoryType = $isSubcategory ? 'subcategory' : 'category';
        
        $stmt = $db->prepare($insertQuery);
        $stmt->execute([
            $merchant_id,
            $data['parent_id'] ?? null,
            $data['name'],
            $data['name_ar'] ?? '',
            $data['name_en'] ?? $data['name'],
            $data['name_fr'] ?? '',
            $slug,
            $data['description'] ?? '',
            $data['description_ar'] ?? '',
            $data['description_en'] ?? $data['description'] ?? '',
            $data['description_fr'] ?? '',
            $data['color'] ?? '#007bff',
            $data['icon'] ?? 'fas fa-tag',
            $categoryType,
            $merchant_id
        ]);
        
        $categoryId = $db->lastInsertId();
        
        echo json_encode([
            'success' => true,
            'data' => [
                'id' => $categoryId,
                'message' => 'Catégorie créée avec succès'
            ]
        ]);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la création: ' . $e->getMessage()]);
    }
}

// Autres fonctions (updateCategory, deleteCategory, getCategoryCount) à implémenter...

?>
