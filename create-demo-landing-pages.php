<?php
require_once 'php/config/database.php';

try {
    // $pdo est déjà défini dans database.php
    
    echo "🔍 Création des landing pages de démonstration...\n\n";
    
    // Vérifier les stores existants
    $storeQuery = "SELECT id, merchant_id, store_name FROM stores WHERE merchant_id = 1";
    $stores = $pdo->query($storeQuery)->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($stores)) {
        echo "❌ Aucun store trouvé pour le merchant 1\n";
        exit;
    }
    
    $store = $stores[0];
    echo "✅ Store trouvé: {$store['store_name']} (ID: {$store['id']}, Merchant: {$store['merchant_id']})\n";
    
    // Créer des landing pages de démonstration
    $landingPages = [
        [
            'title' => 'TechStore Algeria - Électronique',
            'title_ar' => 'متجر التكنولوجيا الجزائر - إلكترونيات',
            'title_fr' => 'TechStore Algeria - Électronique',
            'title_en' => 'TechStore Algeria - Electronics',
            'slug' => 'electronics',
            'description' => 'Découvrez notre sélection de produits électroniques de qualité',
            'status' => 'published'
        ],
        [
            'title' => 'Smartphones et Accessoires',
            'title_ar' => 'الهواتف الذكية والملحقات',
            'title_fr' => 'Smartphones et Accessoires',
            'title_en' => 'Smartphones and Accessories',
            'slug' => 'smartphones',
            'description' => 'Les derniers smartphones et leurs accessoires',
            'status' => 'published'
        ],
        [
            'title' => 'Gaming et Consoles',
            'title_ar' => 'الألعاب وأجهزة الألعاب',
            'title_fr' => 'Gaming et Consoles',
            'title_en' => 'Gaming and Consoles',
            'slug' => 'gaming',
            'description' => 'Tout pour les gamers passionnés',
            'status' => 'published'
        ]
    ];
    
    foreach ($landingPages as $page) {
        // Vérifier si la landing page existe déjà
        $checkQuery = "SELECT id FROM landing_pages WHERE merchant_id = ? AND slug = ?";
        $checkStmt = $pdo->prepare($checkQuery);
        $checkStmt->execute([$store['merchant_id'], $page['slug']]);
        
        if ($checkStmt->fetch()) {
            echo "ℹ️ Landing page '{$page['title']}' existe déjà\n";
            continue;
        }
        
        // Créer la landing page
        $insertQuery = "
            INSERT INTO landing_pages (
                merchant_id, title, title_ar, title_fr, title_en, slug, description,
                status, template_id, created_at, updated_at
            ) VALUES (
                ?, ?, ?, ?, ?, ?, ?, ?, 1, NOW(), NOW()
            )
        ";
        
        $insertStmt = $pdo->prepare($insertQuery);
        $result = $insertStmt->execute([
            $store['merchant_id'],
            $page['title'],
            $page['title_ar'],
            $page['title_fr'],
            $page['title_en'],
            $page['slug'],
            $page['description'],
            $page['status']
        ]);
        
        if ($result) {
            $landingPageId = $pdo->lastInsertId();
            echo "✅ Landing page '{$page['title']}' créée (ID: {$landingPageId})\n";
        } else {
            echo "❌ Erreur lors de la création de '{$page['title']}'\n";
        }
    }
    
    // Vérifier les résultats
    echo "\n📊 Résumé:\n";
    $countQuery = "SELECT COUNT(*) as count FROM landing_pages WHERE merchant_id = ?";
    $countStmt = $pdo->prepare($countQuery);
    $countStmt->execute([$store['merchant_id']]);
    $count = $countStmt->fetch()['count'];
    
    echo "  - Landing pages pour le merchant {$store['merchant_id']}: {$count}\n";
    
    // Afficher les URLs de test
    echo "\n🔗 URLs de test:\n";
    echo "  - Store principal: http://localhost:8000/store/TechStore%20Algeria\n";
    echo "  - Electronics: http://localhost:8000/store/TechStore%20Algeria/electronics\n";
    echo "  - Smartphones: http://localhost:8000/store/TechStore%20Algeria/smartphones\n";
    echo "  - Gaming: http://localhost:8000/store/TechStore%20Algeria/gaming\n";
    
    if (!empty($store['subdomain'])) {
        echo "  - Subdomain: http://localhost:8000/{$store['subdomain']}\n";
    }
    
    echo "\n✅ Landing pages de démonstration créées avec succès!\n";
    
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
    echo "📍 Fichier: " . $e->getFile() . " ligne " . $e->getLine() . "\n";
}
?>
