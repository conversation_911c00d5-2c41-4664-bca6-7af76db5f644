<?php
// Test du clonage de produit
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'php/config/database.php';

try {
    echo "🔍 Test de l'API de clonage de produit\n\n";
    
    // Initialiser la base de données
    $database = new Database();
    $db = $database->getConnection();
    echo "✅ Connexion à la base de données réussie\n";
    
    // Vérifier la structure de la table products
    echo "\n📋 Structure de la table products:\n";
    $stmt = $db->query("DESCRIBE products");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    foreach ($columns as $column) {
        echo "  - {$column['Field']} ({$column['Type']})\n";
    }
    
    // Vérifier s'il y a des produits existants
    echo "\n📦 Produits existants:\n";
    $stmt = $db->query("SELECT id, name_ar, name_fr, name_en, sku, store_id FROM products LIMIT 5");
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($products)) {
        echo "  ❌ Aucun produit trouvé\n";
        exit;
    }
    
    foreach ($products as $product) {
        echo "  - ID: {$product['id']}, Store: {$product['store_id']}, SKU: {$product['sku']}, Nom: {$product['name_ar']}\n";
    }
    
    // Tester le clonage avec le premier produit
    $test_product = $products[0];
    $product_id = $test_product['id'];
    $store_id = $test_product['store_id'];
    
    echo "\n🔄 Test de clonage du produit ID: {$product_id}, Store: {$store_id}\n";
    
    // Récupérer le produit original
    $check_stmt = $db->prepare("SELECT * FROM products WHERE id = ? AND store_id = ?");
    $check_stmt->execute([$product_id, $store_id]);
    $original_product = $check_stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$original_product) {
        echo "  ❌ Produit non trouvé\n";
        exit;
    }
    
    echo "  ✅ Produit original trouvé\n";
    
    // Créer une copie
    $new_name_ar = $original_product['name_ar'] . ' (Copie Test)';
    $new_name_fr = ($original_product['name_fr'] ?: $original_product['name_ar']) . ' (Copie Test)';
    $new_name_en = ($original_product['name_en'] ?: $original_product['name_ar']) . ' (Copy Test)';
    $new_sku = $original_product['sku'] . '_TEST_' . time();
    
    echo "  📝 Nouveau SKU: {$new_sku}\n";
    
    // Préparer la requête d'insertion
    $stmt = $db->prepare("
        INSERT INTO products (
            store_id, name_ar, name_fr, name_en, description_ar, description_fr, description_en,
            sku, price, compare_price, cost_price, stock_quantity, track_inventory,
            weight, dimensions, category_id, tags, images, status, seo_title, seo_description,
            created_at, updated_at
        ) VALUES (
            ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'draft', ?, ?, NOW(), NOW()
        )
    ");
    
    $result = $stmt->execute([
        $store_id,
        $new_name_ar,
        $new_name_fr,
        $new_name_en,
        $original_product['description_ar'],
        $original_product['description_fr'],
        $original_product['description_en'],
        $new_sku,
        $original_product['price'],
        $original_product['compare_price'],
        $original_product['cost_price'],
        0, // Stock à 0 pour la copie
        $original_product['track_inventory'],
        $original_product['weight'],
        $original_product['dimensions'],
        $original_product['category_id'],
        $original_product['tags'],
        $original_product['images'],
        $original_product['seo_title'] . ' (Copie Test)',
        $original_product['seo_description']
    ]);
    
    if ($result) {
        $new_product_id = $db->lastInsertId();
        echo "  ✅ Produit cloné avec succès! Nouvel ID: {$new_product_id}\n";
        
        // Nettoyer le test
        $db->prepare("DELETE FROM products WHERE id = ?")->execute([$new_product_id]);
        echo "  🧹 Produit de test supprimé\n";
    } else {
        echo "  ❌ Erreur lors du clonage\n";
        print_r($stmt->errorInfo());
    }
    
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
    echo "📍 Fichier: " . $e->getFile() . " ligne " . $e->getLine() . "\n";
}
?>
