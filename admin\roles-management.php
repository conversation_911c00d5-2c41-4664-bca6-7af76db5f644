<?php
/**
 * Gestion des Rôles - Interface d'administration
 * Permet de c<PERSON>, modifier et gérer les rôles utilisateurs
 */

require_once '../config/database.php';
require_once '../php/Auth.php';

session_start();

// Vérification de l'authentification admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../auth/login.php');
    exit;
}

$lang = $_GET['lang'] ?? $_SESSION['language'] ?? 'ar';

// Traductions
$translations = [
    'ar' => [
        'title' => 'إدارة الأدوار',
        'roles_list' => 'قائمة الأدوار',
        'add_role' => 'إضافة دور جديد',
        'role_name' => 'اسم الدور',
        'permissions' => 'الصلاحيات',
        'users_count' => 'عدد المستخدمين',
        'actions' => 'الإجراءات',
        'edit' => 'تعديل',
        'delete' => 'حذف',
        'save' => 'حفظ',
        'cancel' => 'إلغاء',
        'role_added' => 'تم إضافة الدور بنجاح',
        'role_updated' => 'تم تحديث الدور بنجاح',
        'role_deleted' => 'تم حذف الدور بنجاح',
        'error' => 'حدث خطأ',
        'confirm_delete' => 'هل أنت متأكد من حذف هذا الدور؟',
        'back_to_dashboard' => 'العودة إلى لوحة التحكم'
    ],
    'fr' => [
        'title' => 'Gestion des Rôles',
        'roles_list' => 'Liste des Rôles',
        'add_role' => 'Ajouter un Nouveau Rôle',
        'role_name' => 'Nom du Rôle',
        'permissions' => 'Permissions',
        'users_count' => 'Nombre d\'Utilisateurs',
        'actions' => 'Actions',
        'edit' => 'Modifier',
        'delete' => 'Supprimer',
        'save' => 'Enregistrer',
        'cancel' => 'Annuler',
        'role_added' => 'Rôle ajouté avec succès',
        'role_updated' => 'Rôle mis à jour avec succès',
        'role_deleted' => 'Rôle supprimé avec succès',
        'error' => 'Une erreur s\'est produite',
        'confirm_delete' => 'Êtes-vous sûr de vouloir supprimer ce rôle ?',
        'back_to_dashboard' => 'Retour au Tableau de Bord'
    ],
    'en' => [
        'title' => 'Roles Management',
        'roles_list' => 'Roles List',
        'add_role' => 'Add New Role',
        'role_name' => 'Role Name',
        'permissions' => 'Permissions',
        'users_count' => 'Users Count',
        'actions' => 'Actions',
        'edit' => 'Edit',
        'delete' => 'Delete',
        'save' => 'Save',
        'cancel' => 'Cancel',
        'role_added' => 'Role added successfully',
        'role_updated' => 'Role updated successfully',
        'role_deleted' => 'Role deleted successfully',
        'error' => 'An error occurred',
        'confirm_delete' => 'Are you sure you want to delete this role?',
        'back_to_dashboard' => 'Back to Dashboard'
    ]
];

$t = $translations[$lang];

// Traitement des actions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'add':
                    $stmt = $pdo->prepare("
                        INSERT INTO user_roles (role_name, permissions, created_at) 
                        VALUES (?, ?, NOW())
                    ");
                    $stmt->execute([
                        $_POST['role_name'],
                        json_encode($_POST['permissions'] ?? [])
                    ]);
                    $message = $t['role_added'];
                    $messageType = 'success';
                    break;
                    
                case 'update':
                    $stmt = $pdo->prepare("
                        UPDATE user_roles 
                        SET role_name = ?, permissions = ?, updated_at = NOW() 
                        WHERE id = ?
                    ");
                    $stmt->execute([
                        $_POST['role_name'],
                        json_encode($_POST['permissions'] ?? []),
                        $_POST['role_id']
                    ]);
                    $message = $t['role_updated'];
                    $messageType = 'success';
                    break;
                    
                case 'delete':
                    $stmt = $pdo->prepare("DELETE FROM user_roles WHERE id = ?");
                    $stmt->execute([$_POST['role_id']]);
                    $message = $t['role_deleted'];
                    $messageType = 'success';
                    break;
            }
        }
    } catch (Exception $e) {
        $message = $t['error'] . ': ' . $e->getMessage();
        $messageType = 'danger';
    }
}

// Récupération des rôles
try {
    $roles = $pdo->query("
        SELECT ur.*, 
               COUNT(u.id) as users_count
        FROM user_roles ur
        LEFT JOIN users u ON u.role = ur.role_name
        GROUP BY ur.id
        ORDER BY ur.created_at DESC
    ")->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $roles = [];
}

// Permissions disponibles
$available_permissions = [
    'manage_products' => 'Gérer les produits',
    'manage_orders' => 'Gérer les commandes',
    'manage_customers' => 'Gérer les clients',
    'view_analytics' => 'Voir les analyses',
    'manage_settings' => 'Gérer les paramètres',
    'manage_users' => 'Gérer les utilisateurs',
    'manage_payments' => 'Gérer les paiements',
    'export_data' => 'Exporter les données'
];
?>

<!DOCTYPE html>
<html lang="<?= $lang ?>" dir="<?= $lang === 'ar' ? 'rtl' : 'ltr' ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $t['title'] ?> - Landing Pages SaaS</title>
    
    <!-- Bootstrap 5 RTL Support -->
    <?php if ($lang === 'ar'): ?>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <?php else: ?>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <?php endif; ?>
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #64748b;
            --success-color: #059669;
            --warning-color: #d97706;
            --danger-color: #dc2626;
        }
        
        body {
            background-color: #f8fafc;
            font-family: <?= $lang === 'ar' ? '\'Cairo\', \'Segoe UI\'' : '\'Inter\', \'Segoe UI\'' ?>, sans-serif;
        }
        
        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), #1e40af);
            border: none;
            border-radius: 8px;
        }
        
        .table th {
            background-color: #f8fafc;
            border-bottom: 2px solid #e2e8f0;
            font-weight: 600;
        }
        
        .badge {
            font-size: 0.75rem;
            padding: 0.5rem 0.75rem;
        }
        
        .permission-checkbox {
            margin: 0.25rem 0;
        }
        
        .modal-content {
            border-radius: 12px;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-0"><?= $t['title'] ?></h1>
                <p class="text-muted mb-0">Gérez les rôles et permissions des utilisateurs</p>
            </div>
            <div>
                <a href="index.php" class="btn btn-outline-secondary me-2">
                    <i class="fas fa-arrow-left me-1"></i>
                    <?= $t['back_to_dashboard'] ?>
                </a>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addRoleModal">
                    <i class="fas fa-plus me-1"></i>
                    <?= $t['add_role'] ?>
                </button>
            </div>
        </div>
        
        <!-- Messages -->
        <?php if ($message): ?>
            <div class="alert alert-<?= $messageType ?> alert-dismissible fade show" role="alert">
                <?= htmlspecialchars($message) ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
        
        <!-- Roles Table -->
        <div class="card">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-users-cog me-2"></i>
                    <?= $t['roles_list'] ?>
                </h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th><?= $t['role_name'] ?></th>
                                <th><?= $t['permissions'] ?></th>
                                <th><?= $t['users_count'] ?></th>
                                <th class="text-center"><?= $t['actions'] ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($roles)): ?>
                                <tr>
                                    <td colspan="4" class="text-center py-4 text-muted">
                                        <i class="fas fa-info-circle me-2"></i>
                                        Aucun rôle trouvé
                                    </td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($roles as $role): ?>
                                    <tr>
                                        <td>
                                            <strong><?= htmlspecialchars($role['role_name']) ?></strong>
                                        </td>
                                        <td>
                                            <?php 
                                            $permissions = json_decode($role['permissions'], true) ?? [];
                                            foreach ($permissions as $perm): 
                                            ?>
                                                <span class="badge bg-primary me-1 mb-1"><?= htmlspecialchars($perm) ?></span>
                                            <?php endforeach; ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><?= $role['users_count'] ?> utilisateurs</span>
                                        </td>
                                        <td class="text-center">
                                            <button type="button" class="btn btn-sm btn-outline-primary me-1" 
                                                    onclick="editRole(<?= htmlspecialchars(json_encode($role)) ?>)">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <?php if ($role['role_name'] !== 'admin'): ?>
                                                <button type="button" class="btn btn-sm btn-outline-danger" 
                                                        onclick="deleteRole(<?= $role['id'] ?>, '<?= htmlspecialchars($role['role_name']) ?>')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Add Role Modal -->
    <div class="modal fade" id="addRoleModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <form method="POST">
                    <div class="modal-header">
                        <h5 class="modal-title"><?= $t['add_role'] ?></h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <input type="hidden" name="action" value="add">
                        
                        <div class="mb-3">
                            <label for="role_name" class="form-label"><?= $t['role_name'] ?></label>
                            <input type="text" class="form-control" id="role_name" name="role_name" required>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label"><?= $t['permissions'] ?></label>
                            <div class="row">
                                <?php foreach ($available_permissions as $perm => $label): ?>
                                    <div class="col-md-6">
                                        <div class="form-check permission-checkbox">
                                            <input class="form-check-input" type="checkbox" 
                                                   name="permissions[]" value="<?= $perm ?>" id="perm_<?= $perm ?>">
                                            <label class="form-check-label" for="perm_<?= $perm ?>">
                                                <?= $label ?>
                                            </label>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?= $t['cancel'] ?></button>
                        <button type="submit" class="btn btn-primary"><?= $t['save'] ?></button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Edit Role Modal -->
    <div class="modal fade" id="editRoleModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <form method="POST" id="editRoleForm">
                    <div class="modal-header">
                        <h5 class="modal-title">Modifier le Rôle</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <input type="hidden" name="action" value="update">
                        <input type="hidden" name="role_id" id="edit_role_id">
                        
                        <div class="mb-3">
                            <label for="edit_role_name" class="form-label"><?= $t['role_name'] ?></label>
                            <input type="text" class="form-control" id="edit_role_name" name="role_name" required>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label"><?= $t['permissions'] ?></label>
                            <div class="row" id="edit_permissions">
                                <?php foreach ($available_permissions as $perm => $label): ?>
                                    <div class="col-md-6">
                                        <div class="form-check permission-checkbox">
                                            <input class="form-check-input" type="checkbox" 
                                                   name="permissions[]" value="<?= $perm ?>" id="edit_perm_<?= $perm ?>">
                                            <label class="form-check-label" for="edit_perm_<?= $perm ?>">
                                                <?= $label ?>
                                            </label>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?= $t['cancel'] ?></button>
                        <button type="submit" class="btn btn-primary"><?= $t['save'] ?></button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteRoleModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <form method="POST" id="deleteRoleForm">
                    <div class="modal-header">
                        <h5 class="modal-title">Confirmer la Suppression</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <input type="hidden" name="action" value="delete">
                        <input type="hidden" name="role_id" id="delete_role_id">
                        <p><?= $t['confirm_delete'] ?></p>
                        <p><strong id="delete_role_name"></strong></p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?= $t['cancel'] ?></button>
                        <button type="submit" class="btn btn-danger"><?= $t['delete'] ?></button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function editRole(role) {
            document.getElementById('edit_role_id').value = role.id;
            document.getElementById('edit_role_name').value = role.role_name;
            
            // Reset all checkboxes
            document.querySelectorAll('#edit_permissions input[type="checkbox"]').forEach(cb => {
                cb.checked = false;
            });
            
            // Check permissions
            const permissions = JSON.parse(role.permissions || '[]');
            permissions.forEach(perm => {
                const checkbox = document.getElementById('edit_perm_' + perm);
                if (checkbox) {
                    checkbox.checked = true;
                }
            });
            
            new bootstrap.Modal(document.getElementById('editRoleModal')).show();
        }
        
        function deleteRole(roleId, roleName) {
            document.getElementById('delete_role_id').value = roleId;
            document.getElementById('delete_role_name').textContent = roleName;
            new bootstrap.Modal(document.getElementById('deleteRoleModal')).show();
        }
    </script>
</body>
</html>