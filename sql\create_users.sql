-- Structure de la table `users`
DROP TABLE IF EXISTS `users`;
CREATE TABLE IF NOT EXISTS `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `firebase_uid` varchar(255) UNIQUE NULL,
  `email` varchar(255) UNIQUE NOT NULL,
  `display_name` varchar(255) NULL,
  `photo_url` text NULL,
  `email_verified` tinyint(1) DEFAULT 0,
  `role` enum('admin', 'merchant', 'user') DEFAULT 'merchant',
  `status` enum('active', 'inactive', 'suspended') DEFAULT 'active',
  `subscription_id` int(11) DEFAULT NULL,
  `last_login` timestamp NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  INDEX `idx_firebase_uid` (`firebase_uid`),
  INDEX `idx_email` (`email`),
  INDEX `idx_role` (`role`),
  INDEX `idx_status` (`status`),
  INDEX `idx_subscription_id` (`subscription_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insertion des utilisateurs de test
INSERT INTO `users` (`firebase_uid`, `email`, `display_name`, `role`, `status`, `created_at`) VALUES
('firebase_uid_1', '<EMAIL>', 'Admin Test', 'admin', 'active', NOW()),
('firebase_uid_2', '<EMAIL>', 'Merchant Test', 'merchant', 'active', NOW()),
('firebase_uid_3', '<EMAIL>', 'User Test', 'user', 'active', NOW());