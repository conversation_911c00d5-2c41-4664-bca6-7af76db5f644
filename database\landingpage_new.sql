-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Hôte : 127.0.0.1:3307
-- <PERSON><PERSON><PERSON><PERSON> le : mer. 30 juil. 2025 à 10:58
-- Version du serveur : 11.5.2-MariaDB
-- Version de PHP : 8.3.14

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Base de données : `landingpage_new`
--

DELIMITER $$
--
-- Procédures
--
DROP PROCEDURE IF EXISTS `RecalculateAllQuotas`$$
CREATE DEFINER=`root`@`localhost` PROCEDURE `RecalculateAllQuotas` ()   BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE v_user_id INT;
    DECLARE user_cursor CURSOR FOR SELECT id FROM users WHERE status = 'active';
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    OPEN user_cursor;
    
    read_loop: LOOP
        FETCH user_cursor INTO v_user_id;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        CALL RecalculateUserQuota(v_user_id);
    END LOOP;
    
    CLOSE user_cursor;
END$$

DROP PROCEDURE IF EXISTS `RecalculateUserQuota`$$
CREATE DEFINER=`root`@`localhost` PROCEDURE `RecalculateUserQuota` (IN `p_user_id` INT)   BEGIN
    DECLARE v_products_count INT DEFAULT 0;
    DECLARE v_landing_pages_count INT DEFAULT 0;
    DECLARE v_categories_count INT DEFAULT 0;
    
    
    SELECT COUNT(*) INTO v_products_count
    FROM products 
    WHERE merchant_id = p_user_id;
    
    
    SELECT COUNT(*) INTO v_landing_pages_count
    FROM landing_pages 
    WHERE merchant_id = p_user_id;
    
    
    SELECT COUNT(*) INTO v_categories_count
    FROM categories 
    WHERE user_id = p_user_id;
    
    
    INSERT INTO quota_usage (user_id, products_count, landing_pages_count, categories_count)
    VALUES (p_user_id, v_products_count, v_landing_pages_count, v_categories_count)
    ON DUPLICATE KEY UPDATE 
        products_count = v_products_count,
        landing_pages_count = v_landing_pages_count,
        categories_count = v_categories_count,
        last_calculated = CURRENT_TIMESTAMP;
END$$

DROP PROCEDURE IF EXISTS `update_setting`$$
CREATE DEFINER=`root`@`localhost` PROCEDURE `update_setting` (IN `p_key` VARCHAR(100), IN `p_value` TEXT, IN `p_type` ENUM('string','integer','boolean','json'))   BEGIN
    INSERT INTO `settings` (`setting_key`, `setting_value`, `setting_type`, `updated_at`)
    VALUES (p_key, p_value, p_type, NOW())
    ON DUPLICATE KEY UPDATE 
        `setting_value` = VALUES(`setting_value`),
        `setting_type` = VALUES(`setting_type`),
        `updated_at` = NOW();
END$$

--
-- Fonctions
--
DROP FUNCTION IF EXISTS `get_setting`$$
CREATE DEFINER=`root`@`localhost` FUNCTION `get_setting` (`p_key` VARCHAR(100), `p_default_value` TEXT) RETURNS TEXT CHARSET utf8mb4 COLLATE utf8mb4_unicode_ci DETERMINISTIC READS SQL DATA BEGIN
    DECLARE result TEXT DEFAULT p_default_value;
    
    SELECT `setting_value` INTO result
    FROM `settings`
    WHERE `setting_key` = p_key
    LIMIT 1;
    
    RETURN COALESCE(result, p_default_value);
END$$

DELIMITER ;

-- --------------------------------------------------------

--
-- Structure de la table `activity_logs`
--

DROP TABLE IF EXISTS `activity_logs`;
CREATE TABLE IF NOT EXISTS `activity_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT 'Utilisateur qui a effectu?? l''action (FK vers users)',
  `action` varchar(100) NOT NULL COMMENT 'Type d''action (create_product, delete_category, etc.)',
  `item_type` varchar(50) NOT NULL COMMENT 'Type d''??l??ment concern?? (product, landing_page, category)',
  `item_id` int(11) DEFAULT NULL COMMENT 'ID de l''??l??ment concern??',
  `details` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'D??tails suppl??mentaires (JSON)' CHECK (json_valid(`details`)),
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'Adresse IP de l''utilisateur',
  `user_agent` varchar(500) DEFAULT NULL COMMENT 'User Agent du navigateur',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `action` (`action`),
  KEY `item_type` (`item_type`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Logs d''activit?? des utilisateurs';

-- --------------------------------------------------------

--
-- Structure de la table `ai_generations`
--

DROP TABLE IF EXISTS `ai_generations`;
CREATE TABLE IF NOT EXISTS `ai_generations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `merchant_id` int(11) DEFAULT NULL,
  `type` enum('product_title','product_description','landing_page_title','landing_page_content','email_template','social_media') NOT NULL,
  `target_language` enum('ar','fr','en') NOT NULL,
  `prompt` text NOT NULL,
  `generated_content` text DEFAULT NULL,
  `tokens_used` int(11) DEFAULT 0,
  `ai_provider` enum('openai','anthropic','gemini') DEFAULT 'openai',
  `model_used` varchar(50) DEFAULT NULL,
  `status` enum('pending','completed','failed') DEFAULT 'pending',
  `error_message` text DEFAULT NULL,
  `target_id` int(11) DEFAULT NULL,
  `target_type` varchar(50) DEFAULT NULL,
  `quality_score` decimal(3,2) DEFAULT NULL,
  `user_rating` enum('1','2','3','4','5') DEFAULT NULL,
  `user_feedback` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `completed_at` timestamp NULL DEFAULT NULL,
  `store_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_merchant_id` (`merchant_id`),
  KEY `idx_type` (`type`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_ai_generations_user_status` (`user_id`,`status`,`created_at`),
  KEY `idx_store_id` (`store_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `ai_keys`
--

DROP TABLE IF EXISTS `ai_keys`;
CREATE TABLE IF NOT EXISTS `ai_keys` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `store_id` int(11) NOT NULL,
  `provider` enum('openai','anthropic','google','cohere','huggingface') NOT NULL,
  `key_name` varchar(100) NOT NULL COMMENT 'Display name for the key',
  `encrypted_key` text NOT NULL COMMENT 'Encrypted API key',
  `model_access` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'Available models for this key' CHECK (json_valid(`model_access`)),
  `usage_limits` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'Usage limits and quotas' CHECK (json_valid(`usage_limits`)),
  `is_active` tinyint(1) DEFAULT 1,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `store_provider` (`store_id`,`provider`),
  KEY `provider` (`provider`),
  KEY `is_active` (`is_active`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `ai_keys`
--

INSERT INTO `ai_keys` (`id`, `store_id`, `provider`, `key_name`, `encrypted_key`, `model_access`, `usage_limits`, `is_active`, `last_used_at`, `created_at`, `updated_at`) VALUES
(1, 1, 'openai', 'GPT-4 Key', 'encrypted_key_placeholder_1', NULL, NULL, 1, NULL, '2025-07-28 23:07:53', '2025-07-28 23:07:53'),
(2, 1, 'anthropic', 'Claude Key', 'encrypted_key_placeholder_2', NULL, NULL, 1, NULL, '2025-07-28 23:07:53', '2025-07-28 23:07:53'),
(3, 1, 'google', 'Gemini Key', 'encrypted_key_placeholder_3', NULL, NULL, 0, NULL, '2025-07-28 23:07:53', '2025-07-28 23:07:53');

-- --------------------------------------------------------

--
-- Structure de la table `ai_prompt_templates`
--

DROP TABLE IF EXISTS `ai_prompt_templates`;
CREATE TABLE IF NOT EXISTS `ai_prompt_templates` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `type` enum('product_title','product_description','landing_page_title','landing_page_content','email_template','social_media') NOT NULL,
  `language` enum('ar','fr','en') NOT NULL,
  `prompt_template` text NOT NULL,
  `variables` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`variables`)),
  `is_active` tinyint(1) DEFAULT 1,
  `usage_count` int(11) DEFAULT 0,
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `created_by` (`created_by`),
  KEY `idx_type` (`type`),
  KEY `idx_language` (`language`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=MyISAM AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `ai_prompt_templates`
--

INSERT INTO `ai_prompt_templates` (`id`, `name`, `type`, `language`, `prompt_template`, `variables`, `is_active`, `usage_count`, `created_by`, `created_at`, `updated_at`) VALUES
(1, 'Product Title AR', 'product_title', 'ar', 'اكتب عنوان منتج جذاب باللغة العربية للمنتج التالي:\nاسم المنتج: {{product_name}}\nالفئة: {{category}}\nالوصف: {{description}}\n\nالعنوان يجب أن يكون:\n- جذاب ومقنع\n- يحتوي على كلمات مفتاحية\n- لا يتجاوز 60 حرف\n- مناسب للسوق الجزائري', '{\"product_name\": \"string\", \"category\": \"string\", \"description\": \"string\"}', 1, 0, NULL, '2025-07-28 16:28:45', '2025-07-28 16:28:45'),
(2, 'Product Description AR', 'product_description', 'ar', 'اكتب وصف منتج مفصل وجذاب باللغة العربية للمنتج التالي:\nاسم المنتج: {{product_name}}\nالفئة: {{category}}\nالسعر: {{price}} دج\nالمميزات: {{features}}\n\nالوصف يجب أن يكون:\n- مفصل ومقنع\n- يبرز الفوائد والمميزات\n- يحتوي على كلمات مفتاحية\n- مناسب للسوق الجزائري\n- يشجع على الشراء', '{\"product_name\": \"string\", \"category\": \"string\", \"price\": \"number\", \"features\": \"string\"}', 1, 0, NULL, '2025-07-28 16:28:45', '2025-07-28 16:28:45');

-- --------------------------------------------------------

--
-- Structure de la table `ai_usage`
--

DROP TABLE IF EXISTS `ai_usage`;
CREATE TABLE IF NOT EXISTS `ai_usage` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `provider` varchar(20) NOT NULL,
  `model` varchar(50) NOT NULL,
  `content_type` varchar(50) NOT NULL,
  `tokens_used` int(11) NOT NULL,
  `cost` decimal(8,4) DEFAULT NULL,
  `language` varchar(5) DEFAULT NULL,
  `success` tinyint(1) DEFAULT 1,
  `error_message` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `store_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_provider` (`provider`),
  KEY `idx_content_type` (`content_type`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_store_id` (`store_id`)
) ENGINE=MyISAM AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `ai_usage`
--

INSERT INTO `ai_usage` (`id`, `user_id`, `provider`, `model`, `content_type`, `tokens_used`, `cost`, `language`, `success`, `error_message`, `created_at`, `store_id`) VALUES
(1, 1, 'openai', 'gpt-3.5-turbo', 'product_title', 45, 0.0023, 'ar', 1, NULL, '2025-07-28 15:41:23', NULL),
(2, 1, 'openai', 'gpt-3.5-turbo', 'product_description', 180, 0.0092, 'ar', 1, NULL, '2025-07-28 14:41:23', NULL),
(3, 2, 'anthropic', 'claude-3-sonnet', 'landing_page_content', 320, 0.0164, 'fr', 1, NULL, '2025-07-28 13:41:23', NULL),
(4, 1, 'gemini', 'gemini-pro', 'product_title', 38, 0.0019, 'en', 1, NULL, '2025-07-28 12:41:23', NULL),
(5, 3, 'openai', 'gpt-4', 'product_description', 250, 0.0375, 'ar', 1, NULL, '2025-07-28 11:41:23', NULL),
(6, 2, 'openai', 'gpt-3.5-turbo', 'product_title', 42, 0.0021, 'fr', 0, NULL, '2025-07-28 10:41:23', NULL),
(7, 1, 'openai', 'gpt-3.5-turbo', 'product_title', 45, 0.0023, 'ar', 1, NULL, '2025-07-28 16:04:59', NULL),
(8, 1, 'openai', 'gpt-3.5-turbo', 'product_description', 180, 0.0092, 'ar', 1, NULL, '2025-07-28 15:04:59', NULL),
(9, 2, 'anthropic', 'claude-3-sonnet', 'landing_page_content', 320, 0.0164, 'fr', 1, NULL, '2025-07-28 14:04:59', NULL),
(10, 1, 'gemini', 'gemini-pro', 'product_title', 38, 0.0019, 'en', 1, NULL, '2025-07-28 13:04:59', NULL),
(11, 3, 'openai', 'gpt-4', 'product_description', 250, 0.0375, 'ar', 1, NULL, '2025-07-28 12:04:59', NULL),
(12, 2, 'openai', 'gpt-3.5-turbo', 'product_title', 42, 0.0021, 'fr', 0, NULL, '2025-07-28 11:04:59', NULL);

-- --------------------------------------------------------

--
-- Structure de la table `categories`
--

DROP TABLE IF EXISTS `categories`;
CREATE TABLE IF NOT EXISTS `categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT 'Propri??taire de la cat??gorie (FK vers users)',
  `name` varchar(255) NOT NULL COMMENT 'Nom de la cat??gorie',
  `slug` varchar(255) NOT NULL COMMENT 'Slug URL-friendly',
  `description` text DEFAULT NULL COMMENT 'Description de la cat??gorie',
  `color` varchar(7) DEFAULT '#007bff' COMMENT 'Couleur hexad??cimale pour l''affichage',
  `icon` varchar(50) DEFAULT 'fas fa-folder' COMMENT 'Ic??ne Font Awesome',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'Cat??gorie active (1) ou d??sactiv??e (0)',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT 'Ordre d''affichage',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_slug` (`user_id`,`slug`),
  KEY `user_id` (`user_id`),
  KEY `is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Cat??gories de produits par utilisateur';

--
-- Déclencheurs `categories`
--
DROP TRIGGER IF EXISTS `update_quota_after_category_delete`;
DELIMITER $$
CREATE TRIGGER `update_quota_after_category_delete` AFTER DELETE ON `categories` FOR EACH ROW BEGIN
    UPDATE quota_usage 
    SET categories_count = GREATEST(0, categories_count - 1),
        last_calculated = CURRENT_TIMESTAMP
    WHERE user_id = OLD.user_id;
END
$$
DELIMITER ;
DROP TRIGGER IF EXISTS `update_quota_after_category_insert`;
DELIMITER $$
CREATE TRIGGER `update_quota_after_category_insert` AFTER INSERT ON `categories` FOR EACH ROW BEGIN
    INSERT INTO quota_usage (user_id, products_count, landing_pages_count, categories_count)
    VALUES (NEW.user_id, 0, 0, 1)
    ON DUPLICATE KEY UPDATE 
        categories_count = categories_count + 1,
        last_calculated = CURRENT_TIMESTAMP;
END
$$
DELIMITER ;

-- --------------------------------------------------------

--
-- Structure de la table `email_logs`
--

DROP TABLE IF EXISTS `email_logs`;
CREATE TABLE IF NOT EXISTS `email_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `to_email` varchar(255) NOT NULL,
  `from_email` varchar(255) NOT NULL,
  `subject` varchar(255) NOT NULL,
  `template_id` int(11) DEFAULT NULL,
  `status` enum('pending','sent','failed','bounced') DEFAULT 'pending',
  `error_message` text DEFAULT NULL,
  `sent_at` timestamp NULL DEFAULT NULL,
  `opened_at` timestamp NULL DEFAULT NULL,
  `clicked_at` timestamp NULL DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `order_id` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `template_id` (`template_id`),
  KEY `user_id` (`user_id`),
  KEY `order_id` (`order_id`),
  KEY `idx_to_email` (`to_email`(250)),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `email_templates`
--

DROP TABLE IF EXISTS `email_templates`;
CREATE TABLE IF NOT EXISTS `email_templates` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `type` enum('order_confirmation','order_status_update','merchant_activation','password_reset','welcome','newsletter') NOT NULL,
  `language` enum('ar','fr','en') NOT NULL,
  `subject` varchar(255) NOT NULL,
  `html_content` text NOT NULL,
  `text_content` text DEFAULT NULL,
  `variables` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`variables`)),
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_type` (`type`),
  KEY `idx_language` (`language`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=MyISAM AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `email_templates`
--

INSERT INTO `email_templates` (`id`, `name`, `type`, `language`, `subject`, `html_content`, `text_content`, `variables`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'Order Confirmation AR', 'order_confirmation', 'ar', 'تأكيد الطلب رقم {{order_number}}', '<div dir=\"rtl\"><h2>شكراً لك على طلبك!</h2><p>رقم الطلب: {{order_number}}</p><p>المبلغ الإجمالي: {{total_amount}} {{currency}}</p></div>', NULL, '{\"order_number\": \"string\", \"total_amount\": \"number\", \"currency\": \"string\"}', 1, '2025-07-28 16:28:45', '2025-07-28 16:28:45'),
(2, 'Order Confirmation FR', 'order_confirmation', 'fr', 'Confirmation de commande n°{{order_number}}', '<div><h2>Merci pour votre commande!</h2><p>Numéro de commande: {{order_number}}</p><p>Montant total: {{total_amount}} {{currency}}</p></div>', NULL, '{\"order_number\": \"string\", \"total_amount\": \"number\", \"currency\": \"string\"}', 1, '2025-07-28 16:28:45', '2025-07-28 16:28:45'),
(3, 'Order Confirmation EN', 'order_confirmation', 'en', 'Order Confirmation #{{order_number}}', '<div><h2>Thank you for your order!</h2><p>Order number: {{order_number}}</p><p>Total amount: {{total_amount}} {{currency}}</p></div>', NULL, '{\"order_number\": \"string\", \"total_amount\": \"number\", \"currency\": \"string\"}', 1, '2025-07-28 16:28:45', '2025-07-28 16:28:45');

-- --------------------------------------------------------

--
-- Structure de la table `landing_pages`
--

DROP TABLE IF EXISTS `landing_pages`;
CREATE TABLE IF NOT EXISTS `landing_pages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `merchant_id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `title_ar` varchar(255) DEFAULT NULL,
  `title_fr` varchar(255) DEFAULT NULL,
  `title_en` varchar(255) DEFAULT NULL,
  `slug` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`content`)),
  `template_id` int(11) DEFAULT NULL,
  `status` enum('draft','published','archived') DEFAULT 'draft',
  `seo_title` varchar(255) DEFAULT NULL,
  `seo_description` varchar(500) DEFAULT NULL,
  `seo_keywords` varchar(500) DEFAULT NULL,
  `custom_css` text DEFAULT NULL,
  `custom_js` text DEFAULT NULL,
  `analytics_code` text DEFAULT NULL,
  `conversion_tracking` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`conversion_tracking`)),
  `views_count` int(11) DEFAULT 0,
  `conversions_count` int(11) DEFAULT 0,
  `ai_generated` tinyint(1) DEFAULT 0,
  `ai_generation_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`ai_generation_data`)),
  `published_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `slug` (`slug`) USING HASH,
  KEY `idx_merchant_id` (`merchant_id`),
  KEY `idx_slug` (`slug`(250)),
  KEY `idx_status` (`status`),
  KEY `idx_published_at` (`published_at`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déclencheurs `landing_pages`
--
DROP TRIGGER IF EXISTS `update_quota_after_landing_page_delete`;
DELIMITER $$
CREATE TRIGGER `update_quota_after_landing_page_delete` AFTER DELETE ON `landing_pages` FOR EACH ROW BEGIN
    UPDATE quota_usage 
    SET landing_pages_count = GREATEST(0, landing_pages_count - 1),
        last_calculated = CURRENT_TIMESTAMP
    WHERE user_id = OLD.merchant_id;
END
$$
DELIMITER ;
DROP TRIGGER IF EXISTS `update_quota_after_landing_page_insert`;
DELIMITER $$
CREATE TRIGGER `update_quota_after_landing_page_insert` AFTER INSERT ON `landing_pages` FOR EACH ROW BEGIN
    INSERT INTO quota_usage (user_id, products_count, landing_pages_count, categories_count)
    VALUES (NEW.merchant_id, 0, 1, 0)
    ON DUPLICATE KEY UPDATE 
        landing_pages_count = landing_pages_count + 1,
        last_calculated = CURRENT_TIMESTAMP;
END
$$
DELIMITER ;

-- --------------------------------------------------------

--
-- Structure de la table `merchants`
--

DROP TABLE IF EXISTS `merchants`;
CREATE TABLE IF NOT EXISTS `merchants` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `business_name` varchar(255) NOT NULL,
  `business_name_ar` varchar(255) DEFAULT NULL,
  `business_name_fr` varchar(255) DEFAULT NULL,
  `business_name_en` varchar(255) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `description_ar` text DEFAULT NULL,
  `description_fr` text DEFAULT NULL,
  `description_en` text DEFAULT NULL,
  `logo_url` varchar(500) DEFAULT NULL,
  `website_url` varchar(255) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `city` varchar(100) DEFAULT NULL,
  `country` varchar(100) DEFAULT 'Algeria',
  `phone` varchar(20) DEFAULT NULL,
  `whatsapp` varchar(20) DEFAULT NULL,
  `facebook_url` varchar(255) DEFAULT NULL,
  `instagram_url` varchar(255) DEFAULT NULL,
  `subscription_plan` enum('free','basic','premium','enterprise') DEFAULT 'free',
  `subscription_expires_at` timestamp NULL DEFAULT NULL,
  `ai_tokens_used` int(11) DEFAULT 0,
  `ai_tokens_limit` int(11) DEFAULT 1000,
  `monthly_ai_reset_date` date DEFAULT NULL,
  `is_verified` tinyint(1) DEFAULT 0,
  `verification_documents` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`verification_documents`)),
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_subscription` (`subscription_plan`),
  KEY `idx_verified` (`is_verified`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Doublure de structure pour la vue `merchant_stats`
-- (Voir ci-dessous la vue réelle)
--
DROP VIEW IF EXISTS `merchant_stats`;
CREATE TABLE IF NOT EXISTS `merchant_stats` (
`merchant_id` int(11)
,`business_name` varchar(255)
,`total_orders` bigint(21)
,`completed_orders` bigint(21)
,`total_revenue` decimal(32,2)
,`total_products` bigint(21)
,`total_landing_pages` bigint(21)
,`ai_tokens_used` int(11)
,`ai_tokens_limit` int(11)
);

-- --------------------------------------------------------

--
-- Structure de la table `notifications`
--

DROP TABLE IF EXISTS `notifications`;
CREATE TABLE IF NOT EXISTS `notifications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `type` enum('order_new','order_status','payment_received','ai_generation_done','account_activation','system_alert') NOT NULL,
  `title` varchar(255) NOT NULL,
  `title_ar` varchar(255) DEFAULT NULL,
  `title_fr` varchar(255) DEFAULT NULL,
  `title_en` varchar(255) DEFAULT NULL,
  `message` text NOT NULL,
  `message_ar` text DEFAULT NULL,
  `message_fr` text DEFAULT NULL,
  `message_en` text DEFAULT NULL,
  `data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`data`)),
  `is_read` tinyint(1) DEFAULT 0,
  `is_email_sent` tinyint(1) DEFAULT 0,
  `is_push_sent` tinyint(1) DEFAULT 0,
  `priority` enum('low','medium','high','urgent') DEFAULT 'medium',
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `read_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_type` (`type`),
  KEY `idx_is_read` (`is_read`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_notifications_user_read` (`user_id`,`is_read`,`created_at`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `notification_preferences`
--

DROP TABLE IF EXISTS `notification_preferences`;
CREATE TABLE IF NOT EXISTS `notification_preferences` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `notification_type` enum('order_new','order_status','payment_received','ai_generation_done','account_activation','system_alert') NOT NULL,
  `email_enabled` tinyint(1) DEFAULT 1,
  `push_enabled` tinyint(1) DEFAULT 1,
  `sms_enabled` tinyint(1) DEFAULT 0,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user_type` (`user_id`,`notification_type`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `orders`
--

DROP TABLE IF EXISTS `orders`;
CREATE TABLE IF NOT EXISTS `orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_number` varchar(50) NOT NULL,
  `merchant_id` int(11) NOT NULL,
  `store_id` int(11) DEFAULT NULL,
  `customer_email` varchar(255) NOT NULL,
  `customer_name` varchar(255) NOT NULL,
  `customer_phone` varchar(20) DEFAULT NULL,
  `customer_address` text DEFAULT NULL,
  `customer_city` varchar(100) DEFAULT NULL,
  `customer_postal_code` varchar(20) DEFAULT NULL,
  `customer_country` varchar(100) DEFAULT 'Algeria',
  `subtotal` decimal(10,2) NOT NULL,
  `tax_amount` decimal(10,2) DEFAULT 0.00,
  `shipping_amount` decimal(10,2) DEFAULT 0.00,
  `discount_amount` decimal(10,2) DEFAULT 0.00,
  `total_amount` decimal(10,2) NOT NULL,
  `currency` varchar(3) DEFAULT 'DZD',
  `status` enum('pending','confirmed','processing','shipped','delivered','cancelled','refunded') DEFAULT 'pending',
  `payment_method` enum('ccp','baridimob','bank_transfer','cod','online') NOT NULL,
  `payment_status` enum('pending','paid','failed','refunded') DEFAULT 'pending',
  `payment_details` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`payment_details`)),
  `payment_proof_url` varchar(500) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `admin_notes` text DEFAULT NULL,
  `language` enum('ar','fr','en') DEFAULT 'ar',
  `source` varchar(50) DEFAULT 'landing_page',
  `utm_source` varchar(100) DEFAULT NULL,
  `utm_medium` varchar(100) DEFAULT NULL,
  `utm_campaign` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_number` (`order_number`),
  KEY `idx_order_number` (`order_number`),
  KEY `idx_merchant_id` (`merchant_id`),
  KEY `idx_status` (`status`),
  KEY `idx_payment_method` (`payment_method`),
  KEY `idx_payment_status` (`payment_status`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_orders_merchant_status` (`merchant_id`,`status`,`created_at`),
  KEY `store_id` (`store_id`),
  KEY `idx_store_id` (`store_id`)
) ENGINE=MyISAM AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `orders`
--

INSERT INTO `orders` (`id`, `order_number`, `merchant_id`, `store_id`, `customer_email`, `customer_name`, `customer_phone`, `customer_address`, `customer_city`, `customer_postal_code`, `customer_country`, `subtotal`, `tax_amount`, `shipping_amount`, `discount_amount`, `total_amount`, `currency`, `status`, `payment_method`, `payment_status`, `payment_details`, `payment_proof_url`, `notes`, `admin_notes`, `language`, `source`, `utm_source`, `utm_medium`, `utm_campaign`, `created_at`, `updated_at`) VALUES
(1, 'ORD-2024-001', 0, NULL, '<EMAIL>', 'Ahmed Benali', '+************', NULL, NULL, NULL, 'Algeria', 0.00, 0.00, 0.00, 0.00, 15000.00, 'DZD', 'pending', 'ccp', 'pending', NULL, NULL, NULL, NULL, 'ar', 'landing_page', NULL, NULL, NULL, '2025-07-28 15:04:59', '2025-07-28 17:04:59'),
(2, 'ORD-2024-002', 0, NULL, '<EMAIL>', 'Fatima Zohra', '+************', NULL, NULL, NULL, 'Algeria', 0.00, 0.00, 0.00, 0.00, 8500.00, 'DZD', 'pending', 'baridimob', 'paid', NULL, NULL, NULL, NULL, 'ar', 'landing_page', NULL, NULL, NULL, '2025-07-27 17:04:59', '2025-07-28 17:04:59'),
(3, 'ORD-2024-003', 0, NULL, '<EMAIL>', 'Mohamed Amine', '+************', NULL, NULL, NULL, 'Algeria', 0.00, 0.00, 0.00, 0.00, 25000.00, 'DZD', 'pending', 'bank_transfer', 'pending', NULL, NULL, NULL, NULL, 'ar', 'landing_page', NULL, NULL, NULL, '2025-07-28 14:04:59', '2025-07-28 17:04:59'),
(4, 'ORD-2024-004', 0, NULL, '<EMAIL>', 'Aicha Mansouri', '+************', NULL, NULL, NULL, 'Algeria', 0.00, 0.00, 0.00, 0.00, 12000.00, 'DZD', 'pending', 'cod', 'paid', NULL, NULL, NULL, NULL, 'ar', 'landing_page', NULL, NULL, NULL, '2025-07-26 17:04:59', '2025-07-28 17:04:59'),
(5, 'ORD-2024-005', 0, NULL, '<EMAIL>', 'Karim Benaissa', '+************', NULL, NULL, NULL, 'Algeria', 0.00, 0.00, 0.00, 0.00, 18000.00, 'DZD', 'pending', 'ccp', 'failed', NULL, NULL, NULL, NULL, 'ar', 'landing_page', NULL, NULL, NULL, '2025-07-28 12:04:59', '2025-07-28 17:04:59');

-- --------------------------------------------------------

--
-- Structure de la table `order_items`
--

DROP TABLE IF EXISTS `order_items`;
CREATE TABLE IF NOT EXISTS `order_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `product_name` varchar(255) NOT NULL,
  `product_sku` varchar(100) DEFAULT NULL,
  `quantity` int(11) NOT NULL,
  `unit_price` decimal(10,2) NOT NULL,
  `total_price` decimal(10,2) NOT NULL,
  `product_options` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`product_options`)),
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_product_id` (`product_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Doublure de structure pour la vue `order_summary`
-- (Voir ci-dessous la vue réelle)
--
DROP VIEW IF EXISTS `order_summary`;
CREATE TABLE IF NOT EXISTS `order_summary` (
`id` int(11)
,`order_number` varchar(50)
,`merchant_id` int(11)
,`customer_email` varchar(255)
,`customer_name` varchar(255)
,`customer_phone` varchar(20)
,`customer_address` text
,`customer_city` varchar(100)
,`customer_postal_code` varchar(20)
,`customer_country` varchar(100)
,`subtotal` decimal(10,2)
,`tax_amount` decimal(10,2)
,`shipping_amount` decimal(10,2)
,`discount_amount` decimal(10,2)
,`total_amount` decimal(10,2)
,`currency` varchar(3)
,`status` enum('pending','confirmed','processing','shipped','delivered','cancelled','refunded')
,`payment_method` enum('ccp','baridimob','bank_transfer','cod','online')
,`payment_status` enum('pending','paid','failed','refunded')
,`payment_details` longtext
,`payment_proof_url` varchar(500)
,`notes` text
,`admin_notes` text
,`language` enum('ar','fr','en')
,`source` varchar(50)
,`utm_source` varchar(100)
,`utm_medium` varchar(100)
,`utm_campaign` varchar(100)
,`created_at` timestamp
,`updated_at` timestamp
,`business_name` varchar(255)
,`items_count` bigint(21)
,`products_list` mediumtext
);

-- --------------------------------------------------------

--
-- Structure de la table `page_analytics`
--

DROP TABLE IF EXISTS `page_analytics`;
CREATE TABLE IF NOT EXISTS `page_analytics` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `landing_page_id` int(11) DEFAULT NULL,
  `merchant_id` int(11) NOT NULL,
  `session_id` varchar(255) DEFAULT NULL,
  `user_ip` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `referrer` varchar(500) DEFAULT NULL,
  `utm_source` varchar(100) DEFAULT NULL,
  `utm_medium` varchar(100) DEFAULT NULL,
  `utm_campaign` varchar(100) DEFAULT NULL,
  `event_type` enum('view','click','conversion','bounce') NOT NULL,
  `event_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`event_data`)),
  `country` varchar(100) DEFAULT NULL,
  `city` varchar(100) DEFAULT NULL,
  `device_type` enum('desktop','mobile','tablet') DEFAULT 'desktop',
  `browser` varchar(50) DEFAULT NULL,
  `os` varchar(50) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_landing_page_id` (`landing_page_id`),
  KEY `idx_merchant_id` (`merchant_id`),
  KEY `idx_session_id` (`session_id`(250)),
  KEY `idx_event_type` (`event_type`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `payments`
--

DROP TABLE IF EXISTS `payments`;
CREATE TABLE IF NOT EXISTS `payments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_id` int(11) NOT NULL,
  `store_id` int(11) DEFAULT NULL,
  `method` enum('ccp','baridimob','bank_transfer','cod') NOT NULL,
  `status` enum('pending','confirmed','rejected','refunded') DEFAULT 'pending',
  `amount` decimal(10,2) NOT NULL,
  `currency` varchar(3) DEFAULT 'DZD',
  `transaction_id` varchar(100) DEFAULT NULL,
  `payment_data` text DEFAULT NULL COMMENT 'JSON data for payment details',
  `proof_file` varchar(255) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `processed_by` int(11) DEFAULT NULL,
  `processed_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_status` (`status`),
  KEY `idx_method` (`method`),
  KEY `idx_created_at` (`created_at`),
  KEY `store_id` (`store_id`),
  KEY `idx_store_id` (`store_id`)
) ENGINE=MyISAM AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `payments`
--

INSERT INTO `payments` (`id`, `order_id`, `store_id`, `method`, `status`, `amount`, `currency`, `transaction_id`, `payment_data`, `proof_file`, `notes`, `processed_by`, `processed_at`, `created_at`, `updated_at`) VALUES
(1, 1, NULL, 'ccp', 'pending', 15000.00, 'DZD', 'CCP123456789', '{\"ccp_number\":\"**********\",\"ccp_rip\":\"12\",\"sender_name\":\"Ahmed Benali\"}', NULL, 'Paiement en attente de vérification', NULL, NULL, '2025-07-28 14:41:23', '2025-07-28 16:41:23'),
(2, 2, NULL, 'baridimob', 'confirmed', 8500.00, 'DZD', 'BM987654321', '{\"phone_number\":\"+************\",\"transaction_ref\":\"BM123456\"}', NULL, 'Paiement confirmé', NULL, NULL, '2025-07-27 16:41:23', '2025-07-28 16:41:23'),
(3, 3, NULL, 'bank_transfer', 'pending', 25000.00, 'DZD', 'BT456789123', '{\"bank_name\":\"BEA\",\"account_number\":\"************\",\"transfer_ref\":\"TR123456\"}', NULL, 'Virement bancaire en cours de vérification', NULL, NULL, '2025-07-28 13:41:23', '2025-07-28 16:41:23'),
(4, 4, NULL, 'cod', 'confirmed', 12000.00, 'DZD', NULL, '{\"delivery_address\":\"Alger, Algérie\",\"delivery_fee\":200}', NULL, 'Livraison effectuée', NULL, NULL, '2025-07-26 16:41:23', '2025-07-28 16:41:23'),
(5, 5, NULL, 'ccp', 'rejected', 18000.00, 'DZD', 'CCP987654321', '{\"ccp_number\":\"**********\",\"ccp_rip\":\"34\"}', NULL, 'Reçu illisible, demande de renvoi', NULL, NULL, '2025-07-28 11:41:23', '2025-07-28 16:41:23'),
(6, 1, NULL, 'ccp', 'pending', 15000.00, 'DZD', 'CCP123456789', '{\"ccp_number\":\"**********\",\"ccp_rip\":\"12\",\"sender_name\":\"Ahmed Benali\"}', NULL, 'Paiement en attente de vérification', NULL, NULL, '2025-07-28 15:04:59', '2025-07-28 17:04:59'),
(7, 2, NULL, 'baridimob', 'confirmed', 8500.00, 'DZD', 'BM987654321', '{\"phone_number\":\"+************\",\"transaction_ref\":\"BM123456\"}', NULL, 'Paiement confirmé', NULL, NULL, '2025-07-27 17:04:59', '2025-07-28 17:04:59'),
(8, 3, NULL, 'bank_transfer', 'pending', 25000.00, 'DZD', 'BT456789123', '{\"bank_name\":\"BEA\",\"account_number\":\"************\",\"transfer_ref\":\"TR123456\"}', NULL, 'Virement bancaire en cours de vérification', NULL, NULL, '2025-07-28 14:04:59', '2025-07-28 17:04:59'),
(9, 4, NULL, 'cod', 'confirmed', 12000.00, 'DZD', NULL, '{\"delivery_address\":\"Alger, Algérie\",\"delivery_fee\":200}', NULL, 'Livraison effectuée', NULL, NULL, '2025-07-26 17:04:59', '2025-07-28 17:04:59'),
(10, 5, NULL, 'ccp', 'rejected', 18000.00, 'DZD', 'CCP987654321', '{\"ccp_number\":\"**********\",\"ccp_rip\":\"34\"}', NULL, 'Reçu illisible, demande de renvoi', NULL, NULL, '2025-07-28 12:04:59', '2025-07-28 17:04:59');

-- --------------------------------------------------------

--
-- Structure de la table `payment_gateway_configs`
--

DROP TABLE IF EXISTS `payment_gateway_configs`;
CREATE TABLE IF NOT EXISTS `payment_gateway_configs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `store_id` int(11) NOT NULL,
  `gateway_name` varchar(50) NOT NULL,
  `api_key` text DEFAULT NULL,
  `secret_key` text DEFAULT NULL,
  `endpoint` varchar(255) DEFAULT NULL,
  `currency` varchar(10) DEFAULT 'DZD',
  `mode` enum('sandbox','live') DEFAULT 'sandbox',
  `enabled` tinyint(1) DEFAULT 0,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_store_gateway` (`store_id`,`gateway_name`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `payment_logs`
--

DROP TABLE IF EXISTS `payment_logs`;
CREATE TABLE IF NOT EXISTS `payment_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `payment_id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `action` varchar(50) NOT NULL,
  `old_status` varchar(20) DEFAULT NULL,
  `new_status` varchar(20) DEFAULT NULL,
  `details` text DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_payment_id` (`payment_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_action` (`action`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=MyISAM AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `payment_logs`
--

INSERT INTO `payment_logs` (`id`, `payment_id`, `order_id`, `action`, `old_status`, `new_status`, `details`, `user_id`, `ip_address`, `user_agent`, `created_at`) VALUES
(1, 1, 1, 'created', NULL, 'pending', 'Paiement CCP créé', 1, '***********00', NULL, '2025-07-28 14:41:23'),
(2, 2, 2, 'created', NULL, 'pending', 'Paiement BaridiMob créé', 2, '***********01', NULL, '2025-07-27 16:41:23'),
(3, 2, 2, 'status_updated', 'pending', 'confirmed', 'Paiement confirmé par admin', 1, '***********', NULL, '2025-07-27 17:41:23'),
(4, 3, 3, 'created', NULL, 'pending', 'Virement bancaire créé', 3, '***********02', NULL, '2025-07-28 13:41:23'),
(5, 4, 4, 'created', NULL, 'pending', 'Commande COD créée', 4, '***********03', NULL, '2025-07-26 16:41:23'),
(6, 4, 4, 'status_updated', 'pending', 'confirmed', 'Livraison confirmée', 1, '***********', NULL, '2025-07-27 18:41:23'),
(7, 5, 5, 'created', NULL, 'pending', 'Paiement CCP créé', 5, '***********04', NULL, '2025-07-28 11:41:23'),
(8, 5, 5, 'status_updated', 'pending', 'rejected', 'Reçu illisible', 1, '***********', NULL, '2025-07-28 12:41:23'),
(9, 1, 1, 'created', NULL, 'pending', 'Paiement CCP créé', 1, '***********00', NULL, '2025-07-28 15:04:59'),
(10, 2, 2, 'created', NULL, 'pending', 'Paiement BaridiMob créé', 2, '***********01', NULL, '2025-07-27 17:04:59'),
(11, 2, 2, 'status_updated', 'pending', 'confirmed', 'Paiement confirmé par admin', 1, '***********', NULL, '2025-07-27 18:04:59'),
(12, 3, 3, 'created', NULL, 'pending', 'Virement bancaire créé', 3, '***********02', NULL, '2025-07-28 14:04:59'),
(13, 4, 4, 'created', NULL, 'pending', 'Commande COD créée', 4, '***********03', NULL, '2025-07-26 17:04:59'),
(14, 4, 4, 'status_updated', 'pending', 'confirmed', 'Livraison confirmée', 1, '***********', NULL, '2025-07-27 19:04:59'),
(15, 5, 5, 'created', NULL, 'pending', 'Paiement CCP créé', 5, '***********04', NULL, '2025-07-28 12:04:59'),
(16, 5, 5, 'status_updated', 'pending', 'rejected', 'Reçu illisible', 1, '***********', NULL, '2025-07-28 13:04:59');

-- --------------------------------------------------------

--
-- Structure de la table `payment_methods`
--

DROP TABLE IF EXISTS `payment_methods`;
CREATE TABLE IF NOT EXISTS `payment_methods` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `store_id` int(11) NOT NULL,
  `method_type` enum('baridimob','ccp','bank_transfer','cod','paypal','stripe') NOT NULL,
  `method_name` varchar(100) NOT NULL,
  `configuration` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'Method-specific configuration' CHECK (json_valid(`configuration`)),
  `is_active` tinyint(1) DEFAULT 1,
  `display_order` int(11) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `store_id` (`store_id`),
  KEY `method_type` (`method_type`),
  KEY `is_active` (`is_active`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `payment_methods`
--

INSERT INTO `payment_methods` (`id`, `store_id`, `method_type`, `method_name`, `configuration`, `is_active`, `display_order`, `created_at`, `updated_at`) VALUES
(1, 1, 'baridimob', '?????????? ??????', '{\"number\": \"\", \"name\": \"\", \"instructions\": \"???? ???????????????? ?????? ?????? ?????????? ?????? ?????????? ?????????? ??????????\"}', 1, 1, '2025-07-28 23:07:53', '2025-07-28 23:07:53'),
(2, 1, 'ccp', '???????? ???????? ??????????', '{\"account_number\": \"\", \"account_name\": \"\", \"instructions\": \"???? ???????????????? ?????? ???????????? ???????????? ?????????? ?????????? ??????????\"}', 1, 2, '2025-07-28 23:07:53', '2025-07-28 23:07:53'),
(3, 1, 'bank_transfer', '?????????? ??????????', '{\"bank_name\": \"\", \"account_number\": \"\", \"account_name\": \"\", \"instructions\": \"???? ???????????????? ?????????????? ?????????? ?????????? ??????????\"}', 1, 3, '2025-07-28 23:07:53', '2025-07-28 23:07:53'),
(4, 1, 'cod', '?????? ?????? ??????????????', '{\"additional_fee\": 0, \"available_areas\": [], \"instructions\": \"???????? ?????????? ?????? ???????????? ??????????\"}', 1, 4, '2025-07-28 23:07:53', '2025-07-28 23:07:53'),
(5, 1, 'baridimob', 'BaridiMob - Paiement Mobile', '{\"number\":\"**********\",\"name\":\"Nom du Commer\\u00e7ant\",\"instructions\":\"Veuillez effectuer le paiement via BaridiMob vers le num\\u00e9ro indiqu\\u00e9 et envoyer la capture d\'\\u00e9cran.\"}', 1, 1, '2025-07-29 10:52:39', '2025-07-29 10:52:39');

-- --------------------------------------------------------

--
-- Structure de la table `products`
--

DROP TABLE IF EXISTS `products`;
CREATE TABLE IF NOT EXISTS `products` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `merchant_id` int(11) NOT NULL,
  `store_id` int(11) DEFAULT NULL,
  `sku` varchar(100) DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `name_ar` varchar(255) DEFAULT NULL,
  `name_fr` varchar(255) DEFAULT NULL,
  `name_en` varchar(255) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `description_ar` text DEFAULT NULL,
  `description_fr` text DEFAULT NULL,
  `description_en` text DEFAULT NULL,
  `short_description` varchar(500) DEFAULT NULL,
  `short_description_ar` varchar(500) DEFAULT NULL,
  `short_description_fr` varchar(500) DEFAULT NULL,
  `short_description_en` varchar(500) DEFAULT NULL,
  `price` decimal(10,2) NOT NULL,
  `compare_price` decimal(10,2) DEFAULT NULL,
  `cost_price` decimal(10,2) DEFAULT NULL,
  `currency` varchar(3) DEFAULT 'DZD',
  `weight` decimal(8,2) DEFAULT NULL,
  `dimensions` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`dimensions`)),
  `stock_quantity` int(11) DEFAULT 0,
  `low_stock_threshold` int(11) DEFAULT 5,
  `manage_stock` tinyint(1) DEFAULT 1,
  `status` enum('active','inactive','draft','archived') DEFAULT 'draft',
  `featured` tinyint(1) DEFAULT 0,
  `images` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`images`)),
  `category_id` int(11) DEFAULT NULL,
  `tags` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`tags`)),
  `seo_title` varchar(255) DEFAULT NULL,
  `seo_description` varchar(500) DEFAULT NULL,
  `ai_generated` tinyint(1) DEFAULT 0,
  `ai_generation_prompt` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_merchant_id` (`merchant_id`),
  KEY `idx_sku` (`sku`),
  KEY `idx_status` (`status`),
  KEY `idx_featured` (`featured`),
  KEY `idx_price` (`price`),
  KEY `idx_products_merchant_status` (`merchant_id`,`status`,`featured`),
  KEY `store_id` (`store_id`),
  KEY `idx_store_id` (`store_id`)
) ENGINE=MyISAM AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `products`
--

INSERT INTO `products` (`id`, `merchant_id`, `store_id`, `sku`, `name`, `name_ar`, `name_fr`, `name_en`, `description`, `description_ar`, `description_fr`, `description_en`, `short_description`, `short_description_ar`, `short_description_fr`, `short_description_en`, `price`, `compare_price`, `cost_price`, `currency`, `weight`, `dimensions`, `stock_quantity`, `low_stock_threshold`, `manage_stock`, `status`, `featured`, `images`, `category_id`, `tags`, `seo_title`, `seo_description`, `ai_generated`, `ai_generation_prompt`, `created_at`, `updated_at`) VALUES
(1, 1, 1, 'TEST-PRODUCT-001', 'Test smartphone', 'هاتف ذكي تجريبي', 'Smartphone de test', 'Test smartphone', NULL, 'وصف المنتج التجريبي', 'Description du produit de test', 'Test product description', NULL, NULL, NULL, NULL, 25000.00, 30000.00, NULL, 'DZD', NULL, NULL, 10, 5, 1, 'active', 1, NULL, NULL, NULL, NULL, NULL, 0, NULL, '2025-07-29 10:20:17', '2025-07-29 10:20:17'),
(2, 1, 1, 'PRD-6888A1788BAF0', 'Test Product', 'منتج تجريبي', 'Produit de test', 'Test Product', NULL, 'وصف المنتج التجريبي', NULL, NULL, NULL, NULL, NULL, NULL, 15000.00, NULL, NULL, 'DZD', NULL, NULL, 10, 5, 1, 'active', 1, NULL, NULL, NULL, NULL, NULL, 0, NULL, '2025-07-29 10:24:56', '2025-07-29 10:24:56');

--
-- Déclencheurs `products`
--
DROP TRIGGER IF EXISTS `update_quota_after_product_delete`;
DELIMITER $$
CREATE TRIGGER `update_quota_after_product_delete` AFTER DELETE ON `products` FOR EACH ROW BEGIN
    UPDATE quota_usage 
    SET products_count = GREATEST(0, products_count - 1),
        last_calculated = CURRENT_TIMESTAMP
    WHERE user_id = OLD.merchant_id;
END
$$
DELIMITER ;
DROP TRIGGER IF EXISTS `update_quota_after_product_insert`;
DELIMITER $$
CREATE TRIGGER `update_quota_after_product_insert` AFTER INSERT ON `products` FOR EACH ROW BEGIN
    INSERT INTO quota_usage (user_id, products_count, landing_pages_count, categories_count)
    VALUES (NEW.merchant_id, 1, 0, 0)
    ON DUPLICATE KEY UPDATE 
        products_count = products_count + 1,
        last_calculated = CURRENT_TIMESTAMP;
END
$$
DELIMITER ;

-- --------------------------------------------------------

--
-- Structure de la table `product_categories`
--

DROP TABLE IF EXISTS `product_categories`;
CREATE TABLE IF NOT EXISTS `product_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `name_ar` varchar(255) DEFAULT NULL,
  `name_fr` varchar(255) DEFAULT NULL,
  `name_en` varchar(255) DEFAULT NULL,
  `slug` varchar(255) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `parent_id` int(11) DEFAULT NULL,
  `sort_order` int(11) DEFAULT 0,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `slug` (`slug`) USING HASH,
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_slug` (`slug`(250))
) ENGINE=MyISAM AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `product_categories`
--

INSERT INTO `product_categories` (`id`, `name`, `name_ar`, `name_fr`, `name_en`, `slug`, `description`, `parent_id`, `sort_order`, `is_active`, `created_at`) VALUES
(1, 'إلكترونيات', 'إلكترونيات', 'Électronique', 'Electronics', 'electronics', NULL, NULL, 0, 1, '2025-07-28 16:28:45'),
(2, 'ملابس', 'ملابس', 'Vêtements', 'Clothing', 'clothing', NULL, NULL, 0, 1, '2025-07-28 16:28:45'),
(3, 'منزل وحديقة', 'منزل وحديقة', 'Maison et Jardin', 'Home & Garden', 'home-garden', NULL, NULL, 0, 1, '2025-07-28 16:28:45'),
(4, 'رياضة ولياقة', 'رياضة ولياقة', 'Sport et Fitness', 'Sports & Fitness', 'sports-fitness', NULL, NULL, 0, 1, '2025-07-28 16:28:45'),
(5, 'جمال وعناية', 'جمال وعناية', 'Beauté et Soins', 'Beauty & Care', 'beauty-care', NULL, NULL, 0, 1, '2025-07-28 16:28:45');

-- --------------------------------------------------------

--
-- Doublure de structure pour la vue `public_settings`
-- (Voir ci-dessous la vue réelle)
--
DROP VIEW IF EXISTS `public_settings`;
CREATE TABLE IF NOT EXISTS `public_settings` (
`setting_key` varchar(100)
,`setting_value` text
,`setting_type` enum('string','integer','boolean','json')
);

-- --------------------------------------------------------

--
-- Structure de la table `quota_usage`
--

DROP TABLE IF EXISTS `quota_usage`;
CREATE TABLE IF NOT EXISTS `quota_usage` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT 'Utilisateur concern?? (FK vers users)',
  `products_count` int(11) NOT NULL DEFAULT 0 COMMENT 'Nombre actuel de produits',
  `landing_pages_count` int(11) NOT NULL DEFAULT 0 COMMENT 'Nombre actuel de landing pages',
  `categories_count` int(11) NOT NULL DEFAULT 0 COMMENT 'Nombre actuel de cat??gories',
  `last_calculated` timestamp NOT NULL DEFAULT current_timestamp() COMMENT 'Derni??re mise ?? jour du cache',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_id` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Cache des quotas d''utilisation par utilisateur';

--
-- Déchargement des données de la table `quota_usage`
--

INSERT INTO `quota_usage` (`id`, `user_id`, `products_count`, `landing_pages_count`, `categories_count`, `last_calculated`, `created_at`, `updated_at`) VALUES
(1, 1, 0, 0, 0, '2025-07-28 20:05:28', '2025-07-28 19:49:39', '2025-07-28 20:05:28'),
(2, 2, 0, 0, 0, '2025-07-28 20:05:28', '2025-07-28 19:49:39', '2025-07-28 20:05:28');

-- --------------------------------------------------------

--
-- Doublure de structure pour la vue `seller_dashboard_stats`
-- (Voir ci-dessous la vue réelle)
--
DROP VIEW IF EXISTS `seller_dashboard_stats`;
CREATE TABLE IF NOT EXISTS `seller_dashboard_stats` (
`store_id` int(11)
,`user_id` varchar(255)
,`store_name` varchar(255)
,`total_orders` bigint(21)
,`total_revenue` decimal(32,2)
,`total_products` bigint(21)
,`orders_last_30_days` bigint(21)
,`revenue_last_30_days` decimal(32,2)
,`active_products` bigint(21)
);

-- --------------------------------------------------------

--
-- Doublure de structure pour la vue `seller_order_summary`
-- (Voir ci-dessous la vue réelle)
--
DROP VIEW IF EXISTS `seller_order_summary`;
CREATE TABLE IF NOT EXISTS `seller_order_summary` (
`id` int(11)
,`order_number` varchar(50)
,`customer_name` varchar(255)
,`customer_email` varchar(255)
,`customer_phone` varchar(20)
,`total_amount` decimal(10,2)
,`status` enum('pending','confirmed','processing','shipped','delivered','cancelled','refunded')
,`payment_method` enum('ccp','baridimob','bank_transfer','cod','online')
,`payment_status` enum('pending','paid','failed','refunded')
,`created_at` timestamp
,`store_name` varchar(255)
,`seller_id` varchar(255)
,`item_count` bigint(21)
);

-- --------------------------------------------------------

--
-- Structure de la table `settings`
--

DROP TABLE IF EXISTS `settings`;
CREATE TABLE IF NOT EXISTS `settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `setting_type` enum('string','integer','boolean','json') DEFAULT 'string',
  `description` varchar(255) DEFAULT NULL,
  `is_public` tinyint(1) DEFAULT 0 COMMENT 'Si le paramètre peut être lu par les utilisateurs non-admin',
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `setting_key` (`setting_key`),
  UNIQUE KEY `unique_setting_key` (`setting_key`),
  KEY `idx_setting_type` (`setting_type`),
  KEY `idx_is_public` (`is_public`),
  KEY `idx_settings_key_public` (`setting_key`,`is_public`)
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Table de configuration système pour l''interface d''administration';

--
-- Déchargement des données de la table `settings`
--

INSERT INTO `settings` (`id`, `setting_key`, `setting_value`, `setting_type`, `description`, `is_public`, `created_at`, `updated_at`) VALUES
(1, 'site_name', 'Landing Pages SaaS', 'string', 'Nom du site web', 1, '2025-07-29 09:26:46', '2025-07-29 09:26:46'),
(2, 'site_description', 'Plateforme de création de landing pages pour tous', 'string', 'Description du site', 1, '2025-07-29 09:26:46', '2025-07-29 09:26:46'),
(3, 'admin_email', '<EMAIL>', 'string', 'Email de l\'administrateur principal', 0, '2025-07-29 09:26:46', '2025-07-29 09:26:46'),
(4, 'default_language', 'fr', 'string', 'Langue par défaut du système', 1, '2025-07-29 09:26:46', '2025-07-29 09:26:46'),
(5, 'timezone', 'Europe/Paris', 'string', 'Fuseau horaire par défaut', 1, '2025-07-29 09:26:46', '2025-07-29 09:26:46'),
(6, 'currency', 'EUR', 'string', 'Devise par défaut', 1, '2025-07-29 09:26:46', '2025-07-29 09:26:46'),
(7, 'maintenance_mode', '0', 'boolean', 'Mode maintenance activé/désactivé', 1, '2025-07-29 09:26:46', '2025-07-29 09:26:46'),
(8, 'registration_enabled', '1', 'boolean', 'Inscription des nouveaux utilisateurs autorisée', 1, '2025-07-29 09:26:46', '2025-07-29 09:26:46'),
(9, 'smtp_host', '', 'string', 'Serveur SMTP pour l\'envoi d\'emails', 0, '2025-07-29 09:26:46', '2025-07-29 09:26:46'),
(10, 'smtp_port', '587', 'integer', 'Port SMTP', 0, '2025-07-29 09:26:46', '2025-07-29 09:26:46'),
(11, 'smtp_username', '', 'string', 'Nom d\'utilisateur SMTP', 0, '2025-07-29 09:26:46', '2025-07-29 09:26:46'),
(12, 'smtp_password', '', 'string', 'Mot de passe SMTP (chiffré)', 0, '2025-07-29 09:26:46', '2025-07-29 09:26:46'),
(13, 'smtp_encryption', 'tls', 'string', 'Type de chiffrement SMTP (tls/ssl/none)', 0, '2025-07-29 09:26:46', '2025-07-29 09:26:46'),
(14, 'max_landing_pages_per_user', '10', 'integer', 'Nombre maximum de landing pages par utilisateur', 0, '2025-07-29 09:26:46', '2025-07-29 09:26:46'),
(15, 'max_products_per_store', '100', 'integer', 'Nombre maximum de produits par boutique', 0, '2025-07-29 09:26:46', '2025-07-29 09:26:46'),
(16, 'file_upload_max_size', '10485760', 'integer', 'Taille maximale des fichiers uploadés (en bytes)', 0, '2025-07-29 09:26:46', '2025-07-29 09:26:46'),
(17, 'allowed_file_types', 'jpg,jpeg,png,gif,pdf,doc,docx', 'string', 'Types de fichiers autorisés pour l\'upload', 0, '2025-07-29 09:26:46', '2025-07-29 09:26:46'),
(18, 'backup_frequency', 'daily', 'string', 'Fréquence des sauvegardes automatiques', 0, '2025-07-29 09:26:46', '2025-07-29 09:26:46'),
(19, 'session_timeout', '3600', 'integer', 'Durée de session en secondes', 0, '2025-07-29 09:26:46', '2025-07-29 09:26:46'),
(20, 'api_rate_limit', '1000', 'integer', 'Limite de requêtes API par heure par utilisateur', 0, '2025-07-29 09:26:46', '2025-07-29 09:26:46');

-- --------------------------------------------------------

--
-- Structure de la table `stores`
--

DROP TABLE IF EXISTS `stores`;
CREATE TABLE IF NOT EXISTS `stores` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` varchar(255) NOT NULL COMMENT 'Firebase UID of the seller',
  `merchant_id` int(11) DEFAULT NULL COMMENT 'Link to merchants table if needed',
  `store_name` varchar(255) NOT NULL,
  `store_name_ar` varchar(255) DEFAULT NULL,
  `store_name_fr` varchar(255) DEFAULT NULL,
  `store_name_en` varchar(255) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `description_ar` text DEFAULT NULL,
  `description_fr` text DEFAULT NULL,
  `description_en` text DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `address_ar` text DEFAULT NULL,
  `address_fr` text DEFAULT NULL,
  `address_en` text DEFAULT NULL,
  `logo_url` varchar(500) DEFAULT NULL,
  `banner_url` varchar(500) DEFAULT NULL,
  `website_url` varchar(500) DEFAULT NULL,
  `social_media` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'Social media links' CHECK (json_valid(`social_media`)),
  `business_type` varchar(100) DEFAULT NULL,
  `category` varchar(100) DEFAULT NULL,
  `status` enum('active','inactive','suspended','pending') DEFAULT 'pending',
  `verification_status` enum('unverified','pending','verified','rejected') DEFAULT 'unverified',
  `verification_documents` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`verification_documents`)),
  `settings` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'Store-specific settings' CHECK (json_valid(`settings`)),
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_id` (`user_id`),
  KEY `merchant_id` (`merchant_id`),
  KEY `status` (`status`),
  KEY `verification_status` (`verification_status`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `stores`
--

INSERT INTO `stores` (`id`, `user_id`, `merchant_id`, `store_name`, `store_name_ar`, `store_name_fr`, `store_name_en`, `description`, `description_ar`, `description_fr`, `description_en`, `phone`, `email`, `address`, `address_ar`, `address_fr`, `address_en`, `logo_url`, `banner_url`, `website_url`, `social_media`, `business_type`, `category`, `status`, `verification_status`, `verification_documents`, `settings`, `created_at`, `updated_at`) VALUES
(1, 'firebase_uid_1', NULL, 'Test Store', '???????? ????????????', NULL, NULL, 'A test store for development', NULL, NULL, NULL, '+************', '<EMAIL>', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'active', 'verified', NULL, NULL, '2025-07-28 23:07:53', '2025-07-28 23:07:53'),
(2, 'firebase_uid_2', NULL, 'Demo Shop', '???????? ???????????? 2', NULL, NULL, 'Another test store', NULL, NULL, NULL, '+************', '<EMAIL>', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'active', 'pending', NULL, NULL, '2025-07-28 23:07:53', '2025-07-28 23:07:53');

-- --------------------------------------------------------

--
-- Structure de la table `store_analytics`
--

DROP TABLE IF EXISTS `store_analytics`;
CREATE TABLE IF NOT EXISTS `store_analytics` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `store_id` int(11) NOT NULL,
  `date` date NOT NULL,
  `metric_type` enum('sales','orders','visitors','conversions','ai_usage') NOT NULL,
  `metric_value` decimal(15,2) NOT NULL DEFAULT 0.00,
  `additional_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`additional_data`)),
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `store_date_metric` (`store_id`,`date`,`metric_type`),
  KEY `date` (`date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `subscriptions`
--

DROP TABLE IF EXISTS `subscriptions`;
CREATE TABLE IF NOT EXISTS `subscriptions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT 'Nom du plan (Gratuit, Starter, Pro, Enterprise)',
  `slug` varchar(50) NOT NULL COMMENT 'Identifiant unique du plan',
  `description` text DEFAULT NULL COMMENT 'Description du plan',
  `price` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT 'Prix mensuel en euros',
  `currency` varchar(3) NOT NULL DEFAULT 'EUR' COMMENT 'Devise (EUR, USD, etc.)',
  `max_products` int(11) NOT NULL DEFAULT 0 COMMENT 'Nombre maximum de produits (-1 = illimit??)',
  `max_landing_pages` int(11) NOT NULL DEFAULT 0 COMMENT 'Nombre maximum de landing pages (-1 = illimit??)',
  `max_categories` int(11) NOT NULL DEFAULT 0 COMMENT 'Nombre maximum de cat??gories (-1 = illimit??)',
  `features` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'Fonctionnalit??s incluses (JSON)' CHECK (json_valid(`features`)),
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'Plan actif (1) ou d??sactiv?? (0)',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT 'Ordre d''affichage',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `slug` (`slug`),
  KEY `is_active` (`is_active`),
  KEY `sort_order` (`sort_order`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Plans d''abonnement avec limites de quotas';

--
-- Déchargement des données de la table `subscriptions`
--

INSERT INTO `subscriptions` (`id`, `name`, `slug`, `description`, `price`, `currency`, `max_products`, `max_landing_pages`, `max_categories`, `features`, `is_active`, `sort_order`, `created_at`, `updated_at`) VALUES
(1, 'Gratuit', 'free', 'Plan gratuit pour d??couvrir la plateforme', 0.00, 'EUR', 5, 2, 3, '[\"Support par email\", \"Templates de base\", \"Analytics basiques\"]', 1, 1, '2025-07-28 19:49:39', '2025-07-28 19:49:39'),
(2, 'Starter', 'starter', 'Plan id??al pour les petites entreprises', 29.00, 'EUR', 50, 10, 10, '[\"Support prioritaire\", \"Templates premium\", \"Analytics avanc??es\", \"Domaine personnalis??\"]', 1, 2, '2025-07-28 19:49:39', '2025-07-28 19:49:39'),
(3, 'Pro', 'pro', 'Plan professionnel pour les entreprises en croissance', 99.00, 'EUR', 200, 50, 25, '[\"Support 24/7\", \"Tous les templates\", \"Analytics compl??tes\", \"API access\", \"White-label\"]', 1, 3, '2025-07-28 19:49:39', '2025-07-28 19:49:39'),
(4, 'Enterprise', 'enterprise', 'Plan entreprise avec ressources illimit??es', 299.00, 'EUR', -1, -1, -1, '[\"Support d??di??\", \"Ressources illimit??es\", \"Int??grations personnalis??es\", \"Formation incluse\"]', 1, 4, '2025-07-28 19:49:39', '2025-07-28 19:49:39');

-- --------------------------------------------------------

--
-- Structure de la table `users`
--

DROP TABLE IF EXISTS `users`;
CREATE TABLE IF NOT EXISTS `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `firebase_uid` varchar(128) DEFAULT NULL,
  `email` varchar(255) NOT NULL,
  `password_hash` varchar(255) DEFAULT NULL,
  `role` enum('admin','merchant','customer') DEFAULT 'customer',
  `subscription_id` int(11) DEFAULT 1 COMMENT 'ID du plan d''abonnement (FK vers subscriptions)',
  `subscription_start` timestamp NULL DEFAULT NULL COMMENT 'Date de d??but d''abonnement',
  `subscription_end` timestamp NULL DEFAULT NULL COMMENT 'Date de fin d''abonnement (NULL = permanent)',
  `status` enum('active','inactive','suspended') DEFAULT 'inactive',
  `preferred_language` enum('ar','fr','en') DEFAULT 'ar',
  `first_name` varchar(100) DEFAULT NULL,
  `last_name` varchar(100) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `avatar_url` varchar(500) DEFAULT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `last_login_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `firebase_uid` (`firebase_uid`),
  UNIQUE KEY `idx_firebase_uid` (`firebase_uid`),
  UNIQUE KEY `email` (`email`) USING HASH,
  KEY `idx_email` (`email`(250)),
  KEY `idx_role` (`role`),
  KEY `idx_status` (`status`),
  KEY `idx_subscription_id` (`subscription_id`)
) ENGINE=MyISAM AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `users`
--

INSERT INTO `users` (`id`, `firebase_uid`, `email`, `password_hash`, `role`, `subscription_id`, `subscription_start`, `subscription_end`, `status`, `preferred_language`, `first_name`, `last_name`, `phone`, `avatar_url`, `email_verified_at`, `last_login_at`, `created_at`, `updated_at`) VALUES
(1, NULL, '<EMAIL>', '.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 1, NULL, NULL, 'active', 'ar', 'Admin', 'System', NULL, NULL, NULL, NULL, '2025-07-28 17:09:34', '2025-07-28 17:09:34'),
(2, NULL, '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 4, '2025-07-28 20:01:01', NULL, 'active', 'ar', 'Administrateur', 'Syst??me', NULL, NULL, NULL, NULL, '2025-07-28 19:49:39', '2025-07-28 20:01:01');

-- --------------------------------------------------------

--
-- Structure de la table `user_roles`
--

DROP TABLE IF EXISTS `user_roles`;
CREATE TABLE IF NOT EXISTS `user_roles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` varchar(255) NOT NULL,
  `role` enum('admin','seller','agent','customer') NOT NULL DEFAULT 'customer',
  `permissions` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`permissions`)),
  `store_id` int(11) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `assigned_by` varchar(255) DEFAULT NULL,
  `assigned_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_role` (`user_id`,`role`),
  KEY `role` (`role`),
  KEY `store_id` (`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `user_roles`
--

INSERT INTO `user_roles` (`id`, `user_id`, `role`, `permissions`, `store_id`, `is_active`, `assigned_by`, `assigned_at`, `expires_at`, `created_at`, `updated_at`) VALUES
(1, 'firebase_uid_1', 'seller', NULL, 1, 1, NULL, '2025-07-28 23:07:53', NULL, '2025-07-28 23:07:53', '2025-07-28 23:07:53'),
(2, 'firebase_uid_2', 'seller', NULL, 2, 1, NULL, '2025-07-28 23:07:53', NULL, '2025-07-28 23:07:53', '2025-07-28 23:07:53'),
(3, 'admin_firebase_uid', 'admin', NULL, NULL, 1, NULL, '2025-07-28 23:07:53', NULL, '2025-07-28 23:07:53', '2025-07-28 23:07:53'),
(4, 'firebase_uid_1', 'admin', NULL, NULL, 1, NULL, '2025-07-29 17:07:18', NULL, '2025-07-29 17:07:18', '2025-07-29 17:07:18');

-- --------------------------------------------------------

--
-- Structure de la vue `merchant_stats`
--
DROP TABLE IF EXISTS `merchant_stats`;

DROP VIEW IF EXISTS `merchant_stats`;
CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `merchant_stats`  AS SELECT `m`.`id` AS `merchant_id`, `m`.`business_name` AS `business_name`, count(distinct `o`.`id`) AS `total_orders`, count(distinct case when `o`.`status` = 'delivered' then `o`.`id` end) AS `completed_orders`, sum(case when `o`.`status` = 'delivered' then `o`.`total_amount` else 0 end) AS `total_revenue`, count(distinct `p`.`id`) AS `total_products`, count(distinct `lp`.`id`) AS `total_landing_pages`, `m`.`ai_tokens_used` AS `ai_tokens_used`, `m`.`ai_tokens_limit` AS `ai_tokens_limit` FROM (((`merchants` `m` left join `orders` `o` on(`m`.`id` = `o`.`merchant_id`)) left join `products` `p` on(`m`.`id` = `p`.`merchant_id`)) left join `landing_pages` `lp` on(`m`.`id` = `lp`.`merchant_id`)) GROUP BY `m`.`id` ;

-- --------------------------------------------------------

--
-- Structure de la vue `order_summary`
--
DROP TABLE IF EXISTS `order_summary`;

DROP VIEW IF EXISTS `order_summary`;
CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `order_summary`  AS SELECT `o`.`id` AS `id`, `o`.`order_number` AS `order_number`, `o`.`merchant_id` AS `merchant_id`, `o`.`customer_email` AS `customer_email`, `o`.`customer_name` AS `customer_name`, `o`.`customer_phone` AS `customer_phone`, `o`.`customer_address` AS `customer_address`, `o`.`customer_city` AS `customer_city`, `o`.`customer_postal_code` AS `customer_postal_code`, `o`.`customer_country` AS `customer_country`, `o`.`subtotal` AS `subtotal`, `o`.`tax_amount` AS `tax_amount`, `o`.`shipping_amount` AS `shipping_amount`, `o`.`discount_amount` AS `discount_amount`, `o`.`total_amount` AS `total_amount`, `o`.`currency` AS `currency`, `o`.`status` AS `status`, `o`.`payment_method` AS `payment_method`, `o`.`payment_status` AS `payment_status`, `o`.`payment_details` AS `payment_details`, `o`.`payment_proof_url` AS `payment_proof_url`, `o`.`notes` AS `notes`, `o`.`admin_notes` AS `admin_notes`, `o`.`language` AS `language`, `o`.`source` AS `source`, `o`.`utm_source` AS `utm_source`, `o`.`utm_medium` AS `utm_medium`, `o`.`utm_campaign` AS `utm_campaign`, `o`.`created_at` AS `created_at`, `o`.`updated_at` AS `updated_at`, `m`.`business_name` AS `business_name`, count(`oi`.`id`) AS `items_count`, group_concat(`oi`.`product_name` separator ', ') AS `products_list` FROM ((`orders` `o` join `merchants` `m` on(`o`.`merchant_id` = `m`.`id`)) left join `order_items` `oi` on(`o`.`id` = `oi`.`order_id`)) GROUP BY `o`.`id` ;

-- --------------------------------------------------------

--
-- Structure de la vue `public_settings`
--
DROP TABLE IF EXISTS `public_settings`;

DROP VIEW IF EXISTS `public_settings`;
CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `public_settings`  AS SELECT `settings`.`setting_key` AS `setting_key`, `settings`.`setting_value` AS `setting_value`, `settings`.`setting_type` AS `setting_type` FROM `settings` WHERE `settings`.`is_public` = 1 ;

-- --------------------------------------------------------

--
-- Structure de la vue `seller_dashboard_stats`
--
DROP TABLE IF EXISTS `seller_dashboard_stats`;

DROP VIEW IF EXISTS `seller_dashboard_stats`;
CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `seller_dashboard_stats`  AS SELECT `s`.`id` AS `store_id`, `s`.`user_id` AS `user_id`, `s`.`store_name` AS `store_name`, count(distinct `o`.`id`) AS `total_orders`, coalesce(sum(`o`.`total_amount`),0) AS `total_revenue`, count(distinct `p`.`id`) AS `total_products`, count(distinct case when `o`.`created_at` >= current_timestamp() - interval 30 day then `o`.`id` end) AS `orders_last_30_days`, coalesce(sum(case when `o`.`created_at` >= current_timestamp() - interval 30 day then `o`.`total_amount` else 0 end),0) AS `revenue_last_30_days`, count(distinct case when `p`.`status` = 'active' then `p`.`id` end) AS `active_products` FROM ((`stores` `s` left join `orders` `o` on(`s`.`id` = `o`.`store_id`)) left join `products` `p` on(`s`.`id` = `p`.`store_id`)) GROUP BY `s`.`id`, `s`.`user_id`, `s`.`store_name` ;

-- --------------------------------------------------------

--
-- Structure de la vue `seller_order_summary`
--
DROP TABLE IF EXISTS `seller_order_summary`;

DROP VIEW IF EXISTS `seller_order_summary`;
CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `seller_order_summary`  AS SELECT `o`.`id` AS `id`, `o`.`order_number` AS `order_number`, `o`.`customer_name` AS `customer_name`, `o`.`customer_email` AS `customer_email`, `o`.`customer_phone` AS `customer_phone`, `o`.`total_amount` AS `total_amount`, `o`.`status` AS `status`, `o`.`payment_method` AS `payment_method`, `o`.`payment_status` AS `payment_status`, `o`.`created_at` AS `created_at`, `s`.`store_name` AS `store_name`, `s`.`user_id` AS `seller_id`, count(`oi`.`id`) AS `item_count` FROM ((`orders` `o` join `stores` `s` on(`o`.`store_id` = `s`.`id`)) left join `order_items` `oi` on(`o`.`id` = `oi`.`order_id`)) GROUP BY `o`.`id` ;

--
-- Contraintes pour les tables déchargées
--

--
-- Contraintes pour la table `activity_logs`
--
ALTER TABLE `activity_logs`
  ADD CONSTRAINT `fk_activity_logs_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Contraintes pour la table `ai_keys`
--
ALTER TABLE `ai_keys`
  ADD CONSTRAINT `fk_ai_keys_store` FOREIGN KEY (`store_id`) REFERENCES `stores` (`id`) ON DELETE CASCADE;

--
-- Contraintes pour la table `categories`
--
ALTER TABLE `categories`
  ADD CONSTRAINT `fk_categories_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Contraintes pour la table `payment_methods`
--
ALTER TABLE `payment_methods`
  ADD CONSTRAINT `fk_payment_methods_store` FOREIGN KEY (`store_id`) REFERENCES `stores` (`id`) ON DELETE CASCADE;

--
-- Contraintes pour la table `quota_usage`
--
ALTER TABLE `quota_usage`
  ADD CONSTRAINT `fk_quota_usage_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Contraintes pour la table `stores`
--
ALTER TABLE `stores`
  ADD CONSTRAINT `fk_stores_merchant` FOREIGN KEY (`merchant_id`) REFERENCES `merchants` (`id`) ON DELETE SET NULL;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
