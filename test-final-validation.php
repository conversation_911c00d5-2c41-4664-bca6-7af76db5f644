<?php
require_once 'php/config/database.php';

header('Content-Type: text/html; charset=UTF-8');

echo "<h1>✅ Validation finale des corrections</h1>";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .success{color:green;} .error{color:red;} .warning{color:orange;} table{border-collapse:collapse;width:100%;margin:20px 0;} th,td{border:1px solid #ddd;padding:8px;text-align:left;} th{background-color:#f2f2f2;}</style>";

$tests_passed = 0;
$total_tests = 0;

function runTest($name, $test_function) {
    global $tests_passed, $total_tests;
    $total_tests++;
    
    echo "<h2>🧪 Test: $name</h2>";
    
    try {
        $result = $test_function();
        if ($result) {
            echo "<p class='success'>✅ RÉUSSI</p>";
            $tests_passed++;
        } else {
            echo "<p class='error'>❌ ÉCHOUÉ</p>";
        }
    } catch (Exception $e) {
        echo "<p class='error'>❌ ERREUR: " . $e->getMessage() . "</p>";
    }
    
    echo "<hr>";
}

// Test 1: Encodage UTF-8 pour l'arabe
runTest("Affichage correct de l'arabe", function() use ($pdo) {
    $stmt = $pdo->prepare("SELECT store_name_ar FROM stores WHERE id = 1");
    $stmt->execute();
    $result = $stmt->fetchColumn();
    
    echo "<p>Nom en arabe récupéré: <span dir='rtl'>" . htmlspecialchars($result) . "</span></p>";
    
    // Vérifier que ce n'est pas des points d'interrogation
    return $result && $result !== '???????? ????????????' && mb_strlen($result) > 0;
});

// Test 2: Table reviews existe et fonctionne
runTest("Table reviews et API products", function() use ($pdo) {
    // Vérifier que la table existe
    $stmt = $pdo->query("SHOW TABLES LIKE 'reviews'");
    if (!$stmt->fetch()) {
        throw new Exception("Table reviews n'existe pas");
    }
    
    // Tester la requête de l'API products
    $stmt = $pdo->prepare("
        SELECT
            p.*,
            COALESCE(AVG(r.rating), 0) as average_rating,
            COUNT(r.id) as review_count
        FROM products p
        LEFT JOIN reviews r ON p.id = r.product_id
        WHERE p.id = ?
        GROUP BY p.id
    ");
    $stmt->execute([2]);
    $product = $stmt->fetch();
    
    if (!$product) {
        throw new Exception("Produit non trouvé");
    }
    
    echo "<p>Produit: " . htmlspecialchars($product['name']) . "</p>";
    echo "<p>Note moyenne: " . round($product['average_rating'], 1) . "/5</p>";
    echo "<p>Nombre d'avis: " . $product['review_count'] . "</p>";
    
    return true;
});

// Test 3: Table roles existe et contient des données
runTest("Système de rôles", function() use ($pdo) {
    // Vérifier que la table existe
    $stmt = $pdo->query("SHOW TABLES LIKE 'roles'");
    if (!$stmt->fetch()) {
        throw new Exception("Table roles n'existe pas");
    }
    
    // Vérifier qu'il y a des rôles
    $stmt = $pdo->query("SELECT COUNT(*) FROM roles");
    $count = $stmt->fetchColumn();
    
    if ($count == 0) {
        throw new Exception("Aucun rôle trouvé");
    }
    
    echo "<p>Nombre de rôles: $count</p>";
    
    // Afficher les rôles
    $stmt = $pdo->query("SELECT name, display_name, user_count FROM (SELECT r.name, r.display_name, COUNT(ur.id) as user_count FROM roles r LEFT JOIN user_roles ur ON r.id = ur.role_id GROUP BY r.id) as role_stats");
    $roles = $stmt->fetchAll();
    
    echo "<table>";
    echo "<tr><th>Nom</th><th>Nom d'affichage</th><th>Utilisateurs</th></tr>";
    foreach ($roles as $role) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($role['name']) . "</td>";
        echo "<td>" . htmlspecialchars($role['display_name']) . "</td>";
        echo "<td>" . $role['user_count'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    return $count >= 3; // Au moins admin, merchant, customer
});

// Test 4: API roles fonctionne
runTest("API Roles", function() {
    $url = 'http://localhost:8000/api/roles.php';
    
    // Utiliser cURL pour tester l'API
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Authorization: Bearer demo_token'
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($http_code !== 200) {
        echo "<p class='warning'>⚠️ API non accessible (serveur non démarré?). Code HTTP: $http_code</p>";
        return false;
    }
    
    $data = json_decode($response, true);
    
    if (!$data || !$data['success']) {
        throw new Exception("Réponse API invalide");
    }
    
    echo "<p>Nombre de rôles retournés par l'API: " . count($data['data']['roles']) . "</p>";
    
    return count($data['data']['roles']) >= 3;
});

// Test 5: Vérifier que les stores s'affichent correctement
runTest("API Stores avec noms en arabe", function() {
    $url = 'http://localhost:8000/api/stores-simple.php';
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($http_code !== 200) {
        echo "<p class='warning'>⚠️ API stores non accessible. Code HTTP: $http_code</p>";
        return false;
    }
    
    $data = json_decode($response, true);
    
    if (!$data || !$data['success']) {
        throw new Exception("Réponse API stores invalide");
    }
    
    $stores = $data['data']['stores'];
    echo "<p>Nombre de stores: " . count($stores) . "</p>";
    
    // Vérifier qu'au moins un store a un nom en arabe correct
    foreach ($stores as $store) {
        if (!empty($store['store_name_ar']) && $store['store_name_ar'] !== '???????? ????????????') {
            echo "<p>Store avec nom arabe correct: <span dir='rtl'>" . htmlspecialchars($store['store_name_ar']) . "</span></p>";
            return true;
        }
    }
    
    return false;
});

echo "<h2>📊 Résumé des tests</h2>";
echo "<p><strong>Tests réussis:</strong> $tests_passed / $total_tests</p>";

if ($tests_passed === $total_tests) {
    echo "<div style='background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>🎉 Tous les tests sont réussis !</h3>";
    echo "<p>✅ L'encodage UTF-8 pour l'arabe fonctionne correctement</p>";
    echo "<p>✅ La table reviews est créée et l'API products fonctionne</p>";
    echo "<p>✅ Le système de rôles est opérationnel</p>";
    echo "<p>✅ L'API roles est fonctionnelle</p>";
    echo "<p>✅ Les stores s'affichent avec les noms en arabe</p>";
    echo "<p><strong>Le dashboard devrait maintenant fonctionner correctement !</strong></p>";
    echo "</div>";
} else {
    echo "<div style='background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>⚠️ Certains tests ont échoué</h3>";
    echo "<p>Veuillez vérifier les erreurs ci-dessus et corriger les problèmes restants.</p>";
    echo "</div>";
}
?>
