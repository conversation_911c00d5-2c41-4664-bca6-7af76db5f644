<?php
require_once 'php/config/database.php';

try {
    echo "🔍 Test des APIs et correction des erreurs 500...\n\n";

    // Test de la base de données
    echo "📊 Test de la connexion base de données...\n";
    $database = new Database();
    $db = $database->getConnection();
    echo "  ✅ Connexion base de données OK\n";

    // Vérifier les tables nécessaires
    echo "\n📋 Vérification des tables...\n";

    $tables = [
        'users' => 'Table des utilisateurs',
        'user_roles' => 'Table des rôles utilisateurs',
        'roles' => 'Table des rôles',
        'products' => 'Table des produits',
        'orders' => 'Table des commandes',
        'order_items' => 'Table des articles de commande',
        'subscriptions' => 'Table des abonnements',
        'subscription_plans' => 'Table des plans d\'abonnement'
    ];

    foreach ($tables as $table => $description) {
        $query = "SHOW TABLES LIKE '$table'";
        $result = $db->query($query);

        if ($result->rowCount() > 0) {
            echo "  ✅ $description ($table) existe\n";
        } else {
            echo "  ❌ $description ($table) manquante\n";

            // Créer les tables manquantes
            switch ($table) {
                case 'orders':
                    echo "    🔧 Création de la table orders...\n";
                    $createOrdersTable = "
                        CREATE TABLE IF NOT EXISTS orders (
                            id INT AUTO_INCREMENT PRIMARY KEY,
                            store_id INT NOT NULL,
                            customer_name VARCHAR(255) NULL,
                            customer_email VARCHAR(255) NULL,
                            customer_phone VARCHAR(50) NULL,
                            shipping_address JSON NULL,
                            billing_address JSON NULL,
                            status ENUM('pending', 'processing', 'shipped', 'delivered', 'cancelled') DEFAULT 'pending',
                            payment_method ENUM('cod', 'card', 'paypal', 'bank_transfer') NULL,
                            payment_status ENUM('pending', 'paid', 'refunded') DEFAULT 'pending',
                            total_amount DECIMAL(10,2) NOT NULL,
                            shipping_cost DECIMAL(10,2) DEFAULT 0,
                            tax_amount DECIMAL(10,2) DEFAULT 0,
                            discount_amount DECIMAL(10,2) DEFAULT 0,
                            order_number VARCHAR(50) UNIQUE NOT NULL,
                            notes TEXT NULL,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                            INDEX idx_store_id (store_id),
                            INDEX idx_status (status),
                            INDEX idx_order_number (order_number)
                        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                    ";
                    $db->exec($createOrdersTable);
                    echo "    ✅ Table orders créée\n";
                    break;

                case 'order_items':
                    echo "    🔧 Création de la table order_items...\n";
                    $createOrderItemsTable = "
                        CREATE TABLE IF NOT EXISTS order_items (
                            id INT AUTO_INCREMENT PRIMARY KEY,
                            order_id INT NOT NULL,
                            product_id INT NULL,
                            product_name VARCHAR(255) NOT NULL,
                            product_sku VARCHAR(100) NULL,
                            quantity INT NOT NULL,
                            unit_price DECIMAL(10,2) NOT NULL,
                            total_price DECIMAL(10,2) NOT NULL,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
                            INDEX idx_order_id (order_id),
                            INDEX idx_product_id (product_id)
                        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                    ";
                    $db->exec($createOrderItemsTable);
                    echo "    ✅ Table order_items créée\n";
                    break;
            }
        }
    }

    // Test des APIs
    echo "\n🔗 Test des APIs...\n";

    $apis = [
        'orders.php' => 'API des commandes',
        'products.php' => 'API des produits',
        'firebase-users.php' => 'API des utilisateurs Firebase'
    ];

    foreach ($apis as $api => $description) {
        echo "  🧪 Test de $description ($api)...\n";

        // Simuler une requête GET simple
        $url = "http://localhost:8000/api/$api";

        // Utiliser cURL pour tester
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer demo_token',
            'Content-Type: application/json'
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode === 200) {
            echo "    ✅ $description fonctionne (HTTP 200)\n";
        } else {
            echo "    ❌ $description erreur (HTTP $httpCode)\n";
            if ($response) {
                $decoded = json_decode($response, true);
                if ($decoded && isset($decoded['error'])) {
                    echo "    📝 Erreur: {$decoded['error']}\n";
                }
            }
        }
    }

    // Créer quelques données de test
    echo "\n📝 Création de données de test...\n";

    // Récupérer le merchant_id du store 3
    $storeQuery = "SELECT merchant_id FROM stores WHERE id = 3";
    $storeStmt = $pdo->prepare($storeQuery);
    $storeStmt->execute();
    $storeData = $storeStmt->fetch(PDO::FETCH_ASSOC);
    $merchantId = $storeData ? $storeData['merchant_id'] : 4; // Default à 4 si pas trouvé

    // Créer quelques commandes de test
    $testOrders = [
        [
            'merchant_id' => $merchantId,
            'store_id' => 3,
            'customer_name' => 'Ahmed Benali',
            'customer_email' => '<EMAIL>',
            'customer_phone' => '+213 555 123 456',
            'status' => 'delivered',
            'payment_method' => 'ccp',
            'payment_status' => 'paid',
            'subtotal' => 25000.00,
            'total_amount' => 25000.00,
            'order_number' => 'ORD-' . date('Y') . '-001'
        ],
        [
            'merchant_id' => $merchantId,
            'store_id' => 3,
            'customer_name' => 'Fatima Zohra',
            'customer_email' => '<EMAIL>',
            'customer_phone' => '+213 555 987 654',
            'status' => 'processing',
            'payment_method' => 'baridimob',
            'payment_status' => 'paid',
            'subtotal' => 45000.00,
            'total_amount' => 45000.00,
            'order_number' => 'ORD-' . date('Y') . '-002'
        ],
        [
            'merchant_id' => $merchantId,
            'store_id' => 3,
            'customer_name' => 'Mohamed Larbi',
            'customer_email' => '<EMAIL>',
            'customer_phone' => '+213 555 456 789',
            'status' => 'pending',
            'payment_method' => 'cod',
            'payment_status' => 'pending',
            'subtotal' => 15000.00,
            'total_amount' => 15000.00,
            'order_number' => 'ORD-' . date('Y') . '-003'
        ]
    ];

    // Supprimer les anciennes commandes de test
    $db->exec("DELETE FROM order_items WHERE order_id IN (SELECT id FROM orders WHERE store_id = 3)");
    $db->exec("DELETE FROM orders WHERE store_id = 3");

    foreach ($testOrders as $order) {
        $query = "
            INSERT INTO orders (
                merchant_id, store_id, customer_name, customer_email, customer_phone,
                status, payment_method, payment_status, subtotal, total_amount, order_number,
                created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
        ";

        $stmt = $db->prepare($query);
        $result = $stmt->execute([
            $order['merchant_id'],
            $order['store_id'],
            $order['customer_name'],
            $order['customer_email'],
            $order['customer_phone'],
            $order['status'],
            $order['payment_method'],
            $order['payment_status'],
            $order['subtotal'],
            $order['total_amount'],
            $order['order_number']
        ]);

        if ($result) {
            $orderId = $db->lastInsertId();
            echo "  ✅ Commande test '{$order['order_number']}' créée (ID: $orderId)\n";

            // Ajouter quelques articles à la commande
            $items = [
                ['product_name' => 'iPhone 15 Pro', 'quantity' => 1, 'unit_price' => $order['total_amount']],
            ];

            foreach ($items as $item) {
                $itemQuery = "
                    INSERT INTO order_items (
                        order_id, product_id, product_name, quantity, unit_price, total_price
                    ) VALUES (?, ?, ?, ?, ?, ?)
                ";

                $itemStmt = $db->prepare($itemQuery);
                $itemStmt->execute([
                    $orderId,
                    null, // product_id peut être null pour les produits de test
                    $item['product_name'],
                    $item['quantity'],
                    $item['unit_price'],
                    $item['unit_price'] * $item['quantity']
                ]);
            }
        } else {
            echo "  ❌ Erreur lors de la création de '{$order['order_number']}'\n";
        }
    }

    // Résumé final
    echo "\n📊 Résumé final:\n";
    $orderCount = $db->query("SELECT COUNT(*) FROM orders WHERE store_id = 3")->fetchColumn();
    $productCount = $db->query("SELECT COUNT(*) FROM products WHERE store_id = 3")->fetchColumn();
    $userCount = $db->query("SELECT COUNT(*) FROM users")->fetchColumn();

    echo "  - Commandes de test: $orderCount\n";
    echo "  - Produits: $productCount\n";
    echo "  - Utilisateurs: $userCount\n";

    echo "\n✅ Test et correction des APIs terminés!\n";
    echo "🔗 Testez maintenant le dashboard: http://localhost:8000/seller-dashboard.html\n";
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
    echo "📍 Fichier: " . $e->getFile() . " ligne " . $e->getLine() . "\n";
}
