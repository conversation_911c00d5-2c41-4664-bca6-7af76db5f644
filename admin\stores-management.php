<?php
/**
 * Gestion des Stores/Magasins
 * Interface d'administration pour gérer les marchands et leurs boutiques
 */

require_once '../config/database.php';
require_once '../php/QuotaManager.php';

session_start();

// Vérification de l'authentification admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../auth/login.php');
    exit;
}

$lang = $_GET['lang'] ?? $_SESSION['language'] ?? 'ar';
$action = $_GET['action'] ?? 'list';
$store_id = $_GET['id'] ?? null;

// Traductions
$translations = [
    'ar' => [
        'title' => 'إدارة المتاجر',
        'stores_list' => 'قائمة المتاجر',
        'add_store' => 'إضافة متجر جديد',
        'edit_store' => 'تعديل المتجر',
        'store_details' => 'تفاصيل المتجر',
        'store_name' => 'اسم المتجر',
        'owner_name' => 'اسم المالك',
        'email' => 'البريد الإلكتروني',
        'phone' => 'رقم الهاتف',
        'status' => 'الحالة',
        'subscription' => 'الاشتراك',
        'products_count' => 'عدد المنتجات',
        'landing_pages_count' => 'عدد صفحات الهبوط',
        'created_at' => 'تاريخ الإنشاء',
        'actions' => 'الإجراءات',
        'active' => 'نشط',
        'inactive' => 'غير نشط',
        'suspended' => 'معلق',
        'view' => 'عرض',
        'edit' => 'تعديل',
        'delete' => 'حذف',
        'activate' => 'تفعيل',
        'suspend' => 'تعليق',
        'save' => 'حفظ',
        'cancel' => 'إلغاء',
        'search' => 'بحث',
        'filter_by_status' => 'تصفية حسب الحالة',
        'all_statuses' => 'جميع الحالات',
        'export' => 'تصدير',
        'bulk_actions' => 'إجراءات مجمعة',
        'select_all' => 'تحديد الكل',
        'password' => 'كلمة المرور',
        'confirm_password' => 'تأكيد كلمة المرور',
        'role' => 'الدور',
        'seller' => 'بائع',
        'agent' => 'وكيل',
        'language' => 'اللغة',
        'arabic' => 'العربية',
        'french' => 'الفرنسية',
        'english' => 'الإنجليزية'
    ],
    'fr' => [
        'title' => 'Gestion des Magasins',
        'stores_list' => 'Liste des Magasins',
        'add_store' => 'Ajouter un Magasin',
        'edit_store' => 'Modifier le Magasin',
        'store_details' => 'Détails du Magasin',
        'store_name' => 'Nom du Magasin',
        'owner_name' => 'Nom du Propriétaire',
        'email' => 'Email',
        'phone' => 'Téléphone',
        'status' => 'Statut',
        'subscription' => 'Abonnement',
        'products_count' => 'Nombre de Produits',
        'landing_pages_count' => 'Nombre de Landing Pages',
        'created_at' => 'Date de Création',
        'actions' => 'Actions',
        'active' => 'Actif',
        'inactive' => 'Inactif',
        'suspended' => 'Suspendu',
        'view' => 'Voir',
        'edit' => 'Modifier',
        'delete' => 'Supprimer',
        'activate' => 'Activer',
        'suspend' => 'Suspendre',
        'save' => 'Enregistrer',
        'cancel' => 'Annuler',
        'search' => 'Rechercher',
        'filter_by_status' => 'Filtrer par Statut',
        'all_statuses' => 'Tous les Statuts',
        'export' => 'Exporter',
        'bulk_actions' => 'Actions en Lot',
        'select_all' => 'Tout Sélectionner',
        'password' => 'Mot de Passe',
        'confirm_password' => 'Confirmer le Mot de Passe',
        'role' => 'Rôle',
        'seller' => 'Vendeur',
        'agent' => 'Agent',
        'language' => 'Langue',
        'arabic' => 'Arabe',
        'french' => 'Français',
        'english' => 'Anglais'
    ],
    'en' => [
        'title' => 'Stores Management',
        'stores_list' => 'Stores List',
        'add_store' => 'Add Store',
        'edit_store' => 'Edit Store',
        'store_details' => 'Store Details',
        'store_name' => 'Store Name',
        'owner_name' => 'Owner Name',
        'email' => 'Email',
        'phone' => 'Phone',
        'status' => 'Status',
        'subscription' => 'Subscription',
        'products_count' => 'Products Count',
        'landing_pages_count' => 'Landing Pages Count',
        'created_at' => 'Created At',
        'actions' => 'Actions',
        'active' => 'Active',
        'inactive' => 'Inactive',
        'suspended' => 'Suspended',
        'view' => 'View',
        'edit' => 'Edit',
        'delete' => 'Delete',
        'activate' => 'Activate',
        'suspend' => 'Suspend',
        'save' => 'Save',
        'cancel' => 'Cancel',
        'search' => 'Search',
        'filter_by_status' => 'Filter by Status',
        'all_statuses' => 'All Statuses',
        'export' => 'Export',
        'bulk_actions' => 'Bulk Actions',
        'select_all' => 'Select All',
        'password' => 'Password',
        'confirm_password' => 'Confirm Password',
        'role' => 'Role',
        'seller' => 'Seller',
        'agent' => 'Agent',
        'language' => 'Language',
        'arabic' => 'Arabic',
        'french' => 'French',
        'english' => 'English'
    ]
];

$t = $translations[$lang];

// Traitement des actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if ($action === 'add' || $action === 'edit') {
            $name = $_POST['name'] ?? '';
            $email = $_POST['email'] ?? '';
            $phone = $_POST['phone'] ?? '';
            $role = $_POST['role'] ?? 'seller';
            $status = $_POST['status'] ?? 'active';
            $language = $_POST['language'] ?? 'ar';
            $subscription_id = $_POST['subscription_id'] ?? 1;
            
            if ($action === 'add') {
                $password = password_hash($_POST['password'], PASSWORD_DEFAULT);
                
                $stmt = $pdo->prepare("
                    INSERT INTO users (name, email, phone, password, role, status, language, subscription_id, subscription_start, subscription_status) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), 'active')
                ");
                $stmt->execute([$name, $email, $phone, $password, $role, $status, $language, $subscription_id]);
                
                $message = "Magasin ajouté avec succès!";
            } else {
                $stmt = $pdo->prepare("
                    UPDATE users 
                    SET name = ?, email = ?, phone = ?, role = ?, status = ?, language = ?, subscription_id = ?
                    WHERE id = ?
                ");
                $stmt->execute([$name, $email, $phone, $role, $status, $language, $subscription_id, $store_id]);
                
                if (!empty($_POST['password'])) {
                    $password = password_hash($_POST['password'], PASSWORD_DEFAULT);
                    $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE id = ?");
                    $stmt->execute([$password, $store_id]);
                }
                
                $message = "Magasin modifié avec succès!";
            }
            
            header('Location: stores-management.php?success=1');
            exit;
        }
        
        if ($action === 'delete' && $store_id) {
            $stmt = $pdo->prepare("DELETE FROM users WHERE id = ? AND role IN ('seller', 'agent')");
            $stmt->execute([$store_id]);
            
            header('Location: stores-management.php?deleted=1');
            exit;
        }
        
        if ($action === 'toggle_status' && $store_id) {
            $stmt = $pdo->prepare("SELECT status FROM users WHERE id = ?");
            $stmt->execute([$store_id]);
            $current_status = $stmt->fetchColumn();
            
            $new_status = ($current_status === 'active') ? 'suspended' : 'active';
            
            $stmt = $pdo->prepare("UPDATE users SET status = ? WHERE id = ?");
            $stmt->execute([$new_status, $store_id]);
            
            header('Location: stores-management.php?status_changed=1');
            exit;
        }
        
    } catch (Exception $e) {
        $error = "Erreur: " . $e->getMessage();
    }
}

// Récupération des données
$search = $_GET['search'] ?? '';
$status_filter = $_GET['status'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = 20;
$offset = ($page - 1) * $per_page;

// Construction de la requête
$where_conditions = ["role IN ('seller', 'agent')"];
$params = [];

if ($search) {
    $where_conditions[] = "(name LIKE ? OR email LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if ($status_filter) {
    $where_conditions[] = "status = ?";
    $params[] = $status_filter;
}

$where_clause = implode(' AND ', $where_conditions);

// Récupération des stores avec statistiques
$stores_query = "
    SELECT u.*, s.name as subscription_name,
           COALESCE(p.products_count, 0) as products_count,
           COALESCE(lp.landing_pages_count, 0) as landing_pages_count
    FROM users u
    LEFT JOIN subscriptions s ON u.subscription_id = s.id
    LEFT JOIN (
        SELECT user_id, COUNT(*) as products_count 
        FROM products 
        GROUP BY user_id
    ) p ON u.id = p.user_id
    LEFT JOIN (
        SELECT user_id, COUNT(*) as landing_pages_count 
        FROM landing_pages 
        GROUP BY user_id
    ) lp ON u.id = lp.user_id
    WHERE $where_clause
    ORDER BY u.created_at DESC
    LIMIT $per_page OFFSET $offset
";

$stores = $pdo->prepare($stores_query);
$stores->execute($params);
$stores = $stores->fetchAll(PDO::FETCH_ASSOC);

// Comptage total pour pagination
$total_query = "SELECT COUNT(*) FROM users WHERE $where_clause";
$total_stmt = $pdo->prepare($total_query);
$total_stmt->execute($params);
$total_stores = $total_stmt->fetchColumn();
$total_pages = ceil($total_stores / $per_page);

// Récupération des abonnements pour le formulaire
$subscriptions = $pdo->query("SELECT * FROM subscriptions WHERE is_active = 1 ORDER BY sort_order")->fetchAll(PDO::FETCH_ASSOC);

// Récupération des détails d'un store spécifique pour édition
$store_details = null;
if (($action === 'edit' || $action === 'view') && $store_id) {
    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ? AND role IN ('seller', 'agent')");
    $stmt->execute([$store_id]);
    $store_details = $stmt->fetch(PDO::FETCH_ASSOC);
}
?>

<!DOCTYPE html>
<html lang="<?= $lang ?>" dir="<?= $lang === 'ar' ? 'rtl' : 'ltr' ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $t['title'] ?> - Landing Pages SaaS</title>
    
    <!-- Bootstrap 5 RTL Support -->
    <?php if ($lang === 'ar'): ?>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <?php else: ?>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <?php endif; ?>
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- DataTables -->
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #64748b;
            --success-color: #059669;
            --warning-color: #d97706;
            --danger-color: #dc2626;
        }
        
        body {
            background-color: #f8fafc;
            font-family: <?= $lang === 'ar' ? '\'Cairo\', \'Segoe UI\'' : '\'Inter\', \'Segoe UI\'' ?>, sans-serif;
        }
        
        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), #1e40af);
            border: none;
            border-radius: 8px;
        }
        
        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
        }
        
        .status-active {
            background-color: #dcfce7;
            color: #166534;
        }
        
        .status-inactive {
            background-color: #fef3c7;
            color: #92400e;
        }
        
        .status-suspended {
            background-color: #fee2e2;
            color: #991b1b;
        }
        
        .table th {
            background-color: #f8fafc;
            border-bottom: 2px solid #e2e8f0;
            font-weight: 600;
            color: #374151;
        }
        
        .action-buttons .btn {
            margin: 0 2px;
            padding: 6px 12px;
            border-radius: 6px;
        }
        
        .search-filters {
            background: white;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center py-3 mb-4">
            <div>
                <h1 class="h3 mb-0"><?= $t['title'] ?></h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item"><a href="index.php">Dashboard</a></li>
                        <li class="breadcrumb-item active"><?= $t['title'] ?></li>
                    </ol>
                </nav>
            </div>
            <div>
                <a href="?action=add" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    <?= $t['add_store'] ?>
                </a>
            </div>
        </div>
        
        <!-- Messages -->
        <?php if (isset($_GET['success'])): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                Opération réussie!
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
        
        <?php if (isset($error)): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i>
                <?= htmlspecialchars($error) ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
        
        <?php if ($action === 'list' || $action === ''): ?>
            <!-- Filters -->
            <div class="search-filters">
                <form method="GET" class="row g-3">
                    <div class="col-md-4">
                        <label class="form-label"><?= $t['search'] ?></label>
                        <input type="text" class="form-control" name="search" value="<?= htmlspecialchars($search) ?>" placeholder="Nom, email...">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label"><?= $t['filter_by_status'] ?></label>
                        <select class="form-select" name="status">
                            <option value=""><?= $t['all_statuses'] ?></option>
                            <option value="active" <?= $status_filter === 'active' ? 'selected' : '' ?>><?= $t['active'] ?></option>
                            <option value="inactive" <?= $status_filter === 'inactive' ? 'selected' : '' ?>><?= $t['inactive'] ?></option>
                            <option value="suspended" <?= $status_filter === 'suspended' ? 'selected' : '' ?>><?= $t['suspended'] ?></option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-1"></i>
                                <?= $t['search'] ?>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <a href="?" class="btn btn-outline-secondary me-2">
                                <i class="fas fa-undo me-1"></i>
                                Reset
                            </a>
                            <button type="button" class="btn btn-outline-success" onclick="exportData()">
                                <i class="fas fa-download me-1"></i>
                                <?= $t['export'] ?>
                            </button>
                        </div>
                    </div>
                </form>
            </div>
            
            <!-- Stores Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-store me-2"></i>
                        <?= $t['stores_list'] ?> (<?= number_format($total_stores) ?>)
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="storesTable">
                            <thead>
                                <tr>
                                    <th><input type="checkbox" id="selectAll"></th>
                                    <th><?= $t['store_name'] ?></th>
                                    <th><?= $t['email'] ?></th>
                                    <th><?= $t['phone'] ?></th>
                                    <th><?= $t['role'] ?></th>
                                    <th><?= $t['status'] ?></th>
                                    <th><?= $t['subscription'] ?></th>
                                    <th><?= $t['products_count'] ?></th>
                                    <th><?= $t['landing_pages_count'] ?></th>
                                    <th><?= $t['created_at'] ?></th>
                                    <th><?= $t['actions'] ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($stores as $store): ?>
                                    <tr>
                                        <td><input type="checkbox" name="selected_stores[]" value="<?= $store['id'] ?>"></td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3">
                                                    <?= strtoupper(substr($store['name'], 0, 2)) ?>
                                                </div>
                                                <div>
                                                    <strong><?= htmlspecialchars($store['name']) ?></strong>
                                                    <br>
                                                    <small class="text-muted">ID: <?= $store['id'] ?></small>
                                                </div>
                                            </div>
                                        </td>
                                        <td><?= htmlspecialchars($store['email']) ?></td>
                                        <td><?= htmlspecialchars($store['phone'] ?? '-') ?></td>
                                        <td>
                                            <span class="badge bg-info">
                                                <?= $store['role'] === 'seller' ? $t['seller'] : $t['agent'] ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="status-badge status-<?= $store['status'] ?>">
                                                <?= $t[$store['status']] ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">
                                                <?= htmlspecialchars($store['subscription_name'] ?? 'Aucun') ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary">
                                                <?= number_format($store['products_count']) ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-warning">
                                                <?= number_format($store['landing_pages_count']) ?>
                                            </span>
                                        </td>
                                        <td>
                                            <small><?= date('d/m/Y', strtotime($store['created_at'])) ?></small>
                                        </td>
                                        <td>
                                            <div class="action-buttons">
                                                <a href="?action=view&id=<?= $store['id'] ?>" class="btn btn-sm btn-outline-info" title="<?= $t['view'] ?>">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="?action=edit&id=<?= $store['id'] ?>" class="btn btn-sm btn-outline-primary" title="<?= $t['edit'] ?>">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button type="button" class="btn btn-sm btn-outline-<?= $store['status'] === 'active' ? 'warning' : 'success' ?>" 
                                                        onclick="toggleStatus(<?= $store['id'] ?>)" 
                                                        title="<?= $store['status'] === 'active' ? $t['suspend'] : $t['activate'] ?>">
                                                    <i class="fas fa-<?= $store['status'] === 'active' ? 'pause' : 'play' ?>"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-danger" 
                                                        onclick="deleteStore(<?= $store['id'] ?>)" 
                                                        title="<?= $t['delete'] ?>">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <?php if ($total_pages > 1): ?>
                        <nav aria-label="Pagination">
                            <ul class="pagination justify-content-center">
                                <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                                    <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                                        <a class="page-link" href="?page=<?= $i ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status_filter) ?>">
                                            <?= $i ?>
                                        </a>
                                    </li>
                                <?php endfor; ?>
                            </ul>
                        </nav>
                    <?php endif; ?>
                </div>
            </div>
            
        <?php elseif ($action === 'add' || $action === 'edit'): ?>
            <!-- Add/Edit Form -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-<?= $action === 'add' ? 'plus' : 'edit' ?> me-2"></i>
                        <?= $action === 'add' ? $t['add_store'] : $t['edit_store'] ?>
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label"><?= $t['store_name'] ?> *</label>
                            <input type="text" class="form-control" name="name" 
                                   value="<?= htmlspecialchars($store_details['name'] ?? '') ?>" required>
                        </div>
                        
                        <div class="col-md-6">
                            <label class="form-label"><?= $t['email'] ?> *</label>
                            <input type="email" class="form-control" name="email" 
                                   value="<?= htmlspecialchars($store_details['email'] ?? '') ?>" required>
                        </div>
                        
                        <div class="col-md-6">
                            <label class="form-label"><?= $t['phone'] ?></label>
                            <input type="tel" class="form-control" name="phone" 
                                   value="<?= htmlspecialchars($store_details['phone'] ?? '') ?>">
                        </div>
                        
                        <div class="col-md-6">
                            <label class="form-label"><?= $t['role'] ?> *</label>
                            <select class="form-select" name="role" required>
                                <option value="seller" <?= ($store_details['role'] ?? '') === 'seller' ? 'selected' : '' ?>>
                                    <?= $t['seller'] ?>
                                </option>
                                <option value="agent" <?= ($store_details['role'] ?? '') === 'agent' ? 'selected' : '' ?>>
                                    <?= $t['agent'] ?>
                                </option>
                            </select>
                        </div>
                        
                        <div class="col-md-6">
                            <label class="form-label"><?= $t['status'] ?> *</label>
                            <select class="form-select" name="status" required>
                                <option value="active" <?= ($store_details['status'] ?? '') === 'active' ? 'selected' : '' ?>>
                                    <?= $t['active'] ?>
                                </option>
                                <option value="inactive" <?= ($store_details['status'] ?? '') === 'inactive' ? 'selected' : '' ?>>
                                    <?= $t['inactive'] ?>
                                </option>
                                <option value="suspended" <?= ($store_details['status'] ?? '') === 'suspended' ? 'selected' : '' ?>>
                                    <?= $t['suspended'] ?>
                                </option>
                            </select>
                        </div>
                        
                        <div class="col-md-6">
                            <label class="form-label"><?= $t['language'] ?> *</label>
                            <select class="form-select" name="language" required>
                                <option value="ar" <?= ($store_details['language'] ?? '') === 'ar' ? 'selected' : '' ?>>
                                    <?= $t['arabic'] ?>
                                </option>
                                <option value="fr" <?= ($store_details['language'] ?? '') === 'fr' ? 'selected' : '' ?>>
                                    <?= $t['french'] ?>
                                </option>
                                <option value="en" <?= ($store_details['language'] ?? '') === 'en' ? 'selected' : '' ?>>
                                    <?= $t['english'] ?>
                                </option>
                            </select>
                        </div>
                        
                        <div class="col-md-6">
                            <label class="form-label"><?= $t['subscription'] ?> *</label>
                            <select class="form-select" name="subscription_id" required>
                                <?php foreach ($subscriptions as $subscription): ?>
                                    <option value="<?= $subscription['id'] ?>" 
                                            <?= ($store_details['subscription_id'] ?? 1) == $subscription['id'] ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($subscription['name']) ?> - $<?= $subscription['price'] ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="col-md-6">
                            <label class="form-label"><?= $t['password'] ?> <?= $action === 'add' ? '*' : '(laisser vide pour ne pas changer)' ?></label>
                            <input type="password" class="form-control" name="password" <?= $action === 'add' ? 'required' : '' ?>>
                        </div>
                        
                        <?php if ($action === 'add'): ?>
                            <div class="col-md-6">
                                <label class="form-label"><?= $t['confirm_password'] ?> *</label>
                                <input type="password" class="form-control" name="confirm_password" required>
                            </div>
                        <?php endif; ?>
                        
                        <div class="col-12">
                            <hr>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>
                                    <?= $t['save'] ?>
                                </button>
                                <a href="stores-management.php" class="btn btn-secondary">
                                    <i class="fas fa-times me-2"></i>
                                    <?= $t['cancel'] ?>
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            
        <?php elseif ($action === 'view' && $store_details): ?>
            <!-- View Details -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-eye me-2"></i>
                        <?= $t['store_details'] ?>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <th><?= $t['store_name'] ?>:</th>
                                    <td><?= htmlspecialchars($store_details['name']) ?></td>
                                </tr>
                                <tr>
                                    <th><?= $t['email'] ?>:</th>
                                    <td><?= htmlspecialchars($store_details['email']) ?></td>
                                </tr>
                                <tr>
                                    <th><?= $t['phone'] ?>:</th>
                                    <td><?= htmlspecialchars($store_details['phone'] ?? '-') ?></td>
                                </tr>
                                <tr>
                                    <th><?= $t['role'] ?>:</th>
                                    <td>
                                        <span class="badge bg-info">
                                            <?= $store_details['role'] === 'seller' ? $t['seller'] : $t['agent'] ?>
                                        </span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <th><?= $t['status'] ?>:</th>
                                    <td>
                                        <span class="status-badge status-<?= $store_details['status'] ?>">
                                            <?= $t[$store_details['status']] ?>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <th><?= $t['language'] ?>:</th>
                                    <td><?= strtoupper($store_details['language']) ?></td>
                                </tr>
                                <tr>
                                    <th><?= $t['created_at'] ?>:</th>
                                    <td><?= date('d/m/Y H:i', strtotime($store_details['created_at'])) ?></td>
                                </tr>
                                <tr>
                                    <th>Dernière connexion:</th>
                                    <td><?= $store_details['last_login_at'] ? date('d/m/Y H:i', strtotime($store_details['last_login_at'])) : 'Jamais' ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    <div class="mt-3">
                        <a href="?action=edit&id=<?= $store_details['id'] ?>" class="btn btn-primary">
                            <i class="fas fa-edit me-2"></i>
                            <?= $t['edit'] ?>
                        </a>
                        <a href="stores-management.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>
                            Retour
                        </a>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- DataTables -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    
    <script>
        // Initialize DataTable
        $(document).ready(function() {
            $('#storesTable').DataTable({
                "pageLength": 20,
                "language": {
                    "url": "<?= $lang === 'ar' ? '//cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json' : ($lang === 'fr' ? '//cdn.datatables.net/plug-ins/1.13.6/i18n/fr-FR.json' : '') ?>"
                },
                "order": [[9, "desc"]], // Sort by created_at
                "columnDefs": [
                    { "orderable": false, "targets": [0, 10] } // Disable sorting for checkbox and actions
                ]
            });
        });
        
        // Select all checkbox
        document.getElementById('selectAll').addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('input[name="selected_stores[]"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
        });
        
        // Toggle status function
        function toggleStatus(storeId) {
            if (confirm('Êtes-vous sûr de vouloir changer le statut de ce magasin?')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = '?action=toggle_status&id=' + storeId;
                document.body.appendChild(form);
                form.submit();
            }
        }
        
        // Delete store function
        function deleteStore(storeId) {
            if (confirm('Êtes-vous sûr de vouloir supprimer ce magasin? Cette action est irréversible.')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = '?action=delete&id=' + storeId;
                document.body.appendChild(form);
                form.submit();
            }
        }
        
        // Export function
        function exportData() {
            const selectedStores = [];
            document.querySelectorAll('input[name="selected_stores[]"]:checked').forEach(checkbox => {
                selectedStores.push(checkbox.value);
            });
            
            let url = 'export-stores.php?';
            if (selectedStores.length > 0) {
                url += 'ids=' + selectedStores.join(',');
            } else {
                url += 'all=1';
            }
            
            window.open(url, '_blank');
        }
        
        // Password confirmation validation
        <?php if ($action === 'add'): ?>
        document.querySelector('form').addEventListener('submit', function(e) {
            const password = document.querySelector('input[name="password"]').value;
            const confirmPassword = document.querySelector('input[name="confirm_password"]').value;
            
            if (password !== confirmPassword) {
                e.preventDefault();
                alert('Les mots de passe ne correspondent pas!');
            }
        });
        <?php endif; ?>
    </script>
</body>
</html>