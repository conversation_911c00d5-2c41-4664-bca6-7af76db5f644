<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .result { background: #f5f5f5; padding: 10px; margin: 10px 0; }
        button { padding: 10px 15px; margin: 5px; }
    </style>
</head>
<body>
    <h1>Products API Test</h1>
    
    <div class="test-section">
        <h3>Test 1: Get Products</h3>
        <button onclick="testGetProducts()">Get Products</button>
        <div id="getProductsResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>Test 2: Create Product</h3>
        <button onclick="testCreateProduct()">Create Product</button>
        <div id="createProductResult" class="result"></div>
    </div>

    <script>
        async function testGetProducts() {
            try {
                const response = await fetch('/api/products.php', {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer demo_token',
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                document.getElementById('getProductsResult').innerHTML = 
                    `<strong>Status:</strong> ${response.status}<br>
                     <strong>Response:</strong><br>
                     <pre>${JSON.stringify(data, null, 2)}</pre>`;
            } catch (error) {
                document.getElementById('getProductsResult').innerHTML = 
                    `<strong>Error:</strong> ${error.message}`;
            }
        }
        
        async function testCreateProduct() {
            const productData = {
                name: 'Test Product',
                name_ar: 'منتج تجريبي',
                name_fr: 'Produit de test',
                name_en: 'Test Product',
                description_ar: 'وصف المنتج التجريبي',
                price: 15000.00,
                stock_quantity: 10,
                status: 'active',
                featured: 1
            };
            
            try {
                const response = await fetch('/api/products.php', {
                    method: 'POST',
                    headers: {
                        'Authorization': 'Bearer demo_token',
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(productData)
                });
                
                const data = await response.json();
                document.getElementById('createProductResult').innerHTML = 
                    `<strong>Status:</strong> ${response.status}<br>
                     <strong>Response:</strong><br>
                     <pre>${JSON.stringify(data, null, 2)}</pre>`;
            } catch (error) {
                document.getElementById('createProductResult').innerHTML = 
                    `<strong>Error:</strong> ${error.message}`;
            }
        }
    </script>
</body>
</html>
