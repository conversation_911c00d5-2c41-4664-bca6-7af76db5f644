<?php

/**
 * API simple pour les stores
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// <PERSON>le preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Configuration de la base de données
$host = 'localhost';
$port = 3307;
$dbname = 'landingpage_new';
$username = 'root';
$password = '';

try {
    // Connexion à la base de données
    $pdo = new PDO(
        "mysql:host={$host};port={$port};dbname={$dbname};charset=utf8mb4",
        $username,
        $password,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]
    );

    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // Vérifier s'il y a un ID dans l'URL pour récupérer un store spécifique
        $pathParts = explode('/', trim($_SERVER['REQUEST_URI'], '/'));
        $storeId = null;

        // Chercher un ID numérique dans l'URL
        foreach ($pathParts as $part) {
            if (is_numeric($part)) {
                $storeId = $part;
                break;
            }
        }

        if ($storeId) {
            // Récupérer un store spécifique avec le nombre de produits
            $query = "SELECT s.*, COUNT(p.id) as product_count 
                     FROM stores s 
                     LEFT JOIN products p ON s.id = p.store_id 
                     WHERE s.id = ? 
                     GROUP BY s.id";
            $stmt = $pdo->prepare($query);
            $stmt->execute([$storeId]);
            $store = $stmt->fetch();

            if (!$store) {
                throw new Exception('Store non trouvé');
            }

            $formattedStore = [
                'id' => $store['id'],
                'name_ar' => $store['store_name_ar'] ?? '',
                'name_en' => $store['store_name_en'] ?? '',
                'domain' => $store['domain'] ?? '',
                'status' => $store['status'] ?? 'active',
                'created_at' => $store['created_at'] ?? date('Y-m-d H:i:s'),
                'user_id' => $store['user_id'] ?? null,
                'description_ar' => $store['description_ar'] ?? '',
                'description_en' => $store['description_en'] ?? '',
                'product_count' => (int)$store['product_count']
            ];

            echo json_encode([
                'success' => true,
                'data' => $formattedStore
            ]);
        } else {
            // Récupérer tous les stores avec le nombre de produits
            $query = "SELECT s.*, COUNT(p.id) as product_count 
                     FROM stores s 
                     LEFT JOIN products p ON s.id = p.store_id 
                     GROUP BY s.id 
                     ORDER BY s.created_at DESC";
            $stmt = $pdo->query($query);
            $stores = $stmt->fetchAll();

        // Formater les données pour le frontend
        $formattedStores = array_map(function ($store) {
            return [
                'id' => $store['id'],
                'name' => $store['store_name'] ?? $store['name'] ?? 'Store sans nom',
                'status' => $store['status'] ?? 'active',
                'created_at' => $store['created_at'] ?? date('Y-m-d H:i:s'),
                'user_id' => $store['user_id'] ?? null,
                'description' => $store['description'] ?? '',
                'products_count' => 0 // À calculer si nécessaire
            ];
        }, $stores);

        echo json_encode([
            'success' => true,
            'data' => [
                'stores' => $formattedStores,
                'total' => count($formattedStores)
            ]
        ]);
    } elseif ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Créer un nouveau store
        $input = json_decode(file_get_contents('php://input'), true);

        $name = $input['name'] ?? '';
        $description = $input['description'] ?? '';
        $user_id = $input['user_id'] ?? 1; // Valeur par défaut

        if (empty($name)) {
            throw new Exception('Le nom du store est requis');
        }

        $stmt = $pdo->prepare("INSERT INTO stores (store_name, description, user_id, status, created_at) VALUES (?, ?, ?, 'active', NOW())");
        $stmt->execute([$name, $description, $user_id]);

        $newId = $pdo->lastInsertId();

        echo json_encode([
            'success' => true,
            'message' => 'Store créé avec succès',
            'data' => ['id' => $newId]
        ]);
    } elseif ($_SERVER['REQUEST_METHOD'] === 'PUT') {
        // Mettre à jour un store
        $pathParts = explode('/', trim($_SERVER['REQUEST_URI'], '/'));
        $storeId = end($pathParts);

        if (!is_numeric($storeId)) {
            throw new Exception('ID du store invalide');
        }

        $input = json_decode(file_get_contents('php://input'), true);

        $name = $input['name'] ?? '';
        $description = $input['description'] ?? '';

        if (empty($name)) {
            throw new Exception('Le nom du store est requis');
        }

        $stmt = $pdo->prepare("UPDATE stores SET store_name = ?, description = ? WHERE id = ?");
        $stmt->execute([$name, $description, $storeId]);

        if ($stmt->rowCount() === 0) {
            throw new Exception('Store non trouvé ou aucune modification effectuée');
        }

        echo json_encode([
            'success' => true,
            'message' => 'Store mis à jour avec succès'
        ]);
    } elseif ($_SERVER['REQUEST_METHOD'] === 'DELETE') {
        // Supprimer un store
        $pathParts = explode('/', trim($_SERVER['REQUEST_URI'], '/'));
        $storeId = end($pathParts);

        if (!is_numeric($storeId)) {
            throw new Exception('ID du store invalide');
        }

        $stmt = $pdo->prepare("DELETE FROM stores WHERE id = ?");
        $stmt->execute([$storeId]);

        if ($stmt->rowCount() === 0) {
            throw new Exception('Store non trouvé');
        }

        echo json_encode([
            'success' => true,
            'message' => 'Store supprimé avec succès'
        ]);
    } else {
        throw new Exception('Méthode non supportée');
    }
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Erreur de base de données: ' . $e->getMessage()
    ]);
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
