<?php

error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/error.log');

/**
 * API simple pour les stores
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Configuration de la base de données
$host = 'localhost';
$port = 3307;
$dbname = 'landingpage_new';
$username = 'root';
$password = '';

try {
    // Test de connexion à la base de données avec gestion d'erreur détaillée
    try {
        $dsn = "mysql:host={$host};port={$port};dbname={$dbname};charset=utf8mb4";
        error_log("Tentative de connexion avec DSN: " . $dsn);
        
        $pdo = new PDO($dsn, $username, $password, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]);
    } catch (PDOException $e) {
        error_log("Erreur de connexion PDO: " . $e->getMessage());
        throw new Exception("Erreur de connexion à la base de données: " . $e->getMessage());
    }

    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // Vérifier s'il y a un ID dans les paramètres GET ou dans l'URL
        $storeId = $_GET['id'] ?? null;

        // Si pas d'ID dans GET, chercher dans l'URL
        if (!$storeId) {
            $pathParts = explode('/', trim($_SERVER['REQUEST_URI'], '/'));
            foreach ($pathParts as $part) {
                if (is_numeric($part)) {
                    $storeId = $part;
                    break;
                }
            }
        }

        if ($storeId) {
            $query = "SELECT s.*, COUNT(p.id) as product_count,
                            COALESCE(m.name, 'Propriétaire inconnu') as owner_name,
                            m.email as owner_email
                     FROM stores s
                     LEFT JOIN products p ON s.id = p.store_id
                     LEFT JOIN merchants m ON s.merchant_id = m.id
                     WHERE s.id = ?
                     GROUP BY s.id";
            try {
                $stmt = $pdo->prepare($query);
                $stmt->execute([$storeId]);
                $store = $stmt->fetch();
            } catch (PDOException $e) {
                error_log("Erreur lors de l'exécution de la requête: " . $e->getMessage());
                throw new Exception("Erreur lors de la récupération du store: " . $e->getMessage());
            }

            if (!$store) {
                throw new Exception('Store non trouvé');
            }

            $formattedStore = [
                'id' => (int)$store['id'],
                'store_name_ar' => $store['store_name_ar'] ?? '',
                'store_name_en' => $store['store_name_en'] ?? '',
                'name_ar' => $store['store_name_ar'] ?? '',
                'name_en' => $store['store_name_en'] ?? '',
                'store_name' => $store['store_name'] ?? '',
                'domain' => $store['domain'] ?? '',
                'subdomain' => $store['subdomain'] ?? '',
                'status' => $store['status'] ?? 'active',
                'created_at' => $store['created_at'] ?? date('Y-m-d H:i:s'),
                'merchant_id' => $store['merchant_id'] ?? null,
                'description_ar' => $store['description_ar'] ?? '',
                'description_en' => $store['description_en'] ?? '',
                'description' => $store['description'] ?? '',
                'product_count' => (int)$store['product_count'],
                'owner_name' => $store['owner_name'] ?? 'Propriétaire inconnu',
                'owner_email' => $store['owner_email'] ?? ''
            ];

            echo json_encode([
                'success' => true,
                'data' => $formattedStore
            ]);
        } else {
            try {
                $query = "SELECT s.*, COUNT(p.id) as product_count,
                                COALESCE(m.name, 'Propriétaire inconnu') as owner_name,
                                m.email as owner_email
                         FROM stores s
                         LEFT JOIN products p ON s.id = p.store_id
                         LEFT JOIN merchants m ON s.merchant_id = m.id
                         GROUP BY s.id
                         ORDER BY s.created_at DESC";
                $stmt = $pdo->query($query);
                $stores = $stmt->fetchAll();
            } catch (PDOException $e) {
                error_log("Erreur lors de l'exécution de la requête: " . $e->getMessage());
                throw new Exception("Erreur lors de la récupération des stores: " . $e->getMessage());
            }

            $formattedStores = array_map(function ($store) {
                return [
                    'id' => (int)$store['id'],
                    'store_name' => $store['store_name'] ?? 'Store sans nom',
                    'store_name_ar' => $store['store_name_ar'] ?? '',
                    'store_name_en' => $store['store_name_en'] ?? '',
                    'name_ar' => $store['store_name_ar'] ?? '',
                    'name_en' => $store['store_name_en'] ?? '',
                    'domain' => $store['domain'] ?? '',
                    'subdomain' => $store['subdomain'] ?? '',
                    'status' => $store['status'] ?? 'active',
                    'created_at' => $store['created_at'] ?? date('Y-m-d H:i:s'),
                    'merchant_id' => $store['merchant_id'] ?? null,
                    'description' => $store['description'] ?? '',
                    'description_ar' => $store['description_ar'] ?? '',
                    'description_en' => $store['description_en'] ?? '',
                    'product_count' => (int)$store['product_count'],
                    'owner_name' => $store['owner_name'] ?? 'Propriétaire inconnu',
                    'owner_email' => $store['owner_email'] ?? ''
                ];
            }, $stores);

            echo json_encode([
                'success' => true,
                'data' => [
                    'stores' => $formattedStores,
                    'total' => count($formattedStores)
                ]
            ]);
        }
    }
} catch (Exception $e) {
    error_log("Exception générale: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ]);
}
