/**
 * Test automatisé pour la page d'accueil (landing page)
 * Utilise Puppeteer pour tester les fonctionnalités de la page principale
 */

const PageController = require('../page-controller');
const chalk = require('chalk');

class HomepageTest {
    constructor(options = {}) {
        this.controller = new PageController({
            headless: false, // Mode visible pour les tests
            slowMo: 200,
            debug: true,
            ...options
        });
    }
    
    /**
     * Exécuter tous les tests de la page d'accueil
     */
    async runAllTests() {
        console.log(chalk.blue.bold('🧪 === TESTS DE LA PAGE D\'ACCUEIL ==='));
        
        try {
            await this.controller.init();
            
            // Tests principaux
            await this.testPageLoad();
            await this.testLanguageSwitching();
            await this.testNavigationButtons();
            await this.testStartNowButtons();
            await this.testHeroSection();
            await this.testFeaturesSection();
            await this.testCTASection();
            await this.testResponsiveDesign();
            await this.testAccessibility();
            await this.testPerformance();
            await this.testSmoothScrolling();
            
            console.log(chalk.green.bold('✅ Tous les tests de la page d\'accueil réussis!'));
            
        } catch (error) {
            console.error(chalk.red.bold('❌ Échec des tests:'), error.message);
            throw error;
        } finally {
            await this.controller.close();
        }
    }
    
    /**
     * Test du chargement de la page
     */
    async testPageLoad() {
        console.log(chalk.cyan('\n📄 Test: Chargement de la page d\'accueil'));
        
        await this.controller.navigateTo('index.html');
        
        // Vérifier les éléments essentiels
        await this.controller.waitForElement('header');
        await this.controller.waitForElement('.hero');
        await this.controller.waitForElement('.features');
        await this.controller.waitForElement('.cta');
        await this.controller.waitForElement('footer');
        await this.controller.waitForElement('.lang-toggle');
        
        // Vérifier le titre et les métadonnées
        const pageInfo = await this.controller.getPageInfo();
        console.log(chalk.green(`✅ Page chargée: ${pageInfo.title}`));
        console.log(chalk.gray(`   Langue: ${pageInfo.language}, Direction: ${pageInfo.direction}`));
        
        // Vérifier les métadonnées SEO
        const seoData = await this.controller.evaluate(() => {
            return {
                metaDescription: document.querySelector('meta[name="description"]')?.content || null,
                metaKeywords: document.querySelector('meta[name="keywords"]')?.content || null,
                ogTitle: document.querySelector('meta[property="og:title"]')?.content || null,
                ogDescription: document.querySelector('meta[property="og:description"]')?.content || null,
                charset: document.querySelector('meta[charset]')?.getAttribute('charset') || null
            };
        });
        
        console.log(chalk.green('✅ Métadonnées SEO:'));
        Object.entries(seoData).forEach(([key, value]) => {
            console.log(chalk.gray(`   ${key}: ${value ? 'Présent' : 'Absent'}`));
        });
        
        await this.controller.takeScreenshot('homepage_loaded');
    }
    
    /**
     * Test du changement de langue
     */
    async testLanguageSwitching() {
        console.log(chalk.cyan('\n🌍 Test: Changement de langue'));
        
        const languages = ['fr', 'en', 'ar'];
        
        for (const lang of languages) {
            await this.controller.changeLanguage(lang);
            await this.controller.wait(1000);
            
            const pageInfo = await this.controller.getPageInfo();
            console.log(chalk.green(`✅ Langue changée vers: ${lang} (${pageInfo.language})`));
            
            // Vérifier que le contenu a changé
            const content = await this.controller.evaluate(() => {
                return {
                    heroTitle: document.querySelector('.hero h1')?.textContent.trim(),
                    heroSubtitle: document.querySelector('.hero p')?.textContent.trim(),
                    startNowText: document.querySelector('.btn-primary')?.textContent.trim()
                };
            });
            
            console.log(chalk.gray(`   Titre hero: ${content.heroTitle}`));
            console.log(chalk.gray(`   Bouton principal: ${content.startNowText}`));
            
            await this.controller.takeScreenshot(`homepage_language_${lang}`);
        }
    }
    
    /**
     * Test des boutons de navigation
     */
    async testNavigationButtons() {
        console.log(chalk.cyan('\n🧭 Test: Boutons de navigation'));
        
        // Vérifier la présence des boutons de navigation
        const navButtons = await this.controller.evaluate(() => {
            const buttons = [];
            
            // Boutons dans le header
            document.querySelectorAll('header a, header button').forEach(btn => {
                buttons.push({
                    text: btn.textContent.trim(),
                    href: btn.href || null,
                    type: btn.tagName.toLowerCase(),
                    classes: Array.from(btn.classList)
                });
            });
            
            return buttons;
        });
        
        console.log(chalk.green('✅ Boutons de navigation trouvés:'));
        navButtons.forEach((btn, index) => {
            console.log(chalk.gray(`   ${index + 1}. ${btn.text} (${btn.type})${btn.href ? ' -> ' + btn.href : ''}`));
        });
        
        await this.controller.takeScreenshot('homepage_navigation');
    }
    
    /**
     * Test des boutons "Start Now"
     */
    async testStartNowButtons() {
        console.log(chalk.cyan('\n🚀 Test: Boutons "Start Now"'));
        
        // Chercher tous les boutons "Start Now" ou équivalents
        const startButtons = await this.controller.evaluate(() => {
            const buttons = [];
            
            // Chercher par texte et par classe
            const selectors = [
                'a[href*="register"]',
                'button[onclick*="register"]',
                '.btn-primary',
                '.cta-button',
                '[data-action="start"]'
            ];
            
            selectors.forEach(selector => {
                document.querySelectorAll(selector).forEach(btn => {
                    if (!buttons.find(b => b.element === btn)) {
                        buttons.push({
                            text: btn.textContent.trim(),
                            href: btn.href || null,
                            onclick: btn.onclick ? btn.onclick.toString() : null,
                            selector: selector,
                            classes: Array.from(btn.classList),
                            element: btn
                        });
                    }
                });
            });
            
            return buttons.map(btn => ({
                text: btn.text,
                href: btn.href,
                onclick: btn.onclick,
                selector: btn.selector,
                classes: btn.classes
            }));
        });
        
        console.log(chalk.green(`✅ ${startButtons.length} bouton(s) "Start Now" trouvé(s):`);
        
        for (let i = 0; i < startButtons.length; i++) {
            const btn = startButtons[i];
            console.log(chalk.gray(`   ${i + 1}. "${btn.text}" -> ${btn.href || 'Action JS'}`));
            
            // Tester le clic sur chaque bouton
            try {
                if (btn.href && btn.href.includes('register')) {
                    console.log(chalk.cyan(`   Test du bouton ${i + 1}...`));
                    
                    // Cliquer et vérifier la navigation
                    await this.controller.clickElement(`a[href*="register"]:nth-of-type(${i + 1})`, { waitForNavigation: true });
                    
                    const newPageInfo = await this.controller.getPageInfo();
                    console.log(chalk.green(`   ✅ Navigation réussie vers: ${newPageInfo.title}`));
                    
                    await this.controller.takeScreenshot(`start_now_navigation_${i + 1}`);
                    
                    // Retourner à la page d'accueil
                    await this.controller.navigateTo('index.html');
                    await this.controller.wait(1000);
                }
            } catch (error) {
                console.log(chalk.red(`   ❌ Erreur avec le bouton ${i + 1}: ${error.message}`));
            }
        }
    }
    
    /**
     * Test de la section hero
     */
    async testHeroSection() {
        console.log(chalk.cyan('\n🎯 Test: Section Hero'));
        
        const heroData = await this.controller.evaluate(() => {
            const hero = document.querySelector('.hero');
            if (!hero) return null;
            
            return {
                title: hero.querySelector('h1')?.textContent.trim(),
                subtitle: hero.querySelector('p')?.textContent.trim(),
                buttons: Array.from(hero.querySelectorAll('button, a')).map(btn => ({
                    text: btn.textContent.trim(),
                    href: btn.href || null,
                    classes: Array.from(btn.classList)
                })),
                backgroundImage: window.getComputedStyle(hero).backgroundImage,
                hasAnimation: hero.classList.contains('animated') || hero.style.animation
            };
        });
        
        if (heroData) {
            console.log(chalk.green('✅ Section Hero analysée:'));
            console.log(chalk.gray(`   Titre: ${heroData.title}`));
            console.log(chalk.gray(`   Sous-titre: ${heroData.subtitle}`));
            console.log(chalk.gray(`   Nombre de boutons: ${heroData.buttons.length}`));
            console.log(chalk.gray(`   Image de fond: ${heroData.backgroundImage !== 'none' ? 'Présente' : 'Absente'}`));
            
            heroData.buttons.forEach((btn, index) => {
                console.log(chalk.gray(`   Bouton ${index + 1}: "${btn.text}" -> ${btn.href || 'Action JS'}`));
            });
        } else {
            console.log(chalk.red('❌ Section Hero non trouvée'));
        }
        
        await this.controller.takeScreenshot('homepage_hero_section');
    }
    
    /**
     * Test de la section des fonctionnalités
     */
    async testFeaturesSection() {
        console.log(chalk.cyan('\n⭐ Test: Section des fonctionnalités'));
        
        const featuresData = await this.controller.evaluate(() => {
            const features = document.querySelector('.features');
            if (!features) return null;
            
            const featureCards = Array.from(features.querySelectorAll('.feature-card, .card, .feature-item')).map(card => ({
                title: card.querySelector('h3, h4, .title')?.textContent.trim(),
                description: card.querySelector('p, .description')?.textContent.trim(),
                icon: card.querySelector('i, .icon, svg') ? 'Présent' : 'Absent',
                classes: Array.from(card.classList)
            }));
            
            return {
                sectionTitle: features.querySelector('h2')?.textContent.trim(),
                cardCount: featureCards.length,
                cards: featureCards
            };
        });
        
        if (featuresData) {
            console.log(chalk.green('✅ Section des fonctionnalités analysée:'));
            console.log(chalk.gray(`   Titre de section: ${featuresData.sectionTitle}`));
            console.log(chalk.gray(`   Nombre de cartes: ${featuresData.cardCount}`));
            
            featuresData.cards.forEach((card, index) => {
                console.log(chalk.gray(`   Carte ${index + 1}: "${card.title}" (Icône: ${card.icon})`));
            });
        } else {
            console.log(chalk.red('❌ Section des fonctionnalités non trouvée'));
        }
        
        await this.controller.takeScreenshot('homepage_features_section');
    }
    
    /**
     * Test de la section CTA (Call to Action)
     */
    async testCTASection() {
        console.log(chalk.cyan('\n📢 Test: Section CTA'));
        
        const ctaData = await this.controller.evaluate(() => {
            const cta = document.querySelector('.cta');
            if (!cta) return null;
            
            return {
                title: cta.querySelector('h2, h3')?.textContent.trim(),
                subtitle: cta.querySelector('p')?.textContent.trim(),
                buttons: Array.from(cta.querySelectorAll('button, a')).map(btn => ({
                    text: btn.textContent.trim(),
                    href: btn.href || null,
                    classes: Array.from(btn.classList)
                })),
                backgroundColor: window.getComputedStyle(cta).backgroundColor
            };
        });
        
        if (ctaData) {
            console.log(chalk.green('✅ Section CTA analysée:'));
            console.log(chalk.gray(`   Titre: ${ctaData.title}`));
            console.log(chalk.gray(`   Sous-titre: ${ctaData.subtitle}`));
            console.log(chalk.gray(`   Nombre de boutons: ${ctaData.buttons.length}`));
            
            ctaData.buttons.forEach((btn, index) => {
                console.log(chalk.gray(`   Bouton ${index + 1}: "${btn.text}" -> ${btn.href || 'Action JS'}`));
            });
        } else {
            console.log(chalk.red('❌ Section CTA non trouvée'));
        }
        
        await this.controller.takeScreenshot('homepage_cta_section');
    }
    
    /**
     * Test du design responsive
     */
    async testResponsiveDesign() {
        console.log(chalk.cyan('\n📱 Test: Design responsive'));
        
        const results = await this.controller.testResponsiveness();
        
        results.forEach(result => {
            if (result.success) {
                console.log(chalk.green(`✅ ${result.viewport.name}: OK`));
            } else {
                console.log(chalk.red(`❌ ${result.viewport.name}: ${result.error}`));
            }
        });
        
        return results;
    }
    
    /**
     * Test d'accessibilité
     */
    async testAccessibility() {
        console.log(chalk.cyan('\n♿ Test: Accessibilité'));
        
        const accessibility = await this.controller.evaluate(() => {
            const checks = {
                langAttribute: !!document.documentElement.getAttribute('lang'),
                headingStructure: document.querySelectorAll('h1').length === 1,
                altTexts: Array.from(document.querySelectorAll('img')).every(img => img.alt),
                ariaLabels: document.querySelectorAll('[aria-label]').length > 0,
                skipLinks: !!document.querySelector('a[href="#main"], a[href="#content"]'),
                focusableElements: document.querySelectorAll('a, button, input, select, textarea').length > 0,
                colorContrast: true // Nécessiterait un calcul plus complexe
            };
            
            return checks;
        });
        
        Object.entries(accessibility).forEach(([check, passed]) => {
            console.log(chalk[passed ? 'green' : 'red'](`${passed ? '✅' : '❌'} ${check}: ${passed ? 'OK' : 'MANQUANT'}`));
        });
        
        // Test de navigation au clavier
        console.log(chalk.cyan('🎹 Test de navigation clavier...'));
        
        for (let i = 0; i < 8; i++) {
            await this.controller.page.keyboard.press('Tab');
            await this.controller.wait(300);
        }
        
        await this.controller.takeScreenshot('homepage_keyboard_navigation');
        console.log(chalk.green('✅ Test de navigation clavier terminé'));
    }
    
    /**
     * Test de performance
     */
    async testPerformance() {
        console.log(chalk.cyan('\n⚡ Test: Performance'));
        
        const metrics = await this.controller.getPerformanceMetrics();
        
        console.log(chalk.green('✅ Métriques de performance:'));
        console.log(chalk.gray(`   Nodes DOM: ${metrics.puppeteerMetrics.Nodes}`));
        console.log(chalk.gray(`   Layouts: ${metrics.puppeteerMetrics.LayoutCount}`));
        console.log(chalk.gray(`   Recalculs de style: ${metrics.puppeteerMetrics.RecalcStyleCount}`));
        console.log(chalk.gray(`   Taille JS Heap: ${Math.round(metrics.puppeteerMetrics.JSHeapUsedSize / 1024 / 1024)}MB`));
        
        // Calculer le temps de chargement
        const loadTime = metrics.performanceTiming.loadEventEnd - metrics.performanceTiming.navigationStart;
        console.log(chalk.gray(`   Temps de chargement: ${loadTime}ms`));
        
        // Vérifier les ressources
        const resources = await this.controller.evaluate(() => {
            return {
                images: document.querySelectorAll('img').length,
                scripts: document.querySelectorAll('script').length,
                stylesheets: document.querySelectorAll('link[rel="stylesheet"]').length
            };
        });
        
        console.log(chalk.gray(`   Images: ${resources.images}`));
        console.log(chalk.gray(`   Scripts: ${resources.scripts}`));
        console.log(chalk.gray(`   Feuilles de style: ${resources.stylesheets}`));
        
        return { metrics, resources, loadTime };
    }
    
    /**
     * Test du défilement fluide
     */
    async testSmoothScrolling() {
        console.log(chalk.cyan('\n🌊 Test: Défilement fluide'));
        
        // Chercher les liens d'ancrage
        const anchorLinks = await this.controller.evaluate(() => {
            return Array.from(document.querySelectorAll('a[href^="#"]')).map(link => ({
                href: link.href,
                text: link.textContent.trim(),
                target: link.getAttribute('href')
            }));
        });
        
        console.log(chalk.green(`✅ ${anchorLinks.length} lien(s) d'ancrage trouvé(s)`));
        
        for (const link of anchorLinks) {
            if (link.target && link.target !== '#') {
                console.log(chalk.cyan(`   Test du lien: ${link.text} -> ${link.target}`));
                
                try {
                    await this.controller.clickElement(`a[href="${link.target}"]`);
                    await this.controller.wait(1000);
                    
                    // Vérifier si l'élément cible existe
                    const targetExists = await this.controller.evaluate((target) => {
                        return !!document.querySelector(target);
                    }, link.target);
                    
                    console.log(chalk.green(`   ✅ Cible ${link.target}: ${targetExists ? 'Trouvée' : 'Non trouvée'}`));
                    
                } catch (error) {
                    console.log(chalk.red(`   ❌ Erreur avec ${link.target}: ${error.message}`));
                }
            }
        }
        
        await this.controller.takeScreenshot('homepage_smooth_scrolling');
    }
}

// Exporter la classe
module.exports = HomepageTest;

// Exécution directe si le fichier est appelé
if (require.main === module) {
    const test = new HomepageTest();
    test.runAllTests().catch(console.error);
}