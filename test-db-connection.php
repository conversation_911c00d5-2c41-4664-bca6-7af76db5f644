<?php

/**
 * Database Connection Test Script
 * Tests the database connection with the provided credentials
 */

require_once 'api/config/database.php';

echo "<h2>Database Connection Test</h2>\n";
echo "<pre>\n";

try {
    // Test database connection
    echo "Testing database connection...\n";
    echo "Host: localhost:3307\n";
    echo "Database: landingpage_new\n";
    echo "Username: root\n";
    echo "Password: (empty)\n\n";

    $database = new Database();
    $db = $database->getConnection();

    if ($db) {
        echo "✅ Database connection successful!\n\n";

        // Test basic query
        echo "Testing basic query...\n";
        $stmt = $db->query("SELECT DATABASE() as current_db, NOW() as `current_time`");
        $result = $stmt->fetch();

        echo "Current Database: " . $result['current_db'] . "\n";
        echo "Current Time: " . $result['current_time'] . "\n\n";

        // Test table existence
        echo "Checking table structure...\n";
        $tables = $db->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);

        echo "Found " . count($tables) . " tables:\n";
        foreach ($tables as $table) {
            echo "- " . $table . "\n";
        }

        // Test some key tables
        $key_tables = ['users', 'stores', 'products', 'orders', 'payments'];
        echo "\nChecking key tables:\n";

        foreach ($key_tables as $table) {
            if (in_array($table, $tables)) {
                $count = $db->query("SELECT COUNT(*) FROM $table")->fetchColumn();
                echo "✅ $table: $count records\n";
            } else {
                echo "❌ $table: Table not found\n";
            }
        }
    } else {
        echo "❌ Database connection failed!\n";
    }
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "</pre>\n";
