<?php
/**
 * Test Users API
 */

echo "<h2>Users API Test</h2>\n";
echo "<pre>\n";

// Set up environment for API testing
$_SERVER['REQUEST_METHOD'] = 'GET';
$_SERVER['REQUEST_URI'] = '/api/users/profile';
$_SERVER['HTTP_AUTHORIZATION'] = 'Bearer demo_token';

// Capture output
ob_start();

try {
    echo "Testing Users API (Profile)...\n";
    
    // Include the users API
    include 'api/users.php';
    
} catch (Exception $e) {
    echo "Exception: " . $e->getMessage() . "\n";
} catch (Error $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

$output = ob_get_clean();
echo $output;

echo "</pre>\n";
?>
