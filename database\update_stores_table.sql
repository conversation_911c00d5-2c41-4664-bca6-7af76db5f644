-- Mise à jour de la table stores
ALTER TABLE `stores`
  -- Ajout du champ domain
  ADD COLUMN `domain` varchar(255) DEFAULT NULL COMMENT 'Domaine personnalisé du store' AFTER `website_url`,
  
  -- Ajout du champ product_count
  ADD COLUMN `product_count` int(11) DEFAULT 0 COMMENT 'Nombre de produits dans le store',
  
  -- Ajout d'index pour améliorer les performances
  ADD INDEX `idx_domain` (`domain`),
  ADD INDEX `idx_created_at` (`created_at`),
  ADD INDEX `idx_updated_at` (`updated_at`);

-- Procédure pour mettre à jour le nombre de produits
DELIMITER $$

CREATE PROCEDURE IF NOT EXISTS `update_store_product_count`(IN store_id INT)
BEGIN
    UPDATE `stores` s
    SET s.product_count = (
        SELECT COUNT(*) 
        FROM `products` p 
        WHERE p.store_id = s.id
    )
    WHERE s.id = store_id;
END$$

DELIMITER ;

-- Trigger pour maintenir le compteur de produits à jour
DELIMITER $$

CREATE TRIGGER IF NOT EXISTS `after_product_insert`
AFTER INSERT ON `products`
FOR EACH ROW
BEGIN
    CALL update_store_product_count(NEW.store_id);
END$$

CREATE TRIGGER IF NOT EXISTS `after_product_delete`
AFTER DELETE ON `products`
FOR EACH ROW
BEGIN
    CALL update_store_product_count(OLD.store_id);
END$$

DELIMITER ;

-- Mise à jour initiale des compteurs de produits
UPDATE `stores` s
SET s.product_count = (
    SELECT COUNT(*) 
    FROM `products` p 
    WHERE p.store_id = s.id
);