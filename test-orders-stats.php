<?php
/**
 * Test Orders Stats API
 */

echo "<h2>Orders Stats API Test</h2>\n";
echo "<pre>\n";

// Set up environment for API testing
$_SERVER['REQUEST_METHOD'] = 'GET';
$_SERVER['REQUEST_URI'] = '/api/orders/stats';
$_SERVER['HTTP_AUTHORIZATION'] = 'Bearer demo_token';

// Capture output
ob_start();

try {
    echo "Testing Orders Stats API...\n";
    
    // Include the orders API
    include 'api/orders.php';
    
} catch (Exception $e) {
    echo "Exception: " . $e->getMessage() . "\n";
} catch (Error $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

$output = ob_get_clean();
echo $output;

echo "</pre>\n";
?>
