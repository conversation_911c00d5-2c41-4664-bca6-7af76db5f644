<?php
header('Content-Type: text/html; charset=UTF-8');

echo "<h1>🧪 Test de toutes les APIs</h1>";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .success{color:green;} .error{color:red;} .warning{color:orange;}</style>";

$apis_to_test = [
    'Users API' => 'http://localhost:8000/api/users.php?action=all',
    'Roles API' => 'http://localhost:8000/api/roles.php',
    'Products API' => 'http://localhost:8000/api/products.php',
    'Stores API' => 'http://localhost:8000/api/stores.php',
    'Orders API' => 'http://localhost:8000/api/orders.php',
    'Firebase Dashboard API' => 'http://localhost:8000/firebase-dashboard-api.php?action=stats'
];

foreach ($apis_to_test as $name => $url) {
    echo "<h2>🔍 Test: $name</h2>";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Authorization: Bearer demo_token'
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "<p class='error'>❌ Erreur cURL: $error</p>";
        continue;
    }
    
    echo "<p><strong>Code HTTP:</strong> $http_code</p>";
    
    if ($http_code === 200) {
        echo "<p class='success'>✅ API accessible</p>";
        
        $data = json_decode($response, true);
        if ($data) {
            echo "<p><strong>Réponse JSON valide:</strong> " . (isset($data['success']) ? ($data['success'] ? 'Succès' : 'Échec') : 'Format inconnu') . "</p>";
            if (isset($data['message'])) {
                echo "<p><strong>Message:</strong> " . htmlspecialchars($data['message']) . "</p>";
            }
        } else {
            echo "<p class='warning'>⚠️ Réponse non-JSON ou invalide</p>";
            echo "<pre>" . htmlspecialchars(substr($response, 0, 200)) . "...</pre>";
        }
    } else {
        echo "<p class='error'>❌ Erreur HTTP $http_code</p>";
        if ($response) {
            echo "<pre>" . htmlspecialchars(substr($response, 0, 500)) . "</pre>";
        }
    }
    
    echo "<hr>";
}

echo "<h2>📊 Résumé</h2>";
echo "<p>Test terminé. Vérifiez les résultats ci-dessus pour identifier les APIs qui fonctionnent.</p>";
?>
