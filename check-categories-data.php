<?php
require_once 'php/config/database.php';

try {
    echo "🔍 Vérification des données catégories...\n\n";
    
    // Vérifier le store_id 3
    echo "📊 STORE ID 3:\n";
    $storeQuery = "SELECT id, merchant_id, store_name FROM stores WHERE id = 3";
    $storeStmt = $pdo->prepare($storeQuery);
    $storeStmt->execute();
    $store = $storeStmt->fetch(PDO::FETCH_ASSOC);
    
    if ($store) {
        echo "  - Store ID: {$store['id']}\n";
        echo "  - Merchant ID: {$store['merchant_id']}\n";
        echo "  - Nom: {$store['store_name']}\n";
    } else {
        echo "  ❌ Store ID 3 non trouvé\n";
        exit;
    }
    
    $merchantId = $store['merchant_id'];
    
    // Vérifier les catégories pour ce merchant
    echo "\n📂 CATÉGORIES POUR MERCHANT {$merchantId}:\n";
    $catQuery = "SELECT id, user_id, name, name_ar, name_fr, name_en, slug, category_type FROM categories WHERE user_id = ?";
    $catStmt = $pdo->prepare($catQuery);
    $catStmt->execute([$merchantId]);
    $categories = $catStmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($categories)) {
        echo "  ❌ Aucune catégorie trouvée pour merchant {$merchantId}\n";
        
        // Vérifier toutes les catégories
        echo "\n📂 TOUTES LES CATÉGORIES:\n";
        $allCatQuery = "SELECT id, user_id, name, name_ar, slug, category_type FROM categories";
        $allCategories = $pdo->query($allCatQuery)->fetchAll(PDO::FETCH_ASSOC);
        
        if (empty($allCategories)) {
            echo "  ❌ Aucune catégorie dans la base\n";
        } else {
            foreach ($allCategories as $cat) {
                echo "  - ID: {$cat['id']}, User: {$cat['user_id']}, Nom: {$cat['name']}, Type: {$cat['category_type']}\n";
            }
        }
    } else {
        foreach ($categories as $cat) {
            echo "  - ID: {$cat['id']}, Nom: {$cat['name']} ({$cat['name_ar']}), Slug: {$cat['slug']}\n";
        }
    }
    
    // Vérifier les product_categories
    echo "\n📦 PRODUCT_CATEGORIES:\n";
    $pcQuery = "SELECT id, name, name_ar, name_fr, name_en, slug FROM product_categories";
    $pcCategories = $pdo->query($pcQuery)->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($pcCategories)) {
        echo "  ❌ Aucune product_category trouvée\n";
    } else {
        foreach ($pcCategories as $pc) {
            echo "  - ID: {$pc['id']}, Nom: {$pc['name']} ({$pc['name_ar']}), Slug: {$pc['slug']}\n";
        }
    }
    
    // Test de l'API categories
    echo "\n🔗 Test de l'API categories:\n";
    echo "  URL: /api/categories?store_id=3\n";
    
    // Simuler l'appel API
    $_GET['store_id'] = 3;
    $_SERVER['REQUEST_METHOD'] = 'GET';
    $_SERVER['HTTP_AUTHORIZATION'] = 'Bearer demo_token';
    
    ob_start();
    try {
        include 'api/categories.php';
    } catch (Exception $e) {
        echo "  ❌ Erreur API: " . $e->getMessage() . "\n";
    }
    $apiOutput = ob_get_clean();
    
    echo "  Réponse API: " . substr($apiOutput, 0, 200) . "...\n";
    
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
}
?>
