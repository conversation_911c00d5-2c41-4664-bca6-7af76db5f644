-- Structure de la table `templates`
DROP TABLE IF EXISTS `templates`;
CREATE TABLE IF NOT EXISTS `templates` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `category` enum('e-commerce','promo','catalogue','saisonnier','presentation') NOT NULL DEFAULT 'e-commerce',
  `preview_image` varchar(500) DEFAULT NULL,
  `html_content` text NOT NULL,
  `css_content` text DEFAULT NULL,
  `js_content` text DEFAULT NULL,
  `features` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`features`)),
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_category` (`category`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insertion des templates par défaut
INSERT INTO `templates` (`name`, `description`, `category`, `preview_image`, `html_content`, `css_content`, `js_content`, `features`, `status`) VALUES
('E-commerce Premium', 'Template e-commerce moderne avec slider de produits et sections personnalisables', 'e-commerce', 'templates/e-commerce-premium.svg', '<!-- Template E-commerce Premium -->
<div class="container">
  <header class="hero-section">
    <div class="product-slider">
      <!-- Slider content -->
    </div>
    <div class="cta-primary">
      <!-- Call to action -->
    </div>
  </header>
  <section class="featured-products">
    <!-- Featured products grid -->
  </section>
  <section class="testimonials">
    <!-- Customer testimonials -->
  </section>
  <section class="social-proof">
    <!-- Social media integration -->
  </section>
</div>', '/* Styles E-commerce Premium */
.container { max-width: 1200px; margin: 0 auto; }
.hero-section { /* ... */ }
.product-slider { /* ... */ }
.featured-products { /* ... */ }
.testimonials { /* ... */ }
.social-proof { /* ... */ }', '// Scripts E-commerce Premium
document.addEventListener("DOMContentLoaded", function() {
  // Initialize product slider
  // Handle testimonials
  // Setup social media feeds
});', '{"sections": ["hero", "products", "testimonials", "social"], "customizable": true, "responsive": true}', 'active'),

('Promo Flash', 'Template pour promotions avec compte à rebours et mise en avant des réductions', 'promo', 'templates/promo-flash.svg', '<!-- Template Promo Flash -->
<div class="promo-container">
  <div class="countdown-timer">
    <!-- Countdown component -->
  </div>
  <div class="offer-highlight">
    <!-- Promotional offer -->
  </div>
  <div class="product-grid">
    <!-- Products on sale -->
  </div>
</div>', '/* Styles Promo Flash */
.promo-container { /* ... */ }
.countdown-timer { /* ... */ }
.offer-highlight { /* ... */ }
.product-grid { /* ... */ }', '// Scripts Promo Flash
const countdownTimer = {
  // Timer implementation
};
// Promotional animations', '{"features": ["countdown", "animations", "sale_badges"], "customizable": true}', 'active'),

('Catalogue Produit', 'Template catalogue avec grille de produits et filtres dynamiques', 'catalogue', 'templates/catalogue-produit.svg', '<!-- Template Catalogue Produit -->
<div class="catalogue-container">
  <aside class="filters">
    <!-- Product filters -->
  </aside>
  <main class="product-showcase">
    <!-- Product grid -->
  </main>
</div>', '/* Styles Catalogue Produit */
.catalogue-container { /* ... */ }
.filters { /* ... */ }
.product-showcase { /* ... */ }', '// Scripts Catalogue Produit
const productFilters = {
  // Filter implementation
};
// Quick view functionality', '{"features": ["filters", "quick_view", "ratings"], "customizable": true}', 'active'),

('Landing Page Saisonnière', 'Template adaptable aux événements saisonniers avec thèmes personnalisables', 'saisonnier', 'templates/landing-saisonniere.svg', '<!-- Template Landing Page Saisonnière -->
<div class="seasonal-container">
  <div class="theme-wrapper">
    <!-- Seasonal content -->
  </div>
  <div class="special-offers">
    <!-- Seasonal offers -->
  </div>
</div>', '/* Styles Landing Page Saisonnière */
.seasonal-container { /* ... */ }
.theme-wrapper { /* ... */ }
.special-offers { /* ... */ }', '// Scripts Landing Page Saisonnière
const seasonalTheme = {
  // Theme switcher
};
// Seasonal animations', '{"themes": ["ramadan", "aid", "summer", "winter"], "customizable": true}', 'active'),

('Présentation Produit', 'Template de présentation produit avec galerie HD et vidéo', 'presentation', 'templates/presentation-produit.svg', '<!-- Template Présentation Produit -->
<div class="product-presentation">
  <div class="media-gallery">
    <!-- Product media -->
  </div>
  <div class="product-details">
    <!-- Product information -->
  </div>
  <div class="interactive-faq">
    <!-- FAQ section -->
  </div>
</div>', '/* Styles Présentation Produit */
.product-presentation { /* ... */ }
.media-gallery { /* ... */ }
.product-details { /* ... */ }
.interactive-faq { /* ... */ }', '// Scripts Présentation Produit
const mediaGallery = {
  // Gallery implementation
};
// FAQ interactions', '{"features": ["hd_gallery", "video_support", "interactive_faq"], "customizable": true}', 'active');