<?php
/**
 * Simple Products API Test
 */

echo "<h2>Products API Test</h2>\n";
echo "<pre>\n";

// Set up environment for API testing
$_SERVER['REQUEST_METHOD'] = 'GET';
$_SERVER['REQUEST_URI'] = '/api/products';
$_SERVER['HTTP_AUTHORIZATION'] = 'Bearer demo_token';

// Capture output and errors
ob_start();
$error_output = '';

try {
    // Include the products API
    include 'api/products.php';
} catch (Exception $e) {
    $error_output = "Exception: " . $e->getMessage() . "\n";
} catch (Error $e) {
    $error_output = "Error: " . $e->getMessage() . "\n";
}

$api_output = ob_get_clean();

echo "=== API OUTPUT ===\n";
echo $api_output;

if ($error_output) {
    echo "\n=== ERRORS ===\n";
    echo $error_output;
}

echo "</pre>\n";
?>
