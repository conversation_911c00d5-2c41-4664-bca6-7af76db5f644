// Gestion des abonnements

// Fonction pour récupérer le token Firebase
function getFirebaseToken() {
    // En mode développement, on retourne un token de test
    return 'demo_token_123';
    
    // En production, on utiliserait :
    // return firebase.auth().currentUser?.getIdToken() || null;
}

async function loadSubscriptions() {
    const container = document.getElementById('subscriptionsContainer');

    try {
        // Afficher un indicateur de chargement
        container.innerHTML = `
            <div class="text-center py-4">
                <div class="spinner-border" role="status"></div>
                <p>Chargement des abonnements...</p>
            </div>
        `;

        // Charger les abonnements depuis l'API
        const response = await fetch('/api/subscriptions.php?action=plans', {
            method: 'GET',
            headers: {
                'Authorization': 'Bearer ' + getFirebaseToken(),
                'Content-Type': 'application/json'
            }
        });

        const data = await response.json();

        if (!data.success) {
            throw new Error(data.message || 'Erreur lors du chargement des abonnements');
        }

        const subscriptions = data.plans;

        // Afficher les statistiques
        let html = `
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-primary">Total Plans</h5>
                            <h2>${subscriptions.length}</h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-success">Revenus Mensuels</h5>
                            <h2>${calculateMonthlyRevenue(subscriptions)}€</h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-info">Plans Actifs</h5>
                            <h2>${subscriptions.filter(sub => sub.is_active).length}</h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-warning">Taux de Conversion</h5>
                            <h2>${calculateConversionRate(subscriptions)}%</h2>
                        </div>
                    </div>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>Plan</th>
                            <th>Prix</th>
                            <th>Limites</th>
                            <th>Abonnés</th>
                            <th>Statut</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        subscriptions.forEach(subscription => {
            const statusBadge = subscription.is_active ? 'bg-success' : 'bg-danger';
            html += `
                <tr>
                    <td>
                        <strong>${subscription.display_name}</strong><br>
                        <small class="text-muted">${subscription.description}</small>
                    </td>
                    <td>
                        <strong>${subscription.price.toLocaleString()} ${subscription.currency}</strong><br>
                        <small class="text-muted">/${subscription.billing_cycle}</small>
                    </td>
                    <td>
                        <small>
                            ${subscription.max_products} produits<br>
                            ${subscription.max_landing_pages} pages<br>
                            ${subscription.max_storage_mb}MB stockage
                        </small>
                    </td>
                    <td>
                        <span class="badge bg-primary">${subscription.active_subscribers}</span>
                    </td>
                    <td>
                        <span class="badge ${statusBadge}">
                            ${subscription.is_active ? 'ACTIF' : 'INACTIF'}
                        </span>
                    </td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary me-1" onclick="editSubscription(${subscription.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-warning me-1" onclick="toggleSubscriptionStatus(${subscription.id}, ${subscription.is_active})">
                            <i class="fas fa-toggle-${subscription.is_active ? 'on' : 'off'}"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-info" onclick="viewSubscriptionDetails(${subscription.id})">
                            <i class="fas fa-eye"></i>
                        </button>
                    </td>
                </tr>
            `;
        });

        html += `
                    </tbody>
                </table>
            </div>
        `;

        container.innerHTML = html;
        console.log('✅ Abonnements chargés:', subscriptions);

    } catch (error) {
        console.error('❌ Erreur lors du chargement des abonnements:', error);
        container.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                Erreur lors du chargement des abonnements: ${error.message}
            </div>
        `;
    }
}

// Fonctions utilitaires
function calculateMonthlyRevenue(subscriptions) {
    return subscriptions.reduce((total, sub) => {
        return total + (sub.price * sub.active_subscribers);
    }, 0).toLocaleString();
}

function calculateConversionRate(subscriptions) {
    const totalSubscribers = subscriptions.reduce((total, sub) => total + sub.active_subscribers, 0);
    const freeSubscribers = subscriptions.find(sub => sub.price === 0)?.active_subscribers || 0;
    if (totalSubscribers === 0) return 0;
    return Math.round(((totalSubscribers - freeSubscribers) / totalSubscribers) * 100);
}

// Fonctions de gestion
async function addNewSubscription() {
    // TODO: Implémenter la création d'un nouveau plan
}

async function editSubscription(id) {
    // TODO: Implémenter l'édition d'un plan
}

async function toggleSubscriptionStatus(id, currentStatus) {
    // TODO: Implémenter le changement de statut
}

async function viewSubscriptionDetails(id) {
    // TODO: Implémenter l'affichage des détails
}