# Redirection automatique vers index.html
DirectoryIndex index.html

# Redirection de la racine vers index.html
RewriteEngine On

# Redirection pour les URLs de stores
# Format: /store/{store_name} ou /store/{store_name}/{landing_page_slug}
RewriteRule ^store/([^/]+)/?(.*)$ store-landing.php [QSA,L]

# Redirection pour les subdomains personnalisés
# Format: /{subdomain} ou /{subdomain}/{landing_page_slug}
# Exclure les fichiers et dossiers existants
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} !^/(api|js|css|images|assets|admin|dashboard|preview\.php|store-landing\.php|index\.html)
RewriteRule ^([^/]+)/?(.*)$ store-landing.php [QSA,L]

# Redirection pour l'ancien système de preview
RewriteRule ^preview\.php\?id=([0-9]+)$ /api/landing-pages.php?action=preview&id=$1 [R=301,L]

# Redirection de la racine vers index.html (doit être après les autres règles)
RewriteRule ^$ index.html [L]

# Headers pour éviter le cache
<FilesMatch "\.(html|htm|js|css)$">
    Header set Cache-Control "no-cache, no-store, must-revalidate"
    Header set Pragma "no-cache"
    Header set Expires 0
</FilesMatch>

# Support UTF-8
AddDefaultCharset UTF-8

# Compression GZIP
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>
