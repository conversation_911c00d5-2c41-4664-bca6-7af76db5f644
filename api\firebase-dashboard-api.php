<?php

/**
 * API pour le Dashboard Firebase
 * Fournit les données de la base de données au dashboard Firebase
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Gestion des requêtes OPTIONS (CORS preflight)
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once __DIR__ . '/../php/config/database.php';

try {
    $database = new Database();
    $pdo = $database->getConnection();
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Erreur de connexion à la base de données: ' . $e->getMessage()
    ]);
    exit();
}

// Récupérer l'action demandée
$action = $_GET['action'] ?? $_POST['action'] ?? '';

switch ($action) {
    case 'stats':
        getStats($pdo);
        break;

    case 'database_status':
        getDatabaseStatus($pdo);
        break;

    case 'recent_orders':
        getRecentOrders($pdo);
        break;

    case 'payment_stats':
        getPaymentStats($pdo);
        break;

    case 'ai_usage':
        getAIUsage($pdo);
        break;

    case 'verify_admin':
        verifyAdmin();
        break;

    default:
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => 'Action non spécifiée ou invalide'
        ]);
        break;
}

/**
 * Récupérer les statistiques générales
 */
function getStats($pdo)
{
    try {
        $stats = [];

        // Nombre total d'utilisateurs (basé sur user_roles)
        $stmt = $pdo->query("SELECT COUNT(DISTINCT user_id) FROM user_roles");
        $stats['total_users'] = (int) $stmt->fetchColumn();

        // Nombre total de stores
        $stmt = $pdo->query("SELECT COUNT(*) FROM stores");
        $stats['total_stores'] = (int) $stmt->fetchColumn();

        // Stores actifs
        $stmt = $pdo->query("SELECT COUNT(*) FROM stores WHERE status = 'active'");
        $stats['active_stores'] = (int) $stmt->fetchColumn();

        // Nombre total de produits
        $stmt = $pdo->query("SELECT COUNT(*) FROM products");
        $stats['total_products'] = (int) $stmt->fetchColumn();

        // Produits actifs
        $stmt = $pdo->query("SELECT COUNT(*) FROM products WHERE status = 'active'");
        $stats['active_products'] = (int) $stmt->fetchColumn();

        // Nombre total d'avis
        $stmt = $pdo->query("SELECT COUNT(*) FROM reviews");
        $stats['total_reviews'] = (int) $stmt->fetchColumn();

        // Note moyenne des avis
        $stmt = $pdo->query("SELECT COALESCE(AVG(rating), 0) FROM reviews");
        $stats['average_rating'] = (float) $stmt->fetchColumn();

        // Nombre de rôles
        $stmt = $pdo->query("SELECT COUNT(*) FROM roles");
        $stats['total_roles'] = (int) $stmt->fetchColumn();

        // Données simulées pour les graphiques
        $stats['revenue_month'] = 15000.50;
        $stats['orders_today'] = 12;
        $stats['total_orders'] = 156;
        $stats['ai_usage_total'] = 89;
        $stats['ai_usage_month'] = 23;

        echo json_encode([
            'success' => true,
            'data' => $stats
        ]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => 'Erreur lors de la récupération des statistiques: ' . $e->getMessage()
        ]);
    }
}

/**
 * Récupérer l'état de la base de données
 */
function getDatabaseStatus($pdo)
{
    try {
        $tables = [];

        // Liste des tables importantes
        $importantTables = ['users', 'orders', 'payments', 'payment_logs', 'ai_usage'];

        foreach ($importantTables as $tableName) {
            try {
                // Vérifier si la table existe
                $stmt = $pdo->query("SHOW TABLES LIKE '$tableName'");
                $exists = $stmt->rowCount() > 0;

                if ($exists) {
                    // Compter les enregistrements
                    $stmt = $pdo->query("SELECT COUNT(*) FROM `$tableName`");
                    $count = (int) $stmt->fetchColumn();

                    $status = $count > 0 ? 'active' : 'empty';
                } else {
                    $count = 0;
                    $status = 'missing';
                }

                $tables[] = [
                    'name' => $tableName,
                    'exists' => $exists,
                    'records' => $count,
                    'status' => $status
                ];
            } catch (Exception $e) {
                $tables[] = [
                    'name' => $tableName,
                    'exists' => false,
                    'records' => 0,
                    'status' => 'error',
                    'error' => $e->getMessage()
                ];
            }
        }

        // Compter le nombre total de tables
        $stmt = $pdo->query("SHOW TABLES");
        $totalTables = $stmt->rowCount();

        echo json_encode([
            'success' => true,
            'data' => [
                'tables' => $tables,
                'total_tables' => $totalTables
            ]
        ]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => 'Erreur lors de la vérification de la base de données: ' . $e->getMessage()
        ]);
    }
}

/**
 * Récupérer les commandes récentes
 */
function getRecentOrders($pdo)
{
    try {
        $limit = (int) ($_GET['limit'] ?? 10);

        $stmt = $pdo->prepare("
            SELECT
                o.*,
                p.method as payment_method,
                p.status as payment_status,
                p.transaction_id
            FROM orders o
            LEFT JOIN payments p ON o.id = p.order_id
            ORDER BY o.created_at DESC
            LIMIT ?
        ");

        $stmt->execute([$limit]);
        $orders = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Formater les données
        foreach ($orders as &$order) {
            $order['created_at_formatted'] = date('d/m/Y H:i', strtotime($order['created_at']));
            $order['total_amount_formatted'] = number_format($order['total_amount'], 2) . ' DA';
        }

        echo json_encode([
            'success' => true,
            'data' => $orders
        ]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => 'Erreur lors de la récupération des commandes: ' . $e->getMessage()
        ]);
    }
}

/**
 * Récupérer les statistiques de paiement
 */
function getPaymentStats($pdo)
{
    try {
        $stats = [];

        // Statistiques par méthode de paiement
        $stmt = $pdo->query("
            SELECT
                method,
                COUNT(*) as count,
                SUM(amount) as total_amount
            FROM payments
            GROUP BY method
        ");
        $stats['by_method'] = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Statistiques par statut
        $stmt = $pdo->query("
            SELECT
                status,
                COUNT(*) as count,
                SUM(amount) as total_amount
            FROM payments
            GROUP BY status
        ");
        $stats['by_status'] = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Évolution des paiements (7 derniers jours)
        $stmt = $pdo->query("
            SELECT
                DATE(created_at) as date,
                COUNT(*) as count,
                SUM(amount) as total_amount
            FROM payments
            WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
            GROUP BY DATE(created_at)
            ORDER BY date
        ");
        $stats['evolution'] = $stmt->fetchAll(PDO::FETCH_ASSOC);

        echo json_encode([
            'success' => true,
            'data' => $stats
        ]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => 'Erreur lors de la récupération des statistiques de paiement: ' . $e->getMessage()
        ]);
    }
}

/**
 * Récupérer les statistiques d'usage IA
 */
function getAIUsage($pdo)
{
    try {
        $stats = [];

        // Usage par provider
        $stmt = $pdo->query("
            SELECT
                provider,
                COUNT(*) as requests,
                SUM(tokens_used) as total_tokens,
                SUM(cost) as total_cost
            FROM ai_usage
            GROUP BY provider
        ");
        $stats['by_provider'] = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Usage par type de requête
        $stmt = $pdo->query("
            SELECT
                request_type,
                COUNT(*) as requests,
                AVG(tokens_used) as avg_tokens
            FROM ai_usage
            GROUP BY request_type
        ");
        $stats['by_type'] = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Évolution de l'usage (7 derniers jours)
        $stmt = $pdo->query("
            SELECT
                DATE(created_at) as date,
                COUNT(*) as requests,
                SUM(tokens_used) as tokens,
                SUM(cost) as cost
            FROM ai_usage
            WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
            GROUP BY DATE(created_at)
            ORDER BY date
        ");
        $stats['evolution'] = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Coût total
        $stmt = $pdo->query("SELECT SUM(cost) FROM ai_usage");
        $stats['total_cost'] = (float) $stmt->fetchColumn();

        echo json_encode([
            'success' => true,
            'data' => $stats
        ]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => 'Erreur lors de la récupération des statistiques IA: ' . $e->getMessage()
        ]);
    }
}

/**
 * Vérifier si un utilisateur est admin autorisé
 */
function verifyAdmin()
{
    $email = $_POST['email'] ?? $_GET['email'] ?? '';

    if (empty($email)) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => 'Email requis'
        ]);
        return;
    }

    // Liste des admins autorisés
    $authorizedAdmins = [
        '<EMAIL>' => [
            'role' => 'super_admin',
            'permissions' => ['all']
        ],
        '<EMAIL>' => [
            'role' => 'admin',
            'permissions' => ['dashboard', 'orders', 'payments']
        ],
        '<EMAIL>' => [
            'role' => 'admin',
            'permissions' => ['dashboard', 'orders', 'payments', 'analytics']
        ]
    ];

    if (isset($authorizedAdmins[$email])) {
        echo json_encode([
            'success' => true,
            'data' => [
                'authorized' => true,
                'email' => $email,
                'role' => $authorizedAdmins[$email]['role'],
                'permissions' => $authorizedAdmins[$email]['permissions']
            ]
        ]);
    } else {
        echo json_encode([
            'success' => true,
            'data' => [
                'authorized' => false,
                'email' => $email,
                'role' => 'user',
                'permissions' => []
            ]
        ]);
    }
}

/**
 * Fonction utilitaire pour logger les erreurs
 */
function logError($message, $context = [])
{
    $logEntry = [
        'timestamp' => date('Y-m-d H:i:s'),
        'message' => $message,
        'context' => $context,
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
    ];

    $logFile = __DIR__ . '/../logs/api-errors.log';

    // Créer le dossier logs s'il n'existe pas
    $logDir = dirname($logFile);
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }

    file_put_contents($logFile, json_encode($logEntry) . "\n", FILE_APPEND | LOCK_EX);
}
