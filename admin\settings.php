<?php
/**
 * Paramètres Généraux - Interface d'administration
 * Configuration SMTP, paramètres système et autres réglages
 */

require_once '../config/database.php';
require_once '../php/Auth.php';

session_start();

// Vérification de l'authentification admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../auth/login.php');
    exit;
}

$lang = $_GET['lang'] ?? $_SESSION['language'] ?? 'ar';

// Traductions
$translations = [
    'ar' => [
        'title' => 'إعدادات النظام',
        'general_settings' => 'الإعدادات العامة',
        'smtp_settings' => 'إعدادات SMTP',
        'system_settings' => 'إعدادات النظام',
        'site_name' => 'اسم الموقع',
        'site_description' => 'وصف الموقع',
        'admin_email' => 'بريد المدير الإلكتروني',
        'smtp_host' => 'خادم SMTP',
        'smtp_port' => 'منفذ SMTP',
        'smtp_username' => 'اسم مستخدم SMTP',
        'smtp_password' => 'كلمة مرور SMTP',
        'smtp_encryption' => 'تشفير SMTP',
        'default_language' => 'اللغة الافتراضية',
        'timezone' => 'المنطقة الزمنية',
        'currency' => 'العملة',
        'maintenance_mode' => 'وضع الصيانة',
        'registration_enabled' => 'تفعيل التسجيل',
        'save' => 'حفظ',
        'test_smtp' => 'اختبار SMTP',
        'settings_updated' => 'تم تحديث الإعدادات بنجاح',
        'smtp_test_success' => 'اختبار SMTP نجح',
        'smtp_test_failed' => 'فشل اختبار SMTP',
        'error' => 'حدث خطأ',
        'back_to_dashboard' => 'العودة إلى لوحة التحكم',
        'enabled' => 'مفعل',
        'disabled' => 'معطل'
    ],
    'fr' => [
        'title' => 'Paramètres Système',
        'general_settings' => 'Paramètres Généraux',
        'smtp_settings' => 'Paramètres SMTP',
        'system_settings' => 'Paramètres Système',
        'site_name' => 'Nom du Site',
        'site_description' => 'Description du Site',
        'admin_email' => 'Email Administrateur',
        'smtp_host' => 'Serveur SMTP',
        'smtp_port' => 'Port SMTP',
        'smtp_username' => 'Nom d\'utilisateur SMTP',
        'smtp_password' => 'Mot de passe SMTP',
        'smtp_encryption' => 'Chiffrement SMTP',
        'default_language' => 'Langue par Défaut',
        'timezone' => 'Fuseau Horaire',
        'currency' => 'Devise',
        'maintenance_mode' => 'Mode Maintenance',
        'registration_enabled' => 'Inscription Activée',
        'save' => 'Enregistrer',
        'test_smtp' => 'Tester SMTP',
        'settings_updated' => 'Paramètres mis à jour avec succès',
        'smtp_test_success' => 'Test SMTP réussi',
        'smtp_test_failed' => 'Échec du test SMTP',
        'error' => 'Une erreur s\'est produite',
        'back_to_dashboard' => 'Retour au Tableau de Bord',
        'enabled' => 'Activé',
        'disabled' => 'Désactivé'
    ],
    'en' => [
        'title' => 'System Settings',
        'general_settings' => 'General Settings',
        'smtp_settings' => 'SMTP Settings',
        'system_settings' => 'System Settings',
        'site_name' => 'Site Name',
        'site_description' => 'Site Description',
        'admin_email' => 'Admin Email',
        'smtp_host' => 'SMTP Host',
        'smtp_port' => 'SMTP Port',
        'smtp_username' => 'SMTP Username',
        'smtp_password' => 'SMTP Password',
        'smtp_encryption' => 'SMTP Encryption',
        'default_language' => 'Default Language',
        'timezone' => 'Timezone',
        'currency' => 'Currency',
        'maintenance_mode' => 'Maintenance Mode',
        'registration_enabled' => 'Registration Enabled',
        'save' => 'Save',
        'test_smtp' => 'Test SMTP',
        'settings_updated' => 'Settings updated successfully',
        'smtp_test_success' => 'SMTP test successful',
        'smtp_test_failed' => 'SMTP test failed',
        'error' => 'An error occurred',
        'back_to_dashboard' => 'Back to Dashboard',
        'enabled' => 'Enabled',
        'disabled' => 'Disabled'
    ]
];

$t = $translations[$lang];

// Traitement des actions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'update_settings':
                    // Mise à jour des paramètres généraux
                    $settings = [
                        'site_name' => $_POST['site_name'],
                        'site_description' => $_POST['site_description'],
                        'admin_email' => $_POST['admin_email'],
                        'default_language' => $_POST['default_language'],
                        'timezone' => $_POST['timezone'],
                        'currency' => $_POST['currency'],
                        'maintenance_mode' => isset($_POST['maintenance_mode']) ? 1 : 0,
                        'registration_enabled' => isset($_POST['registration_enabled']) ? 1 : 0
                    ];
                    
                    foreach ($settings as $key => $value) {
                        $stmt = $pdo->prepare("
                            INSERT INTO settings (setting_key, setting_value, updated_at) 
                            VALUES (?, ?, NOW()) 
                            ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value), updated_at = NOW()
                        ");
                        $stmt->execute([$key, $value]);
                    }
                    
                    $message = $t['settings_updated'];
                    $messageType = 'success';
                    break;
                    
                case 'update_smtp':
                    // Mise à jour des paramètres SMTP
                    $smtpSettings = [
                        'smtp_host' => $_POST['smtp_host'],
                        'smtp_port' => $_POST['smtp_port'],
                        'smtp_username' => $_POST['smtp_username'],
                        'smtp_password' => $_POST['smtp_password'],
                        'smtp_encryption' => $_POST['smtp_encryption']
                    ];
                    
                    foreach ($smtpSettings as $key => $value) {
                        $stmt = $pdo->prepare("
                            INSERT INTO settings (setting_key, setting_value, updated_at) 
                            VALUES (?, ?, NOW()) 
                            ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value), updated_at = NOW()
                        ");
                        $stmt->execute([$key, $value]);
                    }
                    
                    $message = $t['settings_updated'];
                    $messageType = 'success';
                    break;
                    
                case 'test_smtp':
                    // Test de la configuration SMTP
                    $testResult = testSmtpConnection($_POST);
                    if ($testResult) {
                        $message = $t['smtp_test_success'];
                        $messageType = 'success';
                    } else {
                        $message = $t['smtp_test_failed'];
                        $messageType = 'warning';
                    }
                    break;
            }
        }
    } catch (Exception $e) {
        $message = $t['error'] . ': ' . $e->getMessage();
        $messageType = 'danger';
    }
}

// Fonction de test SMTP (simulation)
function testSmtpConnection($config) {
    // Simulation - dans un vrai projet, vous utiliseriez PHPMailer ou similar
    return !empty($config['smtp_host']) && !empty($config['smtp_port']);
}

// Récupération des paramètres actuels
try {
    $currentSettings = [];
    $stmt = $pdo->query("SELECT setting_key, setting_value FROM settings");
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $currentSettings[$row['setting_key']] = $row['setting_value'];
    }
} catch (Exception $e) {
    $currentSettings = [];
}

// Valeurs par défaut
$defaults = [
    'site_name' => 'Landing Pages SaaS',
    'site_description' => 'Plateforme de création de landing pages',
    'admin_email' => '<EMAIL>',
    'default_language' => 'fr',
    'timezone' => 'Europe/Paris',
    'currency' => 'EUR',
    'maintenance_mode' => 0,
    'registration_enabled' => 1,
    'smtp_host' => '',
    'smtp_port' => '587',
    'smtp_username' => '',
    'smtp_password' => '',
    'smtp_encryption' => 'tls'
];

// Fusion avec les valeurs par défaut
$settings = array_merge($defaults, $currentSettings);
?>

<!DOCTYPE html>
<html lang="<?= $lang ?>" dir="<?= $lang === 'ar' ? 'rtl' : 'ltr' ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $t['title'] ?> - Landing Pages SaaS</title>
    
    <!-- Bootstrap 5 RTL Support -->
    <?php if ($lang === 'ar'): ?>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <?php else: ?>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <?php endif; ?>
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #64748b;
            --success-color: #059669;
            --warning-color: #d97706;
            --danger-color: #dc2626;
        }
        
        body {
            background-color: #f8fafc;
            font-family: <?= $lang === 'ar' ? '\'Cairo\', \'Segoe UI\'' : '\'Inter\', \'Segoe UI\'' ?>, sans-serif;
        }
        
        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), #1e40af);
            border: none;
            border-radius: 8px;
        }
        
        .form-label {
            font-weight: 600;
            color: #374151;
        }
        
        .form-control, .form-select {
            border-radius: 8px;
            border: 1px solid #d1d5db;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
        }
        
        .nav-tabs .nav-link {
            border-radius: 8px 8px 0 0;
            border: none;
            color: #6b7280;
        }
        
        .nav-tabs .nav-link.active {
            background-color: var(--primary-color);
            color: white;
        }
        
        .tab-content {
            background-color: white;
            border-radius: 0 0 12px 12px;
            padding: 2rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-0"><?= $t['title'] ?></h1>
                <p class="text-muted mb-0">Configuration générale du système</p>
            </div>
            <div>
                <a href="index.php" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>
                    <?= $t['back_to_dashboard'] ?>
                </a>
            </div>
        </div>
        
        <!-- Messages -->
        <?php if ($message): ?>
            <div class="alert alert-<?= $messageType ?> alert-dismissible fade show" role="alert">
                <?= htmlspecialchars($message) ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
        
        <!-- Settings Tabs -->
        <div class="card">
            <div class="card-header bg-white border-0">
                <ul class="nav nav-tabs card-header-tabs" id="settingsTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="general-tab" data-bs-toggle="tab" data-bs-target="#general" type="button" role="tab">
                            <i class="fas fa-cog me-2"></i>
                            <?= $t['general_settings'] ?>
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="smtp-tab" data-bs-toggle="tab" data-bs-target="#smtp" type="button" role="tab">
                            <i class="fas fa-envelope me-2"></i>
                            <?= $t['smtp_settings'] ?>
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="system-tab" data-bs-toggle="tab" data-bs-target="#system" type="button" role="tab">
                            <i class="fas fa-server me-2"></i>
                            <?= $t['system_settings'] ?>
                        </button>
                    </li>
                </ul>
            </div>
            
            <div class="tab-content" id="settingsTabContent">
                <!-- General Settings -->
                <div class="tab-pane fade show active" id="general" role="tabpanel">
                    <form method="POST">
                        <input type="hidden" name="action" value="update_settings">
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="site_name" class="form-label"><?= $t['site_name'] ?></label>
                                    <input type="text" class="form-control" id="site_name" name="site_name" 
                                           value="<?= htmlspecialchars($settings['site_name']) ?>" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="admin_email" class="form-label"><?= $t['admin_email'] ?></label>
                                    <input type="email" class="form-control" id="admin_email" name="admin_email" 
                                           value="<?= htmlspecialchars($settings['admin_email']) ?>" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="site_description" class="form-label"><?= $t['site_description'] ?></label>
                            <textarea class="form-control" id="site_description" name="site_description" rows="3"><?= htmlspecialchars($settings['site_description']) ?></textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="default_language" class="form-label"><?= $t['default_language'] ?></label>
                                    <select class="form-select" id="default_language" name="default_language">
                                        <option value="ar" <?= $settings['default_language'] === 'ar' ? 'selected' : '' ?>>العربية</option>
                                        <option value="fr" <?= $settings['default_language'] === 'fr' ? 'selected' : '' ?>>Français</option>
                                        <option value="en" <?= $settings['default_language'] === 'en' ? 'selected' : '' ?>>English</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="timezone" class="form-label"><?= $t['timezone'] ?></label>
                                    <select class="form-select" id="timezone" name="timezone">
                                        <option value="Europe/Paris" <?= $settings['timezone'] === 'Europe/Paris' ? 'selected' : '' ?>>Europe/Paris</option>
                                        <option value="Africa/Casablanca" <?= $settings['timezone'] === 'Africa/Casablanca' ? 'selected' : '' ?>>Africa/Casablanca</option>
                                        <option value="America/New_York" <?= $settings['timezone'] === 'America/New_York' ? 'selected' : '' ?>>America/New_York</option>
                                        <option value="Asia/Dubai" <?= $settings['timezone'] === 'Asia/Dubai' ? 'selected' : '' ?>>Asia/Dubai</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="currency" class="form-label"><?= $t['currency'] ?></label>
                                    <select class="form-select" id="currency" name="currency">
                                        <option value="EUR" <?= $settings['currency'] === 'EUR' ? 'selected' : '' ?>>EUR (€)</option>
                                        <option value="USD" <?= $settings['currency'] === 'USD' ? 'selected' : '' ?>>USD ($)</option>
                                        <option value="MAD" <?= $settings['currency'] === 'MAD' ? 'selected' : '' ?>>MAD (DH)</option>
                                        <option value="AED" <?= $settings['currency'] === 'AED' ? 'selected' : '' ?>>AED (د.إ)</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="maintenance_mode" name="maintenance_mode" 
                                           <?= $settings['maintenance_mode'] ? 'checked' : '' ?>>
                                    <label class="form-check-label" for="maintenance_mode">
                                        <?= $t['maintenance_mode'] ?>
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="registration_enabled" name="registration_enabled" 
                                           <?= $settings['registration_enabled'] ? 'checked' : '' ?>>
                                    <label class="form-check-label" for="registration_enabled">
                                        <?= $t['registration_enabled'] ?>
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>
                                <?= $t['save'] ?>
                            </button>
                        </div>
                    </form>
                </div>
                
                <!-- SMTP Settings -->
                <div class="tab-pane fade" id="smtp" role="tabpanel">
                    <form method="POST">
                        <input type="hidden" name="action" value="update_smtp">
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="smtp_host" class="form-label"><?= $t['smtp_host'] ?></label>
                                    <input type="text" class="form-control" id="smtp_host" name="smtp_host" 
                                           value="<?= htmlspecialchars($settings['smtp_host']) ?>" placeholder="smtp.gmail.com">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="smtp_port" class="form-label"><?= $t['smtp_port'] ?></label>
                                    <input type="number" class="form-control" id="smtp_port" name="smtp_port" 
                                           value="<?= htmlspecialchars($settings['smtp_port']) ?>" placeholder="587">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="smtp_username" class="form-label"><?= $t['smtp_username'] ?></label>
                                    <input type="text" class="form-control" id="smtp_username" name="smtp_username" 
                                           value="<?= htmlspecialchars($settings['smtp_username']) ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="smtp_password" class="form-label"><?= $t['smtp_password'] ?></label>
                                    <input type="password" class="form-control" id="smtp_password" name="smtp_password" 
                                           value="<?= htmlspecialchars($settings['smtp_password']) ?>">
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="smtp_encryption" class="form-label"><?= $t['smtp_encryption'] ?></label>
                            <select class="form-select" id="smtp_encryption" name="smtp_encryption">
                                <option value="tls" <?= $settings['smtp_encryption'] === 'tls' ? 'selected' : '' ?>>TLS</option>
                                <option value="ssl" <?= $settings['smtp_encryption'] === 'ssl' ? 'selected' : '' ?>>SSL</option>
                                <option value="none" <?= $settings['smtp_encryption'] === 'none' ? 'selected' : '' ?>>Aucun</option>
                            </select>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <button type="button" class="btn btn-outline-info" onclick="testSmtp()">
                                <i class="fas fa-vial me-1"></i>
                                <?= $t['test_smtp'] ?>
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>
                                <?= $t['save'] ?>
                            </button>
                        </div>
                    </form>
                </div>
                
                <!-- System Settings -->
                <div class="tab-pane fade" id="system" role="tabpanel">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card border-0 bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="fas fa-info-circle text-info me-2"></i>
                                        Informations Système
                                    </h6>
                                    <ul class="list-unstyled mb-0">
                                        <li><strong>Version PHP:</strong> <?= PHP_VERSION ?></li>
                                        <li><strong>Serveur Web:</strong> <?= $_SERVER['SERVER_SOFTWARE'] ?? 'N/A' ?></li>
                                        <li><strong>Base de données:</strong> MySQL/MariaDB</li>
                                        <li><strong>Espace disque:</strong> <?= disk_free_space('.') ? round(disk_free_space('.') / 1024 / 1024 / 1024, 2) . ' GB' : 'N/A' ?></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-0 bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="fas fa-chart-line text-success me-2"></i>
                                        Statistiques
                                    </h6>
                                    <ul class="list-unstyled mb-0">
                                        <li><strong>Utilisateurs actifs:</strong> <?= $pdo->query("SELECT COUNT(*) FROM users WHERE status = 'active'")->fetchColumn() ?></li>
                                        <li><strong>Landing pages:</strong> <?= $pdo->query("SELECT COUNT(*) FROM landing_pages")->fetchColumn() ?></li>
                                        <li><strong>Produits:</strong> <?= $pdo->query("SELECT COUNT(*) FROM products")->fetchColumn() ?></li>
                                        <li><strong>Commandes:</strong> <?= $pdo->query("SELECT COUNT(*) FROM orders")->fetchColumn() ?></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Test SMTP Modal -->
    <div class="modal fade" id="testSmtpModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <form method="POST" id="testSmtpForm">
                    <div class="modal-header">
                        <h5 class="modal-title">Test Configuration SMTP</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <input type="hidden" name="action" value="test_smtp">
                        <input type="hidden" name="smtp_host" id="test_smtp_host">
                        <input type="hidden" name="smtp_port" id="test_smtp_port">
                        <input type="hidden" name="smtp_username" id="test_smtp_username">
                        <input type="hidden" name="smtp_password" id="test_smtp_password">
                        <input type="hidden" name="smtp_encryption" id="test_smtp_encryption">
                        
                        <p>Voulez-vous tester la configuration SMTP actuelle ?</p>
                        <div class="alert alert-info">
                            <small>Un email de test sera envoyé à l'adresse administrateur.</small>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?= $t['cancel'] ?></button>
                        <button type="submit" class="btn btn-info"><?= $t['test_smtp'] ?></button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function testSmtp() {
            // Copier les valeurs actuelles dans le formulaire de test
            document.getElementById('test_smtp_host').value = document.getElementById('smtp_host').value;
            document.getElementById('test_smtp_port').value = document.getElementById('smtp_port').value;
            document.getElementById('test_smtp_username').value = document.getElementById('smtp_username').value;
            document.getElementById('test_smtp_password').value = document.getElementById('smtp_password').value;
            document.getElementById('test_smtp_encryption').value = document.getElementById('smtp_encryption').value;
            
            new bootstrap.Modal(document.getElementById('testSmtpModal')).show();
        }
    </script>
</body>
</html>