/**
 * Styles des composants spécifiques
 * Header, Hero, Features, Templates, Testimonials, Pricing, Footer
 */

/* ===== HEADER & NAVIGATION ===== */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--gray-200);
  z-index: 1000;
  transition: all var(--transition-normal);
}

.header.scrolled {
  background-color: rgba(255, 255, 255, 0.98);
  box-shadow: var(--shadow-md);
}

.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-4) 0;
  min-height: 70px;
}

.navbar-brand {
  display: flex;
  align-items: center;
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--gray-900);
  text-decoration: none;
}

.navbar-brand img {
  height: 40px;
  margin-right: var(--spacing-3);
}

.rtl .navbar-brand img {
  margin-right: 0;
  margin-left: var(--spacing-3);
}

.navbar-nav {
  display: flex;
  align-items: center;
  list-style: none;
  gap: var(--spacing-8);
}

.navbar-nav .nav-link {
  font-weight: 500;
  color: var(--gray-700);
  transition: color var(--transition-fast);
  position: relative;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
  color: var(--primary-color);
}

.navbar-nav .nav-link::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--primary-color);
  transition: width var(--transition-fast);
}

.navbar-nav .nav-link:hover::after,
.navbar-nav .nav-link.active::after {
  width: 100%;
}

.navbar-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
}

.language-switcher {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.language-switcher .lang-btn {
  padding: var(--spacing-1) var(--spacing-2);
  font-size: var(--font-size-sm);
  border: 1px solid var(--gray-300);
  background: var(--white);
  color: var(--gray-600);
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.language-switcher .lang-btn.active,
.language-switcher .lang-btn:hover {
  background: var(--primary-color);
  color: var(--white);
  border-color: var(--primary-color);
}

/* Mobile Menu */
.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  gap: 4px;
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--spacing-2);
}

.mobile-menu-toggle span {
  width: 25px;
  height: 3px;
  background-color: var(--gray-700);
  transition: all var(--transition-fast);
  border-radius: 2px;
}

.mobile-menu-toggle.active span:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.mobile-menu-toggle.active span:nth-child(2) {
  opacity: 0;
}

.mobile-menu-toggle.active span:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

.mobile-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--white);
  border-top: 1px solid var(--gray-200);
  box-shadow: var(--shadow-lg);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all var(--transition-normal);
}

.mobile-menu.active {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.mobile-menu .navbar-nav {
  flex-direction: column;
  padding: var(--spacing-6);
  gap: var(--spacing-4);
}

.mobile-menu .navbar-actions {
  padding: 0 var(--spacing-6) var(--spacing-6);
  border-top: 1px solid var(--gray-200);
  margin-top: var(--spacing-4);
  padding-top: var(--spacing-6);
}

/* ===== HERO SECTION ===== */
.hero {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: var(--white);
  padding: 120px 0 var(--spacing-20);
  position: relative;
  overflow: hidden;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse"><path d="M 50 0 L 0 0 0 50" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100%" height="100%" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.hero-content {
  position: relative;
  z-index: 2;
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.hero-title {
  font-size: var(--font-size-5xl);
  font-weight: 800;
  margin-bottom: var(--spacing-6);
  line-height: 1.1;
}

.hero-subtitle {
  font-size: var(--font-size-xl);
  margin-bottom: var(--spacing-4);
  opacity: 0.9;
  font-weight: 500;
}

.hero-description {
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-8);
  opacity: 0.8;
  line-height: 1.6;
}

.hero-actions {
  display: flex;
  gap: var(--spacing-4);
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: var(--spacing-12);
}

.hero-trusted {
  opacity: 0.7;
  font-size: var(--font-size-sm);
}

.hero-stats {
  display: flex;
  justify-content: center;
  gap: var(--spacing-8);
  margin-top: var(--spacing-8);
  flex-wrap: wrap;
}

.hero-stat {
  text-align: center;
}

.hero-stat-number {
  display: block;
  font-size: var(--font-size-2xl);
  font-weight: 700;
  margin-bottom: var(--spacing-1);
}

.hero-stat-label {
  font-size: var(--font-size-sm);
  opacity: 0.8;
}

/* ===== FEATURES SECTION ===== */
.features {
  background: var(--gray-50);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-8);
  margin-top: var(--spacing-12);
}

.feature-card {
  background: var(--white);
  padding: var(--spacing-8);
  border-radius: var(--border-radius-xl);
  text-align: center;
  transition: transform var(--transition-normal), box-shadow var(--transition-normal);
  border: 1px solid var(--gray-200);
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-xl);
}

.feature-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  border-radius: var(--border-radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--spacing-6);
  font-size: var(--font-size-2xl);
  color: var(--white);
}

.feature-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  margin-bottom: var(--spacing-4);
  color: var(--gray-900);
}

.feature-description {
  color: var(--gray-600);
  line-height: 1.6;
}

/* ===== TEMPLATES SECTION ===== */
.templates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-6);
  margin-top: var(--spacing-12);
}

.template-card {
  background: var(--white);
  border-radius: var(--border-radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow);
  transition: all var(--transition-normal);
  position: relative;
}

.template-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-xl);
}

.template-image {
  width: 100%;
  height: 200px;
  background: var(--gray-200);
  position: relative;
  overflow: hidden;
}

.template-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-slow);
}

.template-card:hover .template-image img {
  transform: scale(1.05);
}

.template-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(37, 99, 235, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.template-card:hover .template-overlay {
  opacity: 1;
}

.template-actions {
  display: flex;
  gap: var(--spacing-3);
}

.template-content {
  padding: var(--spacing-6);
}

.template-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  margin-bottom: var(--spacing-2);
  color: var(--gray-900);
}

.template-description {
  color: var(--gray-600);
  font-size: var(--font-size-sm);
  margin-bottom: var(--spacing-4);
}

.template-tags {
  display: flex;
  gap: var(--spacing-2);
  flex-wrap: wrap;
}

.template-tag {
  background: var(--gray-100);
  color: var(--gray-700);
  padding: var(--spacing-1) var(--spacing-3);
  border-radius: var(--border-radius-full);
  font-size: var(--font-size-xs);
  font-weight: 500;
}

/* ===== TESTIMONIALS SECTION ===== */
.testimonials {
  background: var(--white);
}

.testimonials-slider {
  margin-top: var(--spacing-12);
}

.testimonial-card {
  background: var(--white);
  padding: var(--spacing-8);
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow-lg);
  text-align: center;
  margin: var(--spacing-4);
  border: 1px solid var(--gray-100);
}

.testimonial-content {
  font-size: var(--font-size-lg);
  line-height: 1.6;
  color: var(--gray-700);
  margin-bottom: var(--spacing-6);
  font-style: italic;
}

.testimonial-author {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-4);
}

.testimonial-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid var(--primary-color);
}

.testimonial-info h4 {
  font-size: var(--font-size-base);
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--spacing-1);
}

.testimonial-info p {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
  margin: 0;
}

.testimonial-rating {
  display: flex;
  justify-content: center;
  gap: var(--spacing-1);
  margin-bottom: var(--spacing-4);
}

.testimonial-rating .star {
  color: #fbbf24;
  font-size: var(--font-size-lg);
}

/* ===== PRICING SECTION ===== */
.pricing {
  background: var(--gray-50);
}

.pricing-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-12);
}

.pricing-switch {
  position: relative;
  width: 60px;
  height: 30px;
  background: var(--gray-300);
  border-radius: var(--border-radius-full);
  cursor: pointer;
  transition: background-color var(--transition-fast);
}

.pricing-switch.active {
  background: var(--primary-color);
}

.pricing-switch::after {
  content: '';
  position: absolute;
  top: 3px;
  left: 3px;
  width: 24px;
  height: 24px;
  background: var(--white);
  border-radius: 50%;
  transition: transform var(--transition-fast);
}

.pricing-switch.active::after {
  transform: translateX(30px);
}

.pricing-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-8);
  margin-top: var(--spacing-12);
}

.pricing-card {
  background: var(--white);
  border-radius: var(--border-radius-xl);
  padding: var(--spacing-8);
  text-align: center;
  position: relative;
  border: 2px solid var(--gray-200);
  transition: all var(--transition-normal);
}

.pricing-card.featured {
  border-color: var(--primary-color);
  transform: scale(1.05);
  box-shadow: var(--shadow-xl);
}

.pricing-card:hover {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-lg);
}

.pricing-badge {
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--primary-color);
  color: var(--white);
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--border-radius-full);
  font-size: var(--font-size-sm);
  font-weight: 600;
}

.pricing-title {
  font-size: var(--font-size-xl);
  font-weight: 700;
  margin-bottom: var(--spacing-4);
  color: var(--gray-900);
}

.pricing-price {
  margin-bottom: var(--spacing-6);
}

.pricing-amount {
  font-size: var(--font-size-4xl);
  font-weight: 800;
  color: var(--primary-color);
}

.pricing-period {
  font-size: var(--font-size-base);
  color: var(--gray-600);
  margin-left: var(--spacing-2);
}

.rtl .pricing-period {
  margin-left: 0;
  margin-right: var(--spacing-2);
}

.pricing-description {
  color: var(--gray-600);
  margin-bottom: var(--spacing-8);
}

.pricing-features {
  list-style: none;
  margin-bottom: var(--spacing-8);
}

.pricing-features li {
  padding: var(--spacing-3) 0;
  border-bottom: 1px solid var(--gray-100);
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.pricing-features li:last-child {
  border-bottom: none;
}

.pricing-features .check {
  color: var(--secondary-color);
  font-weight: 600;
}

.pricing-features .cross {
  color: var(--gray-400);
}

/* ===== CONTACT SECTION ===== */
.contact {
  background: var(--white);
}

.contact-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-12);
  margin-top: var(--spacing-12);
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-8);
}

.contact-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-4);
}

.contact-icon {
  width: 50px;
  height: 50px;
  background: var(--primary-color);
  color: var(--white);
  border-radius: var(--border-radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xl);
  flex-shrink: 0;
}

.contact-details h4 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  margin-bottom: var(--spacing-2);
  color: var(--gray-900);
}

.contact-details p {
  color: var(--gray-600);
  margin: 0;
}

.contact-form {
  background: var(--gray-50);
  padding: var(--spacing-8);
  border-radius: var(--border-radius-xl);
}

/* ===== FOOTER ===== */
.footer {
  background: var(--gray-900);
  color: var(--gray-300);
  padding: var(--spacing-16) 0 var(--spacing-8);
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-8);
  margin-bottom: var(--spacing-12);
}

.footer-section h4 {
  color: var(--white);
  font-size: var(--font-size-lg);
  font-weight: 600;
  margin-bottom: var(--spacing-6);
}

.footer-section ul {
  list-style: none;
}

.footer-section ul li {
  margin-bottom: var(--spacing-3);
}

.footer-section ul li a {
  color: var(--gray-400);
  transition: color var(--transition-fast);
}

.footer-section ul li a:hover {
  color: var(--white);
}

.footer-social {
  display: flex;
  gap: var(--spacing-4);
  margin-top: var(--spacing-6);
}

.footer-social a {
  width: 40px;
  height: 40px;
  background: var(--gray-800);
  color: var(--gray-400);
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-fast);
}

.footer-social a:hover {
  background: var(--primary-color);
  color: var(--white);
}

.footer-newsletter {
  display: flex;
  gap: var(--spacing-3);
  margin-top: var(--spacing-4);
}

.footer-newsletter input {
  flex: 1;
  padding: var(--spacing-3);
  border: 1px solid var(--gray-700);
  background: var(--gray-800);
  color: var(--white);
  border-radius: var(--border-radius);
}

.footer-newsletter input::placeholder {
  color: var(--gray-500);
}

.footer-bottom {
  border-top: 1px solid var(--gray-800);
  padding-top: var(--spacing-8);
  text-align: center;
  color: var(--gray-500);
}

/* ===== STICKY CTA ===== */
.sticky-cta {
  position: fixed;
  bottom: var(--spacing-4);
  left: var(--spacing-4);
  right: var(--spacing-4);
  z-index: 999;
  background: var(--primary-color);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-xl);
  transform: translateY(100px);
  opacity: 0;
  transition: all var(--transition-normal);
}

.sticky-cta.visible {
  transform: translateY(0);
  opacity: 1;
}

.sticky-cta .btn {
  width: 100%;
  background: transparent;
  border: none;
  color: var(--white);
  font-weight: 600;
  padding: var(--spacing-4);
}

/* ===== RESPONSIVE ADJUSTMENTS ===== */
@media (max-width: 767px) {
  .navbar-nav {
    display: none;
  }
  
  .mobile-menu-toggle {
    display: flex;
  }
  
  .hero {
    padding: 100px 0 var(--spacing-16);
  }
  
  .hero-title {
    font-size: var(--font-size-3xl);
  }
  
  .hero-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .hero-stats {
    gap: var(--spacing-6);
  }
  
  .features-grid,
  .templates-grid,
  .pricing-grid {
    grid-template-columns: 1fr;
  }
  
  .contact-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-8);
  }
  
  .footer-newsletter {
    flex-direction: column;
  }
  
  .pricing-card.featured {
    transform: none;
  }
}

@media (min-width: 768px) {
  .mobile-menu-toggle {
    display: none;
  }
  
  .navbar-nav {
    display: flex;
  }
}