<?php
require_once 'api/config/database.php';

try {
    $database = new Database();
    $pdo = $database->getConnection();
    
    echo "Création de la table subscriptions...\n";
    
    // Créer la table subscriptions
    $createSubscriptions = "
    CREATE TABLE IF NOT EXISTS subscriptions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        display_name VARCHAR(100) NOT NULL,
        description TEXT,
        price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        currency VARCHAR(3) DEFAULT 'USD',
        billing_cycle ENUM('monthly', 'yearly') DEFAULT 'monthly',
        
        max_products INT DEFAULT 10,
        max_landing_pages INT DEFAULT 1,
        max_categories INT DEFAULT 5,
        max_subcategories INT DEFAULT 10,
        max_storage_mb INT DEFAULT 1000,
        max_bandwidth_gb INT DEFAULT 10,
        
        ai_enabled BOOLEAN DEFAULT FALSE,
        ai_monthly_tokens INT DEFAULT 0,
        ai_models JSON,
        
        support_level ENUM('basic', 'priority', 'premium') DEFAULT 'basic',
        custom_domain BOOLEAN DEFAULT FALSE,
        analytics_advanced BOOLEAN DEFAULT FALSE,
        white_label BOOLEAN DEFAULT FALSE,
        api_access BOOLEAN DEFAULT FALSE,
        
        is_active BOOLEAN DEFAULT TRUE,
        is_featured BOOLEAN DEFAULT FALSE,
        sort_order INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    
    $pdo->exec($createSubscriptions);
    echo "✅ Table subscriptions créée\n";
    
    // Créer la table user_subscriptions
    $createUserSubscriptions = "
    CREATE TABLE IF NOT EXISTS user_subscriptions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id VARCHAR(255) NOT NULL,
        subscription_id INT NOT NULL,
        status ENUM('active', 'cancelled', 'expired', 'suspended') DEFAULT 'active',
        started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        expires_at TIMESTAMP NULL,
        cancelled_at TIMESTAMP NULL,
        payment_method VARCHAR(50),
        payment_id VARCHAR(255),
        last_payment_at TIMESTAMP NULL,
        next_payment_at TIMESTAMP NULL,
        current_products INT DEFAULT 0,
        current_landing_pages INT DEFAULT 0,
        current_categories INT DEFAULT 0,
        current_storage_mb INT DEFAULT 0,
        current_bandwidth_gb INT DEFAULT 0,
        current_ai_tokens INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (subscription_id) REFERENCES subscriptions(id) ON DELETE RESTRICT
    )";
    
    $pdo->exec($createUserSubscriptions);
    echo "✅ Table user_subscriptions créée\n";
    
    // Insérer les plans par défaut
    $insertPlans = "
    INSERT IGNORE INTO subscriptions (name, display_name, description, price, max_products, max_landing_pages, max_categories, max_subcategories, max_storage_mb, ai_enabled, ai_monthly_tokens, support_level, sort_order) VALUES
    ('free', 'Gratuit', 'Plan gratuit pour commencer', 0.00, 3, 1, 2, 5, 100, FALSE, 0, 'basic', 1),
    ('basic', 'Basic', 'Plan de base pour petites entreprises', 9.99, 25, 3, 10, 20, 1000, TRUE, 10000, 'basic', 2),
    ('pro', 'Pro', 'Plan professionnel pour entreprises en croissance', 29.99, 100, 10, 50, 100, 5000, TRUE, 50000, 'priority', 3),
    ('premium', 'Premium', 'Plan premium avec toutes les fonctionnalités', 99.99, -1, -1, -1, -1, -1, TRUE, -1, 'premium', 4)
    ";
    
    $pdo->exec($insertPlans);
    echo "✅ Plans d'abonnement insérés\n";
    
    // Mettre à jour avec les modèles IA
    $pdo->exec("UPDATE subscriptions SET ai_models = JSON_ARRAY('gpt-3.5-turbo') WHERE name = 'basic'");
    $pdo->exec("UPDATE subscriptions SET ai_models = JSON_ARRAY('gpt-3.5-turbo', 'gpt-4o-mini', 'claude-3-haiku') WHERE name = 'pro'");
    $pdo->exec("UPDATE subscriptions SET ai_models = JSON_ARRAY('gpt-3.5-turbo', 'gpt-4o-mini', 'gpt-4', 'claude-3', 'claude-3-haiku', 'gemini-pro') WHERE name = 'premium'");
    echo "✅ Modèles IA configurés\n";
    
    // Vérifier les résultats
    echo "\n📊 Plans d'abonnement créés :\n";
    $stmt = $pdo->query("SELECT name, display_name, price, max_products, max_landing_pages, ai_enabled, ai_monthly_tokens FROM subscriptions ORDER BY sort_order");
    $subscriptions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($subscriptions as $sub) {
        echo "- {$sub['display_name']} ({$sub['name']}):\n";
        echo "  Prix: {$sub['price']}€/mois\n";
        echo "  Produits: " . ($sub['max_products'] == -1 ? 'Illimité' : $sub['max_products']) . "\n";
        echo "  Landing Pages: " . ($sub['max_landing_pages'] == -1 ? 'Illimité' : $sub['max_landing_pages']) . "\n";
        echo "  IA: " . ($sub['ai_enabled'] ? 'Oui' : 'Non');
        if ($sub['ai_enabled']) {
            echo " (" . ($sub['ai_monthly_tokens'] == -1 ? 'Illimité' : number_format($sub['ai_monthly_tokens'])) . " tokens/mois)";
        }
        echo "\n\n";
    }
    
    echo "✅ Structure des abonnements créée avec succès !\n";
    
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
}
?>
