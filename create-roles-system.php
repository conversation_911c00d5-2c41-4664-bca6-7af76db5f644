<?php
require_once 'php/config/database.php';

header('Content-Type: text/html; charset=UTF-8');

echo "<h1>👥 Création du système de rôles</h1>";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .success{color:green;} .error{color:red;} .warning{color:orange;}</style>";

try {
    // 1. Créer la table roles
    echo "<h2>🏗️ Création de la table roles</h2>";

    $sql = "
    CREATE TABLE IF NOT EXISTS roles (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(50) NOT NULL UNIQUE,
        display_name VARCHAR(100) NOT NULL,
        description TEXT,
        permissions JSON NOT NULL,
        is_system TINYINT(1) DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_name (name)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";

    $pdo->exec($sql);
    echo "<p class='success'>✅ Table roles créée avec succès</p>";

    // 2. Créer la table user_roles pour associer les utilisateurs aux rôles
    echo "<h2>🔗 Création de la table user_roles</h2>";

    $sql = "
    CREATE TABLE IF NOT EXISTS user_roles (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id VARCHAR(255) NOT NULL,
        role_id INT NOT NULL,
        assigned_by VARCHAR(255),
        assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_user_id (user_id),
        INDEX idx_role_id (role_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";

    $pdo->exec($sql);
    echo "<p class='success'>✅ Table user_roles créée avec succès</p>";

    // 3. Insérer les rôles par défaut
    echo "<h2>📋 Insertion des rôles par défaut</h2>";

    $default_roles = [
        [
            'name' => 'admin',
            'display_name' => 'Administrateur',
            'description' => 'Accès complet à toutes les fonctionnalités du système',
            'permissions' => json_encode([
                'users' => ['create', 'read', 'update', 'delete'],
                'stores' => ['create', 'read', 'update', 'delete'],
                'products' => ['create', 'read', 'update', 'delete'],
                'orders' => ['create', 'read', 'update', 'delete'],
                'analytics' => ['read'],
                'settings' => ['read', 'update'],
                'roles' => ['create', 'read', 'update', 'delete']
            ]),
            'is_system' => 1
        ],
        [
            'name' => 'merchant',
            'display_name' => 'Marchand',
            'description' => 'Peut gérer ses propres magasins et produits',
            'permissions' => json_encode([
                'stores' => ['create', 'read', 'update'],
                'products' => ['create', 'read', 'update', 'delete'],
                'orders' => ['read', 'update'],
                'analytics' => ['read']
            ]),
            'is_system' => 1
        ],
        [
            'name' => 'customer',
            'display_name' => 'Client',
            'description' => 'Peut passer des commandes et consulter ses achats',
            'permissions' => json_encode([
                'products' => ['read'],
                'orders' => ['create', 'read']
            ]),
            'is_system' => 1
        ],
        [
            'name' => 'moderator',
            'display_name' => 'Modérateur',
            'description' => 'Peut modérer le contenu et gérer les utilisateurs',
            'permissions' => json_encode([
                'users' => ['read', 'update'],
                'stores' => ['read', 'update'],
                'products' => ['read', 'update', 'delete'],
                'orders' => ['read', 'update']
            ]),
            'is_system' => 0
        ]
    ];

    $stmt = $pdo->prepare("
        INSERT INTO roles (name, display_name, description, permissions, is_system)
        VALUES (?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
        display_name = VALUES(display_name),
        description = VALUES(description),
        permissions = VALUES(permissions)
    ");

    foreach ($default_roles as $role) {
        $stmt->execute([
            $role['name'],
            $role['display_name'],
            $role['description'],
            $role['permissions'],
            $role['is_system']
        ]);
    }

    echo "<p class='success'>✅ " . count($default_roles) . " rôles par défaut créés</p>";

    // 4. Assigner des rôles aux utilisateurs de test
    echo "<h2>👤 Attribution de rôles aux utilisateurs de test</h2>";

    $user_roles = [
        ['firebase_uid_1', 'admin'],
        ['firebase_uid_2', 'merchant']
    ];

    $stmt = $pdo->prepare("
        INSERT IGNORE INTO user_roles (user_id, role_id, assigned_by)
        SELECT ?, r.id, 'system'
        FROM roles r
        WHERE r.name = ?
    ");

    foreach ($user_roles as $user_role) {
        $stmt->execute($user_role);
    }

    echo "<p class='success'>✅ Rôles attribués aux utilisateurs de test</p>";

    // 5. Afficher les rôles créés
    echo "<h2>📋 Rôles créés</h2>";

    $stmt = $pdo->query("SELECT * FROM roles ORDER BY is_system DESC, name");
    $roles = $stmt->fetchAll();

    echo "<table border='1' style='border-collapse:collapse; width:100%;'>";
    echo "<tr><th>ID</th><th>Nom</th><th>Nom d'affichage</th><th>Description</th><th>Système</th><th>Permissions</th></tr>";
    foreach ($roles as $role) {
        $permissions = json_decode($role['permissions'], true);
        $permissions_str = '';
        foreach ($permissions as $resource => $actions) {
            $permissions_str .= $resource . ': ' . implode(', ', $actions) . '<br>';
        }

        echo "<tr>";
        echo "<td>" . $role['id'] . "</td>";
        echo "<td>" . htmlspecialchars($role['name']) . "</td>";
        echo "<td>" . htmlspecialchars($role['display_name']) . "</td>";
        echo "<td>" . htmlspecialchars($role['description']) . "</td>";
        echo "<td>" . ($role['is_system'] ? 'Oui' : 'Non') . "</td>";
        echo "<td style='font-size:12px;'>" . $permissions_str . "</td>";
        echo "</tr>";
    }
    echo "</table>";

    // 6. Afficher les attributions de rôles
    echo "<h2>🔗 Attributions de rôles</h2>";

    $stmt = $pdo->query("
        SELECT ur.user_id, r.name as role_name, r.display_name, ur.assigned_at
        FROM user_roles ur
        JOIN roles r ON ur.role_id = r.id
        ORDER BY ur.assigned_at DESC
    ");
    $assignments = $stmt->fetchAll();

    echo "<table border='1' style='border-collapse:collapse;'>";
    echo "<tr><th>User ID</th><th>Rôle</th><th>Nom d'affichage</th><th>Attribué le</th></tr>";
    foreach ($assignments as $assignment) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($assignment['user_id']) . "</td>";
        echo "<td>" . htmlspecialchars($assignment['role_name']) . "</td>";
        echo "<td>" . htmlspecialchars($assignment['display_name']) . "</td>";
        echo "<td>" . $assignment['assigned_at'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} catch (Exception $e) {
    echo "<p class='error'>❌ Erreur: " . $e->getMessage() . "</p>";
}
