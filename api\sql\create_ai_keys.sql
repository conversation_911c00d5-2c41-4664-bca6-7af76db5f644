-- Création des tables pour la gestion des API Keys IA

-- Table des modèles IA disponibles
CREATE TABLE IF NOT EXISTS ai_models (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE COMMENT 'Nom du modèle (ex: gpt-4, claude-3)',
    display_name VARCHAR(100) NOT NULL COMMENT 'Nom affiché',
    provider VARCHAR(50) NOT NULL COMMENT 'Fournisseur (OpenAI, Anthropic, Google, Meta)',
    model_type ENUM('text', 'image', 'audio', 'multimodal') DEFAULT 'text',
    cost_per_token DECIMAL(10,8) DEFAULT 0.0 COMMENT 'Coût par token en USD',
    max_tokens INT DEFAULT 4096 COMMENT 'Nombre max de tokens par requête',
    context_window INT DEFAULT 4096 COMMENT 'Taille de la fenêtre de contexte',
    is_active BOOLEAN DEFAULT TRUE,
    is_premium BOOLEAN DEFAULT FALSE COMMENT 'Modèle premium',
    tier ENUM('economique', 'premium') DEFAULT 'economique',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_provider (provider),
    INDEX idx_active (is_active),
    INDEX idx_tier (tier)
);

-- Table des API Keys
CREATE TABLE IF NOT EXISTS ai_api_keys (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT 'Nom de la clé API',
    provider VARCHAR(50) NOT NULL COMMENT 'Fournisseur (OpenAI, Anthropic, etc.)',
    api_key TEXT NOT NULL COMMENT 'Clé API (chiffrée)',
    status ENUM('active', 'inactive', 'expired', 'error') DEFAULT 'active',
    
    -- Limites et usage
    monthly_limit DECIMAL(10,2) DEFAULT 0.00 COMMENT 'Limite mensuelle en USD',
    current_usage DECIMAL(10,2) DEFAULT 0.00 COMMENT 'Usage actuel ce mois',
    total_tokens_used BIGINT DEFAULT 0 COMMENT 'Total des tokens utilisés',
    total_requests INT DEFAULT 0 COMMENT 'Nombre total de requêtes',
    
    -- Métadonnées
    last_used_at TIMESTAMP NULL COMMENT 'Dernière utilisation',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_provider (provider),
    INDEX idx_status (status),
    INDEX idx_last_used (last_used_at)
);

-- Table de l'usage IA par utilisateur
CREATE TABLE IF NOT EXISTS ai_usage (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL COMMENT 'ID utilisateur Firebase',
    model_id INT NOT NULL COMMENT 'ID du modèle utilisé',
    api_key_id INT NOT NULL COMMENT 'ID de la clé API utilisée',
    
    -- Détails de la requête
    request_type ENUM('text_generation', 'image_generation', 'translation', 'summarization', 'other') DEFAULT 'text_generation',
    prompt_tokens INT DEFAULT 0 COMMENT 'Tokens du prompt',
    completion_tokens INT DEFAULT 0 COMMENT 'Tokens de la réponse',
    total_tokens INT DEFAULT 0 COMMENT 'Total des tokens',
    
    -- Coût et performance
    cost_usd DECIMAL(10,6) DEFAULT 0.000000 COMMENT 'Coût en USD',
    response_time_ms INT DEFAULT 0 COMMENT 'Temps de réponse en ms',
    
    -- Métadonnées
    success BOOLEAN DEFAULT TRUE COMMENT 'Requête réussie',
    error_message TEXT NULL COMMENT 'Message d\'erreur si échec',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (model_id) REFERENCES ai_models(id) ON DELETE RESTRICT,
    FOREIGN KEY (api_key_id) REFERENCES ai_api_keys(id) ON DELETE RESTRICT,
    
    INDEX idx_user (user_id),
    INDEX idx_model (model_id),
    INDEX idx_created (created_at),
    INDEX idx_success (success)
);

-- Insérer les modèles IA populaires
INSERT INTO ai_models (name, display_name, provider, cost_per_token, max_tokens, context_window, is_premium, tier) VALUES
-- OpenAI
('gpt-3.5-turbo', 'GPT-3.5-turbo', 'OpenAI', 0.0000020, 4096, 16385, FALSE, 'economique'),
('gpt-4o-mini', 'GPT-4o-mini', 'OpenAI', 0.0000005, 16384, 128000, FALSE, 'economique'),
('gpt-4', 'GPT-4', 'OpenAI', 0.0000300, 8192, 8192, TRUE, 'premium'),
('gpt-4-turbo', 'GPT-4-turbo', 'OpenAI', 0.0000200, 4096, 128000, TRUE, 'premium'),

-- Anthropic
('claude-3-haiku', 'Claude 3 Haiku', 'Anthropic', 0.00000025, 4096, 200000, FALSE, 'economique'),
('claude-3', 'Claude 3', 'Anthropic', 0.0000030, 4096, 200000, TRUE, 'premium'),

-- Google
('gemini-1.5-flash', 'Gemini 1.5 Flash', 'Google', 0.00000015, 8192, 1000000, FALSE, 'economique'),
('gemini-pro', 'Gemini Pro', 'Google', 0.0000015, 32768, 2000000, TRUE, 'premium'),

-- Meta
('llama-3.1-8b', 'Llama 3.1 8B', 'Meta', 0.00000005, 8192, 128000, FALSE, 'economique');

-- Insérer quelques API Keys de démonstration (avec des clés factices)
INSERT INTO ai_api_keys (name, provider, api_key, status, monthly_limit, current_usage, total_tokens_used, total_requests) VALUES
('OpenAI Production', 'OpenAI', 'sk-demo-key-openai-production-encrypted', 'active', 100.00, 45.72, 15240, 89),
('Anthropic Main', 'Anthropic', 'sk-demo-key-anthropic-main-encrypted', 'active', 50.00, 26.85, 8950, 67),
('Google AI Key', 'Google', 'demo-google-ai-key-encrypted', 'inactive', 30.00, 18.15, 12100, 45),
('OpenAI Backup', 'OpenAI', 'sk-demo-key-openai-backup-encrypted', 'active', 25.00, 5.14, 25680, 156),
('Meta Llama', 'Meta', 'demo-meta-llama-key-encrypted', 'inactive', 15.00, 2.26, 45200, 234);

-- Insérer quelques données d'usage pour les statistiques
INSERT INTO ai_usage (user_id, model_id, api_key_id, request_type, prompt_tokens, completion_tokens, total_tokens, cost_usd, response_time_ms, success) VALUES
('firebase_user_1', 1, 1, 'text_generation', 150, 300, 450, 0.0009, 1200, TRUE),
('firebase_user_1', 2, 1, 'text_generation', 200, 400, 600, 0.0003, 800, TRUE),
('firebase_user_2', 5, 2, 'text_generation', 180, 350, 530, 0.0013, 1500, TRUE),
('firebase_user_1', 1, 1, 'summarization', 500, 150, 650, 0.0013, 900, TRUE),
('firebase_user_3', 3, 1, 'text_generation', 100, 200, 300, 0.0090, 2000, TRUE);
