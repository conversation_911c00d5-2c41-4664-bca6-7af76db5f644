-- Create payment_gateway_configs table
CREATE TABLE IF NOT EXISTS payment_gateway_configs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    store_id INT NOT NULL,
    gateway_name VARCHAR(50) NOT NULL,
    api_key TEXT,
    secret_key TEXT,
    endpoint VARCHAR(255),
    currency VARCHAR(10) DEFAULT 'DZD',
    mode ENUM('sandbox', 'live') DEFAULT 'sandbox',
    enabled BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_store_gateway (store_id, gateway_name)
);