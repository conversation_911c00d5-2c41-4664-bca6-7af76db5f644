<?php
require_once '../api/config/database.php';

// Initialize database connection
$database = new Database();
$db = $database->getConnection();

// Get all landing pages with template info
try {
    $query = "    SELECT 
        lp.id,
        lp.title,
        lp.slug,
        lp.status,
        COALESCE(lp.views, 0) as views_count,
        COALESCE(lp.conversions, 0) as conversions_count,
        lp.created_at,
        t.name as template_name,
        s.name as store_name,
        CASE 
            WHEN lp.views > 0 THEN ROUND(CAST(lp.conversions AS FLOAT) / lp.views * 100, 2)
            ELSE 0
        END as conversion_rate
    FROM landing_pages lp
    LEFT JOIN templates t ON lp.template_id = t.id
    LEFT JOIN stores s ON lp.store_id = s.id
    ORDER BY lp.created_at DESC
    ";

    $stmt = $db->prepare($query);
    $stmt->execute();
    $landing_pages = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    error_log('Erreur SQL : ' . $e->getMessage());
    $landing_pages = [];
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestion des Landing Pages</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <style>
        .action-buttons .btn { margin-right: 5px; }
        .status-badge {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.85em;
        }
        .status-draft { background-color: #ffd700; color: #000; }
        .status-published { background-color: #28a745; color: #fff; }
        .status-archived { background-color: #6c757d; color: #fff; }
        .page-slug {
            font-size: 0.8em;
            color: #6c757d;
            margin-top: 2px;
        }
        .stats-cell {
            text-align: right;
            font-family: monospace;
        }
    </style>
</head>
<body>

<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>Landing Pages</h2>
        <a href="create-landing-page.php" class="btn btn-primary">
            <i class="bi bi-plus-lg"></i> Nouvelle Landing Page
        </a>
    </div>

    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table id="landingPagesTable" class="table table-hover">
                    <thead>
                        <tr>
                            <th>Titre</th>
                            <th>Template</th>
                            <th>Boutique</th>
                            <th>Statut</th>
                            <th>Vues</th>
                            <th>Conversions</th>
                            <th>Créé le</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($landing_pages as $page): ?>
                        <tr>
                            <td>
    <div><?php echo htmlspecialchars($page['title']); ?></div>
    <div class="page-slug"><?php echo htmlspecialchars($page['slug']); ?></div>
</td>
                            <td><?php echo htmlspecialchars($page['template_name']); ?></td>
                            <td><?php echo htmlspecialchars($page['store_name']); ?></td>
                            <td>
                                <span class="status-badge status-<?php echo $page['status']; ?>">
                                    <?php 
                                    switch($page['status']) {
                                        case 'draft': echo 'Brouillon'; break;
                                        case 'published': echo 'Publié'; break;
                                        case 'archived': echo 'Archivé'; break;
                                    }
                                    ?>
                                </span>
                            </td>
                            <td class="stats-cell"><?php echo number_format($page['views_count']); ?></td>
                            <td class="stats-cell"><?php echo number_format($page['conversions_count']); ?></td>
                            <td><?php echo date('d/m/Y H:i', strtotime($page['created_at'])); ?></td>
                            <td class="action-buttons">
                                <div class="btn-group">
                                    <a href="preview.php?id=<?php echo $page['id']; ?>" 
                                       class="btn btn-sm btn-outline-primary" 
                                       target="_blank" 
                                       title="Aperçu">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    <a href="edit-landing-page.php?id=<?php echo $page['id']; ?>" 
                                       class="btn btn-sm btn-outline-secondary"
                                       title="Modifier">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    <button type="button" 
                                            class="btn btn-sm btn-outline-success"
                                            onclick="clonePage(<?php echo $page['id']; ?>)"
                                            title="Cloner">
                                        <i class="bi bi-files"></i>
                                    </button>
                                    <button type="button" 
                                            class="btn btn-sm btn-outline-danger"
                                            onclick="deletePage(<?php echo $page['id']; ?>)"
                                            title="Supprimer">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
<script>
$(document).ready(function() {
    // Initialize DataTable with French translation
    $('#landingPagesTable').DataTable({
        language: {
            url: '//cdn.datatables.net/plug-ins/1.11.5/i18n/fr-FR.json'
        },
        order: [[6, 'desc']],
        pageLength: 25
    });
});

function clonePage(id) {
    if (confirm('Voulez-vous vraiment cloner cette landing page ?')) {
        fetch(`/api/landing-pages.php?action=clone&id=${id}`, {
            method: 'POST',
            headers: {
                'Authorization': 'Bearer demo_token'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Erreur lors du clonage : ' + data.error);
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Une erreur est survenue lors du clonage');
        });
    }
}

function deletePage(id) {
    if (confirm('Voulez-vous vraiment supprimer cette landing page ?')) {
        fetch(`/api/landing-pages.php?action=delete&id=${id}`, {
            method: 'DELETE',
            headers: {
                'Authorization': 'Bearer demo_token'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Erreur lors de la suppression : ' + data.error);
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Une erreur est survenue lors de la suppression');
        });
    }
}
</script>

</body>
</html>