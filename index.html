<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>صفحات هبوط للجميع - منصة إنشاء صفحات الهبوط</title>
    <meta
      name="description"
      content="منصة متقدمة لإنشاء صفحات هبوط احترافية بسهولة وسرعة"
    />

    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        line-height: 1.6;
        color: #333;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
      }

      /* Header */
      .header {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        padding: 0.75rem 0;
        position: fixed;
        width: 100%;
        top: 0;
        z-index: 1000;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
      }

      .nav {
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
      }

      .logo {
        font-size: 1.4rem;
        font-weight: 700;
        color: #2c3e50;
        text-decoration: none;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
      }

      .logo:hover {
        transform: scale(1.05);
      }

      .logo::before {
        content: "🚀";
        font-size: 1.2rem;
      }

      .nav-links {
        display: flex;
        gap: 2.5rem;
        list-style: none;
        align-items: center;
      }

      .nav-links a {
        color: #2c3e50;
        text-decoration: none;
        font-weight: 500;
        padding: 0.5rem 1rem;
        border-radius: 8px;
        transition: all 0.3s ease;
        position: relative;
      }

      .nav-links a:hover {
        background: rgba(102, 126, 234, 0.1);
        color: #667eea;
        transform: translateY(-1px);
      }

      .nav-links a::after {
        content: "";
        position: absolute;
        bottom: -2px;
        left: 50%;
        width: 0;
        height: 2px;
        background: #667eea;
        transition: all 0.3s ease;
        transform: translateX(-50%);
      }

      .nav-links a:hover::after {
        width: 80%;
      }

      .auth-buttons {
        display: flex;
        gap: 1rem;
        align-items: center;
      }

      .btn-secondary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 0.6rem 1.8rem;
        border-radius: 25px;
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        position: relative;
        overflow: hidden;
      }

      .btn-secondary::before {
        content: "";
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(255, 255, 255, 0.2),
          transparent
        );
        transition: left 0.5s;
      }

      .btn-secondary:hover::before {
        left: 100%;
      }

      .btn-secondary:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
      }

      /* Hero Section */
      .hero {
        padding: 120px 0 80px;
        text-align: center;
        color: white;
      }

      .hero h1 {
        font-size: 3.5rem;
        margin-bottom: 1rem;
        font-weight: 700;
      }

      .hero p {
        font-size: 1.3rem;
        margin-bottom: 2rem;
        opacity: 0.9;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
      }

      .cta-buttons {
        display: flex;
        gap: 1rem;
        justify-content: center;
        flex-wrap: wrap;
        margin-top: 2rem;
      }

      .btn-primary {
        background: #ff6b6b;
        color: white;
        padding: 1rem 2.5rem;
        border: none;
        border-radius: 50px;
        font-size: 1.1rem;
        font-weight: 600;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s;
        box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
      }

      .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(255, 107, 107, 0.6);
      }

      .btn-outline {
        background: transparent;
        color: white;
        padding: 1rem 2.5rem;
        border: 2px solid white;
        border-radius: 50px;
        font-size: 1.1rem;
        font-weight: 600;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s;
      }

      .btn-outline:hover {
        background: white;
        color: #667eea;
      }

      /* Features Section */
      .features {
        padding: 80px 0;
        background: white;
      }

      .features h2 {
        text-align: center;
        font-size: 2.5rem;
        margin-bottom: 3rem;
        color: #333;
      }

      .features-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
      }

      .feature-card {
        text-align: center;
        padding: 2rem;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s;
      }

      .feature-card:hover {
        transform: translateY(-5px);
      }

      .feature-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
      }

      .feature-card h3 {
        font-size: 1.5rem;
        margin-bottom: 1rem;
        color: #333;
      }

      .feature-card p {
        color: #666;
        line-height: 1.6;
      }

      /* Pricing Section */
      .pricing {
        padding: 80px 0;
        background-color: #f8f9fa;
      }

      .pricing h2 {
        text-align: center;
        font-size: 2.5rem;
        margin-bottom: 1rem;
        color: #333;
      }

      .pricing p {
        text-align: center;
        font-size: 1.2rem;
        color: #666;
        margin-bottom: 3rem;
      }

      .pricing-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        max-width: 1000px;
        margin: 0 auto;
      }

      .pricing-card {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        position: relative;
        border: 2px solid transparent;
      }

      .pricing-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
      }

      .pricing-card.featured {
        border-color: #667eea;
        transform: scale(1.05);
      }

      .pricing-card.featured:hover {
        transform: scale(1.05) translateY(-10px);
      }

      .popular-badge {
        position: absolute;
        top: -10px;
        left: 50%;
        transform: translateX(-50%);
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 0.5rem 1.5rem;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: 600;
      }

      .pricing-header {
        text-align: center;
        margin-bottom: 2rem;
      }

      .pricing-header h3 {
        font-size: 1.5rem;
        margin-bottom: 1rem;
        color: #333;
      }

      .price {
        display: flex;
        align-items: baseline;
        justify-content: center;
        margin-bottom: 1rem;
      }

      .currency {
        font-size: 1.2rem;
        color: #666;
        margin-right: 0.25rem;
      }

      .amount {
        font-size: 3rem;
        font-weight: 700;
        color: #667eea;
      }

      .period {
        font-size: 1rem;
        color: #666;
        margin-left: 0.25rem;
      }

      .pricing-features {
        margin-bottom: 2rem;
      }

      .pricing-features ul {
        list-style: none;
        padding: 0;
        margin: 0;
      }

      .pricing-features li {
        padding: 0.75rem 0;
        border-bottom: 1px solid #eee;
        color: #555;
        font-size: 0.95rem;
      }

      .pricing-features li:last-child {
        border-bottom: none;
      }

      .pricing-footer {
        text-align: center;
      }

      .pricing-footer .btn-primary,
      .pricing-footer .btn-outline {
        width: 100%;
        padding: 1rem;
        font-weight: 600;
        text-decoration: none;
        border-radius: 8px;
        transition: all 0.3s ease;
      }

      /* CTA Section */
      .cta-section {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 80px 0;
        text-align: center;
        color: white;
      }

      .cta-section h2 {
        font-size: 2.5rem;
        margin-bottom: 1rem;
      }

      .cta-section p {
        font-size: 1.2rem;
        margin-bottom: 2rem;
        opacity: 0.9;
      }

      /* Contact Section */
      .contact {
        padding: 80px 0;
        background-color: #fff;
      }

      .contact h2 {
        font-size: 2.5rem;
        margin-bottom: 1rem;
        color: #333;
      }

      .contact p {
        font-size: 1.1rem;
        color: #666;
        margin-bottom: 2rem;
      }

      .contact-info {
        margin-top: 2rem;
      }

      .contact-item {
        display: flex;
        align-items: center;
        margin-bottom: 2rem;
        padding: 1.5rem;
        background: #f8f9fa;
        border-radius: 10px;
        transition: transform 0.3s ease;
      }

      .contact-item:hover {
        transform: translateX(10px);
      }

      .contact-item i {
        font-size: 1.5rem;
        color: #667eea;
        margin-right: 1rem;
        width: 30px;
        text-align: center;
      }

      .contact-item h4 {
        margin: 0 0 0.5rem 0;
        color: #333;
        font-size: 1.1rem;
      }

      .contact-item p {
        margin: 0;
        color: #666;
        font-size: 1rem;
      }

      .contact-form {
        background: #f8f9fa;
        padding: 2rem;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
      }

      .form-group {
        margin-bottom: 1.5rem;
      }

      .form-group label {
        display: block;
        margin-bottom: 0.5rem;
        color: #333;
        font-weight: 600;
      }

      .form-group input,
      .form-group textarea {
        width: 100%;
        padding: 1rem;
        border: 2px solid #e1e5e9;
        border-radius: 8px;
        font-size: 1rem;
        transition: border-color 0.3s ease;
        background: white;
      }

      .form-group input:focus,
      .form-group textarea:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }

      .form-group textarea {
        resize: vertical;
        min-height: 120px;
      }

      .alert {
        padding: 1rem;
        border-radius: 8px;
        margin-top: 1rem;
        display: flex;
        align-items: center;
      }

      .alert-success {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
      }

      .alert-error {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
      }

      /* Footer */
      .footer {
        background: #2c3e50;
        color: white;
        padding: 40px 0;
        text-align: center;
      }

      /* Language Selector */
      .language-selector {
        position: relative;
        display: inline-block;
      }

      .lang-toggle {
        background: rgba(255, 255, 255, 0.9);
        border: 1px solid rgba(102, 126, 234, 0.2);
        color: #2c3e50;
        padding: 0.6rem 1.2rem;
        border-radius: 20px;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 0.6rem;
        transition: all 0.3s ease;
        font-weight: 500;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        min-width: 120px;
        justify-content: space-between;
      }

      .lang-toggle:hover {
        background: white;
        border-color: #667eea;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
      }

      .lang-toggle:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }

      .lang-current {
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }

      .lang-arrow {
        font-size: 0.8rem;
        transition: transform 0.3s ease;
      }

      .lang-toggle.open .lang-arrow {
        transform: rotate(180deg);
      }

      .lang-dropdown {
        position: absolute;
        top: calc(100% + 8px);
        right: 0;
        background: white;
        border-radius: 12px;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        min-width: 180px;
        opacity: 0;
        visibility: hidden;
        transform: translateY(-10px) scale(0.95);
        transition: all 0.3s ease;
        z-index: 1000;
        border: 1px solid rgba(0, 0, 0, 0.1);
        overflow: hidden;
      }

      .lang-dropdown.show {
        opacity: 1;
        visibility: visible;
        transform: translateY(0) scale(1);
      }

      .lang-option {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.9rem 1.2rem;
        color: #2c3e50;
        text-decoration: none;
        transition: all 0.3s ease;
        font-weight: 500;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
      }

      .lang-option:last-child {
        border-bottom: none;
      }

      .lang-option:hover {
        background: linear-gradient(135deg, #f8f9ff 0%, #e8ecff 100%);
        color: #667eea;
        transform: translateX(4px);
      }

      .lang-option.active {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        position: relative;
      }

      .lang-option.active::after {
        content: "✓";
        position: absolute;
        right: 1rem;
        font-weight: bold;
      }

      .lang-flag {
        font-size: 1.2rem;
        width: 20px;
        text-align: center;
      }

      .lang-name {
        flex: 1;
        font-size: 0.9rem;
      }

      /* Menu Mobile */
      .mobile-menu-toggle {
        display: none;
        background: none;
        border: none;
        color: #2c3e50;
        font-size: 1.5rem;
        cursor: pointer;
        padding: 0.5rem;
        border-radius: 8px;
        transition: all 0.3s ease;
      }

      .mobile-menu-toggle:hover {
        background: rgba(102, 126, 234, 0.1);
      }

      .mobile-nav {
        display: none;
        position: fixed;
        top: 100%;
        left: 0;
        width: 100%;
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(20px);
        padding: 2rem 0;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        z-index: 999;
        transform: translateY(-100%);
        transition: transform 0.3s ease;
      }

      .mobile-nav.show {
        transform: translateY(0);
      }

      .mobile-nav-links {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
        list-style: none;
        text-align: center;
        margin-bottom: 2rem;
      }

      .mobile-nav-links a {
        color: #2c3e50;
        text-decoration: none;
        font-weight: 600;
        font-size: 1.1rem;
        padding: 1rem;
        border-radius: 8px;
        transition: all 0.3s ease;
      }

      .mobile-nav-links a:hover {
        background: rgba(102, 126, 234, 0.1);
        color: #667eea;
      }

      .mobile-auth {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        align-items: center;
        padding: 0 2rem;
      }

      /* Responsive */
      @media (max-width: 768px) {
        .header {
          padding: 0.5rem 0;
        }

        .nav-links {
          display: none;
        }

        .mobile-menu-toggle {
          display: block;
        }

        .mobile-nav {
          display: block;
        }

        .auth-buttons {
          gap: 0.5rem;
        }

        .language-selector {
          order: -1;
        }

        .lang-toggle {
          min-width: 100px;
          padding: 0.5rem 0.8rem;
        }

        .lang-dropdown {
          right: auto;
          left: 0;
          min-width: 150px;
        }

        .hero {
          padding: 100px 0 60px;
        }

        .hero h1 {
          font-size: 2.2rem;
          line-height: 1.2;
        }

        .hero p {
          font-size: 1.1rem;
          padding: 0 1rem;
        }

        .cta-buttons {
          flex-direction: column;
          align-items: center;
          gap: 1rem;
          padding: 0 1rem;
        }

        .btn-primary,
        .btn-outline {
          width: 100%;
          max-width: 280px;
          padding: 1rem 2rem;
        }

        .features-grid {
          grid-template-columns: 1fr;
          gap: 1.5rem;
        }

        .feature-card {
          padding: 1.5rem;
        }
      }

      @media (max-width: 480px) {
        .container {
          padding: 0 15px;
        }

        .logo {
          font-size: 1.2rem;
        }

        .hero h1 {
          font-size: 1.8rem;
        }

        .hero p {
          font-size: 1rem;
        }

        .lang-toggle {
          min-width: 90px;
          padding: 0.4rem 0.6rem;
        }

        .lang-name {
          font-size: 0.8rem;
        }
      }

      /* RTL Support */
      [dir="rtl"] .nav {
        direction: rtl;
      }

      [dir="rtl"] .lang-dropdown {
        right: auto;
        left: 0;
      }
    </style>
  </head>
  <body>
    <!-- Header -->
    <header class="header">
      <nav class="nav container">
        <div class="logo" id="logo">صفحات هبوط للجميع</div>

        <ul class="nav-links">
          <li><a href="#features" id="navFeatures">المميزات</a></li>
          <li><a href="#pricing" id="navPricing">الأسعار</a></li>
          <li><a href="#contact" id="navContact">اتصل بنا</a></li>
        </ul>

        <button
          class="mobile-menu-toggle"
          onclick="toggleMobileMenu()"
          aria-label="Menu mobile"
          aria-expanded="false"
        >
          <span id="menuIcon">☰</span>
        </button>

        <div class="auth-buttons">
          <!-- Language Selector -->
          <div class="language-selector">
            <button
              class="lang-toggle"
              onclick="toggleLanguage()"
              aria-label="Sélecteur de langue"
              aria-expanded="false"
              aria-haspopup="true"
            >
              <div class="lang-current">
                <span class="lang-flag" id="currentFlag">🇩🇿</span>
                <span class="lang-name" id="currentLang">العربية</span>
              </div>
              <span class="lang-arrow">▼</span>
            </button>
            <div
              class="lang-dropdown"
              id="langDropdown"
              role="menu"
              aria-label="Options de langue"
            >
              <a
                href="#"
                class="lang-option active"
                data-lang="ar"
                onclick="changeLanguage('ar')"
                role="menuitem"
                aria-label="Changer vers l'arabe"
              >
                <span class="lang-flag">🇩🇿</span>
                <span class="lang-name">العربية</span>
              </a>
              <a
                href="#"
                class="lang-option"
                data-lang="fr"
                onclick="changeLanguage('fr')"
                role="menuitem"
                aria-label="Changer vers le français"
              >
                <span class="lang-flag">🇫🇷</span>
                <span class="lang-name">Français</span>
              </a>
              <a
                href="#"
                class="lang-option"
                data-lang="en"
                onclick="changeLanguage('en')"
                role="menuitem"
                aria-label="Changer vers l'anglais"
              >
                <span class="lang-flag">🇺🇸</span>
                <span class="lang-name">English</span>
              </a>
            </div>
          </div>

          <a href="login.html" class="btn-secondary" id="loginBtn"
            >تسجيل الدخول</a
          >
        </div>
      </nav>

      <!-- Navigation Mobile -->
      <div class="mobile-nav" id="mobileNav">
        <div class="container">
          <ul class="mobile-nav-links">
            <li>
              <a
                href="#features"
                id="mobileNavFeatures"
                onclick="closeMobileMenu()"
                >المميزات</a
              >
            </li>
            <li>
              <a
                href="#pricing"
                id="mobileNavPricing"
                onclick="closeMobileMenu()"
                >الأسعار</a
              >
            </li>
            <li>
              <a
                href="#contact"
                id="mobileNavContact"
                onclick="closeMobileMenu()"
                >اتصل بنا</a
              >
            </li>
          </ul>
          <div class="mobile-auth">
            <a href="login.html" class="btn-secondary" id="mobileLoginBtn"
              >تسجيل الدخول</a
            >
          </div>
        </div>
      </div>
    </header>

    <!-- Hero Section -->
    <section class="hero">
      <div class="container">
        <h1 id="heroTitle">أنشئ صفحات هبوط احترافية في دقائق</h1>
        <p id="heroSubtitle">
          منصة متقدمة وسهلة الاستخدام لإنشاء صفحات هبوط تحويلية عالية الجودة
          بدون الحاجة لخبرة تقنية
        </p>

        <div class="cta-buttons">
          <a href="register.html" class="btn-primary" id="startNowBtn"
            >ابدأ الآن مجاناً</a
          >
          <a href="#features" class="btn-outline" id="learnMoreBtn"
            >تعرف على المزيد</a
          >
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="features" id="features">
      <div class="container">
        <h2 id="featuresTitle">لماذا تختار منصتنا؟</h2>

        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon">🚀</div>
            <h3 id="feature1Title">سرعة في الإنشاء</h3>
            <p id="feature1Desc">
              أنشئ صفحة هبوط احترافية في أقل من 10 دقائق باستخدام قوالبنا
              الجاهزة
            </p>
          </div>

          <div class="feature-card">
            <div class="feature-icon">📱</div>
            <h3 id="feature2Title">متجاوب مع جميع الأجهزة</h3>
            <p id="feature2Desc">
              صفحات تعمل بشكل مثالي على الهواتف والأجهزة اللوحية وأجهزة
              الكمبيوتر
            </p>
          </div>

          <div class="feature-card">
            <div class="feature-icon">🎨</div>
            <h3 id="feature3Title">تصميمات عصرية</h3>
            <p id="feature3Desc">
              قوالب حديثة ومتنوعة تناسب جميع أنواع الأعمال والخدمات
            </p>
          </div>

          <div class="feature-card">
            <div class="feature-icon">📊</div>
            <h3 id="feature4Title">تحليلات متقدمة</h3>
            <p id="feature4Desc">تتبع أداء صفحاتك وقياس معدلات التحويل بدقة</p>
          </div>

          <div class="feature-card">
            <div class="feature-icon">🔧</div>
            <h3 id="feature5Title">سهولة التخصيص</h3>
            <p id="feature5Desc">
              عدّل الألوان والنصوص والصور بسهولة بدون الحاجة لمعرفة تقنية
            </p>
          </div>

          <div class="feature-card">
            <div class="feature-icon">⚡</div>
            <h3 id="feature6Title">سرعة تحميل عالية</h3>
            <p id="feature6Desc">
              صفحات محسّنة للسرعة لضمان تجربة مستخدم ممتازة
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- Pricing Section -->
    <section class="pricing" id="pricing">
      <div class="container">
        <h2 id="pricingTitle">خطط الأسعار</h2>
        <p id="pricingSubtitle">اختر الخطة التي تناسب احتياجاتك</p>

        <div class="pricing-grid">
          <div class="pricing-card">
            <div class="pricing-header">
              <h3 id="plan1Name">مجاني</h3>
              <div class="price">
                <span class="currency" id="plan1Currency">$</span>
                <span class="amount" id="plan1Price">0</span>
                <span class="period" id="plan1Period">/شهر</span>
              </div>
            </div>
            <div class="pricing-features">
              <ul>
                <li id="plan1Feature1">✓ 3 صفحات هبوط</li>
                <li id="plan1Feature2">✓ 5 منتجات</li>
                <li id="plan1Feature3">✓ 2 فئات</li>
                <li id="plan1Feature4">✓ دعم أساسي</li>
                <li id="plan1Feature5">✓ قوالب محدودة</li>
              </ul>
            </div>
            <div class="pricing-footer">
              <a href="register.html" class="btn-outline" id="plan1Btn"
                >ابدأ مجاناً</a
              >
            </div>
          </div>

          <div class="pricing-card featured">
            <div class="popular-badge" id="popularBadge">الأكثر شعبية</div>
            <div class="pricing-header">
              <h3 id="plan2Name">احترافي</h3>
              <div class="price">
                <span class="currency" id="plan2Currency">$</span>
                <span class="amount" id="plan2Price">19</span>
                <span class="period" id="plan2Period">/شهر</span>
              </div>
            </div>
            <div class="pricing-features">
              <ul>
                <li id="plan2Feature1">✓ 15 صفحة هبوط</li>
                <li id="plan2Feature2">✓ 50 منتج</li>
                <li id="plan2Feature3">✓ 10 فئات</li>
                <li id="plan2Feature4">✓ دعم متقدم</li>
                <li id="plan2Feature5">✓ جميع القوالب</li>
                <li id="plan2Feature6">✓ تحليلات متقدمة</li>
                <li id="plan2Feature7">✓ نطاق مخصص</li>
              </ul>
            </div>
            <div class="pricing-footer">
              <a href="register.html" class="btn-primary" id="plan2Btn"
                >اختر الخطة</a
              >
            </div>
          </div>

          <div class="pricing-card">
            <div class="pricing-header">
              <h3 id="plan3Name">مؤسسي</h3>
              <div class="price">
                <span class="currency" id="plan3Currency">$</span>
                <span class="amount" id="plan3Price">49</span>
                <span class="period" id="plan3Period">/شهر</span>
              </div>
            </div>
            <div class="pricing-features">
              <ul>
                <li id="plan3Feature1">✓ صفحات غير محدودة</li>
                <li id="plan3Feature2">✓ منتجات غير محدودة</li>
                <li id="plan3Feature3">✓ فئات غير محدودة</li>
                <li id="plan3Feature4">✓ دعم أولوية</li>
                <li id="plan3Feature5">✓ قوالب حصرية</li>
                <li id="plan3Feature6">✓ تحليلات شاملة</li>
                <li id="plan3Feature7">✓ API متقدم</li>
                <li id="plan3Feature8">✓ تدريب شخصي</li>
              </ul>
            </div>
            <div class="pricing-footer">
              <a href="register.html" class="btn-outline" id="plan3Btn"
                >اختر الخطة</a
              >
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section">
      <div class="container">
        <h2 id="ctaTitle">جاهز لبدء رحلتك؟</h2>
        <p id="ctaSubtitle">
          انضم إلى آلاف المستخدمين الذين يثقون بمنصتنا لإنشاء صفحات هبوط ناجحة
        </p>
        <a href="register.html" class="btn-primary" id="ctaBtn"
          >ابدأ مجاناً الآن</a
        >
      </div>
    </section>

    <!-- Contact Section -->
    <section class="contact" id="contact">
      <div class="container">
        <div class="row">
          <div class="col-md-6">
            <h2 id="contactTitle">تواصل معنا</h2>
            <p id="contactSubtitle">
              نحن هنا لمساعدتك. أرسل لنا رسالة وسنرد عليك في أقرب وقت ممكن.
            </p>

            <div class="contact-info">
              <div class="contact-item">
                <i class="fas fa-envelope"></i>
                <div>
                  <h4 id="emailTitle">البريد الإلكتروني</h4>
                  <p><EMAIL></p>
                </div>
              </div>
              <div class="contact-item">
                <i class="fas fa-phone"></i>
                <div>
                  <h4 id="phoneTitle">الهاتف</h4>
                  <p>+****************</p>
                </div>
              </div>
              <div class="contact-item">
                <i class="fas fa-map-marker-alt"></i>
                <div>
                  <h4 id="addressTitle">العنوان</h4>
                  <p id="addressText">123 شارع التقنية، المدينة الرقمية</p>
                </div>
              </div>
            </div>
          </div>

          <div class="col-md-6">
            <div class="contact-form">
              <form id="contactForm">
                <div class="form-group">
                  <label for="contactName" id="nameLabel">الاسم الكامل</label>
                  <input type="text" id="contactName" name="name" required />
                </div>

                <div class="form-group">
                  <label for="contactEmail" id="emailLabel"
                    >البريد الإلكتروني</label
                  >
                  <input type="email" id="contactEmail" name="email" required />
                </div>

                <div class="form-group">
                  <label for="contactSubject" id="subjectLabel">الموضوع</label>
                  <input
                    type="text"
                    id="contactSubject"
                    name="subject"
                    required
                  />
                </div>

                <div class="form-group">
                  <label for="contactMessage" id="messageLabel">الرسالة</label>
                  <textarea
                    id="contactMessage"
                    name="message"
                    rows="5"
                    required
                  ></textarea>
                </div>

                <button type="submit" class="btn-primary" id="submitBtn">
                  <i class="fas fa-paper-plane me-2"></i>
                  <span id="submitText">إرسال الرسالة</span>
                </button>
              </form>

              <div
                id="contactSuccess"
                class="alert alert-success"
                style="display: none"
              >
                <i class="fas fa-check-circle me-2"></i>
                <span id="successMessage"
                  >تم إرسال رسالتك بنجاح! سنرد عليك قريباً.</span
                >
              </div>

              <div
                id="contactError"
                class="alert alert-error"
                style="display: none"
              >
                <i class="fas fa-exclamation-triangle me-2"></i>
                <span id="errorMessage"
                  >حدث خطأ أثناء إرسال الرسالة. يرجى المحاولة مرة أخرى.</span
                >
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
      <div class="container">
        <p id="footerText">
          &copy; 2024 صفحات هبوط للجميع. جميع الحقوق محفوظة.
        </p>
      </div>
    </footer>

    <script>
      // Configuration des langues
      const LANGUAGE_CONFIG = {
        ar: {
          code: "ar",
          name: "العربية",
          flag: "🇩🇿",
          dir: "rtl",
          locale: "ar-DZ",
        },
        fr: {
          code: "fr",
          name: "Français",
          flag: "🇫🇷",
          dir: "ltr",
          locale: "fr-FR",
        },
        en: {
          code: "en",
          name: "English",
          flag: "🇺🇸",
          dir: "ltr",
          locale: "en-US",
        },
      };

      // Traductions
      const TRANSLATIONS = {
        ar: {
          logo: "صفحات هبوط للجميع",
          navFeatures: "المميزات",
          navPricing: "الأسعار",
          navContact: "اتصل بنا",
          loginBtn: "تسجيل الدخول",
          mobileNavFeatures: "المميزات",
          mobileNavPricing: "الأسعار",
          mobileNavContact: "اتصل بنا",
          mobileLoginBtn: "تسجيل الدخول",
          heroTitle: "أنشئ صفحات هبوط احترافية في دقائق",
          heroSubtitle:
            "منصة متقدمة وسهلة الاستخدام لإنشاء صفحات هبوط تحويلية عالية الجودة بدون الحاجة لخبرة تقنية",
          startNowBtn: "ابدأ الآن مجاناً",
          learnMoreBtn: "تعرف على المزيد",
          featuresTitle: "لماذا تختار منصتنا؟",
          feature1Title: "سرعة في الإنشاء",
          feature1Desc:
            "أنشئ صفحة هبوط احترافية في أقل من 10 دقائق باستخدام قوالبنا الجاهزة",
          feature2Title: "متجاوب مع جميع الأجهزة",
          feature2Desc:
            "صفحات تعمل بشكل مثالي على الهواتف والأجهزة اللوحية وأجهزة الكمبيوتر",
          feature3Title: "تصميمات عصرية",
          feature3Desc: "قوالب حديثة ومتنوعة تناسب جميع أنواع الأعمال والخدمات",
          feature4Title: "تحليلات متقدمة",
          feature4Desc: "تتبع أداء صفحاتك وقياس معدلات التحويل بدقة",
          feature5Title: "سهولة التخصيص",
          feature5Desc:
            "عدّل الألوان والنصوص والصور بسهولة بدون الحاجة لمعرفة تقنية",
          feature6Title: "سرعة تحميل عالية",
          feature6Desc: "صفحات محسّنة للسرعة لضمان تجربة مستخدم ممتازة",
          // Pricing
          pricingTitle: "خطط الأسعار",
          pricingSubtitle: "اختر الخطة التي تناسب احتياجاتك",
          popularBadge: "الأكثر شعبية",
          plan1Name: "مجاني",
          plan1Price: "0",
          plan1Period: "/شهر",
          plan1Feature1: "✓ 3 صفحات هبوط",
          plan1Feature2: "✓ 5 منتجات",
          plan1Feature3: "✓ 2 فئات",
          plan1Feature4: "✓ دعم أساسي",
          plan1Feature5: "✓ قوالب محدودة",
          plan1Btn: "ابدأ مجاناً",
          plan2Name: "احترافي",
          plan2Price: "19",
          plan2Period: "/شهر",
          plan2Feature1: "✓ 15 صفحة هبوط",
          plan2Feature2: "✓ 50 منتج",
          plan2Feature3: "✓ 10 فئات",
          plan2Feature4: "✓ دعم متقدم",
          plan2Feature5: "✓ جميع القوالب",
          plan2Feature6: "✓ تحليلات متقدمة",
          plan2Feature7: "✓ نطاق مخصص",
          plan2Btn: "اختر الخطة",
          plan3Name: "مؤسسي",
          plan3Price: "49",
          plan3Period: "/شهر",
          plan3Feature1: "✓ صفحات غير محدودة",
          plan3Feature2: "✓ منتجات غير محدودة",
          plan3Feature3: "✓ فئات غير محدودة",
          plan3Feature4: "✓ دعم أولوية",
          plan3Feature5: "✓ قوالب حصرية",
          plan3Feature6: "✓ تحليلات شاملة",
          plan3Feature7: "✓ API متقدم",
          plan3Feature8: "✓ تدريب شخصي",
          plan3Btn: "اختر الخطة",
          // Contact
          contactTitle: "تواصل معنا",
          contactSubtitle:
            "نحن هنا لمساعدتك. أرسل لنا رسالة وسنرد عليك في أقرب وقت ممكن.",
          emailTitle: "البريد الإلكتروني",
          phoneTitle: "الهاتف",
          addressTitle: "العنوان",
          addressText: "123 شارع التقنية، المدينة الرقمية",
          nameLabel: "الاسم الكامل",
          emailLabel: "البريد الإلكتروني",
          subjectLabel: "الموضوع",
          messageLabel: "الرسالة",
          submitText: "إرسال الرسالة",
          successMessage: "تم إرسال رسالتك بنجاح! سنرد عليك قريباً.",
          errorMessage: "حدث خطأ أثناء إرسال الرسالة. يرجى المحاولة مرة أخرى.",
          ctaTitle: "جاهز لبدء رحلتك؟",
          ctaSubtitle:
            "انضم إلى آلاف المستخدمين الذين يثقون بمنصتنا لإنشاء صفحات هبوط ناجحة",
          ctaBtn: "ابدأ مجاناً الآن",
          footerText: "© 2024 صفحات هبوط للجميع. جميع الحقوق محفوظة.",
        },
        fr: {
          logo: "Pages de destination pour tous",
          navFeatures: "Fonctionnalités",
          navPricing: "Tarifs",
          navContact: "Contact",
          loginBtn: "Connexion",
          mobileNavFeatures: "Fonctionnalités",
          mobileNavPricing: "Tarifs",
          mobileNavContact: "Contact",
          mobileLoginBtn: "Connexion",
          heroTitle:
            "Créez des pages de destination professionnelles en minutes",
          heroSubtitle:
            "Plateforme avancée et facile à utiliser pour créer des pages de destination à fort taux de conversion sans expertise technique",
          startNowBtn: "Commencer gratuitement",
          learnMoreBtn: "En savoir plus",
          featuresTitle: "Pourquoi choisir notre plateforme ?",
          feature1Title: "Création rapide",
          feature1Desc:
            "Créez une page de destination professionnelle en moins de 10 minutes avec nos modèles prêts",
          feature2Title: "Responsive sur tous appareils",
          feature2Desc:
            "Pages qui fonctionnent parfaitement sur mobiles, tablettes et ordinateurs",
          feature3Title: "Designs modernes",
          feature3Desc:
            "Modèles contemporains et variés adaptés à tous types d'entreprises et services",
          feature4Title: "Analyses avancées",
          feature4Desc:
            "Suivez les performances de vos pages et mesurez les taux de conversion avec précision",
          feature5Title: "Personnalisation facile",
          feature5Desc:
            "Modifiez couleurs, textes et images facilement sans connaissances techniques",
          feature6Title: "Vitesse de chargement élevée",
          feature6Desc:
            "Pages optimisées pour la vitesse pour garantir une excellente expérience utilisateur",
          // Pricing
          pricingTitle: "Plans Tarifaires",
          pricingSubtitle: "Choisissez le plan qui correspond à vos besoins",
          popularBadge: "Le plus populaire",
          plan1Name: "Gratuit",
          plan1Price: "0",
          plan1Period: "/mois",
          plan1Feature1: "✓ 3 pages de destination",
          plan1Feature2: "✓ 5 produits",
          plan1Feature3: "✓ 2 catégories",
          plan1Feature4: "✓ Support de base",
          plan1Feature5: "✓ Modèles limités",
          plan1Btn: "Commencer gratuitement",
          plan2Name: "Professionnel",
          plan2Price: "19",
          plan2Period: "/mois",
          plan2Feature1: "✓ 15 pages de destination",
          plan2Feature2: "✓ 50 produits",
          plan2Feature3: "✓ 10 catégories",
          plan2Feature4: "✓ Support avancé",
          plan2Feature5: "✓ Tous les modèles",
          plan2Feature6: "✓ Analyses avancées",
          plan2Feature7: "✓ Domaine personnalisé",
          plan2Btn: "Choisir ce plan",
          plan3Name: "Entreprise",
          plan3Price: "49",
          plan3Period: "/mois",
          plan3Feature1: "✓ Pages illimitées",
          plan3Feature2: "✓ Produits illimités",
          plan3Feature3: "✓ Catégories illimitées",
          plan3Feature4: "✓ Support prioritaire",
          plan3Feature5: "✓ Modèles exclusifs",
          plan3Feature6: "✓ Analyses complètes",
          plan3Feature7: "✓ API avancée",
          plan3Feature8: "✓ Formation personnelle",
          plan3Btn: "Choisir ce plan",
          // Contact
          contactTitle: "Contactez-nous",
          contactSubtitle:
            "Nous sommes là pour vous aider. Envoyez-nous un message et nous vous répondrons dès que possible.",
          emailTitle: "Email",
          phoneTitle: "Téléphone",
          addressTitle: "Adresse",
          addressText: "123 Rue de la Technologie, Ville Numérique",
          nameLabel: "Nom complet",
          emailLabel: "Email",
          subjectLabel: "Sujet",
          messageLabel: "Message",
          submitText: "Envoyer le message",
          successMessage:
            "Votre message a été envoyé avec succès ! Nous vous répondrons bientôt.",
          errorMessage:
            "Une erreur s'est produite lors de l'envoi du message. Veuillez réessayer.",
          ctaTitle: "Prêt à commencer votre voyage ?",
          ctaSubtitle:
            "Rejoignez des milliers d'utilisateurs qui font confiance à notre plateforme pour créer des pages de destination réussies",
          ctaBtn: "Commencer gratuitement maintenant",
          footerText:
            "© 2024 Pages de destination pour tous. Tous droits réservés.",
        },
        en: {
          logo: "Landing Pages for Everyone",
          navFeatures: "Features",
          navPricing: "Pricing",
          navContact: "Contact",
          loginBtn: "Sign In",
          mobileNavFeatures: "Features",
          mobileNavPricing: "Pricing",
          mobileNavContact: "Contact",
          mobileLoginBtn: "Sign In",
          heroTitle: "Create Professional Landing Pages in Minutes",
          heroSubtitle:
            "Advanced and easy-to-use platform for creating high-converting landing pages without technical expertise",
          startNowBtn: "Start Free Now",
          learnMoreBtn: "Learn More",
          featuresTitle: "Why Choose Our Platform?",
          feature1Title: "Fast Creation",
          feature1Desc:
            "Create a professional landing page in less than 10 minutes using our ready-made templates",
          feature2Title: "Responsive on All Devices",
          feature2Desc:
            "Pages that work perfectly on phones, tablets, and computers",
          feature3Title: "Modern Designs",
          feature3Desc:
            "Contemporary and diverse templates suitable for all types of businesses and services",
          feature4Title: "Advanced Analytics",
          feature4Desc:
            "Track your pages' performance and measure conversion rates accurately",
          feature5Title: "Easy Customization",
          feature5Desc:
            "Edit colors, texts, and images easily without technical knowledge",
          feature6Title: "High Loading Speed",
          feature6Desc:
            "Speed-optimized pages to ensure excellent user experience",
          // Pricing
          pricingTitle: "Pricing Plans",
          pricingSubtitle: "Choose the plan that fits your needs",
          popularBadge: "Most Popular",
          plan1Name: "Free",
          plan1Price: "0",
          plan1Period: "/month",
          plan1Feature1: "✓ 3 landing pages",
          plan1Feature2: "✓ 5 products",
          plan1Feature3: "✓ 2 categories",
          plan1Feature4: "✓ Basic support",
          plan1Feature5: "✓ Limited templates",
          plan1Btn: "Start Free",
          plan2Name: "Professional",
          plan2Price: "19",
          plan2Period: "/month",
          plan2Feature1: "✓ 15 landing pages",
          plan2Feature2: "✓ 50 products",
          plan2Feature3: "✓ 10 categories",
          plan2Feature4: "✓ Advanced support",
          plan2Feature5: "✓ All templates",
          plan2Feature6: "✓ Advanced analytics",
          plan2Feature7: "✓ Custom domain",
          plan2Btn: "Choose Plan",
          plan3Name: "Enterprise",
          plan3Price: "49",
          plan3Period: "/month",
          plan3Feature1: "✓ Unlimited pages",
          plan3Feature2: "✓ Unlimited products",
          plan3Feature3: "✓ Unlimited categories",
          plan3Feature4: "✓ Priority support",
          plan3Feature5: "✓ Exclusive templates",
          plan3Feature6: "✓ Comprehensive analytics",
          plan3Feature7: "✓ Advanced API",
          plan3Feature8: "✓ Personal training",
          plan3Btn: "Choose Plan",
          // Contact
          contactTitle: "Contact Us",
          contactSubtitle:
            "We're here to help you. Send us a message and we'll respond as soon as possible.",
          emailTitle: "Email",
          phoneTitle: "Phone",
          addressTitle: "Address",
          addressText: "123 Technology Street, Digital City",
          nameLabel: "Full Name",
          emailLabel: "Email",
          subjectLabel: "Subject",
          messageLabel: "Message",
          submitText: "Send Message",
          successMessage:
            "Your message has been sent successfully! We'll get back to you soon.",
          errorMessage:
            "An error occurred while sending the message. Please try again.",
          ctaTitle: "Ready to Start Your Journey?",
          ctaSubtitle:
            "Join thousands of users who trust our platform to create successful landing pages",
          ctaBtn: "Start Free Now",
          footerText: "© 2024 Landing Pages for Everyone. All rights reserved.",
        },
      };

      // État de l'application
      let currentLanguage = "ar";
      let isDropdownOpen = false;
      let isMobileMenuOpen = false;

      // Fonction de détection de la langue système
      function detectSystemLanguage() {
        // Vérifier le localStorage
        const saved = localStorage.getItem("preferred_language");
        if (saved && LANGUAGE_CONFIG[saved]) {
          return saved;
        }

        // Détecter la langue du navigateur
        const browserLang = navigator.language || navigator.userLanguage;

        if (browserLang.startsWith("ar")) return "ar";
        if (browserLang.startsWith("fr")) return "fr";
        if (browserLang.startsWith("en")) return "en";

        return "ar"; // Par défaut
      }

      // Fonction de traduction
      function t(key) {
        return TRANSLATIONS[currentLanguage][key] || key;
      }

      // Fonction pour mettre à jour l'interface
      function updateUI() {
        const config = LANGUAGE_CONFIG[currentLanguage];

        // Mettre à jour les attributs HTML
        document.documentElement.lang = config.code;
        document.documentElement.dir = config.dir;

        // Mettre à jour le titre du document
        document.title =
          currentLanguage === "ar"
            ? "صفحات هبوط للجميع - منصة إنشاء صفحات الهبوط"
            : currentLanguage === "fr"
            ? "Pages de destination pour tous - Plateforme de création"
            : "Landing Pages for Everyone - Creation Platform";

        // Mettre à jour tous les textes
        Object.keys(TRANSLATIONS[currentLanguage]).forEach((key) => {
          const element = document.getElementById(key);
          if (element) {
            element.textContent = t(key);
          }
        });

        // Mettre à jour le sélecteur de langue
        document.getElementById("currentFlag").textContent = config.flag;
        document.getElementById("currentLang").textContent = config.name;

        // Mettre à jour les options actives
        document.querySelectorAll(".lang-option").forEach((option) => {
          option.classList.toggle(
            "active",
            option.dataset.lang === currentLanguage
          );
        });
      }

      // Fonction pour changer de langue
      function changeLanguage(lang) {
        if (LANGUAGE_CONFIG[lang] && lang !== currentLanguage) {
          currentLanguage = lang;
          localStorage.setItem("preferred_language", lang);
          updateUI();
          closeDropdown();
        }
      }

      // Fonction pour ouvrir/fermer le dropdown
      function toggleLanguage() {
        isDropdownOpen = !isDropdownOpen;
        const dropdown = document.getElementById("langDropdown");
        const toggle = document.querySelector(".lang-toggle");

        dropdown.classList.toggle("show", isDropdownOpen);
        toggle.classList.toggle("open", isDropdownOpen);
        toggle.setAttribute("aria-expanded", isDropdownOpen.toString());

        // Focus management pour l'accessibilité
        if (isDropdownOpen) {
          const firstOption = dropdown.querySelector(".lang-option");
          if (firstOption) {
            setTimeout(() => firstOption.focus(), 100);
          }
        }
      }

      // Fonction pour fermer le dropdown
      function closeDropdown() {
        isDropdownOpen = false;
        const dropdown = document.getElementById("langDropdown");
        const toggle = document.querySelector(".lang-toggle");

        dropdown.classList.remove("show");
        toggle.classList.remove("open");
        toggle.setAttribute("aria-expanded", "false");
      }

      // Fonctions pour gérer le menu mobile
      function toggleMobileMenu() {
        const mobileNav = document.getElementById("mobileNav");
        const menuToggle = document.querySelector(".mobile-menu-toggle");
        const menuIcon = document.getElementById("menuIcon");

        if (isMobileMenuOpen) {
          closeMobileMenu();
        } else {
          openMobileMenu();
        }
      }

      function openMobileMenu() {
        const mobileNav = document.getElementById("mobileNav");
        const menuToggle = document.querySelector(".mobile-menu-toggle");
        const menuIcon = document.getElementById("menuIcon");

        if (mobileNav && menuToggle && menuIcon) {
          mobileNav.classList.add("show");
          menuToggle.classList.add("active");
          menuToggle.setAttribute("aria-expanded", "true");
          menuIcon.textContent = "✕";
          isMobileMenuOpen = true;

          // Empêcher le scroll du body
          document.body.style.overflow = "hidden";
        }
      }

      function closeMobileMenu() {
        const mobileNav = document.getElementById("mobileNav");
        const menuToggle = document.querySelector(".mobile-menu-toggle");
        const menuIcon = document.getElementById("menuIcon");

        if (mobileNav && menuToggle && menuIcon) {
          mobileNav.classList.remove("show");
          menuToggle.classList.remove("active");
          menuToggle.setAttribute("aria-expanded", "false");
          menuIcon.textContent = "☰";
          isMobileMenuOpen = false;

          // Restaurer le scroll du body
          document.body.style.overflow = "";
        }
      }

      // Fermer le dropdown et menu mobile en cliquant ailleurs
      document.addEventListener("click", function (e) {
        const languageSelector = document.querySelector(".language-selector");
        const mobileMenuToggle = document.querySelector(".mobile-menu-toggle");
        const mobileNav = document.getElementById("mobileNav");

        // Fermer le dropdown de langue
        if (
          languageSelector &&
          !languageSelector.contains(e.target) &&
          isDropdownOpen
        ) {
          closeDropdown();
        }

        // Fermer le menu mobile
        if (
          mobileMenuToggle &&
          mobileNav &&
          !mobileMenuToggle.contains(e.target) &&
          !mobileNav.contains(e.target) &&
          isMobileMenuOpen
        ) {
          closeMobileMenu();
        }
      });

      // Gestion du clavier pour l'accessibilité
      document.addEventListener("keydown", function (e) {
        if (!isDropdownOpen) return;

        const options = document.querySelectorAll(".lang-option");
        const currentFocus = document.activeElement;
        const currentIndex = Array.from(options).indexOf(currentFocus);

        switch (e.key) {
          case "Escape":
            closeDropdown();
            document.querySelector(".lang-toggle").focus();
            e.preventDefault();
            break;

          case "ArrowDown":
            e.preventDefault();
            const nextIndex =
              currentIndex < options.length - 1 ? currentIndex + 1 : 0;
            options[nextIndex].focus();
            break;

          case "ArrowUp":
            e.preventDefault();
            const prevIndex =
              currentIndex > 0 ? currentIndex - 1 : options.length - 1;
            options[prevIndex].focus();
            break;

          case "Enter":
          case " ":
            if (
              currentFocus &&
              currentFocus.classList.contains("lang-option")
            ) {
              e.preventDefault();
              currentFocus.click();
            }
            break;
        }

        // Gestion du menu mobile avec Escape
        if (e.key === "Escape" && isMobileMenuOpen) {
          closeMobileMenu();
        }
      });

      // Améliorer la gestion du focus
      document.querySelectorAll(".lang-option").forEach((option) => {
        option.addEventListener("focus", function () {
          // Retirer le focus des autres options
          document.querySelectorAll(".lang-option").forEach((opt) => {
            opt.style.outline = "none";
          });
          // Ajouter un style de focus visible
          this.style.outline = "2px solid #667eea";
          this.style.outlineOffset = "-2px";
        });

        option.addEventListener("blur", function () {
          this.style.outline = "none";
        });
      });

      // Gestion du formulaire de contact
      function handleContactForm() {
        const form = document.getElementById("contactForm");
        const successDiv = document.getElementById("contactSuccess");
        const errorDiv = document.getElementById("contactError");

        if (form) {
          form.addEventListener("submit", async function (e) {
            e.preventDefault();

            // Récupérer les données du formulaire
            const formData = new FormData(form);
            const data = {
              name: formData.get("name"),
              email: formData.get("email"),
              subject: formData.get("subject"),
              message: formData.get("message"),
            };

            // Désactiver le bouton de soumission
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.disabled = true;
            submitBtn.innerHTML =
              '<i class="fas fa-spinner fa-spin me-2"></i>' + t("submitText");

            try {
              // Envoyer les données à l'API
              const response = await fetch("/api/contact.php", {
                method: "POST",
                headers: {
                  "Content-Type": "application/json",
                },
                body: JSON.stringify(data),
              });

              if (response.ok) {
                // Succès
                successDiv.style.display = "block";
                errorDiv.style.display = "none";
                form.reset();

                // Masquer le message de succès après 5 secondes
                setTimeout(() => {
                  successDiv.style.display = "none";
                }, 5000);
              } else {
                throw new Error("Erreur serveur");
              }
            } catch (error) {
              // Erreur
              errorDiv.style.display = "block";
              successDiv.style.display = "none";

              // Masquer le message d'erreur après 5 secondes
              setTimeout(() => {
                errorDiv.style.display = "none";
              }, 5000);
            } finally {
              // Réactiver le bouton
              submitBtn.disabled = false;
              submitBtn.innerHTML = originalText;
            }
          });
        }
      }

      // Initialisation
      document.addEventListener("DOMContentLoaded", function () {
        currentLanguage = detectSystemLanguage();
        updateUI();
        handleContactForm();
      });

      // Smooth scrolling pour les liens d'ancrage
      document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
        anchor.addEventListener("click", function (e) {
          e.preventDefault();
          const target = document.querySelector(this.getAttribute("href"));
          if (target) {
            target.scrollIntoView({
              behavior: "smooth",
              block: "start",
            });
          }
        });
      });
    </script>
  </body>
</html>
