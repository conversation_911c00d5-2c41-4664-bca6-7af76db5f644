<?php
require_once 'api/config/database.php';

try {
    $database = new Database();
    $pdo = $database->getConnection();
    
    echo "Mise à jour de la table roles...\n";
    
    // Ajouter les colonnes de limites
    $alterQueries = [
        "ALTER TABLE roles ADD COLUMN IF NOT EXISTS max_products INT DEFAULT NULL COMMENT 'Nombre maximum de produits autorisés (-1 = illimité)'",
        "ALTER TABLE roles ADD COLUMN IF NOT EXISTS max_landing_pages INT DEFAULT NULL COMMENT 'Nombre maximum de landing pages autorisées (-1 = illimité)'",
        "ALTER TABLE roles ADD COLUMN IF NOT EXISTS max_categories INT DEFAULT NULL COMMENT 'Nombre maximum de catégories autorisées (-1 = illimité)'",
        "ALTER TABLE roles ADD COLUMN IF NOT EXISTS max_subcategories INT DEFAULT NULL COMMENT 'Nombre maximum de sous-catégories autorisées (-1 = illimité)'"
    ];
    
    foreach ($alterQueries as $query) {
        try {
            $pdo->exec($query);
            echo "✅ Colonne ajoutée avec succès\n";
        } catch (Exception $e) {
            echo "⚠️ Colonne peut-être déjà existante: " . $e->getMessage() . "\n";
        }
    }
    
    // Mettre à jour les rôles existants
    $updateQuery = "
        UPDATE roles SET 
            max_products = CASE 
                WHEN name = 'admin' THEN -1 
                WHEN name = 'seller' THEN 100 
                WHEN name = 'agent' THEN 0 
                WHEN name = 'customer' THEN 0 
                ELSE 10 
            END,
            max_landing_pages = CASE 
                WHEN name = 'admin' THEN -1 
                WHEN name = 'seller' THEN 10 
                WHEN name = 'agent' THEN 0 
                WHEN name = 'customer' THEN 0 
                ELSE 1 
            END,
            max_categories = CASE 
                WHEN name = 'admin' THEN -1 
                WHEN name = 'seller' THEN 20 
                WHEN name = 'agent' THEN 0 
                WHEN name = 'customer' THEN 0 
                ELSE 5 
            END,
            max_subcategories = CASE 
                WHEN name = 'admin' THEN -1 
                WHEN name = 'seller' THEN 50 
                WHEN name = 'agent' THEN 0 
                WHEN name = 'customer' THEN 0 
                ELSE 10 
            END
    ";
    
    $pdo->exec($updateQuery);
    echo "✅ Rôles mis à jour avec les limites\n";
    
    // Vérifier les résultats
    $stmt = $pdo->query("SELECT name, display_name, max_products, max_landing_pages, max_categories, max_subcategories FROM roles");
    $roles = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "\n📊 Rôles avec limites :\n";
    foreach ($roles as $role) {
        echo "- {$role['display_name']} ({$role['name']}):\n";
        echo "  Produits: " . ($role['max_products'] == -1 ? 'Illimité' : $role['max_products']) . "\n";
        echo "  Landing Pages: " . ($role['max_landing_pages'] == -1 ? 'Illimité' : $role['max_landing_pages']) . "\n";
        echo "  Catégories: " . ($role['max_categories'] == -1 ? 'Illimité' : $role['max_categories']) . "\n";
        echo "  Sous-catégories: " . ($role['max_subcategories'] == -1 ? 'Illimité' : $role['max_subcategories']) . "\n\n";
    }
    
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
}
?>
