<?php
/**
 * Test script for Products API
 */

echo "<h2>Products API Test</h2>\n";
echo "<pre>\n";

// Test 1: GET /api/products (without authentication - should fail)
echo "=== Test 1: GET /api/products (no auth) ===\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost:8000/api/products.php');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json'
]);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "HTTP Code: $http_code\n";
echo "Response: $response\n\n";

// Test 2: GET /api/products (with demo token)
echo "=== Test 2: GET /api/products (with demo token) ===\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost:8000/api/products.php');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Authorization: Bearer demo_token'
]);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "HTTP Code: $http_code\n";
echo "Response: $response\n\n";

// Test 3: POST /api/products (create product)
echo "=== Test 3: POST /api/products (create product) ===\n";
$product_data = [
    'name_ar' => 'هاتف ذكي جديد',
    'name_fr' => 'Nouveau smartphone',
    'name_en' => 'New smartphone',
    'description_ar' => 'وصف المنتج باللغة العربية',
    'price' => 25000.00,
    'category' => 'electronics',
    'stock_quantity' => 10,
    'status' => 'published'
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost:8000/api/products.php');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($product_data));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Authorization: Bearer demo_token'
]);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "HTTP Code: $http_code\n";
echo "Response: $response\n\n";

echo "</pre>\n";
?>
