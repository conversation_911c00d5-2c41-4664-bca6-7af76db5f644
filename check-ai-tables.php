<?php
require_once 'api/config/database.php';

try {
    $database = new Database();
    $pdo = $database->getConnection();
    
    echo "Vérification des tables IA...\n";
    
    $stmt = $pdo->query("SHOW TABLES LIKE 'ai_%'");
    $tables = $stmt->fetchAll(PDO::FETCH_NUM);
    
    if (empty($tables)) {
        echo "❌ Aucune table IA trouvée\n";
    } else {
        echo "✅ Tables IA trouvées :\n";
        foreach ($tables as $table) {
            echo "- {$table[0]}\n";
        }
    }
    
    // Vérifier les données dans ai_models
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM ai_models");
    $count = $stmt->fetch()['count'];
    echo "\n📊 Modèles IA : $count\n";
    
    // Vérifier les données dans ai_api_keys
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM ai_api_keys");
    $count = $stmt->fetch()['count'];
    echo "🔑 API Keys : $count\n";
    
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
}
?>
