<?php
require_once 'api/config/database.php';

try {
    $database = new Database();
    $pdo = $database->getConnection();
    
    echo "Création des tables IA...\n";
    
    // Lire et exécuter le script SQL
    $sql = file_get_contents('api/sql/create_ai_keys.sql');
    
    // Diviser en requêtes individuelles
    $queries = array_filter(array_map('trim', explode(';', $sql)));
    
    foreach ($queries as $query) {
        if (!empty($query) && !str_starts_with(trim($query), '--')) {
            try {
                $pdo->exec($query);
                echo "✅ Requête exécutée\n";
            } catch (Exception $e) {
                echo "⚠️ " . $e->getMessage() . "\n";
            }
        }
    }
    
    // Vérifier les résultats
    echo "\n📊 Modèles IA créés :\n";
    $stmt = $pdo->query("SELECT display_name, provider, tier, cost_per_token FROM ai_models ORDER BY provider, tier");
    $models = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($models as $model) {
        echo "- {$model['display_name']} ({$model['provider']}) - {$model['tier']} - \${$model['cost_per_token']}/token\n";
    }
    
    echo "\n🔑 API Keys créées :\n";
    $stmt = $pdo->query("SELECT name, provider, status, monthly_limit, current_usage, total_tokens_used FROM ai_api_keys ORDER BY provider");
    $keys = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($keys as $key) {
        $usage_percent = $key['monthly_limit'] > 0 ? round(($key['current_usage'] / $key['monthly_limit']) * 100, 1) : 0;
        echo "- {$key['name']} ({$key['provider']}) - {$key['status']} - \${$key['current_usage']}/\${$key['monthly_limit']} ({$usage_percent}%) - " . number_format($key['total_tokens_used']) . " tokens\n";
    }
    
    echo "\n📈 Usage IA :\n";
    $stmt = $pdo->query("SELECT COUNT(*) as requests, SUM(total_tokens) as tokens, SUM(cost_usd) as cost FROM ai_usage");
    $usage = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "- Total requêtes : " . number_format($usage['requests']) . "\n";
    echo "- Total tokens : " . number_format($usage['tokens']) . "\n";
    echo "- Coût total : \$" . number_format($usage['cost'], 4) . "\n";
    
    echo "\n✅ Structure IA créée avec succès !\n";
    
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
}
?>
