<?php
/**
 * Test Payments API
 */

echo "<h2>Payments API Test</h2>\n";
echo "<pre>\n";

// Test 1: Get Payment Methods
echo "=== Test 1: Get Payment Methods ===\n";
$_SERVER['REQUEST_METHOD'] = 'GET';
$_SERVER['REQUEST_URI'] = '/api/payments/methods';
$_SERVER['HTTP_AUTHORIZATION'] = 'Bearer demo_token';

ob_start();
try {
    include 'api/payments.php';
} catch (Exception $e) {
    echo "Exception: " . $e->getMessage() . "\n";
}
$output = ob_get_clean();
echo $output . "\n";

// Test 2: Create Payment Method
echo "=== Test 2: Create Payment Method ===\n";
$_SERVER['REQUEST_METHOD'] = 'POST';
$_SERVER['REQUEST_URI'] = '/api/payments/methods';

// Create sample payment method data
$payment_method_data = [
    'method_type' => 'ccp',
    'method_name' => 'CCP - Compte Chèques Postaux',
    'configuration' => [
        'account_number' => '**********',
        'account_holder' => 'Nom du Titulaire',
        'instructions' => 'Veuillez effectuer le virement vers le compte CCP mentionné et envoyer le reçu.'
    ],
    'is_active' => 1,
    'display_order' => 1
];

// Mock POST data
$GLOBALS['HTTP_RAW_POST_DATA'] = json_encode($payment_method_data);

ob_start();
try {
    include 'api/payments.php';
} catch (Exception $e) {
    echo "Exception: " . $e->getMessage() . "\n";
}
$output = ob_get_clean();
echo $output . "\n";

// Test 3: Get Payments
echo "=== Test 3: Get Payments ===\n";
$_SERVER['REQUEST_METHOD'] = 'GET';
$_SERVER['REQUEST_URI'] = '/api/payments';

ob_start();
try {
    include 'api/payments.php';
} catch (Exception $e) {
    echo "Exception: " . $e->getMessage() . "\n";
}
$output = ob_get_clean();
echo $output . "\n";

echo "</pre>\n";
?>
