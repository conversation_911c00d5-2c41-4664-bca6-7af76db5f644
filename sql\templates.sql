-- Insert modern landing page templates
INSERT INTO `templates` (`name`, `description`, `preview_image`, `html_content`, `css_content`, `js_content`, `category`, `status`) VALUES

-- E-commerce Template
('Modern Shop', 'Template moderne pour boutique en ligne avec sections hero, produits vedettes, et témoignages', '/assets/templates/modern-shop-preview.svg',
'<div class="hero-section">
  <div class="container">
    <h1>Votre Boutique En Ligne</h1>
    <p>Découvrez notre collection exclusive</p>
    <a href="#products" class="cta-button">Voir les produits</a>
  </div>
</div>
<div class="featured-products" id="products">
  <div class="container">
    <h2>Produits Vedettes</h2>
    <div class="product-grid"></div>
  </div>
</div>
<div class="testimonials">
  <div class="container">
    <h2>Avis Clients</h2>
    <div class="testimonial-slider"></div>
  </div>
</div>',
'/* Modern Shop Styles */
.hero-section {
  background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
  color: white;
  padding: 80px 0;
  text-align: center;
}
.cta-button {
  background: #fff;
  color: #4f46e5;
  padding: 15px 30px;
  border-radius: 25px;
  text-decoration: none;
  font-weight: bold;
  transition: all 0.3s;
}
.product-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
  padding: 40px 0;
}
.testimonials {
  background: #f8fafc;
  padding: 60px 0;
}',
'// Modern Shop Scripts
document.addEventListener("DOMContentLoaded", function() {
  // Initialize product grid
  // Initialize testimonial slider
});',
'e-commerce', 'active'),

-- SaaS Template
('SaaS Landing', 'Template moderne pour applications SaaS avec focus sur les fonctionnalités et pricing', '/assets/templates/saas-preview.svg',
'<div class="hero-section">
  <div class="container">
    <h1>Votre Solution SaaS</h1>
    <p>Simplifiez votre workflow avec notre plateforme</p>
    <div class="cta-group">
      <a href="#pricing" class="primary-cta">Commencer</a>
      <a href="#demo" class="secondary-cta">Voir la démo</a>
    </div>
  </div>
</div>
<div class="features" id="features">
  <div class="container">
    <h2>Fonctionnalités</h2>
    <div class="feature-grid"></div>
  </div>
</div>
<div class="pricing" id="pricing">
  <div class="container">
    <h2>Tarifs</h2>
    <div class="pricing-plans"></div>
  </div>
</div>',
'/* SaaS Landing Styles */
.hero-section {
  background: #0f172a;
  color: white;
  padding: 100px 0;
}
.cta-group {
  display: flex;
  gap: 20px;
  justify-content: center;
  margin-top: 40px;
}
.primary-cta {
  background: #3b82f6;
  color: white;
  padding: 15px 30px;
  border-radius: 8px;
  text-decoration: none;
}
.feature-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 40px;
  margin: 60px 0;
}
.pricing-plans {
  display: flex;
  justify-content: center;
  gap: 30px;
  padding: 60px 0;
}',
'// SaaS Landing Scripts
document.addEventListener("DOMContentLoaded", function() {
  // Initialize features
  // Initialize pricing toggle
});',
'saas', 'active'),

-- Portfolio Template
('Creative Portfolio', 'Template portfolio moderne pour créatifs et freelances', '/assets/templates/portfolio-preview.svg',
'<div class="hero-section">
  <div class="container">
    <h1>Votre Portfolio</h1>
    <p>Créatif & Designer</p>
  </div>
</div>
<div class="work" id="work">
  <div class="container">
    <h2>Mes Projets</h2>
    <div class="project-grid"></div>
  </div>
</div>
<div class="contact" id="contact">
  <div class="container">
    <h2>Contact</h2>
    <form class="contact-form"></form>
  </div>
</div>',
'/* Portfolio Styles */
.hero-section {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #18181b;
  color: white;
}
.project-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  padding: 40px 0;
}
.contact-form {
  max-width: 600px;
  margin: 0 auto;
  padding: 40px 0;
}',
'// Portfolio Scripts
document.addEventListener("DOMContentLoaded", function() {
  // Initialize project grid
  // Initialize contact form
});',
'portfolio', 'active'),

-- Service Business Template
('Service Pro', 'Template moderne pour entreprises de services', '/assets/templates/service-preview.svg',
'<div class="hero-section">
  <div class="container">
    <h1>Vos Services Pro</h1>
    <p>Solutions sur mesure pour votre entreprise</p>
    <a href="#contact" class="cta-button">Nous contacter</a>
  </div>
</div>
<div class="services" id="services">
  <div class="container">
    <h2>Nos Services</h2>
    <div class="service-grid"></div>
  </div>
</div>
<div class="why-us" id="why-us">
  <div class="container">
    <h2>Pourquoi Nous Choisir</h2>
    <div class="benefits-grid"></div>
  </div>
</div>',
'/* Service Pro Styles */
.hero-section {
  background: linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%);
  color: white;
  padding: 120px 0;
  text-align: center;
}
.service-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 30px;
  margin: 50px 0;
}
.benefits-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 40px;
  padding: 60px 0;
}',
'// Service Pro Scripts
document.addEventListener("DOMContentLoaded", function() {
  // Initialize service grid
  // Initialize benefits animation
});',
'service', 'active'),

-- Mobile App Template
('App Launch', 'Template moderne pour landing page d''application mobile', '/assets/templates/app-preview.svg',
'<div class="hero-section">
  <div class="container">
    <h1>Votre App Mobile</h1>
    <p>Disponible sur iOS et Android</p>
    <div class="app-stores">
      <a href="#" class="app-store-button">App Store</a>
      <a href="#" class="play-store-button">Play Store</a>
    </div>
  </div>
</div>
<div class="features" id="features">
  <div class="container">
    <h2>Fonctionnalités</h2>
    <div class="feature-list"></div>
  </div>
</div>
<div class="screenshots" id="screenshots">
  <div class="container">
    <h2>Captures d''écran</h2>
    <div class="screenshot-slider"></div>
  </div>
</div>',
'/* App Launch Styles */
.hero-section {
  background: #000;
  color: white;
  padding: 100px 0;
  text-align: center;
}
.app-stores {
  display: flex;
  gap: 20px;
  justify-content: center;
  margin-top: 40px;
}
.feature-list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 40px;
  margin: 60px 0;
}
.screenshot-slider {
  overflow: hidden;
  padding: 40px 0;
}',
'// App Launch Scripts
document.addEventListener("DOMContentLoaded", function() {
  // Initialize feature animations
  // Initialize screenshot slider
});',
'app', 'active');