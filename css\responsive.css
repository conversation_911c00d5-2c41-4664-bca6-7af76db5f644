/**
 * Responsive CSS - Styles adaptatifs pour tous les appareils
 * Optimisé pour mobile-first avec support RTL
 */

/* Variables CSS pour la responsivité */
:root {
    /* Breakpoints */
    --mobile-max: 767px;
    --tablet-min: 768px;
    --tablet-max: 1023px;
    --desktop-min: 1024px;
    --desktop-large-min: 1440px;
    
    /* Espacements responsifs */
    --container-padding-mobile: 1rem;
    --container-padding-tablet: 2rem;
    --container-padding-desktop: 3rem;
    
    /* Tailles de police responsives */
    --font-size-h1-mobile: 2rem;
    --font-size-h1-tablet: 2.5rem;
    --font-size-h1-desktop: 3rem;
    
    --font-size-h2-mobile: 1.5rem;
    --font-size-h2-tablet: 2rem;
    --font-size-h2-desktop: 2.5rem;
    
    --font-size-h3-mobile: 1.25rem;
    --font-size-h3-tablet: 1.5rem;
    --font-size-h3-desktop: 1.75rem;
}

/* Base responsive styles */
* {
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    overflow-x: hidden;
    line-height: 1.6;
}

/* Container responsive */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--container-padding-mobile);
}

/* Images responsives */
img {
    max-width: 100%;
    height: auto;
    display: block;
}

/* Vidéos responsives */
video {
    max-width: 100%;
    height: auto;
}

/* Tableaux responsifs */
.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

table {
    width: 100%;
    border-collapse: collapse;
}

/* Grilles responsives */
.grid {
    display: grid;
    gap: 1rem;
}

.grid-1 { grid-template-columns: 1fr; }
.grid-2 { grid-template-columns: repeat(2, 1fr); }
.grid-3 { grid-template-columns: repeat(3, 1fr); }
.grid-4 { grid-template-columns: repeat(4, 1fr); }

/* Flexbox utilitaires */
.flex {
    display: flex;
}

.flex-column {
    flex-direction: column;
}

.flex-wrap {
    flex-wrap: wrap;
}

.justify-center {
    justify-content: center;
}

.justify-between {
    justify-content: space-between;
}

.align-center {
    align-items: center;
}

/* Utilitaires d'affichage */
.hide-mobile {
    display: none;
}

.show-mobile {
    display: block;
}

.hide-tablet {
    display: block;
}

.hide-desktop {
    display: block;
}

/* Typographie responsive */
h1 {
    font-size: var(--font-size-h1-mobile);
    line-height: 1.2;
    margin-bottom: 1rem;
}

h2 {
    font-size: var(--font-size-h2-mobile);
    line-height: 1.3;
    margin-bottom: 0.875rem;
}

h3 {
    font-size: var(--font-size-h3-mobile);
    line-height: 1.4;
    margin-bottom: 0.75rem;
}

p {
    margin-bottom: 1rem;
    line-height: 1.6;
}

/* Boutons responsifs */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 0.5rem;
    font-size: 1rem;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    min-height: 44px; /* Accessibilité tactile */
    min-width: 44px;
}

.btn-block {
    width: 100%;
    display: flex;
}

/* Navigation responsive */
.navbar {
    position: relative;
    z-index: 1000;
}

.navbar-toggle {
    display: block;
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
}

.navbar-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.navbar-menu.active {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
}

.navbar-menu ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.navbar-menu li {
    border-bottom: 1px solid #eee;
}

.navbar-menu a {
    display: block;
    padding: 1rem;
    text-decoration: none;
    color: inherit;
}

/* Cards responsives */
.card {
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 1rem;
}

.card-header,
.card-body,
.card-footer {
    padding: 1rem;
}

.card-header {
    border-bottom: 1px solid #eee;
}

.card-footer {
    border-top: 1px solid #eee;
    background: #f8f9fa;
}

/* Formulaires responsifs */
.form-group {
    margin-bottom: 1rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 0.25rem;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    border-color: var(--primary-color, #007bff);
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

/* Support RTL */
[dir="rtl"] .navbar-menu {
    left: auto;
    right: 0;
}

[dir="rtl"] .flex {
    direction: rtl;
}

[dir="rtl"] .text-left {
    text-align: right;
}

[dir="rtl"] .text-right {
    text-align: left;
}

/* TABLET STYLES (768px et plus) */
@media (min-width: 768px) {
    .container {
        padding: 0 var(--container-padding-tablet);
    }
    
    h1 {
        font-size: var(--font-size-h1-tablet);
    }
    
    h2 {
        font-size: var(--font-size-h2-tablet);
    }
    
    h3 {
        font-size: var(--font-size-h3-tablet);
    }
    
    /* Grilles tablet */
    .grid-tablet-2 {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .grid-tablet-3 {
        grid-template-columns: repeat(3, 1fr);
    }
    
    /* Utilitaires d'affichage tablet */
    .hide-mobile {
        display: block;
    }
    
    .show-mobile {
        display: none;
    }
    
    .hide-tablet {
        display: none;
    }
    
    /* Navigation tablet */
    .navbar-toggle {
        display: none;
    }
    
    .navbar-menu {
        position: static;
        transform: none;
        opacity: 1;
        visibility: visible;
        background: transparent;
        box-shadow: none;
    }
    
    .navbar-menu ul {
        display: flex;
        gap: 2rem;
    }
    
    .navbar-menu li {
        border-bottom: none;
    }
    
    .navbar-menu a {
        padding: 0.5rem 0;
    }
    
    /* Cards en grille */
    .cards-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
    }
    
    /* Boutons côte à côte */
    .btn-group {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
    }
    
    .btn-group .btn {
        flex: 1;
        min-width: 150px;
    }
}

/* DESKTOP STYLES (1024px et plus) */
@media (min-width: 1024px) {
    .container {
        padding: 0 var(--container-padding-desktop);
    }
    
    h1 {
        font-size: var(--font-size-h1-desktop);
    }
    
    h2 {
        font-size: var(--font-size-h2-desktop);
    }
    
    h3 {
        font-size: var(--font-size-h3-desktop);
    }
    
    /* Grilles desktop */
    .grid-desktop-2 {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .grid-desktop-3 {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .grid-desktop-4 {
        grid-template-columns: repeat(4, 1fr);
    }
    
    /* Utilitaires d'affichage desktop */
    .hide-desktop {
        display: none;
    }
    
    .show-desktop {
        display: block;
    }
    
    /* Espacements plus généreux */
    .section {
        padding: 4rem 0;
    }
    
    /* Hover effects pour desktop */
    .card:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }
    
    .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
}

/* LARGE DESKTOP STYLES (1440px et plus) */
@media (min-width: 1440px) {
    .container {
        max-width: 1400px;
    }
    
    /* Grilles plus larges */
    .grid-xl-5 {
        grid-template-columns: repeat(5, 1fr);
    }
    
    .grid-xl-6 {
        grid-template-columns: repeat(6, 1fr);
    }
}

/* PRINT STYLES */
@media print {
    * {
        background: transparent !important;
        color: black !important;
        box-shadow: none !important;
        text-shadow: none !important;
    }
    
    .navbar,
    .btn,
    .hide-print {
        display: none !important;
    }
    
    .container {
        max-width: none;
        padding: 0;
    }
    
    h1, h2, h3 {
        page-break-after: avoid;
    }
    
    img {
        max-width: 100% !important;
    }
    
    p, h2, h3 {
        orphans: 3;
        widows: 3;
    }
}

/* ACCESSIBILITÉ */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* Mode sombre */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-color: #1a1a1a;
        --text-color: #ffffff;
        --border-color: #333333;
    }
    
    body {
        background-color: var(--bg-color);
        color: var(--text-color);
    }
    
    .card {
        background: #2a2a2a;
        border: 1px solid var(--border-color);
    }
    
    .form-input,
    .form-select,
    .form-textarea {
        background: #2a2a2a;
        border-color: var(--border-color);
        color: var(--text-color);
    }
}

/* Utilitaires de débordement */
.overflow-hidden {
    overflow: hidden;
}

.overflow-auto {
    overflow: auto;
}

.overflow-scroll {
    overflow: scroll;
}

/* Utilitaires de position */
.relative {
    position: relative;
}

.absolute {
    position: absolute;
}

.fixed {
    position: fixed;
}

.sticky {
    position: sticky;
}

/* Utilitaires de z-index */
.z-10 {
    z-index: 10;
}

.z-20 {
    z-index: 20;
}

.z-30 {
    z-index: 30;
}

.z-40 {
    z-index: 40;
}

.z-50 {
    z-index: 50;
}

/* Utilitaires d'espacement */
.m-0 { margin: 0; }
.m-1 { margin: 0.25rem; }
.m-2 { margin: 0.5rem; }
.m-3 { margin: 0.75rem; }
.m-4 { margin: 1rem; }
.m-5 { margin: 1.25rem; }
.m-6 { margin: 1.5rem; }

.p-0 { padding: 0; }
.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 0.75rem; }
.p-4 { padding: 1rem; }
.p-5 { padding: 1.25rem; }
.p-6 { padding: 1.5rem; }

/* Utilitaires de largeur */
.w-full { width: 100%; }
.w-auto { width: auto; }
.w-1\/2 { width: 50%; }
.w-1\/3 { width: 33.333333%; }
.w-2\/3 { width: 66.666667%; }
.w-1\/4 { width: 25%; }
.w-3\/4 { width: 75%; }

/* Utilitaires de hauteur */
.h-full { height: 100%; }
.h-auto { height: auto; }
.h-screen { height: 100vh; }

/* Utilitaires de texte */
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-justify { text-align: justify; }

.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.font-extrabold { font-weight: 800; }

.text-xs { font-size: 0.75rem; }
.text-sm { font-size: 0.875rem; }
.text-base { font-size: 1rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }
.text-3xl { font-size: 1.875rem; }
.text-4xl { font-size: 2.25rem; }
.text-5xl { font-size: 3rem; }

/* Animations responsives */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInUp {
    from {
        transform: translateY(30px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes slideInDown {
    from {
        transform: translateY(-30px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.animate-fadeIn {
    animation: fadeIn 0.5s ease-out;
}

.animate-slideInUp {
    animation: slideInUp 0.5s ease-out;
}

.animate-slideInDown {
    animation: slideInDown 0.5s ease-out;
}

/* Responsive pour les animations */
@media (max-width: 767px) {
    .animate-fadeIn,
    .animate-slideInUp,
    .animate-slideInDown {
        animation: none; /* Désactiver les animations sur mobile pour les performances */
    }
}