<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI API Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .result { background: #f5f5f5; padding: 10px; margin: 10px 0; }
        button { padding: 10px 15px; margin: 5px; }
        .success { color: green; }
        .error { color: red; }
    </style>
</head>
<body>
    <h1>AI API Test</h1>
    
    <div class="test-section">
        <h3>Test 1: Get AI Keys</h3>
        <button onclick="testGetAiKeys()">Get AI Keys</button>
        <div id="keysResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>Test 2: Get AI Usage</h3>
        <button onclick="testGetAiUsage()">Get AI Usage</button>
        <div id="usageResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>Test 3: Get AI Analytics</h3>
        <button onclick="testGetAiAnalytics()">Get AI Analytics</button>
        <div id="analyticsResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>Test 4: Create AI Key</h3>
        <button onclick="testCreateAiKey()">Create OpenAI Key</button>
        <div id="createResult" class="result"></div>
    </div>

    <script>
        async function testGetAiKeys() {
            try {
                const response = await fetch('/api/ai/keys', {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer demo_token',
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                document.getElementById('keysResult').innerHTML = 
                    `<div class="success"><strong>Status:</strong> ${response.status}</div>
                     <strong>AI Keys Found:</strong> ${data.success ? data.data.length : 0}<br>
                     <strong>Response:</strong><br>
                     <pre>${JSON.stringify(data, null, 2)}</pre>`;
            } catch (error) {
                document.getElementById('keysResult').innerHTML = 
                    `<div class="error"><strong>Error:</strong> ${error.message}</div>`;
            }
        }
        
        async function testGetAiUsage() {
            try {
                const response = await fetch('/api/ai/usage', {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer demo_token',
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                document.getElementById('usageResult').innerHTML = 
                    `<div class="success"><strong>Status:</strong> ${response.status}</div>
                     <strong>Usage Records:</strong> ${data.success ? data.data.usage.length : 0}<br>
                     <strong>Response:</strong><br>
                     <pre>${JSON.stringify(data, null, 2)}</pre>`;
            } catch (error) {
                document.getElementById('usageResult').innerHTML = 
                    `<div class="error"><strong>Error:</strong> ${error.message}</div>`;
            }
        }
        
        async function testGetAiAnalytics() {
            try {
                const response = await fetch('/api/ai/analytics?period=30d', {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer demo_token',
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                document.getElementById('analyticsResult').innerHTML = 
                    `<div class="success"><strong>Status:</strong> ${response.status}</div>
                     <strong>Analytics Period:</strong> ${data.success ? data.data.period : 'N/A'}<br>
                     <strong>Response:</strong><br>
                     <pre>${JSON.stringify(data, null, 2)}</pre>`;
            } catch (error) {
                document.getElementById('analyticsResult').innerHTML = 
                    `<div class="error"><strong>Error:</strong> ${error.message}</div>`;
            }
        }
        
        async function testCreateAiKey() {
            const keyData = {
                provider: 'openai',
                key_name: 'Test OpenAI Key',
                api_key: 'sk-test-key-1234567890abcdef',
                model_access: ['gpt-4', 'gpt-3.5-turbo'],
                usage_limits: {
                    monthly_tokens: 100000,
                    daily_requests: 1000
                },
                is_active: 1
            };
            
            try {
                const response = await fetch('/api/ai/keys', {
                    method: 'POST',
                    headers: {
                        'Authorization': 'Bearer demo_token',
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(keyData)
                });
                
                const data = await response.json();
                document.getElementById('createResult').innerHTML = 
                    `<div class="success"><strong>Status:</strong> ${response.status}</div>
                     <strong>Response:</strong><br>
                     <pre>${JSON.stringify(data, null, 2)}</pre>`;
            } catch (error) {
                document.getElementById('createResult').innerHTML = 
                    `<div class="error"><strong>Error:</strong> ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
