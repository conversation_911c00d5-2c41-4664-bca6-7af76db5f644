<?php
/**
 * Script pour créer les tables categories et product_categories
 */

// Configuration de la base de données
$host = 'localhost';
$port = 3307;
$dbname = 'landingpage_new';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;port=$port;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Créer la table categories
    $createCategoriesTable = "
        CREATE TABLE IF NOT EXISTS categories (
            id INT AUTO_INCREMENT PRIMARY KEY,
            store_id INT NOT NULL,
            name VARCHAR(255) NOT NULL,
            description TEXT,
            slug VARCHAR(255) NOT NULL,
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (store_id) REFERENCES stores(id) ON DELETE CASCADE,
            UNIQUE KEY unique_store_slug (store_id, slug)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";

    $pdo->exec($createCategoriesTable);
    echo "✅ Table categories créée ou existe déjà\n";

    // Créer la table product_categories
    $createProductCategoriesTable = "
        CREATE TABLE IF NOT EXISTS product_categories (
            id INT AUTO_INCREMENT PRIMARY KEY,
            product_id INT NOT NULL,
            category_id INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
            FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE,
            UNIQUE KEY unique_product_category (product_id, category_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";

    $pdo->exec($createProductCategoriesTable);
    echo "✅ Table product_categories créée ou existe déjà\n";

    // Maintenant créer les catégories et associations
    $storeQuery = "SELECT id FROM stores WHERE store_name = 'TechStore Algeria'";
    $storeStmt = $pdo->prepare($storeQuery);
    $storeStmt->execute();
    $store = $storeStmt->fetch();

    if (!$store) {
        throw new Exception("Store TechStore Algeria non trouvé");
    }

    $storeId = $store['id'];

    // Créer les catégories
    $categories = [
        ['Smartphones', 'Téléphones intelligents et accessoires', 'smartphones'],
        ['Ordinateurs', 'Ordinateurs portables et de bureau', 'computers'],
        ['Tablettes', 'Tablettes et accessoires', 'tablets'],
        ['Audio', 'Casques, écouteurs et haut-parleurs', 'audio'],
        ['Gaming', 'Consoles de jeu et accessoires', 'gaming'],
        ['Montres', 'Montres connectées et accessoires', 'watches']
    ];

    $categoryIds = [];
    
    foreach ($categories as $category) {
        // Vérifier si la catégorie existe déjà
        $checkQuery = "SELECT id FROM categories WHERE store_id = ? AND name = ?";
        $checkStmt = $pdo->prepare($checkQuery);
        $checkStmt->execute([$storeId, $category[0]]);
        $existing = $checkStmt->fetch();

        if ($existing) {
            $categoryIds[$category[2]] = $existing['id'];
            echo "✅ Catégorie '{$category[0]}' existe déjà\n";
        } else {
            $insertQuery = "
                INSERT INTO categories (
                    store_id, name, description, slug, status, created_at, updated_at
                ) VALUES (?, ?, ?, ?, 'active', NOW(), NOW())
            ";
            
            $pdo->prepare($insertQuery)->execute([
                $storeId, $category[0], $category[1], $category[2]
            ]);
            
            $categoryIds[$category[2]] = $pdo->lastInsertId();
            echo "✅ Catégorie '{$category[0]}' créée\n";
        }
    }

    // Associer les produits aux catégories
    $productCategories = [
        'Smartphone Samsung Galaxy S24' => 'smartphones',
        'iPhone 15 Pro' => 'smartphones',
        'MacBook Air M2' => 'computers',
        'Dell XPS 13' => 'computers',
        'iPad Pro 12.9"' => 'tablets',
        'AirPods Pro 2' => 'audio',
        'Sony WH-1000XM5' => 'audio',
        'Nintendo Switch OLED' => 'gaming',
        'PlayStation 5' => 'gaming',
        'Apple Watch Series 9' => 'watches'
    ];

    foreach ($productCategories as $productName => $categorySlug) {
        // Récupérer l'ID du produit
        $productQuery = "SELECT id FROM products WHERE store_id = ? AND name = ?";
        $productStmt = $pdo->prepare($productQuery);
        $productStmt->execute([$storeId, $productName]);
        $product = $productStmt->fetch();

        if ($product && isset($categoryIds[$categorySlug])) {
            // Vérifier si l'association existe déjà
            $checkAssocQuery = "SELECT id FROM product_categories WHERE product_id = ? AND category_id = ?";
            $checkAssocStmt = $pdo->prepare($checkAssocQuery);
            $checkAssocStmt->execute([$product['id'], $categoryIds[$categorySlug]]);
            
            if (!$checkAssocStmt->fetch()) {
                // Créer l'association
                $assocQuery = "INSERT INTO product_categories (product_id, category_id) VALUES (?, ?)";
                $pdo->prepare($assocQuery)->execute([$product['id'], $categoryIds[$categorySlug]]);
                echo "✅ Produit '$productName' associé à la catégorie '$categorySlug'\n";
            }
        }
    }

    echo "\n🎉 Tables et données créées avec succès !\n";

} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
}
?>
