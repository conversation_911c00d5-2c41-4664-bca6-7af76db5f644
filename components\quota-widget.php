<?php
/**
 * Widget d'affichage des quotas et limites
 * Composant réutilisable pour l'interface utilisateur
 */

/**
 * Afficher le widget des quotas
 * @param int $userId ID de l'utilisateur
 * @param string $language Langue (ar, fr, en)
 * @param bool $compact Mode compact
 */
function renderQuotaWidget($userId, $language = 'ar', $compact = false) {
    // Cette fonction sera appelée avec les données déjà récupérées
    // Pour éviter les appels multiples à la base de données
    echo '<div id="quota-widget-' . $userId . '" data-user-id="' . $userId . '" data-language="' . $language . '" data-compact="' . ($compact ? 'true' : 'false') . '"></div>';
    echo '<script>loadQuotaWidget(' . $userId . ', "' . $language . '", ' . ($compact ? 'true' : 'false') . ');</script>';
}

/**
 * Afficher une barre de progression pour un quota spécifique
 * @param array $quota Données du quota
 * @param string $type Type de ressource
 * @param string $language Langue
 */
function renderQuotaBar($quota, $type, $language = 'ar') {
    $translations = [
        'ar' => [
            'products' => 'المنتجات',
            'landing_pages' => 'صفحات الهبوط',
            'categories' => 'الفئات',
            'unlimited' => 'غير محدود',
            'remaining' => 'متبقي',
            'used' => 'مستخدم'
        ],
        'fr' => [
            'products' => 'Produits',
            'landing_pages' => 'Pages de destination',
            'categories' => 'Catégories',
            'unlimited' => 'Illimité',
            'remaining' => 'Restant',
            'used' => 'Utilisé'
        ],
        'en' => [
            'products' => 'Products',
            'landing_pages' => 'Landing Pages',
            'categories' => 'Categories',
            'unlimited' => 'Unlimited',
            'remaining' => 'Remaining',
            'used' => 'Used'
        ]
    ];
    
    $t = $translations[$language] ?? $translations['ar'];
    $typeName = $t[$type] ?? $type;
    
    $current = $quota['current'] ?? 0;
    $limit = $quota['limit'] ?? 0;
    $percentage = $quota['percentage'] ?? 0;
    $unlimited = $quota['unlimited'] ?? false;
    
    // Déterminer la couleur de la barre
    $colorClass = 'bg-success';
    if ($percentage >= 90) {
        $colorClass = 'bg-danger';
    } elseif ($percentage >= 75) {
        $colorClass = 'bg-warning';
    } elseif ($percentage >= 50) {
        $colorClass = 'bg-info';
    }
    
    $direction = $language === 'ar' ? 'rtl' : 'ltr';
    
    echo '<div class="quota-item mb-3" dir="' . $direction . '">';
    echo '  <div class="d-flex justify-content-between align-items-center mb-1">';
    echo '    <span class="fw-medium">' . htmlspecialchars($typeName) . '</span>';
    
    if ($unlimited) {
        echo '    <span class="badge bg-primary">' . htmlspecialchars($t['unlimited']) . '</span>';
    } else {
        echo '    <span class="text-muted small">' . $current . ' / ' . $limit . '</span>';
    }
    
    echo '  </div>';
    
    if (!$unlimited) {
        echo '  <div class="progress" style="height: 8px;">';
        echo '    <div class="progress-bar ' . $colorClass . '" role="progressbar" style="width: ' . $percentage . '%" aria-valuenow="' . $percentage . '" aria-valuemin="0" aria-valuemax="100"></div>';
        echo '  </div>';
        
        $remaining = max(0, $limit - $current);
        echo '  <div class="d-flex justify-content-between mt-1">';
        echo '    <small class="text-muted">' . htmlspecialchars($t['used']) . ': ' . $current . '</small>';
        echo '    <small class="text-muted">' . htmlspecialchars($t['remaining']) . ': ' . $remaining . '</small>';
        echo '  </div>';
    }
    
    echo '</div>';
}

?>

<!-- CSS pour le widget des quotas -->
<style>
.quota-widget {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
}

.quota-widget.compact {
    padding: 0.75rem;
}

.quota-widget .quota-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e9ecef;
}

.quota-widget .quota-header h6 {
    margin: 0;
    color: #495057;
}

.quota-widget .subscription-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.quota-item {
    transition: all 0.3s ease;
}

.quota-item:hover {
    background-color: #f8f9fa;
    border-radius: 4px;
    padding: 0.5rem;
    margin: -0.5rem;
    margin-bottom: 0.5rem;
}

.progress {
    background-color: #e9ecef;
    border-radius: 4px;
}

.progress-bar {
    transition: width 0.6s ease;
}

.quota-alert {
    border-radius: 6px;
    padding: 0.75rem;
    margin-top: 1rem;
    font-size: 0.875rem;
}

.quota-alert.alert-warning {
    background-color: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

.quota-alert.alert-danger {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

/* RTL Support */
[dir="rtl"] .quota-widget .quota-header {
    text-align: right;
}

[dir="rtl"] .d-flex {
    flex-direction: row-reverse;
}

[dir="rtl"] .justify-content-between {
    flex-direction: row;
}

/* Responsive */
@media (max-width: 768px) {
    .quota-widget {
        padding: 0.75rem;
    }
    
    .quota-widget .quota-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .quota-item .d-flex {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }
}
</style>

<!-- JavaScript pour le widget des quotas -->
<script>
/**
 * Charger et afficher le widget des quotas
 */
async function loadQuotaWidget(userId, language = 'ar', compact = false) {
    const container = document.getElementById(`quota-widget-${userId}`);
    if (!container) return;
    
    try {
        // Afficher un loader
        container.innerHTML = `
            <div class="quota-widget ${compact ? 'compact' : ''}">
                <div class="text-center py-3">
                    <div class="spinner-border spinner-border-sm text-primary" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                </div>
            </div>
        `;
        
        // Récupérer les données
        const response = await fetch(`/api/quota-api.php?action=get_quota_usage&user_id=${userId}&lang=${language}`);
        const data = await response.json();
        
        if (!data.success) {
            throw new Error(data.error || 'Erreur lors du chargement des quotas');
        }
        
        // Afficher le widget
        renderQuotaWidgetHTML(container, data.data, language, compact);
        
    } catch (error) {
        console.error('Erreur lors du chargement du widget des quotas:', error);
        container.innerHTML = `
            <div class="quota-widget ${compact ? 'compact' : ''}">
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Erreur lors du chargement des quotas
                </div>
            </div>
        `;
    }
}

/**
 * Générer le HTML du widget des quotas
 */
function renderQuotaWidgetHTML(container, data, language, compact) {
    const { user, usage, translations } = data;
    const direction = language === 'ar' ? 'rtl' : 'ltr';
    
    // Déterminer le statut global
    let globalStatus = 'success';
    let hasWarnings = false;
    let hasErrors = false;
    
    Object.values(usage).forEach(quota => {
        if (!quota.unlimited && quota.percentage >= 90) {
            hasErrors = true;
        } else if (!quota.unlimited && quota.percentage >= 75) {
            hasWarnings = true;
        }
    });
    
    if (hasErrors) globalStatus = 'danger';
    else if (hasWarnings) globalStatus = 'warning';
    
    let html = `
        <div class="quota-widget ${compact ? 'compact' : ''}" dir="${direction}">
    `;
    
    // En-tête
    if (!compact) {
        html += `
            <div class="quota-header">
                <h6><i class="fas fa-chart-pie me-2"></i>${translations.current_usage}</h6>
                <span class="badge bg-${getSubscriptionBadgeColor(user.subscription_status)} subscription-badge">
                    ${user.subscription_name || 'Aucun abonnement'}
                </span>
            </div>
        `;
    }
    
    // Barres de progression
    Object.entries(usage).forEach(([type, quota]) => {
        html += renderQuotaBarHTML(quota, type, translations, language);
    });
    
    // Alertes
    if (hasErrors) {
        html += `
            <div class="quota-alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                ${translations.quota_exceeded}. ${translations.upgrade_required}.
            </div>
        `;
    } else if (hasWarnings) {
        html += `
            <div class="quota-alert alert-warning">
                <i class="fas fa-exclamation-circle me-2"></i>
                Vous approchez de vos limites d'abonnement.
            </div>
        `;
    }
    
    html += '</div>';
    
    container.innerHTML = html;
}

/**
 * Générer le HTML d'une barre de quota
 */
function renderQuotaBarHTML(quota, type, translations, language) {
    const typeNames = {
        'ar': {
            'products': 'المنتجات',
            'landing_pages': 'صفحات الهبوط',
            'categories': 'الفئات'
        },
        'fr': {
            'products': 'Produits',
            'landing_pages': 'Pages de destination',
            'categories': 'Catégories'
        },
        'en': {
            'products': 'Products',
            'landing_pages': 'Landing Pages',
            'categories': 'Categories'
        }
    };
    
    const typeName = typeNames[language]?.[type] || type;
    const { current, limit, percentage, unlimited } = quota;
    
    let colorClass = 'bg-success';
    if (percentage >= 90) colorClass = 'bg-danger';
    else if (percentage >= 75) colorClass = 'bg-warning';
    else if (percentage >= 50) colorClass = 'bg-info';
    
    let html = `
        <div class="quota-item mb-3">
            <div class="d-flex justify-content-between align-items-center mb-1">
                <span class="fw-medium">${typeName}</span>
    `;
    
    if (unlimited) {
        html += `<span class="badge bg-primary">${translations.unlimited}</span>`;
    } else {
        html += `<span class="text-muted small">${current} / ${limit}</span>`;
    }
    
    html += '</div>';
    
    if (!unlimited) {
        html += `
            <div class="progress" style="height: 8px;">
                <div class="progress-bar ${colorClass}" role="progressbar" 
                     style="width: ${percentage}%" 
                     aria-valuenow="${percentage}" 
                     aria-valuemin="0" 
                     aria-valuemax="100"></div>
            </div>
            <div class="d-flex justify-content-between mt-1">
                <small class="text-muted">${translations.current_usage}: ${current}</small>
                <small class="text-muted">${translations.remaining}: ${Math.max(0, limit - current)}</small>
            </div>
        `;
    }
    
    html += '</div>';
    
    return html;
}

/**
 * Obtenir la couleur du badge d'abonnement
 */
function getSubscriptionBadgeColor(status) {
    switch (status) {
        case 'active': return 'success';
        case 'trial': return 'info';
        case 'expired': return 'warning';
        case 'cancelled': return 'danger';
        default: return 'secondary';
    }
}

/**
 * Vérifier les quotas avant création
 */
async function checkQuotaBeforeCreate(userId, type, language = 'ar') {
    try {
        const response = await fetch('/api/quota-api.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `action=validate_before_create&user_id=${userId}&type=${type}&lang=${language}`
        });
        
        const data = await response.json();
        return data;
        
    } catch (error) {
        console.error('Erreur lors de la vérification des quotas:', error);
        return {
            success: false,
            error: 'network_error',
            message: 'Erreur de connexion'
        };
    }
}

/**
 * Actualiser le widget des quotas
 */
function refreshQuotaWidget(userId, language = 'ar') {
    loadQuotaWidget(userId, language);
}

/**
 * Afficher une notification de quota
 */
function showQuotaNotification(message, type = 'warning') {
    // Utiliser votre système de notification existant
    // ou créer une notification simple
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show`;
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // Ajouter au début du body ou dans un container spécifique
    document.body.insertBefore(notification, document.body.firstChild);
    
    // Auto-supprimer après 5 secondes
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}
</script>