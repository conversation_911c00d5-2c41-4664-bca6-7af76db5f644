const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Fonction pour extraire le JavaScript des fichiers HTML
function extractJavaScriptFromHTML(filePath) {
    try {
        const content = fs.readFileSync(filePath, 'utf8');
        const scriptRegex = /<script[^>]*>([\s\S]*?)<\/script>/gi;
        const scripts = [];
        let match;
        
        while ((match = scriptRegex.exec(content)) !== null) {
            const scriptContent = match[1].trim();
            if (scriptContent && !scriptContent.includes('src=')) {
                scripts.push({
                    content: scriptContent,
                    line: content.substring(0, match.index).split('\n').length
                });
            }
        }
        
        return scripts;
    } catch (error) {
        console.error(`Erreur lecture ${filePath}:`, error.message);
        return [];
    }
}

// Fonction pour vérifier la syntaxe JavaScript
function checkJavaScriptSyntax(jsCode, filePath, lineOffset = 0) {
    try {
        // Créer un fichier temporaire
        const tempFile = path.join(__dirname, 'temp_check.js');
        fs.writeFileSync(tempFile, jsCode);
        
        // Vérifier la syntaxe avec Node.js
        execSync(`node -c "${tempFile}"`, { stdio: 'pipe' });
        
        // Nettoyer le fichier temporaire
        fs.unlinkSync(tempFile);
        
        return { valid: true };
    } catch (error) {
        // Nettoyer le fichier temporaire en cas d'erreur
        const tempFile = path.join(__dirname, 'temp_check.js');
        if (fs.existsSync(tempFile)) {
            fs.unlinkSync(tempFile);
        }
        
        return {
            valid: false,
            error: error.message,
            file: filePath,
            lineOffset: lineOffset
        };
    }
}

// Fonction pour analyser tous les fichiers HTML
function analyzeHTMLFiles(directory) {
    const errors = [];
    
    function scanDirectory(dir) {
        const files = fs.readdirSync(dir);
        
        for (const file of files) {
            const filePath = path.join(dir, file);
            const stat = fs.statSync(filePath);
            
            if (stat.isDirectory() && !file.startsWith('.') && file !== 'node_modules') {
                scanDirectory(filePath);
            } else if (file.endsWith('.html')) {
                console.log(`Analyse de ${filePath}...`);
                const scripts = extractJavaScriptFromHTML(filePath);
                
                for (const script of scripts) {
                    const result = checkJavaScriptSyntax(script.content, filePath, script.line);
                    if (!result.valid) {
                        errors.push(result);
                    }
                }
            }
        }
    }
    
    scanDirectory(directory);
    return errors;
}

// Analyser le projet
console.log('🔍 Analyse des erreurs de syntaxe JavaScript dans les fichiers HTML...');
const projectDir = __dirname;
const errors = analyzeHTMLFiles(projectDir);

if (errors.length === 0) {
    console.log('✅ Aucune erreur de syntaxe JavaScript trouvée!');
} else {
    console.log(`❌ ${errors.length} erreur(s) trouvée(s):`);
    errors.forEach((error, index) => {
        console.log(`\n${index + 1}. Fichier: ${error.file}`);
        console.log(`   Ligne approximative: ${error.lineOffset}`);
        console.log(`   Erreur: ${error.error}`);
    });
}