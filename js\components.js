/**
 * Components JavaScript - Composants réutilisables
 * Gestion des composants UI interactifs avec support RTL
 */

// Namespace pour les composants
window.LandingPageComponents = {
    // Templates de composants
    componentTemplates: {
        hero: {
            type: 'hero',
            content: `
                <section class="hero bg-primary text-white py-5">
                    <div class="container">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h1>Titre Principal</h1>
                                <p class="lead">Description accrocheuse de votre produit ou service</p>
                                <button class="btn btn-light btn-lg">Commencer</button>
                            </div>
                            <div class="col-md-6 text-center">
                                <img src="placeholder.jpg" alt="Hero Image" class="img-fluid">
                            </div>
                        </div>
                    </div>
                </section>
            `
        },
        features: {
            type: 'features',
            content: `
                <section class="features py-5">
                    <div class="container">
                        <h2 class="text-center mb-5">Fonctionnalités</h2>
                        <div class="row">
                            <div class="col-md-4 mb-4">
                                <div class="text-center">
                                    <i class="fas fa-rocket fa-3x mb-3 text-primary"></i>
                                    <h3>Fonctionnalité 1</h3>
                                    <p>Description de la fonctionnalité</p>
                                </div>
                            </div>
                            <div class="col-md-4 mb-4">
                                <div class="text-center">
                                    <i class="fas fa-cog fa-3x mb-3 text-primary"></i>
                                    <h3>Fonctionnalité 2</h3>
                                    <p>Description de la fonctionnalité</p>
                                </div>
                            </div>
                            <div class="col-md-4 mb-4">
                                <div class="text-center">
                                    <i class="fas fa-star fa-3x mb-3 text-primary"></i>
                                    <h3>Fonctionnalité 3</h3>
                                    <p>Description de la fonctionnalité</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            `
        },
        pricing: {
            type: 'pricing',
            content: `
                <section class="pricing py-5">
                    <div class="container">
                        <h2 class="text-center mb-5">Tarifs</h2>
                        <div class="row">
                            <div class="col-md-4 mb-4">
                                <div class="card h-100">
                                    <div class="card-body text-center">
                                        <h3>Basic</h3>
                                        <h4 class="card-price">9€<span>/mois</span></h4>
                                        <ul class="list-unstyled">
                                            <li>Fonctionnalité 1</li>
                                            <li>Fonctionnalité 2</li>
                                            <li>Support email</li>
                                        </ul>
                                        <button class="btn btn-primary">Choisir</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            `
        },
        testimonials: {
            type: 'testimonials',
            content: `
                <section class="testimonials py-5 bg-light">
                    <div class="container">
                        <h2 class="text-center mb-5">Témoignages</h2>
                        <div class="row">
                            <div class="col-md-4 mb-4">
                                <div class="card h-100">
                                    <div class="card-body">
                                        <p class="card-text">"Excellent service, je recommande !"</p>
                                        <div class="d-flex align-items-center">
                                            <img src="avatar1.jpg" alt="Avatar" class="rounded-circle me-3" width="50">
                                            <div>
                                                <h5 class="mb-0">John Doe</h5>
                                                <small class="text-muted">CEO, Company</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            `
        },
        form: {
            type: 'form',
            content: `
                <section class="contact-form py-5">
                    <div class="container">
                        <h2 class="text-center mb-5">Contactez-nous</h2>
                        <div class="row justify-content-center">
                            <div class="col-md-6">
                                <form>
                                    <div class="mb-3">
                                        <input type="text" class="form-control" placeholder="Nom">
                                    </div>
                                    <div class="mb-3">
                                        <input type="email" class="form-control" placeholder="Email">
                                    </div>
                                    <div class="mb-3">
                                        <textarea class="form-control" rows="4" placeholder="Message"></textarea>
                                    </div>
                                    <button type="submit" class="btn btn-primary w-100">Envoyer</button>
                                </form>
                            </div>
                        </div>
                    </div>
                </section>
            `
        },
        text: {
            type: 'text',
            content: `
                <section class="text-section py-5">
                    <div class="container">
                        <div class="row justify-content-center">
                            <div class="col-md-8">
                                <h2>Titre de la section</h2>
                                <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
                            </div>
                        </div>
                    </div>
                </section>
            `
        }
    },
    // Configuration globale
    config: {
        animationDuration: 300,
        breakpoints: {
            mobile: 768,
            tablet: 1024,
            desktop: 1440
        },
        rtl: document.dir === 'rtl' || document.documentElement.dir === 'rtl'
    },

    // Utilitaires
    utils: {
        // Détection de l'appareil
        isMobile: () => window.innerWidth < window.LandingPageComponents.config.breakpoints.mobile,
        isTablet: () => window.innerWidth >= window.LandingPageComponents.config.breakpoints.mobile && window.innerWidth < window.LandingPageComponents.config.breakpoints.tablet,
        isDesktop: () => window.innerWidth >= window.LandingPageComponents.config.breakpoints.desktop,

        // Debounce function
        debounce: (func, wait) => {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },

        // Throttle function
        throttle: (func, limit) => {
            let inThrottle;
            return function() {
                const args = arguments;
                const context = this;
                if (!inThrottle) {
                    func.apply(context, args);
                    inThrottle = true;
                    setTimeout(() => inThrottle = false, limit);
                }
            }
        },

        // Animation avec support des préférences utilisateur
        animate: (element, animation, duration = 300) => {
            if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
                return Promise.resolve();
            }
            
            return new Promise(resolve => {
                element.style.animation = `${animation} ${duration}ms ease-out`;
                element.addEventListener('animationend', () => {
                    element.style.animation = '';
                    resolve();
                }, { once: true });
            });
        },

        // Gestion des événements tactiles
        addTouchSupport: (element, callback) => {
            let startY = 0;
            let startX = 0;
            
            element.addEventListener('touchstart', (e) => {
                startY = e.touches[0].clientY;
                startX = e.touches[0].clientX;
            });
            
            element.addEventListener('touchend', (e) => {
                const endY = e.changedTouches[0].clientY;
                const endX = e.changedTouches[0].clientX;
                const diffY = startY - endY;
                const diffX = startX - endX;
                
                if (Math.abs(diffY) > Math.abs(diffX)) {
                    if (diffY > 50) callback('swipeUp');
                    if (diffY < -50) callback('swipeDown');
                } else {
                    if (diffX > 50) callback('swipeLeft');
                    if (diffX < -50) callback('swipeRight');
                }
            });
        }
    },

    // Composant Modal
    Modal: {
        instances: new Map(),
        
        create: (id, options = {}) => {
            const defaults = {
                closable: true,
                backdrop: true,
                keyboard: true,
                focus: true,
                animation: true
            };
            
            const config = { ...defaults, ...options };
            const modal = document.getElementById(id);
            
            if (!modal) {
                console.error(`Modal with id '${id}' not found`);
                return null;
            }
            
            const instance = {
                element: modal,
                config: config,
                isOpen: false,
                
                open: function() {
                    if (this.isOpen) return;
                    
                    this.isOpen = true;
                    document.body.classList.add('modal-open');
                    this.element.classList.add('active');
                    
                    if (this.config.focus) {
                        this.element.focus();
                    }
                    
                    // Trap focus
                    this.trapFocus();
                    
                    // Événement personnalisé
                    this.element.dispatchEvent(new CustomEvent('modal:open'));
                },
                
                close: function() {
                    if (!this.isOpen) return;
                    
                    this.isOpen = false;
                    document.body.classList.remove('modal-open');
                    this.element.classList.remove('active');
                    
                    // Événement personnalisé
                    this.element.dispatchEvent(new CustomEvent('modal:close'));
                },
                
                toggle: function() {
                    this.isOpen ? this.close() : this.open();
                },
                
                trapFocus: function() {
                    const focusableElements = this.element.querySelectorAll(
                        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
                    );
                    
                    if (focusableElements.length === 0) return;
                    
                    const firstElement = focusableElements[0];
                    const lastElement = focusableElements[focusableElements.length - 1];
                    
                    this.element.addEventListener('keydown', (e) => {
                        if (e.key === 'Tab') {
                            if (e.shiftKey) {
                                if (document.activeElement === firstElement) {
                                    lastElement.focus();
                                    e.preventDefault();
                                }
                            } else {
                                if (document.activeElement === lastElement) {
                                    firstElement.focus();
                                    e.preventDefault();
                                }
                            }
                        }
                    });
                }
            };
            
            // Gestion des événements
            if (config.closable) {
                const closeButtons = modal.querySelectorAll('[data-modal-close]');
                closeButtons.forEach(btn => {
                    btn.addEventListener('click', () => instance.close());
                });
            }
            
            if (config.backdrop) {
                modal.addEventListener('click', (e) => {
                    if (e.target === modal) {
                        instance.close();
                    }
                });
            }
            
            if (config.keyboard) {
                document.addEventListener('keydown', (e) => {
                    if (e.key === 'Escape' && instance.isOpen) {
                        instance.close();
                    }
                });
            }
            
            window.LandingPageComponents.Modal.instances.set(id, instance);
            return instance;
        },
        
        get: (id) => {
            return window.LandingPageComponents.Modal.instances.get(id);
        }
    },

    // Composant Accordion
    Accordion: {
        init: (selector) => {
            const accordions = document.querySelectorAll(selector);
            
            accordions.forEach(accordion => {
                const items = accordion.querySelectorAll('.accordion-item');
                
                items.forEach(item => {
                    const header = item.querySelector('.accordion-header');
                    const content = item.querySelector('.accordion-content');
                    
                    if (!header || !content) return;
                    
                    header.addEventListener('click', () => {
                        const isActive = item.classList.contains('active');
                        
                        // Fermer tous les autres items si accordion simple
                        if (!accordion.hasAttribute('data-multiple')) {
                            items.forEach(otherItem => {
                                if (otherItem !== item) {
                                    otherItem.classList.remove('active');
                                    const otherContent = otherItem.querySelector('.accordion-content');
                                    if (otherContent) {
                                        otherContent.style.maxHeight = '0';
                                    }
                                }
                            });
                        }
                        
                        // Toggle l'item actuel
                        if (isActive) {
                            item.classList.remove('active');
                            content.style.maxHeight = '0';
                        } else {
                            item.classList.add('active');
                            content.style.maxHeight = content.scrollHeight + 'px';
                        }
                    });
                    
                    // Initialiser la hauteur
                    if (item.classList.contains('active')) {
                        content.style.maxHeight = content.scrollHeight + 'px';
                    } else {
                        content.style.maxHeight = '0';
                    }
                });
            });
        }
    },

    // Composant Tabs
    Tabs: {
        init: (selector) => {
            const tabContainers = document.querySelectorAll(selector);
            
            tabContainers.forEach(container => {
                const tabs = container.querySelectorAll('.tab-button');
                const panels = container.querySelectorAll('.tab-panel');
                
                tabs.forEach((tab, index) => {
                    tab.addEventListener('click', () => {
                        // Désactiver tous les tabs
                        tabs.forEach(t => t.classList.remove('active'));
                        panels.forEach(p => p.classList.remove('active'));
                        
                        // Activer le tab cliqué
                        tab.classList.add('active');
                        if (panels[index]) {
                            panels[index].classList.add('active');
                        }
                        
                        // Événement personnalisé
                        container.dispatchEvent(new CustomEvent('tab:change', {
                            detail: { index, tab, panel: panels[index] }
                        }));
                    });
                    
                    // Support clavier
                    tab.addEventListener('keydown', (e) => {
                        if (e.key === 'Enter' || e.key === ' ') {
                            e.preventDefault();
                            tab.click();
                        }
                    });
                });
            });
        }
    },

    // Composant Carousel/Slider
    Carousel: {
        instances: new Map(),
        
        create: (selector, options = {}) => {
            const element = document.querySelector(selector);
            if (!element) return null;
            
            const defaults = {
                autoplay: false,
                autoplayDelay: 3000,
                loop: true,
                navigation: true,
                pagination: true,
                slidesPerView: 1,
                spaceBetween: 20,
                breakpoints: {}
            };
            
            const config = { ...defaults, ...options };
            const slides = element.querySelectorAll('.carousel-slide');
            let currentIndex = 0;
            let autoplayTimer = null;
            
            const instance = {
                element,
                config,
                currentIndex,
                slides,
                
                goTo: function(index) {
                    if (index < 0 || index >= this.slides.length) {
                        if (!this.config.loop) return;
                        index = index < 0 ? this.slides.length - 1 : 0;
                    }
                    
                    this.currentIndex = index;
                    this.updateSlides();
                    this.updatePagination();
                    
                    // Événement personnalisé
                    this.element.dispatchEvent(new CustomEvent('carousel:change', {
                        detail: { index: this.currentIndex }
                    }));
                },
                
                next: function() {
                    this.goTo(this.currentIndex + 1);
                },
                
                prev: function() {
                    this.goTo(this.currentIndex - 1);
                },
                
                updateSlides: function() {
                    const translateX = -this.currentIndex * 100;
                    const slidesContainer = this.element.querySelector('.carousel-slides');
                    if (slidesContainer) {
                        slidesContainer.style.transform = `translateX(${translateX}%)`;
                    }
                },
                
                updatePagination: function() {
                    const dots = this.element.querySelectorAll('.carousel-dot');
                    dots.forEach((dot, index) => {
                        dot.classList.toggle('active', index === this.currentIndex);
                    });
                },
                
                startAutoplay: function() {
                    if (!this.config.autoplay) return;
                    
                    this.stopAutoplay();
                    autoplayTimer = setInterval(() => {
                        this.next();
                    }, this.config.autoplayDelay);
                },
                
                stopAutoplay: function() {
                    if (autoplayTimer) {
                        clearInterval(autoplayTimer);
                        autoplayTimer = null;
                    }
                },
                
                destroy: function() {
                    this.stopAutoplay();
                    window.LandingPageComponents.Carousel.instances.delete(selector);
                }
            };
            
            // Initialisation des contrôles
            if (config.navigation) {
                const prevBtn = element.querySelector('.carousel-prev');
                const nextBtn = element.querySelector('.carousel-next');
                
                if (prevBtn) prevBtn.addEventListener('click', () => instance.prev());
                if (nextBtn) nextBtn.addEventListener('click', () => instance.next());
            }
            
            if (config.pagination) {
                const dots = element.querySelectorAll('.carousel-dot');
                dots.forEach((dot, index) => {
                    dot.addEventListener('click', () => instance.goTo(index));
                });
            }
            
            // Support tactile
            window.LandingPageComponents.utils.addTouchSupport(element, (direction) => {
                if (direction === 'swipeLeft') instance.next();
                if (direction === 'swipeRight') instance.prev();
            });
            
            // Support clavier
            element.addEventListener('keydown', (e) => {
                if (e.key === 'ArrowLeft') instance.prev();
                if (e.key === 'ArrowRight') instance.next();
            });
            
            // Pause autoplay au survol
            element.addEventListener('mouseenter', () => instance.stopAutoplay());
            element.addEventListener('mouseleave', () => instance.startAutoplay());
            
            // Initialisation
            instance.updateSlides();
            instance.updatePagination();
            instance.startAutoplay();
            
            window.LandingPageComponents.Carousel.instances.set(selector, instance);
            return instance;
        },
        
        get: (selector) => {
            return window.LandingPageComponents.Carousel.instances.get(selector);
        }
    },

    // Composant Dropdown
    Dropdown: {
        init: (selector) => {
            const dropdowns = document.querySelectorAll(selector);
            
            dropdowns.forEach(dropdown => {
                const trigger = dropdown.querySelector('.dropdown-trigger');
                const menu = dropdown.querySelector('.dropdown-menu');
                
                if (!trigger || !menu) return;
                
                let isOpen = false;
                
                const open = () => {
                    if (isOpen) return;
                    isOpen = true;
                    dropdown.classList.add('active');
                    menu.classList.add('show');
                    
                    // Focus sur le premier élément
                    const firstItem = menu.querySelector('a, button');
                    if (firstItem) firstItem.focus();
                };
                
                const close = () => {
                    if (!isOpen) return;
                    isOpen = false;
                    dropdown.classList.remove('active');
                    menu.classList.remove('show');
                    trigger.focus();
                };
                
                const toggle = () => {
                    isOpen ? close() : open();
                };
                
                // Événements
                trigger.addEventListener('click', (e) => {
                    e.preventDefault();
                    toggle();
                });
                
                // Fermer en cliquant ailleurs
                document.addEventListener('click', (e) => {
                    if (!dropdown.contains(e.target)) {
                        close();
                    }
                });
                
                // Support clavier
                trigger.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        toggle();
                    }
                    if (e.key === 'Escape') {
                        close();
                    }
                });
                
                menu.addEventListener('keydown', (e) => {
                    if (e.key === 'Escape') {
                        close();
                    }
                });
            });
        }
    },

    // Composant Toast/Notification
    Toast: {
        container: null,
        
        init: () => {
            if (!window.LandingPageComponents.Toast.container) {
                const container = document.createElement('div');
                container.className = 'toast-container';
                container.setAttribute('aria-live', 'polite');
                document.body.appendChild(container);
                window.LandingPageComponents.Toast.container = container;
            }
        },
        
        show: (message, type = 'info', duration = 5000) => {
            window.LandingPageComponents.Toast.init();
            
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            toast.innerHTML = `
                <div class="toast-content">
                    <span class="toast-message">${message}</span>
                    <button class="toast-close" aria-label="Fermer">&times;</button>
                </div>
            `;
            
            const closeBtn = toast.querySelector('.toast-close');
            closeBtn.addEventListener('click', () => {
                window.LandingPageComponents.Toast.hide(toast);
            });
            
            window.LandingPageComponents.Toast.container.appendChild(toast);
            
            // Animation d'entrée
            requestAnimationFrame(() => {
                toast.classList.add('show');
            });
            
            // Auto-hide
            if (duration > 0) {
                setTimeout(() => {
                    window.LandingPageComponents.Toast.hide(toast);
                }, duration);
            }
            
            return toast;
        },
        
        hide: (toast) => {
            toast.classList.remove('show');
            toast.classList.add('hide');
            
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        },
        
        success: (message, duration) => {
            return window.LandingPageComponents.Toast.show(message, 'success', duration);
        },
        
        error: (message, duration) => {
            return window.LandingPageComponents.Toast.show(message, 'error', duration);
        },
        
        warning: (message, duration) => {
            return window.LandingPageComponents.Toast.show(message, 'warning', duration);
        },
        
        info: (message, duration) => {
            return window.LandingPageComponents.Toast.show(message, 'info', duration);
        }
    },

    // Composant Loading
    Loading: {
        show: (target = document.body, message = 'Chargement...') => {
            const existing = target.querySelector('.loading-overlay');
            if (existing) return;
            
            const overlay = document.createElement('div');
            overlay.className = 'loading-overlay';
            overlay.innerHTML = `
                <div class="loading-spinner">
                    <div class="spinner"></div>
                    <div class="loading-message">${message}</div>
                </div>
            `;
            
            target.style.position = 'relative';
            target.appendChild(overlay);
            
            requestAnimationFrame(() => {
                overlay.classList.add('show');
            });
        },
        
        hide: (target = document.body) => {
            const overlay = target.querySelector('.loading-overlay');
            if (!overlay) return;
            
            overlay.classList.remove('show');
            setTimeout(() => {
                if (overlay.parentNode) {
                    overlay.parentNode.removeChild(overlay);
                }
            }, 300);
        }
    },

    // Initialisation automatique
    init: () => {
        // Initialiser les composants automatiquement
        window.LandingPageComponents.Accordion.init('.accordion');
        window.LandingPageComponents.Tabs.init('.tabs');
        window.LandingPageComponents.Dropdown.init('.dropdown');
        
        // Initialiser les carousels
        document.querySelectorAll('.carousel').forEach(carousel => {
            const id = carousel.id || Math.random().toString(36).substr(2, 9);
            carousel.id = id;
            window.LandingPageComponents.Carousel.create(`#${id}`);
        });
        
        console.log('🎯 Composants Landing Page initialisés');
    }
};

// Auto-initialisation quand le DOM est prêt
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', window.LandingPageComponents.init);
} else {
    window.LandingPageComponents.init();
}

// Templates de composants
window.LandingPageComponents.componentTemplates = {
    hero: {
        type: 'hero',
        content: `
            <section class="hero bg-primary text-white py-5">
                <div class="container">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h1>Titre Principal</h1>
                            <p class="lead">Description accrocheuse de votre produit ou service</p>
                            <button class="btn btn-light btn-lg">Commencer</button>
                        </div>
                        <div class="col-md-6 text-center">
                            <img src="placeholder.jpg" alt="Hero Image" class="img-fluid">
                        </div>
                    </div>
                </div>
            </section>
        `
    },
    features: {
        type: 'features',
        content: `
            <section class="features py-5">
                <div class="container">
                    <h2 class="text-center mb-5">Fonctionnalités</h2>
                    <div class="row">
                        <div class="col-md-4 mb-4">
                            <div class="text-center">
                                <i class="fas fa-rocket fa-3x mb-3 text-primary"></i>
                                <h3>Fonctionnalité 1</h3>
                                <p>Description de la fonctionnalité</p>
                            </div>
                        </div>
                        <div class="col-md-4 mb-4">
                            <div class="text-center">
                                <i class="fas fa-cog fa-3x mb-3 text-primary"></i>
                                <h3>Fonctionnalité 2</h3>
                                <p>Description de la fonctionnalité</p>
                            </div>
                        </div>
                        <div class="col-md-4 mb-4">
                            <div class="text-center">
                                <i class="fas fa-star fa-3x mb-3 text-primary"></i>
                                <h3>Fonctionnalité 3</h3>
                                <p>Description de la fonctionnalité</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        `
    },
    pricing: {
        type: 'pricing',
        content: `
            <section class="pricing py-5">
                <div class="container">
                    <h2 class="text-center mb-5">Tarifs</h2>
                    <div class="row">
                        <div class="col-md-4 mb-4">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <h3>Basic</h3>
                                    <h4 class="card-price">9€<span>/mois</span></h4>
                                    <ul class="list-unstyled">
                                        <li>Fonctionnalité 1</li>
                                        <li>Fonctionnalité 2</li>
                                        <li>Support email</li>
                                    </ul>
                                    <button class="btn btn-primary">Choisir</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        `
    },
    testimonials: {
        type: 'testimonials',
        content: `
            <section class="testimonials py-5 bg-light">
                <div class="container">
                    <h2 class="text-center mb-5">Témoignages</h2>
                    <div class="row">
                        <div class="col-md-4 mb-4">
                            <div class="card h-100">
                                <div class="card-body">
                                    <p class="card-text">"Excellent service, je recommande !"</p>
                                    <div class="d-flex align-items-center">
                                        <img src="avatar1.jpg" alt="Avatar" class="rounded-circle me-3" width="50">
                                        <div>
                                            <h5 class="mb-0">John Doe</h5>
                                            <small class="text-muted">CEO, Company</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        `
    },
    form: {
        type: 'form',
        content: `
            <section class="contact-form py-5">
                <div class="container">
                    <h2 class="text-center mb-5">Contactez-nous</h2>
                    <div class="row justify-content-center">
                        <div class="col-md-6">
                            <form>
                                <div class="mb-3">
                                    <input type="text" class="form-control" placeholder="Nom">
                                </div>
                                <div class="mb-3">
                                    <input type="email" class="form-control" placeholder="Email">
                                </div>
                                <div class="mb-3">
                                    <textarea class="form-control" rows="4" placeholder="Message"></textarea>
                                </div>
                                <button type="submit" class="btn btn-primary w-100">Envoyer</button>
                            </form>
                        </div>
                    </div>
                </div>
            </section>
        `
    },
    text: {
        type: 'text',
        content: `
            <section class="text-section py-5">
                <div class="container">
                    <div class="row justify-content-center">
                        <div class="col-md-8">
                            <h2>Titre de la section</h2>
                            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
                        </div>
                    </div>
                </div>
            </section>
        `
    }
};

// Export pour utilisation en module
if (typeof module !== 'undefined' && module.exports) {
    module.exports = window.LandingPageComponents;
}