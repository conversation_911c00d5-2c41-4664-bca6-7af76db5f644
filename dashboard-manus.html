<!DOCTYPE html>
<html lang="fr" dir="ltr">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Dashboard Firebase - صفحات هبوط للجميع</title>

    <!-- Bootstrap CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <!-- Font Awesome -->
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
      rel="stylesheet"
    />
    <!-- Styles -->
    <style>
      /* Styles de base */
      body {
        font-family: 'Arial', sans-serif;
      }
    </style>

    <script>
      // Définition des templates de composants
      const componentTemplates = {
        hero: {
          type: 'hero',
          content: `
            <section class="hero bg-primary text-white py-5">
              <div class="container">
                <div class="row align-items-center">
                  <div class="col-md-6">
                    <h1>Titre Principal</h1>
                    <p class="lead">Description accrocheuse de votre produit ou service</p>
                    <button class="btn btn-light btn-lg">Commencer</button>
                  </div>
                  <div class="col-md-6 text-center">
                    <img src="placeholder.jpg" alt="Hero Image" class="img-fluid">
                  </div>
                </div>
              </div>
            </section>
          `
        },
        features: {
          type: 'features',
          content: `
            <section class="features py-5">
              <div class="container">
                <h2 class="text-center mb-5">Fonctionnalités</h2>
                <div class="row">
                  <div class="col-md-4 mb-4">
                    <div class="text-center">
                      <i class="fas fa-rocket fa-3x mb-3 text-primary"></i>
                      <h3>Fonctionnalité 1</h3>
                      <p>Description de la fonctionnalité</p>
                    </div>
                  </div>
                  <div class="col-md-4 mb-4">
                    <div class="text-center">
                      <i class="fas fa-cog fa-3x mb-3 text-primary"></i>
                      <h3>Fonctionnalité 2</h3>
                      <p>Description de la fonctionnalité</p>
                    </div>
                  </div>
                  <div class="col-md-4 mb-4">
                    <div class="text-center">
                      <i class="fas fa-star fa-3x mb-3 text-primary"></i>
                      <h3>Fonctionnalité 3</h3>
                      <p>Description de la fonctionnalité</p>
                    </div>
                  </div>
                </div>
              </div>
            </section>
          `
        },
        pricing: {
          type: 'pricing',
          content: `
            <section class="pricing py-5">
              <div class="container">
                <h2 class="text-center mb-5">Tarifs</h2>
                <div class="row">
                  <div class="col-md-4 mb-4">
                    <div class="card h-100">
                      <div class="card-body text-center">
                        <h3>Basic</h3>
                        <h4 class="card-price">9€<span>/mois</span></h4>
                        <ul class="list-unstyled">
                          <li>Fonctionnalité 1</li>
                          <li>Fonctionnalité 2</li>
                          <li>Support email</li>
                        </ul>
                        <button class="btn btn-primary">Choisir</button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </section>
          `
        },
        testimonials: {
          type: 'testimonials',
          content: `
            <section class="testimonials py-5 bg-light">
              <div class="container">
                <h2 class="text-center mb-5">Témoignages</h2>
                <div class="row">
                  <div class="col-md-4 mb-4">
                    <div class="card h-100">
                      <div class="card-body">
                        <p class="card-text">"Excellent service, je recommande !"</p>
                        <div class="d-flex align-items-center">
                          <img src="avatar1.jpg" alt="Avatar" class="rounded-circle me-3" width="50">
                          <div>
                            <h5 class="mb-0">John Doe</h5>
                            <small class="text-muted">CEO, Company</small>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </section>
          `
        },
        form: {
          type: 'form',
          content: `
            <section class="contact-form py-5">
              <div class="container">
                <h2 class="text-center mb-5">Contactez-nous</h2>
                <div class="row justify-content-center">
                  <div class="col-md-6">
                    <form>
                      <div class="mb-3">
                        <input type="text" class="form-control" placeholder="Nom">
                      </div>
                      <div class="mb-3">
                        <input type="email" class="form-control" placeholder="Email">
                      </div>
                      <div class="mb-3">
                        <textarea class="form-control" rows="4" placeholder="Message"></textarea>
                      </div>
                      <button type="submit" class="btn btn-primary w-100">Envoyer</button>
                    </form>
                  </div>
                </div>
              </div>
            </section>
          `
        },
        text: {
          type: 'text',
          content: `
            <section class="text-section py-5">
              <div class="container">
                <div class="row justify-content-center">
                  <div class="col-md-8">
                    <h2>Titre de la section</h2>
                    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
                  </div>
                </div>
              </div>
            </section>
          `
        }
      };
    </script>

    <style>
      .sidebar {
        min-height: 100vh;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        position: fixed;
        top: 0;
        left: 0;
        width: 250px;
        z-index: 1000;
        overflow-y: auto;
        transition: transform 0.3s ease;
      }

      @media (max-width: 768px) {
        .sidebar {
          transform: translateX(-100%);
        }

        .sidebar.show {
          transform: translateX(0);
        }

        .main-content {
          margin-left: 0 !important;
        }
      }

      @media (min-width: 769px) {
        .main-content {
          margin-left: 250px;
        }
      }

      .sidebar .nav-link {
        color: rgba(255, 255, 255, 0.8);
        border-radius: 8px;
        margin: 2px 0;
        transition: all 0.3s ease;
      }

      .sidebar .nav-link:hover,
      .sidebar .nav-link.active {
        background: rgba(255, 255, 255, 0.2);
        color: white;
      }

      .card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease;
      }

      .card:hover {
        transform: translateY(-5px);
      }

      .stat-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
      }

      .stat-card.success {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      }

      .stat-card.warning {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      }

      .stat-card.info {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      }

      .main-content {
        background: #f8f9fa;
        min-height: 100vh;
        padding: 20px;
      }

      .mobile-menu-btn {
        display: none;
        position: fixed;
        top: 20px;
        left: 20px;
        z-index: 1001;
        background: #667eea;
        color: white;
        border: none;
        border-radius: 5px;
        padding: 10px;
        font-size: 18px;
      }

      @media (max-width: 768px) {
        .mobile-menu-btn {
          display: block;
        }
      }

      .user-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: #667eea;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
      }

      .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
      }

      .loading-overlay.show {
        opacity: 1;
        visibility: visible;
      }

      .auth-required {
        text-align: center;
        padding: 3rem;
      }

      .firebase-user-info {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 1rem;
      }

      .firebase-user-info img {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        margin-bottom: 0.5rem;
      }

      .status-badge {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 11px;
        font-weight: 600;
      }

      .status-pending {
        background: #fff3cd;
        color: #856404;
      }

      .status-confirmed {
        background: #d1edff;
        color: #0c5460;
      }

      .status-rejected {
        background: #f8d7da;
        color: #721c24;
      }
    </style>
  </head>
  <body>
    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay show">
      <div class="text-center">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">Chargement...</span>
        </div>
        <p class="mt-3">Initialisation Firebase...</p>
      </div>
    </div>

    <!-- Auth Required Screen -->
    <div id="authRequired" class="auth-required" style="display: none">
      <div class="container">
        <div class="row justify-content-center">
          <div class="col-md-6">
            <div class="card">
              <div class="card-body text-center p-5">
                <i class="fas fa-lock fa-3x text-muted mb-3"></i>
                <h3>Authentification Requise</h3>
                <p class="text-muted mb-4">
                  Vous devez être connecté avec Firebase pour accéder au
                  dashboard.
                </p>
                <div class="d-grid gap-2">
                  <a href="login.html" class="btn btn-primary">
                    <i class="fas fa-sign-in-alt me-2"></i>Se connecter
                  </a>
                  <a href="register.html" class="btn btn-outline-secondary">
                    <i class="fas fa-user-plus me-2"></i>Créer un compte
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Dashboard -->
    <div id="mainDashboard" style="display: none">
      <!-- Mobile Menu Button -->
      <button class="mobile-menu-btn" id="mobileMenuBtn">
        <i class="fas fa-bars"></i>
      </button>

      <div class="container-fluid">
        <div class="row">
          <!-- Sidebar -->
          <div class="col-md-3 col-lg-2 sidebar p-3" id="sidebar">
            <div class="text-center mb-4">
              <h4><i class="fas fa-chart-line"></i> Dashboard</h4>
              <small>صفحات هبوط للجميع</small>
            </div>

            <!-- User Info -->
            <div id="firebaseUserInfo" class="firebase-user-info text-center">
              <div id="userAvatar" class="user-avatar mx-auto mb-2">
                <i class="fas fa-user"></i>
              </div>
              <div id="userName" class="fw-bold">Utilisateur</div>
              <div id="userEmail" class="small opacity-75">
                <EMAIL>
              </div>
              <div id="userRole" class="badge bg-light text-dark mt-1">
                Admin
              </div>
            </div>

            <nav class="nav flex-column">
              <a
                class="nav-link active"
                href="#overview"
                data-section="overview"
              >
                <i class="fas fa-tachometer-alt me-2"></i> Vue d'ensemble
              </a>
              <a class="nav-link" href="#stores" data-section="stores">
                <i class="fas fa-store me-2"></i> Gestion des stores
              </a>
              <a class="nav-link" href="#products" data-section="products">
                <i class="fas fa-box me-2"></i> Gestion des produits
              </a>
              <a class="nav-link" href="#landing-pages" data-section="landing-pages">
                <i class="fas fa-file-alt me-2"></i> Landing Pages
              </a>
              <a class="nav-link" href="#roles" data-section="roles">
                <i class="fas fa-user-shield me-2"></i> Gestion des rôles
              </a>
              <a
                class="nav-link"
                href="#subscriptions"
                data-section="subscriptions"
              >
                <i class="fas fa-crown me-2"></i> Gestion des abonnements
              </a>
              <a class="nav-link" href="#orders" data-section="orders">
                <i class="fas fa-shopping-cart me-2"></i> Commandes (Tous
                vendeurs)
              </a>
              <a class="nav-link" href="#payments" data-section="payments">
                <i class="fas fa-credit-card me-2"></i> Paiements & Gateways
              </a>
              <a class="nav-link" href="#analytics" data-section="analytics">
                <i class="fas fa-chart-bar me-2"></i> Analytics
              </a>
              <a class="nav-link" href="#ai-usage" data-section="ai-usage">
                <i class="fas fa-robot me-2"></i> Usage IA & API Keys
              </a>
              <a class="nav-link" href="#settings" data-section="settings">
                <i class="fas fa-cog me-2"></i> Settings (SMTP...)
              </a>
              <a
                class="nav-link"
                href="#firebase-users"
                data-section="firebase-users"
              >
                <i class="fas fa-users me-2"></i> Utilisateurs
              </a>
            </nav>

            <div class="mt-auto pt-4">
              <button
                id="logoutBtn"
                class="nav-link text-danger w-100 border-0 bg-transparent"
              >
                <i class="fas fa-sign-out-alt me-2"></i> Déconnexion
              </button>
            </div>
          </div>

          <!-- Main Content -->
          <div class="main-content">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
              <h2>Dashboard Firebase</h2>
              <div class="d-flex align-items-center">
                <span class="text-muted">Connecté via Firebase</span>
              </div>
            </div>

            <!-- Overview Section -->
            <div id="overview-section" class="section">
              <!-- Stats Cards -->
              <div class="row mb-4">
                <div class="col-md-3 mb-3">
                  <div class="card stat-card">
                    <div class="card-body text-center">
                      <i class="fas fa-store fa-2x mb-2"></i>
                      <h3 id="totalStores">-</h3>
                      <p class="mb-0">Stores Actifs</p>
                    </div>
                  </div>
                </div>
                <div class="col-md-3 mb-3">
                  <div class="card stat-card success">
                    <div class="card-body text-center">
                      <i class="fas fa-box fa-2x mb-2"></i>
                      <h3 id="totalProducts">-</h3>
                      <p class="mb-0">Produits Totaux</p>
                    </div>
                  </div>
                </div>
                <div class="col-md-3 mb-3">
                  <div class="card stat-card warning">
                    <div class="card-body text-center">
                      <i class="fas fa-crown fa-2x mb-2"></i>
                      <h3 id="totalSubscriptions">-</h3>
                      <p class="mb-0">Abonnements</p>
                    </div>
                  </div>
                </div>
                <div class="col-md-3 mb-3">
                  <div class="card stat-card info">
                    <div class="card-body text-center">
                      <i class="fas fa-users fa-2x mb-2"></i>
                      <h3 id="totalUsers">-</h3>
                      <p class="mb-0">Utilisateurs</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- System Overview -->
              <div class="card mb-4">
                <div class="card-header">
                  <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i> Vue d'ensemble du
                    Système
                  </h5>
                </div>
                <div class="card-body">
                  <div class="row">
                    <div class="col-md-6">
                      <h6>État de la Base de Données</h6>
                      <ul class="list-group">
                        <li
                          class="list-group-item d-flex justify-content-between align-items-center"
                        >
                          Users
                          <span class="badge bg-primary rounded-pill"
                            >3 enregistrements</span
                          >
                          <span class="status-badge bg-success text-white"
                            >ACTIVE</span
                          >
                        </li>
                        <li
                          class="list-group-item d-flex justify-content-between align-items-center"
                        >
                          Orders
                          <span class="badge bg-primary rounded-pill"
                            >0 enregistrements</span
                          >
                          <span class="status-badge bg-warning text-dark"
                            >EMPTY</span
                          >
                        </li>
                        <li
                          class="list-group-item d-flex justify-content-between align-items-center"
                        >
                          Products
                          <span class="badge bg-primary rounded-pill"
                            >0 enregistrements</span
                          >
                          <span class="status-badge bg-warning text-dark"
                            >EMPTY</span
                          >
                        </li>
                        <li
                          class="list-group-item d-flex justify-content-between align-items-center"
                        >
                          Stores
                          <span class="badge bg-primary rounded-pill"
                            >1 enregistrements</span
                          >
                          <span class="status-badge bg-success text-white"
                            >ACTIVE</span
                          >
                        </li>
                        <li
                          class="list-group-item d-flex justify-content-between align-items-center"
                        >
                          AI Usage
                          <span class="badge bg-primary rounded-pill"
                            >0 enregistrements</span
                          >
                          <span class="status-badge bg-warning text-dark"
                            >EMPTY</span
                          >
                        </li>
                      </ul>
                      <div class="alert alert-info mt-3">
                        <i class="fas fa-check-circle me-2"></i> Base de
                        données configurée avec succès ! Toutes les tables
                        nécessaires sont présentes et contiennent des données
                        de test.
                      </div>
                    </div>
                    <div class="col-md-6">
                      <h6>Statistiques Rapides</h6>
                      <ul class="list-group">
                        <li
                          class="list-group-item d-flex justify-content-between align-items-center"
                        >
                          Stores
                          <span class="badge bg-success rounded-pill">2</span>
                        </li>
                        <li
                          class="list-group-item d-flex justify-content-between align-items-center"
                        >
                          Products
                          <span class="badge bg-info rounded-pill">0</span>
                        </li>
                        <li
                          class="list-group-item d-flex justify-content-between align-items-center"
                        >
                          Users
                          <span class="badge bg-warning rounded-pill">2</span>
                        </li>
                        <li
                          class="list-group-item d-flex justify-content-between align-items-center"
                        >
                          Subscriptions
                          <span class="badge bg-danger rounded-pill">0</span>
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Landing Pages Section -->
            <div id="landing-pages-section" class="section" style="display: none;">
                <h2>Landing Pages</h2>
                <button class="btn btn-primary mb-3" onclick="openLandingPageBuilder()">Nouvelle Landing Page</button>
                <div class="mb-3">
                    <input type="text" class="form-control" id="landingPageSearch" placeholder="Rechercher des landing pages...">
                </div>
                <div class="row mb-3">
                    <div class="col-md-4">
                        <select class="form-select" id="statusFilter">
                            <option value="all">Tous les statuts</option>
                            <option value="published">Publié</option>
                            <option value="draft">Brouillon</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <select class="form-select" id="languageFilter">
                            <option value="all">Toutes les langues</option>
                            <option value="fr">Français</option>
                            <option value="en">Anglais</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <button class="btn btn-secondary w-100" onclick="filterLandingPages()">Filtrer</button>
                    </div>
                </div>
                <div id="landingPagesContainer" class="row">
                    <!-- Landing pages will be loaded here -->
                </div>
            </div>

            <!-- Landing Page Builder Modal -->
            <div class="modal fade" id="landingPageBuilderModal" tabindex="-1" aria-labelledby="landingPageBuilderModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="landingPageBuilderModalLabel">Landing Page Builder</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-9">
                                    <!-- Canvas -->
                                    <div id="pageBuilder" class="border p-3 mb-3" style="min-height: 500px;">
                                        <p class="text-muted text-center">Glissez des blocs ici pour commencer</p>
                                        <p class="text-muted text-center">Ou utilisez un template pour démarrer rapidement</p>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <button class="btn btn-success" onclick="saveLandingPage()">Sauvegarder</button>
                                        <button class="btn btn-secondary" onclick="previewLandingPage()">Prévisualiser</button>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <!-- Sidebar for components and properties -->
                                    <div class="accordion" id="builderSidebar">
                                        <div class="accordion-item">
                                            <h2 class="accordion-header" id="componentsHeading">
                                                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseComponents" aria-expanded="true" aria-controls="collapseComponents">
                                                    Composants
                                                </button>
                                            </h2>
                                            <div id="collapseComponents" class="accordion-collapse collapse show" aria-labelledby="componentsHeading" data-bs-parent="#builderSidebar">
                                                <div class="accordion-body">
                                                    <div id="componentsPanel">
                                                        <!-- Components will be loaded here by JS -->
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="accordion-item">
                                            <h2 class="accordion-header" id="propertiesHeading">
                                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseProperties" aria-expanded="false" aria-controls="collapseProperties">
                                                    Propriétés
                                                </button>
                                            </h2>
                                            <div id="collapseProperties" class="accordion-collapse collapse" aria-labelledby="propertiesHeading" data-bs-parent="#builderSidebar">
                                                <div class="accordion-body">
                                                    <div id="propertiesPanel">
                                                        <p class="text-muted text-center">Sélectionnez un élément pour modifier ses propriétés</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="accordion-item">
                                            <h2 class="accordion-header" id="settingsHeading">
                                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseSettings" aria-expanded="false" aria-controls="collapseSettings">
                                                    Paramètres de la Page
                                                </button>
                                            </h2>
                                            <div id="collapseSettings" class="accordion-collapse collapse" aria-labelledby="settingsHeading" data-bs-parent="#builderSidebar">
                                                <div class="accordion-body">
                                                    <form id="pageSettingsForm">
                                                        <div class="mb-3">
                                                            <label for="pageTitle" class="form-label">Titre de la Page</label>
                                                            <input type="text" class="form-control" id="pageTitle" value="Ma Landing Page">
                                                        </div>
                                                        <div class="mb-3">
                                                            <label for="pageLanguage" class="form-label">Langue</label>
                                                            <select class="form-select" id="pageLanguage">
                                                                <option value="fr">Français</option>
                                                                <option value="en">Anglais</option>
                                                            </select>
                                                        </div>
                                                        <div class="mb-3">
                                                            <label for="primaryColor" class="form-label">Couleur Primaire</label>
                                                            <input type="color" class="form-control form-control-color" id="primaryColor" value="#007bff">
                                                        </div>
                                                        <div class="mb-3">
                                                            <label for="fontFamily" class="form-label">Police</label>
                                                            <input type="text" class="form-control" id="fontFamily" value="Arial, sans-serif">
                                                        </div>
                                                        <div class="form-check mb-3">
                                                            <input class="form-check-input" type="checkbox" id="enableRTL">
                                                            <label class="form-check-label" for="enableRTL">Activer le mode RTL</label>
                                                        </div>
                                                        <div class="form-check mb-3">
                                                            <input class="form-check-input" type="checkbox" id="enableAnalytics" checked>
                                                            <label class="form-check-label" for="enableAnalytics">Activer les analyses</label>
                                                        </div>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Other Sections (hidden by default) -->
            <div id="stores-section" class="section" style="display: none;">
                <h2>Gestion des Stores</h2>
                <p>Contenu pour la gestion des stores.</p>
            </div>
            <div id="products-section" class="section" style="display: none;">
                <h2>Gestion des Produits</h2>
                <p>Contenu pour la gestion des produits.</p>
            </div>
            <div id="roles-section" class="section" style="display: none;">
                <h2>Gestion des Rôles</h2>
                <p>Contenu pour la gestion des rôles.</p>
            </div>
            <div id="subscriptions-section" class="section" style="display: none;">
                <h2>Gestion des Abonnements</h2>
                <p>Contenu pour la gestion des abonnements.</p>
            </div>
            <div id="orders-section" class="section" style="display: none;">
                <h2>Commandes (Tous vendeurs)</h2>
                <p>Contenu pour les commandes.</p>
            </div>
            <div id="payments-section" class="section" style="display: none;">
                <h2>Paiements & Gateways</h2>
                <p>Contenu pour les paiements.</p>
            </div>
            <div id="analytics-section" class="section" style="display: none;">
                <h2>Analytics</h2>
                <p>Contenu pour les analyses.</p>
            </div>
            <div id="ai-usage-section" class="section" style="display: none;">
                <h2>Usage IA & API Keys</h2>
                <p>Contenu pour l'utilisation de l'IA et les clés API.</p>
            </div>
            <div id="settings-section" class="section" style="display: none;">
                <h2>Settings (SMTP...)</h2>
                <p>Contenu pour les paramètres.</p>
            </div>
            <div id="firebase-users-section" class="section" style="display: none;">
                <h2>Utilisateurs Firebase</h2>
                <p>Contenu pour les utilisateurs Firebase.</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Bootstrap JS Bundle -->
    <script
      src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"
    ></script>
    <!-- Firebase App (c'est un exemple, vous devrez configurer votre propre Firebase) -->
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-auth.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-firestore.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-analytics.js"></script>

    <!-- Votre script principal -->
    <script>
      // Votre configuration Firebase (remplacez par vos propres clés)
      const firebaseConfig = {
        apiKey: "YOUR_API_KEY",
        authDomain: "YOUR_AUTH_DOMAIN",
        projectId: "YOUR_PROJECT_ID",
        storageBucket: "YOUR_STORAGE_BUCKET",
        messagingSenderId: "YOUR_MESSAGING_SENDER_ID",
        appId: "YOUR_APP_ID",
        measurementId: "YOUR_MEASUREMENT_ID",
      };

      // Initialiser Firebase
      firebase.initializeApp(firebaseConfig);
      const auth = firebase.auth();
      const db = firebase.firestore();
      firebase.analytics();

      // Éléments du DOM
      const loadingOverlay = document.getElementById("loadingOverlay");
      const authRequired = document.getElementById("authRequired");
      const mainDashboard = document.getElementById("mainDashboard");
      const logoutBtn = document.getElementById("logoutBtn");
      const firebaseUserInfo = document.getElementById("firebaseUserInfo");
      const userAvatar = document.getElementById("userAvatar");
      const userName = document.getElementById("userName");
      const userEmail = document.getElementById("userEmail");
      const userRole = document.getElementById("userRole");

      // Gérer l'état d'authentification
      auth.onAuthStateChanged(async (user) => {
        if (user) {
          // Utilisateur connecté
          loadingOverlay.classList.remove("show");
          authRequired.style.display = "none";
          mainDashboard.style.display = "flex"; // Utiliser flex pour le layout

          // Mettre à jour les informations utilisateur
          userName.textContent = user.displayName || "Utilisateur";
          userEmail.textContent = user.email || "";
          if (user.photoURL) {
            userAvatar.innerHTML = `<img src="${user.photoURL}" alt="Avatar">`;
          } else {
            userAvatar.innerHTML = `<i class="fas fa-user"></i>`;
          }

          // Récupérer le rôle de l'utilisateur depuis Firestore (exemple)
          const userDoc = await db.collection("users").doc(user.uid).get();
          if (userDoc.exists) {
            const userData = userDoc.data();
            userRole.textContent = userData.role || "Utilisateur";
          } else {
            userRole.textContent = "Utilisateur";
          }

          // Charger les données du dashboard
          loadDashboardData();
        } else {
          // Utilisateur déconnecté
          loadingOverlay.classList.remove("show");
          authRequired.style.display = "block";
          mainDashboard.style.display = "none";
        }
      });

      // Déconnexion
      logoutBtn.addEventListener("click", async () => {
        try {
          await auth.signOut();
          showNotification("Déconnexion réussie !", "success");
        } catch (error) {
          console.error("Erreur de déconnexion:", error);
          showNotification("Erreur de déconnexion.", "danger");
        }
      });

      // Gestion du menu mobile
      const mobileMenuBtn = document.getElementById('mobileMenuBtn');
      const sidebar = document.getElementById('sidebar');

      if (mobileMenuBtn && sidebar) {
        mobileMenuBtn.addEventListener('click', () => {
          sidebar.classList.toggle('show');
        });

        // Fermer le menu en cliquant en dehors
        document.addEventListener('click', (e) => {
          if (!sidebar.contains(e.target) && !mobileMenuBtn.contains(e.target)) {
            sidebar.classList.remove('show');
          }
        });
      }

      // Navigation entre les sections
      document.querySelectorAll(".nav-link").forEach((link) => {
        link.addEventListener("click", function (e) {
          e.preventDefault();
          document.querySelectorAll(".section").forEach((section) => {
            section.style.display = "none";
          });
          const targetSectionId = this.dataset.section + "-section";
          document.getElementById(targetSectionId).style.display = "block";

          // Mettre à jour la classe active du nav-link
          document.querySelectorAll(".nav-link").forEach((nav) => {
            nav.classList.remove("active");
          });
          this.classList.add("active");
        });
      });

      // Fonction pour afficher les notifications
      function showNotification(message, type) {
        // Implémentation simple, vous pouvez utiliser une bibliothèque comme Toastr ou Bootstrap Toasts
        alert(`${type.toUpperCase()}: ${message}`);
      }

      // Charger les données du dashboard (exemple avec des données fictives)
      async function loadDashboardData() {
        try {
          // Simuler le chargement des données depuis Firestore
          const totalStores = 2; // (await db.collection('stores').get()).size;
          const totalProducts = 0; // (await db.collection('products').get()).size;
          const totalSubscriptions = 0; // (await db.collection('subscriptions').get()).size;
          const totalUsers = 2; // (await db.collection('users').get()).size;

          document.getElementById("totalStores").textContent = totalStores;
          document.getElementById("totalProducts").textContent = totalProducts;
          document.getElementById("totalSubscriptions").textContent = totalSubscriptions;
          document.getElementById("totalUsers").textContent = totalUsers;
        } catch (error) {
          console.error("Erreur lors du chargement des données du dashboard:", error);
          showNotification("Erreur lors du chargement des données du dashboard.", "danger");
        }
      }

      // Fonctions du Landing Page Builder (à implémenter ou lier à un fichier externe)
      let pageBuilderData = { elements: [], settings: {title: "", language: "fr", primaryColor: "#007bff", fontFamily: "Inter", enableRTL: false, enableAnalytics: true } };
      let currentSelectedElement = null;

      // Fonction pour ouvrir le constructeur
      function openLandingPageBuilder(pageId = null) {
        // Réinitialiser le constructeur
        document.getElementById('pageBuilder').innerHTML = `
          <p class="text-muted text-center">Glissez des blocs ici pour commencer</p>
          <p class="text-muted text-center">Ou utilisez un template pour démarrer rapidement</p>
        `;
        document.getElementById('propertiesPanel').innerHTML = `
          <p class="text-muted text-center">Sélectionnez un élément pour modifier ses propriétés</p>
        `;

        // Charger les données de la page si un ID est fourni
        if (pageId) {
          // Simuler le chargement des données de la page
          const page = { id: pageId, title: `Ma Page ${pageId}`, elements: [] }; // Données fictives
          pageBuilderData = { elements: page.elements, settings: { title: page.title, language: "fr", primaryColor: "#007bff", fontFamily: "Inter", enableRTL: false, enableAnalytics: true } };
          // Reconstruire le canvas avec les éléments de la page
          page.elements.forEach(element => addElementToCanvas(element.type, element.content));
          document.getElementById('pageTitle').value = page.title;
        } else {
          // Réinitialiser les paramètres pour une nouvelle page
          pageBuilderData = { elements: [], settings: {title: "Ma Landing Page", language: "fr", primaryColor: "#007bff", fontFamily: "Inter", enableRTL: false, enableAnalytics: true } };
          document.getElementById('pageTitle').value = "Ma Landing Page";
          document.getElementById('pageLanguage').value = "fr";
          document.getElementById('primaryColor').value = "#007bff";
          document.getElementById('fontFamily').value = "Arial, sans-serif";
          document.getElementById('enableRTL').checked = false;
          document.getElementById('enableAnalytics').checked = true;
        }

        // Ouvrir le modal
        const modal = new bootstrap.Modal(document.getElementById('landingPageBuilderModal'));
        modal.show();

        // Initialiser le constructeur après l'ouverture du modal
        setTimeout(() => { initLandingPageBuilder(); }, 500);
      }

      // Fonction pour sauvegarder la landing page
      function saveLandingPage() {
        const title = document.getElementById('pageTitle').value || "Ma Landing Page";
        pageBuilderData.settings.title = title;
        pageBuilderData.settings.language = document.getElementById('pageLanguage').value;
        pageBuilderData.settings.primaryColor = document.getElementById('primaryColor').value;
        pageBuilderData.settings.fontFamily= document.getElementById('fontFamily').value;
        pageBuilderData.settings.enableRTL = document.getElementById('enableRTL').checked;
        pageBuilderData.settings.enableAnalytics = document.getElementById('enableAnalytics').checked;

        // Simulation de sauvegarde
        showNotification('Landing page sauvegardée avec succès !', 'success');

        // Fermer le modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('landingPageBuilderModal'));
        modal.hide();

        // Actualiser la liste des landing pages
        loadLandingPages();
      }

      // Fonction pour prévisualiser la landing page
      function previewLandingPage() {
        const previewHTML = `
          <!DOCTYPE html>
          <html lang="fr">
          <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>${pageBuilderData.settings.title}</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
            <style>
              body { font-family: ${pageBuilderData.settings.fontFamily}; }
              .bg-primary { background-color: ${pageBuilderData.settings.primaryColor} !important; }
              .text-primary { color: ${pageBuilderData.settings.primaryColor} !important; }
            </style>
          </head>
          <body dir="${pageBuilderData.settings.enableRTL ? 'rtl' : 'ltr'}">
            ${pageBuilderData.elements.map(el => el.html).join('')}
          </body>
          </html>
        `;
        const previewWindow = window.open('', '_blank');
        previewWindow.document.write(previewHTML);
        previewWindow.document.close();
      }

      // Fonctions d'édition et de manipulation des éléments
      function editElementContent(elementId) {
        const elementData = pageBuilderData.elements.find(el => el.id === elementId);
        if (!elementData) return;
        const newContent = prompt('Modifier le contenu:', elementData.html);
        if (newContent && newContent !== elementData.html) {
          elementData.html = newContent;
          const element = document.querySelector(`[data-element-id="${elementId}"] div:last-child`);
          if (element) {
            element.innerHTML = newContent;
          }
        }
      }

      function updateElementSpacing(elementId, direction, value) {
        const element = document.querySelector(`[data-element-id="${elementId}"] div:last-child > div`);
        if (element) {
          if (direction === 'top') {
            element.style.paddingTop = value + 'px';
          } else if (direction === 'bottom') {
            element.style.paddingBottom = value + 'px';
          }
        }
      }

      function updateElementBackground(elementId, color) {
        const element = document.querySelector(`[data-element-id="${elementId}"] div:last-child > div`);
        if (element) {
          element.style.backgroundColor = color;
        }
      }

      function toggleElementCenter(elementId, centered) {
        const element = document.querySelector(`[data-element-id="${elementId}"] div:last-child > div`);
        if (element) {
          if (centered) {
            element.classList.add('text-center');
          } else {
            element.classList.remove('text-center');
          }
        }
      }

      function moveElementUp(elementId) {
        const element = document.querySelector(`[data-element-id="${elementId}"]`);
        const previousElement = element?.previousElementSibling;
        if (element && previousElement && !previousElement.classList.contains('text-center')) { // Added check for text-center class
          element.parentNode.insertBefore(element, previousElement);
          // Mettre à jour l'ordre dans les données
          const elementIndex = pageBuilderData.elements.findIndex(el => el.id === elementId);
          if (elementIndex > 0) {
            const temp = pageBuilderData.elements[elementIndex];
            pageBuilderData.elements[elementIndex] = pageBuilderData.elements[elementIndex - 1];
            pageBuilderData.elements[elementIndex - 1] = temp;
          }
        }
      }

      function moveElementDown(elementId) {
        const element = document.querySelector(`[data-element-id="${elementId}"]`);
        const nextElement = element?.nextElementSibling;
        if (element && nextElement) {
          element.parentNode.insertBefore(nextElement, element);
          // Mettre à jour l'ordre dans les données
          const elementIndex = pageBuilderData.elements.findIndex(el => el.id === elementId);
          if (elementIndex < pageBuilderData.elements.length - 1) {
            const temp = pageBuilderData.elements[elementIndex];
            pageBuilderData.elements[elementIndex] = pageBuilderData.elements[elementIndex + 1];
            pageBuilderData.elements[elementIndex + 1] = temp;
          }
        }
      }

      // Fonction pour charger les templates rapides
      function loadQuickTemplate(templateType) {
        if (!componentTemplates) {
          console.error('componentTemplates non défini');
          return;
        }
        if (!componentTemplates[templateType]) {
          console.error('Type de template non valide:', templateType);
          return;
        }

        // Réinitialiser le canvas
        const canvas = document.getElementById('pageBuilder');
        if (!canvas) {
          console.error('Canvas non trouvé');
          return;
        }
        canvas.innerHTML = '';

        // Ajouter les éléments séquentiellement
        componentTemplates[templateType].forEach((type, index) => {
          if (componentTemplates[type]) {
            setTimeout(() => {
              try {
                addElementToCanvas(componentTemplates[type], type);
              } catch (error) {
                console.error(`Erreur lors de l'ajout du composant ${type}:`, error);
              }
            }, index * 100); // Délai pour un effet d'animation
          }
        });
        showNotification(`Template "${templateType}" chargé avec succès !`, 'success');
      }

      // Fonction pour ajouter un élément au canvas
      function addElementToCanvas(component, type) {
        const canvas = document.getElementById('pageBuilder');
        if (!canvas) return;

        const elementId = `element-${Date.now()}`;
        const newElement = {
          id: elementId,
          type: type,
          html: component.content,
          data: {}
        };
        pageBuilderData.elements.push(newElement);

        const elementDiv = document.createElement('div');
        elementDiv.setAttribute('data-element-id', elementId);
        elementDiv.classList.add('page-element', 'mb-3', 'p-3', 'border', 'rounded');
        elementDiv.innerHTML = `
          <div class="d-flex justify-content-between align-items-center mb-2">
            <small class="text-muted">${type.toUpperCase()}</small>
            <div>
              <button class="btn btn-sm btn-outline-secondary me-1" onclick="editElementContent('${elementId}')"><i class="fas fa-edit"></i></button>
              <button class="btn btn-sm btn-outline-secondary me-1" onclick="moveElementUp('${elementId}')"><i class="fas fa-arrow-up"></i></button>
              <button class="btn btn-sm btn-outline-secondary me-1" onclick="moveElementDown('${elementId}')"><i class="fas fa-arrow-down"></i></button>
              <button class="btn btn-sm btn-outline-danger" onclick="removeElement('${elementId}')"><i class="fas fa-trash"></i></button>
            </div>
          </div>
          <div class="element-content">${component.content}</div>
        `;
        canvas.appendChild(elementDiv);
      }

      // Fonction pour supprimer un élément du canvas
      function removeElement(elementId) {
        if (confirm('Êtes-vous sûr de vouloir supprimer cet élément ?')) {
          pageBuilderData.elements = pageBuilderData.elements.filter(el => el.id !== elementId);
          document.querySelector(`[data-element-id="${elementId}"]`).remove();
          showNotification('Élément supprimé !', 'success');
        }
      }

      // Fonction pour charger la liste des landing pages
      function loadLandingPages() {
        const container = document.getElementById('landingPagesContainer');
        // Simulation de données
        const landingPages = [
          { id: 1, title: 'Landing Page Produit A', status: 'published', language: 'fr', views: 1250, conversions: 89, lastModified: '2024-01-15' },
          { id: 2, title: 'Page SaaS Service', status: 'draft', language: 'en', views: 0, conversions: 0, lastModified: '2024-01-14' },
          { id: 3, title: 'Événement Marketing', status: 'published', language: 'fr', views: 890, conversions: 45, lastModified: '2024-01-13' }
        ];

        container.innerHTML = landingPages.map(page => `
          <div class="col-md-4 mb-4">
            <div class="card h-100">
              <div class="card-body">
                <h5 class="card-title">${page.title}</h5>
                <p class="card-text">
                  Statut: <span class="badge bg-${page.status === 'published' ? 'success' : 'secondary'}">${page.status}</span><br>
                  Langue: <span class="badge bg-info">${page.language.toUpperCase()}</span><br>
                  Vues: ${page.views}<br>
                  Conversions: ${page.conversions}<br>
                  Dernière modification: ${page.lastModified}
                </p>
                <div class="d-flex justify-content-between">
                  <button class="btn btn-sm btn-primary" onclick="openLandingPageBuilder(${page.id})">Modifier</button>
                  <button class="btn btn-sm btn-info" onclick="previewLandingPageById(${page.id})">Prévisualiser</button>
                  <button class="btn btn-sm btn-warning" onclick="duplicateLandingPage(${page.id})">Dupliquer</button>
                  <button class="btn btn-sm btn-danger" onclick="deleteLandingPage(${page.id})">Supprimer</button>
                </div>
              </div>
            </div>
          </div>
        `).join('');
      }

      // Fonction pour prévisualiser une landing page par ID
      function previewLandingPageById(pageId) {
        // Simulation d'ouverture d'aperçu
        window.open(`/preview-landing-page.php?id=${pageId}`, '_blank');
      }

      // Fonction pour dupliquer une landing page
      function duplicateLandingPage(pageId) {
        if (confirm('Dupliquer cette landing page ?')) {
          showNotification('Landing page dupliquée avec succès !', 'success');
          loadLandingPages();
        }
      }

      // Fonction pour supprimer une landing page
      function deleteLandingPage(pageId) {
        if (confirm('Êtes-vous sûr de vouloir supprimer cette landing page ?')) {
          showNotification('Landing page supprimée avec succès !', 'success');
          loadLandingPages();
        }
      }

      // Fonction de recherche et filtrage
      function filterLandingPages() {
        const searchTerm = document.getElementById('landingPageSearch').value.toLowerCase();
        const statusFilter = document.getElementById('statusFilter').value;
        const languageFilter = document.getElementById('languageFilter').value;

        // Ici on implementerait la logique de filtrage
        console.log('Filtrage:', {searchTerm, statusFilter, languageFilter});
      }

      // Initialiser quand le DOM est prêt
      document.addEventListener('DOMContentLoaded', function() {
        // Ajouter l'événement au bouton "Nouvelle Landing Page"
        const newLandingPageBtn = document.querySelector('[onclick="openLandingPageBuilder()"]');
        if (newLandingPageBtn) {
          newLandingPageBtn.addEventListener('click', () => openLandingPageBuilder());
        }

        // Charger les landing pages au démarrage
        if (document.getElementById('landingPagesContainer')) {
          loadLandingPages();
        }
      });

      // Initialisation du Landing Page Builder (à appeler après l'ouverture du modal)
      function initLandingPageBuilder() {
        // Charger les composants disponibles dans le panneau latéral
        const componentsPanel = document.getElementById('componentsPanel');
        if (componentsPanel) {
          componentsPanel.innerHTML = Object.keys(componentTemplates).map(key => `
            <button class="btn btn-outline-primary btn-sm d-block w-100 mb-2" onclick="addElementToCanvas(componentTemplates['${key}'], '${key}')">
              Ajouter ${key.charAt(0).toUpperCase() + key.slice(1)}
            </button>
          `).join('');
        }
      }
    </script>
    <script src="js/components-manus.js"></script>
  </body>
</html>
