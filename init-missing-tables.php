<?php
require_once 'php/config/database.php';

header('Content-Type: text/html; charset=UTF-8');

echo "<h1>🚀 Initialisation des tables manquantes</h1>";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .success{color:green;} .error{color:red;} .warning{color:orange;}</style>";

try {
    // 1. Créer la table roles
    echo "<h2>👥 Création de la table roles</h2>";
    
    $sql = "
    CREATE TABLE IF NOT EXISTS roles (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(50) NOT NULL UNIQUE,
        display_name VARCHAR(100) NOT NULL,
        description TEXT,
        permissions JSON NOT NULL,
        is_system TINYINT(1) DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_name (name)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $pdo->exec($sql);
    echo "<p class='success'>✅ Table roles créée</p>";
    
    // 2. Créer la table user_roles
    echo "<h2>🔗 Création de la table user_roles</h2>";
    
    $sql = "
    CREATE TABLE IF NOT EXISTS user_roles (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id VARCHAR(255) NOT NULL,
        role_id INT NOT NULL,
        assigned_by VARCHAR(255),
        assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_user_id (user_id),
        INDEX idx_role_id (role_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $pdo->exec($sql);
    echo "<p class='success'>✅ Table user_roles créée</p>";
    
    // 3. Insérer les rôles par défaut
    echo "<h2>📋 Insertion des rôles par défaut</h2>";
    
    $default_roles = [
        [
            'name' => 'admin',
            'display_name' => 'Administrateur',
            'description' => 'Accès complet à toutes les fonctionnalités du système',
            'permissions' => json_encode([
                'users' => ['create', 'read', 'update', 'delete'],
                'stores' => ['create', 'read', 'update', 'delete'],
                'products' => ['create', 'read', 'update', 'delete'],
                'orders' => ['create', 'read', 'update', 'delete'],
                'analytics' => ['read'],
                'settings' => ['read', 'update'],
                'roles' => ['create', 'read', 'update', 'delete']
            ]),
            'is_system' => 1
        ],
        [
            'name' => 'merchant',
            'display_name' => 'Marchand',
            'description' => 'Peut gérer ses propres magasins et produits',
            'permissions' => json_encode([
                'stores' => ['create', 'read', 'update'],
                'products' => ['create', 'read', 'update', 'delete'],
                'orders' => ['read', 'update'],
                'analytics' => ['read']
            ]),
            'is_system' => 1
        ],
        [
            'name' => 'customer',
            'display_name' => 'Client',
            'description' => 'Peut passer des commandes et consulter ses achats',
            'permissions' => json_encode([
                'products' => ['read'],
                'orders' => ['create', 'read']
            ]),
            'is_system' => 1
        ]
    ];
    
    $stmt = $pdo->prepare("
        INSERT IGNORE INTO roles (name, display_name, description, permissions, is_system) 
        VALUES (?, ?, ?, ?, ?)
    ");
    
    foreach ($default_roles as $role) {
        $stmt->execute([
            $role['name'],
            $role['display_name'],
            $role['description'],
            $role['permissions'],
            $role['is_system']
        ]);
    }
    
    echo "<p class='success'>✅ Rôles par défaut insérés</p>";
    
    // 4. Assigner des rôles aux utilisateurs de test
    echo "<h2>👤 Attribution de rôles aux utilisateurs de test</h2>";
    
    // D'abord, obtenir les IDs des rôles
    $stmt = $pdo->prepare("SELECT id FROM roles WHERE name = ?");
    
    $stmt->execute(['admin']);
    $admin_role_id = $stmt->fetchColumn();
    
    $stmt->execute(['merchant']);
    $merchant_role_id = $stmt->fetchColumn();
    
    if ($admin_role_id && $merchant_role_id) {
        $user_roles = [
            ['firebase_uid_1', $admin_role_id],
            ['firebase_uid_2', $merchant_role_id]
        ];
        
        $stmt = $pdo->prepare("
            INSERT IGNORE INTO user_roles (user_id, role_id, assigned_by) 
            VALUES (?, ?, 'system')
        ");
        
        foreach ($user_roles as $user_role) {
            $stmt->execute($user_role);
        }
        
        echo "<p class='success'>✅ Rôles attribués aux utilisateurs de test</p>";
    } else {
        echo "<p class='error'>❌ Erreur lors de la récupération des IDs des rôles</p>";
    }
    
    // 5. Vérifier les résultats
    echo "<h2>✅ Vérification des résultats</h2>";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM roles");
    $roles_count = $stmt->fetchColumn();
    echo "<p>Nombre de rôles créés: " . $roles_count . "</p>";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM user_roles");
    $user_roles_count = $stmt->fetchColumn();
    echo "<p>Nombre d'attributions de rôles: " . $user_roles_count . "</p>";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM reviews");
    $reviews_count = $stmt->fetchColumn();
    echo "<p>Nombre d'avis: " . $reviews_count . "</p>";
    
    echo "<p class='success'>🎉 Initialisation terminée avec succès !</p>";
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Erreur: " . $e->getMessage() . "</p>";
}
?>
