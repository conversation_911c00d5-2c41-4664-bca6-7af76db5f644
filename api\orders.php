<?php

/**
 * API Endpoint pour la gestion des commandes
 * Orders management API for seller dashboard
 */

require_once 'config/database.php';

try {
    // Initialiser la base de données et l'authentification
    $database = new Database();
    $db = $database->getConnection();
    $auth = new Auth($db);

    // Vérifier l'authentification
    $headers = function_exists('getallheaders') ? getallheaders() : [];

    // Fallback for CLI and some servers
    if (empty($headers) && isset($_SERVER['HTTP_AUTHORIZATION'])) {
        $headers['Authorization'] = $_SERVER['HTTP_AUTHORIZATION'];
    }

    $token = isset($headers['Authorization']) ? str_replace('Bearer ', '', $headers['Authorization']) : null;
    $user_id = $auth->verifyFirebaseToken($token);
    $store = $auth->getUserStore($user_id);
    $auth->checkPermission($store['role'], 'orders');

    $method = $_SERVER['REQUEST_METHOD'];
    $request_uri = $_SERVER['REQUEST_URI'];
    $path_parts = explode('/', trim(parse_url($request_uri, PHP_URL_PATH), '/'));

    switch ($method) {
        case 'GET':
            if (isset($path_parts[2]) && is_numeric($path_parts[2])) {
                // GET /api/orders/{id} - Obtenir une commande spécifique
                getOrder($db, $store['id'], $path_parts[2]);
            } elseif (isset($path_parts[2]) && $path_parts[2] === 'stats') {
                // GET /api/orders/stats - Obtenir les statistiques des commandes
                getOrderStats($db, $store['id']);
            } else {
                // GET /api/orders - Obtenir toutes les commandes
                getOrders($db, $store['id']);
            }
            break;

        case 'POST':
            // POST /api/orders - Créer une nouvelle commande
            createOrder($db, $store['id']);
            break;

        case 'PUT':
            if (isset($path_parts[2]) && is_numeric($path_parts[2])) {
                if (isset($path_parts[3]) && $path_parts[3] === 'status') {
                    // PUT /api/orders/{id}/status - Mettre à jour le statut
                    updateOrderStatus($db, $store['id'], $path_parts[2]);
                } else {
                    // PUT /api/orders/{id} - Mettre à jour une commande
                    updateOrder($db, $store['id'], $path_parts[2]);
                }
            } else {
                ApiResponse::error('ID de la commande requis pour la mise à jour', 400);
            }
            break;

        case 'DELETE':
            if (isset($path_parts[2]) && is_numeric($path_parts[2])) {
                // DELETE /api/orders/{id} - Supprimer une commande
                deleteOrder($db, $store['id'], $path_parts[2]);
            } else {
                ApiResponse::error('ID de la commande requis pour la suppression', 400);
            }
            break;

        default:
            ApiResponse::error('Méthode non autorisée', 405);
    }
} catch (Exception $e) {
    error_log("Orders API Error: " . $e->getMessage());
    ApiResponse::error('Erreur interne du serveur', 500);
}

/**
 * Obtenir toutes les commandes du magasin
 */
function getOrders($db, $store_id)
{
    try {
        $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
        $limit = isset($_GET['limit']) ? min(100, max(1, intval($_GET['limit']))) : 20;
        $offset = ($page - 1) * $limit;

        $search = isset($_GET['search']) ? trim($_GET['search']) : '';
        $status = isset($_GET['status']) ? trim($_GET['status']) : '';
        $payment_status = isset($_GET['payment_status']) ? trim($_GET['payment_status']) : '';
        $date_from = isset($_GET['date_from']) ? trim($_GET['date_from']) : '';
        $date_to = isset($_GET['date_to']) ? trim($_GET['date_to']) : '';
        $sort = isset($_GET['sort']) ? trim($_GET['sort']) : 'created_at';
        $order = isset($_GET['order']) && strtolower($_GET['order']) === 'asc' ? 'ASC' : 'DESC';

        // Construire la requête avec filtres
        $where_conditions = ['o.store_id = ?'];
        $params = [$store_id];

        if (!empty($search)) {
            $where_conditions[] = '(o.order_number LIKE ? OR o.customer_name LIKE ? OR o.customer_email LIKE ? OR o.customer_phone LIKE ?)';
            $search_param = '%' . $search . '%';
            $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param]);
        }

        if (!empty($status)) {
            $where_conditions[] = 'o.status = ?';
            $params[] = $status;
        }

        if (!empty($payment_status)) {
            $where_conditions[] = 'o.payment_status = ?';
            $params[] = $payment_status;
        }

        if (!empty($date_from)) {
            $where_conditions[] = 'DATE(o.created_at) >= ?';
            $params[] = $date_from;
        }

        if (!empty($date_to)) {
            $where_conditions[] = 'DATE(o.created_at) <= ?';
            $params[] = $date_to;
        }

        $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);

        // Requête pour compter le total
        $count_sql = "SELECT COUNT(*) as total FROM orders o $where_clause";
        $count_stmt = $db->prepare($count_sql);
        $count_stmt->execute($params);
        $total = $count_stmt->fetch()['total'];

        // Requête principale avec pagination
        $sql = "
            SELECT
                o.*,
                COUNT(oi.id) as item_count,
                GROUP_CONCAT(CONCAT(p.name_ar, ' (', oi.quantity, ')') SEPARATOR ', ') as products_summary
            FROM orders o
            LEFT JOIN order_items oi ON o.id = oi.order_id
            LEFT JOIN products p ON oi.product_id = p.id
            $where_clause
            GROUP BY o.id
            ORDER BY o.$sort $order
            LIMIT $limit OFFSET $offset
        ";

        $stmt = $db->prepare($sql);
        $stmt->execute($params);
        $orders = $stmt->fetchAll();

        // Formater les données
        foreach ($orders as &$order) {
            $order['shipping_address'] = json_decode($order['shipping_address'], true) ?: [];
            $order['billing_address'] = json_decode($order['billing_address'], true) ?: [];
            $order['metadata'] = json_decode($order['metadata'], true) ?: [];
            $order['item_count'] = intval($order['item_count']);
            $order['total_amount'] = floatval($order['total_amount']);
            $order['shipping_cost'] = floatval($order['shipping_cost']);
            $order['tax_amount'] = floatval($order['tax_amount']);
            $order['discount_amount'] = floatval($order['discount_amount']);
        }

        ApiResponse::success([
            'orders' => $orders,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $limit,
                'total' => intval($total),
                'total_pages' => ceil($total / $limit)
            ]
        ]);
    } catch (Exception $e) {
        error_log("Error getting orders: " . $e->getMessage());
        ApiResponse::error('Erreur lors de la récupération des commandes', 500);
    }
}

/**
 * Obtenir une commande spécifique avec ses détails
 */
function getOrder($db, $store_id, $order_id)
{
    try {
        $sql = "
            SELECT o.*
            FROM orders o
            WHERE o.id = ? AND o.store_id = ?
        ";

        $stmt = $db->prepare($sql);
        $stmt->execute([$order_id, $store_id]);
        $order = $stmt->fetch();

        if (!$order) {
            ApiResponse::error('Commande non trouvée', 404);
        }

        // Obtenir les articles de la commande
        $items_sql = "
            SELECT
                oi.*,
                p.name_ar, p.name_fr, p.name_en,
                p.sku, p.images
            FROM order_items oi
            LEFT JOIN products p ON oi.product_id = p.id
            WHERE oi.order_id = ?
            ORDER BY oi.id
        ";

        $items_stmt = $db->prepare($items_sql);
        $items_stmt->execute([$order_id]);
        $items = $items_stmt->fetchAll();

        // Formater les données
        $order['shipping_address'] = json_decode($order['shipping_address'], true) ?: [];
        $order['billing_address'] = json_decode($order['billing_address'], true) ?: [];
        $order['metadata'] = json_decode($order['metadata'], true) ?: [];
        $order['total_amount'] = floatval($order['total_amount']);
        $order['shipping_cost'] = floatval($order['shipping_cost']);
        $order['tax_amount'] = floatval($order['tax_amount']);
        $order['discount_amount'] = floatval($order['discount_amount']);

        foreach ($items as &$item) {
            $item['images'] = json_decode($item['images'], true) ?: [];
            $item['quantity'] = intval($item['quantity']);
            $item['unit_price'] = floatval($item['unit_price']);
            $item['total_price'] = floatval($item['total_price']);
        }

        $order['items'] = $items;

        // Obtenir l'historique des statuts
        $history_sql = "
            SELECT
                status, payment_status, notes, created_at
            FROM order_status_history
            WHERE order_id = ?
            ORDER BY created_at DESC
        ";

        $history_stmt = $db->prepare($history_sql);
        $history_stmt->execute([$order_id]);
        $order['status_history'] = $history_stmt->fetchAll();

        ApiResponse::success($order);
    } catch (Exception $e) {
        error_log("Error getting order: " . $e->getMessage());
        ApiResponse::error('Erreur lors de la récupération de la commande', 500);
    }
}

/**
 * Obtenir les statistiques des commandes
 */
function getOrderStats($db, $store_id)
{
    try {
        $period = isset($_GET['period']) ? $_GET['period'] : '30';
        $date_from = date('Y-m-d', strtotime("-{$period} days"));

        // Statistiques générales
        $stats_sql = "
            SELECT
                COUNT(*) as total_orders,
                COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_orders,
                COUNT(CASE WHEN status = 'processing' THEN 1 END) as processing_orders,
                COUNT(CASE WHEN status = 'shipped' THEN 1 END) as shipped_orders,
                COUNT(CASE WHEN status = 'delivered' THEN 1 END) as delivered_orders,
                COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_orders,
                COALESCE(SUM(total_amount), 0) as total_revenue,
                COALESCE(AVG(total_amount), 0) as average_order_value,
                COUNT(CASE WHEN payment_status = 'paid' THEN 1 END) as paid_orders,
                COUNT(CASE WHEN payment_status = 'pending' THEN 1 END) as pending_payments
            FROM orders
            WHERE store_id = ? AND created_at >= ?
        ";

        $stats_stmt = $db->prepare($stats_sql);
        $stats_stmt->execute([$store_id, $date_from]);
        $stats = $stats_stmt->fetch();

        // Ventes par jour
        $daily_sql = "
            SELECT
                DATE(created_at) as date,
                COUNT(*) as orders_count,
                COALESCE(SUM(total_amount), 0) as revenue
            FROM orders
            WHERE store_id = ? AND created_at >= ?
            GROUP BY DATE(created_at)
            ORDER BY date ASC
        ";

        $daily_stmt = $db->prepare($daily_sql);
        $daily_stmt->execute([$store_id, $date_from]);
        $daily_stats = $daily_stmt->fetchAll();

        // Top produits vendus
        $top_products_sql = "
            SELECT
                p.name_ar, p.name_fr, p.name_en,
                SUM(oi.quantity) as total_sold,
                SUM(oi.total_price) as total_revenue
            FROM order_items oi
            JOIN orders o ON oi.order_id = o.id
            JOIN products p ON oi.product_id = p.id
            WHERE o.store_id = ? AND o.created_at >= ?
            GROUP BY oi.product_id
            ORDER BY total_sold DESC
            LIMIT 10
        ";

        $top_products_stmt = $db->prepare($top_products_sql);
        $top_products_stmt->execute([$store_id, $date_from]);
        $top_products = $top_products_stmt->fetchAll();

        // Méthodes de paiement
        $payment_methods_sql = "
            SELECT
                payment_method,
                COUNT(*) as count,
                SUM(total_amount) as total_amount
            FROM orders
            WHERE store_id = ? AND created_at >= ?
            GROUP BY payment_method
            ORDER BY count DESC
        ";

        $payment_methods_stmt = $db->prepare($payment_methods_sql);
        $payment_methods_stmt->execute([$store_id, $date_from]);
        $payment_methods = $payment_methods_stmt->fetchAll();

        // Formater les données
        foreach ($stats as $key => $value) {
            if (in_array($key, ['total_revenue', 'average_order_value'])) {
                $stats[$key] = floatval($value);
            } else {
                $stats[$key] = intval($value);
            }
        }

        foreach ($daily_stats as &$day) {
            $day['orders_count'] = intval($day['orders_count']);
            $day['revenue'] = floatval($day['revenue']);
        }

        foreach ($top_products as &$product) {
            $product['total_sold'] = intval($product['total_sold']);
            $product['total_revenue'] = floatval($product['total_revenue']);
        }

        foreach ($payment_methods as &$method) {
            $method['count'] = intval($method['count']);
            $method['total_amount'] = floatval($method['total_amount']);
        }

        ApiResponse::success([
            'general_stats' => $stats,
            'daily_stats' => $daily_stats,
            'top_products' => $top_products,
            'payment_methods' => $payment_methods,
            'period' => $period
        ]);
    } catch (Exception $e) {
        error_log("Error getting order stats: " . $e->getMessage());
        ApiResponse::error('Erreur lors de la récupération des statistiques', 500);
    }
}

/**
 * Créer une nouvelle commande
 */
function createOrder($db, $store_id)
{
    try {
        $input = json_decode(file_get_contents('php://input'), true);

        // Validation des champs requis
        ApiResponse::validateRequired($input, ['customer_name', 'customer_email', 'items']);

        // Nettoyer les données
        $data = ApiResponse::sanitizeInput($input);

        if (empty($data['items']) || !is_array($data['items'])) {
            ApiResponse::error('Articles de commande requis', 400);
        }

        $db->beginTransaction();

        try {
            // Générer un numéro de commande unique
            $order_number = 'ORD-' . date('Ymd') . '-' . strtoupper(substr(uniqid(), -6));

            // Calculer les totaux
            $subtotal = 0;
            $items_data = [];

            foreach ($data['items'] as $item) {
                if (!isset($item['product_id']) || !isset($item['quantity'])) {
                    throw new Exception('Données d\'article invalides');
                }

                // Vérifier le produit
                $product_stmt = $db->prepare("SELECT * FROM products WHERE id = ? AND store_id = ? AND status = 'active'");
                $product_stmt->execute([$item['product_id'], $store_id]);
                $product = $product_stmt->fetch();

                if (!$product) {
                    throw new Exception('Produit non trouvé: ' . $item['product_id']);
                }

                $quantity = intval($item['quantity']);
                $unit_price = floatval($product['price']);
                $total_price = $quantity * $unit_price;

                $items_data[] = [
                    'product_id' => $item['product_id'],
                    'quantity' => $quantity,
                    'unit_price' => $unit_price,
                    'total_price' => $total_price
                ];

                $subtotal += $total_price;
            }

            $shipping_cost = floatval($data['shipping_cost'] ?? 0);
            $tax_amount = floatval($data['tax_amount'] ?? 0);
            $discount_amount = floatval($data['discount_amount'] ?? 0);
            $total_amount = $subtotal + $shipping_cost + $tax_amount - $discount_amount;

            // Insérer la commande
            $order_sql = "
                INSERT INTO orders (
                    store_id, order_number, customer_name, customer_email, customer_phone,
                    shipping_address, billing_address, subtotal_amount, shipping_cost,
                    tax_amount, discount_amount, total_amount, currency, status,
                    payment_method, payment_status, notes, metadata, created_at, updated_at
                ) VALUES (
                    ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW()
                )
            ";

            $order_stmt = $db->prepare($order_sql);
            $order_result = $order_stmt->execute([
                $store_id,
                $order_number,
                $data['customer_name'],
                $data['customer_email'],
                $data['customer_phone'] ?? null,
                isset($data['shipping_address']) ? json_encode($data['shipping_address']) : null,
                isset($data['billing_address']) ? json_encode($data['billing_address']) : null,
                $subtotal,
                $shipping_cost,
                $tax_amount,
                $discount_amount,
                $total_amount,
                $data['currency'] ?? 'DZD',
                $data['status'] ?? 'pending',
                $data['payment_method'] ?? 'cod',
                $data['payment_status'] ?? 'pending',
                $data['notes'] ?? null,
                isset($data['metadata']) ? json_encode($data['metadata']) : null
            ]);

            if (!$order_result) {
                throw new Exception('Erreur lors de la création de la commande');
            }

            $order_id = $db->lastInsertId();

            // Insérer les articles de la commande
            $item_sql = "
                INSERT INTO order_items (order_id, product_id, quantity, unit_price, total_price)
                VALUES (?, ?, ?, ?, ?)
            ";

            $item_stmt = $db->prepare($item_sql);

            foreach ($items_data as $item) {
                $item_result = $item_stmt->execute([
                    $order_id,
                    $item['product_id'],
                    $item['quantity'],
                    $item['unit_price'],
                    $item['total_price']
                ]);

                if (!$item_result) {
                    throw new Exception('Erreur lors de l\'ajout des articles');
                }
            }

            $db->commit();

            ApiResponse::success([
                'id' => $order_id,
                'order_number' => $order_number,
                'total_amount' => $total_amount
            ], 'Commande créée avec succès', 201);
        } catch (Exception $e) {
            $db->rollback();
            throw $e;
        }
    } catch (Exception $e) {
        error_log("Error creating order: " . $e->getMessage());
        ApiResponse::error('Erreur lors de la création de la commande: ' . $e->getMessage(), 500);
    }
}

/**
 * Mettre à jour le statut d'une commande
 */
function updateOrderStatus($db, $store_id, $order_id)
{
    try {
        $input = json_decode(file_get_contents('php://input'), true);

        // Validation des champs requis
        ApiResponse::validateRequired($input, ['status']);

        $data = ApiResponse::sanitizeInput($input);

        // Vérifier que la commande existe et appartient au magasin
        $check_stmt = $db->prepare("SELECT id, status, payment_status FROM orders WHERE id = ? AND store_id = ?");
        $check_stmt->execute([$order_id, $store_id]);
        $order = $check_stmt->fetch();

        if (!$order) {
            ApiResponse::error('Commande non trouvée', 404);
        }

        $allowed_statuses = ['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded'];
        if (!in_array($data['status'], $allowed_statuses)) {
            ApiResponse::error('Statut invalide', 400);
        }

        $db->beginTransaction();

        try {
            // Mettre à jour le statut
            $update_fields = ['status = ?', 'updated_at = NOW()'];
            $params = [$data['status']];

            if (isset($data['payment_status'])) {
                $allowed_payment_statuses = ['pending', 'paid', 'failed', 'refunded'];
                if (in_array($data['payment_status'], $allowed_payment_statuses)) {
                    $update_fields[] = 'payment_status = ?';
                    $params[] = $data['payment_status'];
                }
            }

            if (isset($data['notes'])) {
                $update_fields[] = 'notes = ?';
                $params[] = $data['notes'];
            }

            $params[] = $order_id;
            $params[] = $store_id;

            $sql = "UPDATE orders SET " . implode(', ', $update_fields) . " WHERE id = ? AND store_id = ?";
            $stmt = $db->prepare($sql);
            $result = $stmt->execute($params);

            if (!$result) {
                throw new Exception('Erreur lors de la mise à jour du statut');
            }

            // Enregistrer l'historique
            $history_sql = "
                INSERT INTO order_status_history (order_id, status, payment_status, notes, created_at)
                VALUES (?, ?, ?, ?, NOW())
            ";

            $history_stmt = $db->prepare($history_sql);
            $history_stmt->execute([
                $order_id,
                $data['status'],
                $data['payment_status'] ?? $order['payment_status'],
                $data['notes'] ?? null
            ]);

            $db->commit();

            ApiResponse::success(null, 'Statut de la commande mis à jour avec succès');
        } catch (Exception $e) {
            $db->rollback();
            throw $e;
        }
    } catch (Exception $e) {
        error_log("Error updating order status: " . $e->getMessage());
        ApiResponse::error('Erreur lors de la mise à jour du statut: ' . $e->getMessage(), 500);
    }
}

/**
 * Mettre à jour une commande
 */
function updateOrder($db, $store_id, $order_id)
{
    try {
        $input = json_decode(file_get_contents('php://input'), true);

        // Vérifier que la commande existe et appartient au magasin
        $check_stmt = $db->prepare("SELECT id FROM orders WHERE id = ? AND store_id = ?");
        $check_stmt->execute([$order_id, $store_id]);
        if (!$check_stmt->fetch()) {
            ApiResponse::error('Commande non trouvée', 404);
        }

        // Nettoyer les données
        $data = ApiResponse::sanitizeInput($input);

        // Construire la requête de mise à jour dynamiquement
        $update_fields = [];
        $params = [];

        $allowed_fields = [
            'customer_name',
            'customer_email',
            'customer_phone',
            'shipping_cost',
            'tax_amount',
            'discount_amount',
            'payment_method',
            'notes'
        ];

        foreach ($allowed_fields as $field) {
            if (isset($data[$field])) {
                $update_fields[] = "$field = ?";
                $params[] = $data[$field];
            }
        }

        // Champs JSON
        $json_fields = ['shipping_address', 'billing_address', 'metadata'];
        foreach ($json_fields as $field) {
            if (isset($data[$field])) {
                $update_fields[] = "$field = ?";
                $params[] = json_encode($data[$field]);
            }
        }

        if (empty($update_fields)) {
            ApiResponse::error('Aucune donnée à mettre à jour', 400);
        }

        $update_fields[] = "updated_at = NOW()";
        $params[] = $order_id;
        $params[] = $store_id;

        $sql = "UPDATE orders SET " . implode(', ', $update_fields) . " WHERE id = ? AND store_id = ?";

        $stmt = $db->prepare($sql);
        $result = $stmt->execute($params);

        if ($result) {
            ApiResponse::success(null, 'Commande mise à jour avec succès');
        } else {
            ApiResponse::error('Erreur lors de la mise à jour de la commande', 500);
        }
    } catch (Exception $e) {
        error_log("Error updating order: " . $e->getMessage());
        ApiResponse::error('Erreur lors de la mise à jour de la commande', 500);
    }
}

/**
 * Supprimer une commande
 */
function deleteOrder($db, $store_id, $order_id)
{
    try {
        // Vérifier que la commande existe et appartient au magasin
        $check_stmt = $db->prepare("SELECT id, status FROM orders WHERE id = ? AND store_id = ?");
        $check_stmt->execute([$order_id, $store_id]);
        $order = $check_stmt->fetch();

        if (!$order) {
            ApiResponse::error('Commande non trouvée', 404);
        }

        // Ne permettre la suppression que pour certains statuts
        $deletable_statuses = ['pending', 'cancelled'];
        if (!in_array($order['status'], $deletable_statuses)) {
            ApiResponse::error('Impossible de supprimer une commande avec ce statut', 400);
        }

        $db->beginTransaction();

        try {
            // Supprimer les articles de la commande
            $delete_items_stmt = $db->prepare("DELETE FROM order_items WHERE order_id = ?");
            $delete_items_stmt->execute([$order_id]);

            // Supprimer l'historique des statuts
            $delete_history_stmt = $db->prepare("DELETE FROM order_status_history WHERE order_id = ?");
            $delete_history_stmt->execute([$order_id]);

            // Supprimer la commande
            $delete_order_stmt = $db->prepare("DELETE FROM orders WHERE id = ? AND store_id = ?");
            $result = $delete_order_stmt->execute([$order_id, $store_id]);

            if (!$result) {
                throw new Exception('Erreur lors de la suppression de la commande');
            }

            $db->commit();

            ApiResponse::success(null, 'Commande supprimée avec succès');
        } catch (Exception $e) {
            $db->rollback();
            throw $e;
        }
    } catch (Exception $e) {
        error_log("Error deleting order: " . $e->getMessage());
        ApiResponse::error('Erreur lors de la suppression de la commande: ' . $e->getMessage(), 500);
    }
}
