/**
 * Analytics JavaScript - Gestion des analytics et tracking
 * Support pour Google Analytics, Facebook Pixel, et analytics personnalisés
 */

// Namespace pour les analytics
window.LandingPageAnalytics = {
    // Configuration
    config: {
        debug: false,
        consent: {
            analytics: false,
            marketing: false,
            preferences: false
        },
        providers: {
            googleAnalytics: {
                enabled: false,
                trackingId: null,
                config: {
                    anonymize_ip: true,
                    cookie_expires: 63072000, // 2 ans
                    send_page_view: true
                }
            },
            facebookPixel: {
                enabled: false,
                pixelId: null
            },
            customAnalytics: {
                enabled: true,
                endpoint: '/api/analytics'
            }
        }
    },

    // État interne
    state: {
        initialized: false,
        sessionId: null,
        userId: null,
        pageLoadTime: Date.now(),
        events: [],
        pageViews: 0
    },

    // Utilitaires
    utils: {
        // Générer un ID unique
        generateId: () => {
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
                const r = Math.random() * 16 | 0;
                const v = c === 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            });
        },

        // Obtenir les informations de l'appareil
        getDeviceInfo: () => {
            return {
                userAgent: navigator.userAgent,
                language: navigator.language,
                platform: navigator.platform,
                screenResolution: `${screen.width}x${screen.height}`,
                viewportSize: `${window.innerWidth}x${window.innerHeight}`,
                timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                cookieEnabled: navigator.cookieEnabled,
                onlineStatus: navigator.onLine
            };
        },

        // Obtenir les informations de la page
        getPageInfo: () => {
            return {
                url: window.location.href,
                title: document.title,
                referrer: document.referrer,
                pathname: window.location.pathname,
                search: window.location.search,
                hash: window.location.hash,
                loadTime: Date.now() - window.LandingPageAnalytics.state.pageLoadTime
            };
        },

        // Obtenir les paramètres UTM
        getUtmParams: () => {
            const urlParams = new URLSearchParams(window.location.search);
            return {
                utm_source: urlParams.get('utm_source'),
                utm_medium: urlParams.get('utm_medium'),
                utm_campaign: urlParams.get('utm_campaign'),
                utm_term: urlParams.get('utm_term'),
                utm_content: urlParams.get('utm_content')
            };
        },

        // Vérifier le consentement
        hasConsent: (type) => {
            return window.LandingPageAnalytics.config.consent[type] === true;
        },

        // Logger pour debug
        log: (...args) => {
            if (window.LandingPageAnalytics.config.debug) {
                console.log('[Analytics]', ...args);
            }
        },

        // Stocker dans localStorage avec gestion d'erreur
        setStorage: (key, value) => {
            try {
                localStorage.setItem(`lp_analytics_${key}`, JSON.stringify(value));
                return true;
            } catch (e) {
                window.LandingPageAnalytics.utils.log('Erreur localStorage:', e);
                return false;
            }
        },

        // Récupérer depuis localStorage
        getStorage: (key) => {
            try {
                const item = localStorage.getItem(`lp_analytics_${key}`);
                return item ? JSON.parse(item) : null;
            } catch (e) {
                window.LandingPageAnalytics.utils.log('Erreur localStorage:', e);
                return null;
            }
        },

        // Debounce function
        debounce: (func, wait) => {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },

        // Throttle function
        throttle: (func, limit) => {
            let inThrottle;
            return function() {
                const args = arguments;
                const context = this;
                if (!inThrottle) {
                    func.apply(context, args);
                    inThrottle = true;
                    setTimeout(() => inThrottle = false, limit);
                }
            }
        }
    },

    // Gestion du consentement
    consent: {
        // Mettre à jour le consentement
        update: (consents) => {
            Object.assign(window.LandingPageAnalytics.config.consent, consents);
            window.LandingPageAnalytics.utils.setStorage('consent', window.LandingPageAnalytics.config.consent);
            
            // Réinitialiser les providers selon le nouveau consentement
            window.LandingPageAnalytics.providers.init();
            
            window.LandingPageAnalytics.utils.log('Consentement mis à jour:', consents);
        },

        // Charger le consentement sauvegardé
        load: () => {
            const savedConsent = window.LandingPageAnalytics.utils.getStorage('consent');
            if (savedConsent) {
                Object.assign(window.LandingPageAnalytics.config.consent, savedConsent);
            }
        },

        // Vérifier si le consentement est requis
        isRequired: () => {
            // Vérifier si on est dans l'UE (simple heuristique)
            const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
            const euTimezones = ['Europe/', 'Atlantic/Azores', 'Atlantic/Madeira'];
            return euTimezones.some(tz => timezone.startsWith(tz));
        }
    },

    // Gestion des événements
    events: {
        // Tracker un événement
        track: (eventName, properties = {}) => {
            const event = {
                id: window.LandingPageAnalytics.utils.generateId(),
                name: eventName,
                properties: properties,
                timestamp: Date.now(),
                sessionId: window.LandingPageAnalytics.state.sessionId,
                userId: window.LandingPageAnalytics.state.userId,
                page: window.LandingPageAnalytics.utils.getPageInfo(),
                device: window.LandingPageAnalytics.utils.getDeviceInfo(),
                utm: window.LandingPageAnalytics.utils.getUtmParams()
            };

            // Ajouter à la queue locale
            window.LandingPageAnalytics.state.events.push(event);

            // Envoyer aux providers
            window.LandingPageAnalytics.providers.trackEvent(eventName, properties);

            // Envoyer au serveur si consentement analytics
            if (window.LandingPageAnalytics.utils.hasConsent('analytics')) {
                window.LandingPageAnalytics.api.sendEvent(event);
            }

            window.LandingPageAnalytics.utils.log('Événement tracké:', event);
        },

        // Tracker une page vue
        trackPageView: (customProperties = {}) => {
            window.LandingPageAnalytics.state.pageViews++;
            
            const properties = {
                page_number: window.LandingPageAnalytics.state.pageViews,
                ...customProperties
            };

            window.LandingPageAnalytics.events.track('page_view', properties);
        },

        // Tracker un clic
        trackClick: (element, customProperties = {}) => {
            const properties = {
                element_type: element.tagName.toLowerCase(),
                element_id: element.id || null,
                element_class: element.className || null,
                element_text: element.textContent?.trim().substring(0, 100) || null,
                element_href: element.href || null,
                ...customProperties
            };

            window.LandingPageAnalytics.events.track('click', properties);
        },

        // Tracker un formulaire
        trackForm: (form, action, customProperties = {}) => {
            const properties = {
                form_id: form.id || null,
                form_action: form.action || null,
                form_method: form.method || null,
                action: action, // 'start', 'submit', 'error', 'success'
                ...customProperties
            };

            window.LandingPageAnalytics.events.track('form_interaction', properties);
        },

        // Tracker le scroll
        trackScroll: (percentage) => {
            const properties = {
                scroll_percentage: Math.round(percentage),
                page_height: document.documentElement.scrollHeight,
                viewport_height: window.innerHeight
            };

            window.LandingPageAnalytics.events.track('scroll', properties);
        },

        // Tracker le temps passé
        trackTimeOnPage: () => {
            const timeSpent = Date.now() - window.LandingPageAnalytics.state.pageLoadTime;
            
            const properties = {
                time_spent_ms: timeSpent,
                time_spent_seconds: Math.round(timeSpent / 1000)
            };

            window.LandingPageAnalytics.events.track('time_on_page', properties);
        }
    },

    // Gestion des providers externes
    providers: {
        // Initialiser tous les providers
        init: () => {
            if (window.LandingPageAnalytics.utils.hasConsent('analytics')) {
                window.LandingPageAnalytics.providers.initGoogleAnalytics();
            }
            
            if (window.LandingPageAnalytics.utils.hasConsent('marketing')) {
                window.LandingPageAnalytics.providers.initFacebookPixel();
            }
        },

        // Initialiser Google Analytics
        initGoogleAnalytics: () => {
            const ga = window.LandingPageAnalytics.config.providers.googleAnalytics;
            if (!ga.enabled || !ga.trackingId) return;

            // Charger gtag
            const script = document.createElement('script');
            script.async = true;
            script.src = `https://www.googletagmanager.com/gtag/js?id=${ga.trackingId}`;
            document.head.appendChild(script);

            // Initialiser gtag
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            window.gtag = gtag;
            
            gtag('js', new Date());
            gtag('config', ga.trackingId, ga.config);

            window.LandingPageAnalytics.utils.log('Google Analytics initialisé');
        },

        // Initialiser Facebook Pixel
        initFacebookPixel: () => {
            const fb = window.LandingPageAnalytics.config.providers.facebookPixel;
            if (!fb.enabled || !fb.pixelId) return;

            // Code Facebook Pixel
            !function(f,b,e,v,n,t,s)
            {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
            n.callMethod.apply(n,arguments):n.queue.push(arguments)};
            if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
            n.queue=[];t=b.createElement(e);t.async=!0;
            t.src=v;s=b.getElementsByTagName(e)[0];
            s.parentNode.insertBefore(t,s)}(window, document,'script',
            'https://connect.facebook.net/en_US/fbevents.js');
            
            fbq('init', fb.pixelId);
            fbq('track', 'PageView');

            window.LandingPageAnalytics.utils.log('Facebook Pixel initialisé');
        },

        // Envoyer un événement aux providers
        trackEvent: (eventName, properties) => {
            // Google Analytics
            if (window.gtag && window.LandingPageAnalytics.utils.hasConsent('analytics')) {
                window.gtag('event', eventName, properties);
            }

            // Facebook Pixel
            if (window.fbq && window.LandingPageAnalytics.utils.hasConsent('marketing')) {
                window.fbq('track', eventName, properties);
            }
        }
    },

    // API pour envoyer les données au serveur
    api: {
        // Envoyer un événement
        sendEvent: async (event) => {
            const endpoint = window.LandingPageAnalytics.config.providers.customAnalytics.endpoint;
            
            try {
                const response = await fetch(endpoint, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(event)
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }

                window.LandingPageAnalytics.utils.log('Événement envoyé au serveur:', event.name);
            } catch (error) {
                window.LandingPageAnalytics.utils.log('Erreur envoi événement:', error);
                // Stocker pour retry plus tard
                window.LandingPageAnalytics.api.queueForRetry(event);
            }
        },

        // Queue pour retry
        queueForRetry: (event) => {
            const queue = window.LandingPageAnalytics.utils.getStorage('retry_queue') || [];
            queue.push(event);
            window.LandingPageAnalytics.utils.setStorage('retry_queue', queue);
        },

        // Retry des événements en queue
        retryQueuedEvents: async () => {
            const queue = window.LandingPageAnalytics.utils.getStorage('retry_queue') || [];
            if (queue.length === 0) return;

            const successfulEvents = [];
            
            for (const event of queue) {
                try {
                    await window.LandingPageAnalytics.api.sendEvent(event);
                    successfulEvents.push(event);
                } catch (error) {
                    // Garder en queue
                }
            }

            // Retirer les événements envoyés avec succès
            const remainingQueue = queue.filter(event => !successfulEvents.includes(event));
            window.LandingPageAnalytics.utils.setStorage('retry_queue', remainingQueue);
        }
    },

    // Auto-tracking
    autoTrack: {
        // Initialiser le tracking automatique
        init: () => {
            window.LandingPageAnalytics.autoTrack.setupClickTracking();
            window.LandingPageAnalytics.autoTrack.setupFormTracking();
            window.LandingPageAnalytics.autoTrack.setupScrollTracking();
            window.LandingPageAnalytics.autoTrack.setupTimeTracking();
            window.LandingPageAnalytics.autoTrack.setupVisibilityTracking();
        },

        // Tracking automatique des clics
        setupClickTracking: () => {
            document.addEventListener('click', (e) => {
                const element = e.target;
                
                // Ignorer certains éléments
                if (element.hasAttribute('data-no-track')) return;
                
                // Tracker les liens et boutons
                if (element.tagName === 'A' || element.tagName === 'BUTTON' || element.type === 'submit') {
                    window.LandingPageAnalytics.events.trackClick(element);
                }
            });
        },

        // Tracking automatique des formulaires
        setupFormTracking: () => {
            document.addEventListener('submit', (e) => {
                const form = e.target;
                if (form.tagName === 'FORM' && !form.hasAttribute('data-no-track')) {
                    window.LandingPageAnalytics.events.trackForm(form, 'submit');
                }
            });

            // Focus sur les champs de formulaire
            document.addEventListener('focusin', (e) => {
                const element = e.target;
                if ((element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') && 
                    !element.hasAttribute('data-no-track')) {
                    const form = element.closest('form');
                    if (form) {
                        window.LandingPageAnalytics.events.trackForm(form, 'start');
                    }
                }
            });
        },

        // Tracking automatique du scroll
        setupScrollTracking: () => {
            let lastScrollPercentage = 0;
            const milestones = [25, 50, 75, 90, 100];
            
            const trackScroll = window.LandingPageAnalytics.utils.throttle(() => {
                const scrollTop = window.pageYOffset;
                const docHeight = document.documentElement.scrollHeight - window.innerHeight;
                const scrollPercentage = Math.round((scrollTop / docHeight) * 100);
                
                // Tracker les milestones
                for (const milestone of milestones) {
                    if (scrollPercentage >= milestone && lastScrollPercentage < milestone) {
                        window.LandingPageAnalytics.events.trackScroll(milestone);
                    }
                }
                
                lastScrollPercentage = scrollPercentage;
            }, 1000);
            
            window.addEventListener('scroll', trackScroll);
        },

        // Tracking du temps passé
        setupTimeTracking: () => {
            const intervals = [30, 60, 120, 300]; // 30s, 1min, 2min, 5min
            
            intervals.forEach(seconds => {
                setTimeout(() => {
                    if (!document.hidden) {
                        window.LandingPageAnalytics.events.trackTimeOnPage();
                    }
                }, seconds * 1000);
            });
        },

        // Tracking de la visibilité de la page
        setupVisibilityTracking: () => {
            let startTime = Date.now();
            
            document.addEventListener('visibilitychange', () => {
                if (document.hidden) {
                    // Page cachée
                    const timeSpent = Date.now() - startTime;
                    window.LandingPageAnalytics.events.track('page_hidden', {
                        time_spent_ms: timeSpent
                    });
                } else {
                    // Page visible
                    startTime = Date.now();
                    window.LandingPageAnalytics.events.track('page_visible');
                }
            });
            
            // Avant fermeture de la page
            window.addEventListener('beforeunload', () => {
                const timeSpent = Date.now() - startTime;
                window.LandingPageAnalytics.events.track('page_unload', {
                    time_spent_ms: timeSpent
                });
            });
        }
    },

    // Initialisation principale
    init: (config = {}) => {
        if (window.LandingPageAnalytics.state.initialized) {
            window.LandingPageAnalytics.utils.log('Analytics déjà initialisé');
            return;
        }

        // Merger la configuration
        Object.assign(window.LandingPageAnalytics.config, config);

        // Charger le consentement
        window.LandingPageAnalytics.consent.load();

        // Générer les IDs de session
        window.LandingPageAnalytics.state.sessionId = window.LandingPageAnalytics.utils.generateId();
        
        // Récupérer ou générer l'ID utilisateur
        let userId = window.LandingPageAnalytics.utils.getStorage('user_id');
        if (!userId) {
            userId = window.LandingPageAnalytics.utils.generateId();
            window.LandingPageAnalytics.utils.setStorage('user_id', userId);
        }
        window.LandingPageAnalytics.state.userId = userId;

        // Initialiser les providers
        window.LandingPageAnalytics.providers.init();

        // Initialiser le tracking automatique
        window.LandingPageAnalytics.autoTrack.init();

        // Tracker la page vue initiale
        window.LandingPageAnalytics.events.trackPageView();

        // Retry des événements en queue
        window.LandingPageAnalytics.api.retryQueuedEvents();

        // Marquer comme initialisé
        window.LandingPageAnalytics.state.initialized = true;

        window.LandingPageAnalytics.utils.log('Analytics initialisé avec succès');
    }
};

// Auto-initialisation
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.LandingPageAnalytics.init();
    });
} else {
    window.LandingPageAnalytics.init();
}

// Export pour utilisation en module
if (typeof module !== 'undefined' && module.exports) {
    module.exports = window.LandingPageAnalytics;
}

// Alias global pour faciliter l'utilisation
window.analytics = window.LandingPageAnalytics;

// Fonction trackEvent globale pour compatibilité
window.LandingPageAnalytics.trackEvent = window.LandingPageAnalytics.events.track;

console.log('📊 Analytics Landing Page chargé');