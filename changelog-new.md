# Changelog

## [1.1.0] - 2025-07-31

### 🎯 Nouvelles fonctionnalités
- ✨ **Section Abonnements** : Implémentation complète du système d'abonnements
  - Création des tables `subscriptions`, `user_subscriptions`, `subscription_payments`
  - API REST complète pour la gestion des abonnements (`/api/subscriptions.php`)
  - Interface utilisateur dans le dashboard avec affichage des plans et statistiques
  - Support des limites par plan (produits, pages, catégories, stockage, IA)
  - Gestion des cycles de facturation (mensuel/annuel)

- 🤖 **Section IA avec données réelles** : Refonte complète de la gestion IA
  - Création des tables `ai_models`, `ai_api_keys`, `ai_usage`
  - API REST pour la gestion des modèles IA (`/api/ai-keys.php`)
  - Interface utilisateur mise à jour avec données réelles de la base
  - Support de 9 modèles IA (OpenAI, Anthropic, Google, Meta)
  - Gestion des coûts et usage par token
  - Classification économique/premium des modèles

### 🐛 Corrections de bugs
- 🔧 **API Roles** : Correction de l'erreur 500 lors de la création de rôles
  - Suppression de la classe `ApiResponse` dupliquée
  - Résolution des conflits de déclaration de classe

### 📊 Base de données
- 🗄️ **Tables Abonnements** :
  - `subscriptions` : Plans d'abonnement avec limites et fonctionnalités
  - `user_subscriptions` : Abonnements utilisateurs avec statut et usage
  - `subscription_payments` : Historique des paiements
- 🗄️ **Tables IA** :
  - `ai_models` : Modèles IA disponibles avec coûts et caractéristiques
  - `ai_api_keys` : Clés API avec limites et usage
  - `ai_usage` : Historique d'utilisation des modèles IA

### 🎨 Interface utilisateur
- 📱 **Dashboard** : Mise à jour des sections abonnements et IA
  - Chargement dynamique des données depuis les APIs
  - Affichage des statistiques en temps réel
  - Interface responsive et moderne
  - Gestion d'erreur améliorée avec messages utilisateur

### 🔧 API
- 🌐 **Nouvelles APIs** :
  - `/api/subscriptions.php` : CRUD complet pour les abonnements
  - `/api/ai-keys.php` : Gestion des modèles IA et API keys
- 🔄 **APIs mises à jour** :
  - `/api/roles.php` : Correction des erreurs de classe

## [1.0.0] - 2024-01-17

### Base de données
- ✨ Ajout de la colonne `domain` (varchar) à la table `stores`
- ✨ Ajout de la colonne `product_count` (int, default 0) à la table `stores`
- 📊 Création des index sur `domain`, `created_at` et `updated_at`
- ⚡️ Ajout de la procédure stockée `update_store_product_count`
- 🔄 Ajout des triggers `after_product_insert` et `after_product_delete`

### API
- 🌐 Mise à jour de `stores-simple.php` :
  - Support du multilangue (name_ar, name_en, description_ar, description_en)
  - Ajout du champ domain
  - Amélioration de la gestion des erreurs
  - Comptage des produits par store
- 🔄 Mise à jour de `stores.php` :
  - Support des champs bilingues
  - Validation améliorée des données
  - Intégration du comptage des produits

### Interface utilisateur
- 🎨 Mise à jour de `dashboard.html` :
  - Support complet du bilinguisme pour les stores
  - Ajout du bouton "Voir Détails"
  - Amélioration du formulaire d'édition
  - Intégration du champ domain
  - Affichage du nombre de produits
  - Amélioration des messages d'erreur
  - Utilisation de DEMO_TOKEN pour l'authentification

### Sécurité
- 🔒 Standardisation de l'authentification avec DEMO_TOKEN
- ✅ Validation renforcée des données côté serveur

### Performance
- ⚡️ Optimisation des requêtes SQL avec indexes
- 📊 Mise en cache du comptage des produits
- 🔄 Mise à jour automatique via triggers

### À venir
- [ ] Implémentation du cache Redis
- [ ] Optimisation des requêtes N+1
- [ ] Ajout de tests unitaires
- [ ] Documentation API complète
- [ ] Monitoring des performances
