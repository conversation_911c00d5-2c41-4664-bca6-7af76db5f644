<?php
require_once 'php/config/database.php';

try {
    echo "🔍 Correction de la structure de la base de données...\n\n";
    
    $database = new Database();
    $db = $database->getConnection();
    echo "✅ Connexion base de données OK\n";
    
    // 1. Vérifier et corriger la table users
    echo "\n👥 Correction de la table users...\n";
    
    // Vérifier les colonnes existantes
    $columnsQuery = "SHOW COLUMNS FROM users";
    $columnsResult = $db->query($columnsQuery);
    $columns = $columnsResult->fetchAll(PDO::FETCH_ASSOC);
    
    $existingColumns = array_column($columns, 'Field');
    echo "  📋 Colonnes existantes: " . implode(', ', $existingColumns) . "\n";
    
    // Ajouter la colonne 'name' si elle n'existe pas
    if (!in_array('name', $existingColumns)) {
        echo "  🔧 Ajout de la colonne 'name'...\n";
        $addNameColumn = "ALTER TABLE users ADD COLUMN name VARCHAR(255) NULL AFTER email";
        $db->exec($addNameColumn);
        echo "  ✅ Colonne 'name' ajoutée\n";
    } else {
        echo "  ✅ Colonne 'name' existe déjà\n";
    }
    
    // Mettre à jour les noms vides avec l'email
    $updateNamesQuery = "UPDATE users SET name = SUBSTRING_INDEX(email, '@', 1) WHERE name IS NULL OR name = ''";
    $db->exec($updateNamesQuery);
    echo "  ✅ Noms mis à jour à partir des emails\n";
    
    // 2. Créer la table product_images
    echo "\n🖼️ Création de la table product_images...\n";
    
    $createProductImagesTable = "
        CREATE TABLE IF NOT EXISTS product_images (
            id INT AUTO_INCREMENT PRIMARY KEY,
            product_id INT NOT NULL,
            image_url VARCHAR(500) NOT NULL,
            alt_text VARCHAR(255) NULL,
            sort_order INT DEFAULT 0,
            is_primary BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
            INDEX idx_product_id (product_id),
            INDEX idx_sort_order (sort_order)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $db->exec($createProductImagesTable);
    echo "  ✅ Table product_images créée\n";
    
    // Migrer les images existantes des produits vers la nouvelle table
    echo "  🔄 Migration des images existantes...\n";
    
    $migrateImagesQuery = "
        INSERT INTO product_images (product_id, image_url, is_primary)
        SELECT id, images, TRUE
        FROM products 
        WHERE images IS NOT NULL AND images != '' AND images != '[]'
        AND NOT EXISTS (SELECT 1 FROM product_images WHERE product_id = products.id)
    ";
    
    try {
        $db->exec($migrateImagesQuery);
        echo "  ✅ Images migrées vers product_images\n";
    } catch (Exception $e) {
        echo "  ⚠️ Aucune image à migrer ou erreur: " . $e->getMessage() . "\n";
    }
    
    // 3. Créer quelques images de test pour les produits existants
    echo "\n📸 Création d'images de test...\n";
    
    $testImagesQuery = "
        SELECT id, name_ar, sku FROM products WHERE store_id = 3 LIMIT 5
    ";
    
    $testProducts = $db->query($testImagesQuery)->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($testProducts as $product) {
        // Vérifier si le produit a déjà des images
        $checkImagesQuery = "SELECT COUNT(*) as count FROM product_images WHERE product_id = ?";
        $checkStmt = $db->prepare($checkImagesQuery);
        $checkStmt->execute([$product['id']]);
        $imageCount = $checkStmt->fetch()['count'];
        
        if ($imageCount == 0) {
            // Ajouter une image de test
            $insertImageQuery = "
                INSERT INTO product_images (product_id, image_url, alt_text, is_primary)
                VALUES (?, ?, ?, TRUE)
            ";
            
            $imageUrl = "https://via.placeholder.com/400x400/007bff/ffffff?text=" . urlencode($product['sku'] ?: 'Product');
            $altText = $product['name_ar'] ?: 'Produit';
            
            $insertStmt = $db->prepare($insertImageQuery);
            $insertStmt->execute([$product['id'], $imageUrl, $altText]);
            
            echo "  ✅ Image ajoutée pour le produit '{$product['name_ar']}'\n";
        }
    }
    
    // 4. Vérifier les autres tables nécessaires
    echo "\n📋 Vérification des autres tables...\n";
    
    $requiredTables = [
        'categories' => 'Table des catégories',
        'stores' => 'Table des magasins',
        'user_roles' => 'Table des rôles utilisateurs',
        'roles' => 'Table des rôles'
    ];
    
    foreach ($requiredTables as $table => $description) {
        $checkTableQuery = "SHOW TABLES LIKE '$table'";
        $result = $db->query($checkTableQuery);
        
        if ($result->rowCount() > 0) {
            echo "  ✅ $description ($table) existe\n";
        } else {
            echo "  ❌ $description ($table) manquante\n";
        }
    }
    
    // 5. Test final des requêtes
    echo "\n🧪 Test final des requêtes...\n";
    
    // Test requête products avec images
    try {
        $testProductsQuery = "
            SELECT 
                p.id, p.name_ar as name, p.sku, p.price,
                GROUP_CONCAT(pi.image_url) as images
            FROM products p
            LEFT JOIN product_images pi ON p.id = pi.product_id
            WHERE p.store_id = 3
            GROUP BY p.id
            LIMIT 1
        ";
        
        $result = $db->query($testProductsQuery);
        $product = $result->fetch();
        echo "  ✅ Requête products avec images OK\n";
        
        if ($product) {
            echo "    📝 Produit test: {$product['name']} (Images: " . ($product['images'] ?: 'aucune') . ")\n";
        }
    } catch (Exception $e) {
        echo "  ❌ Erreur requête products: " . $e->getMessage() . "\n";
    }
    
    // Test requête users avec name
    try {
        $testUsersQuery = "
            SELECT u.id, u.email, u.name, u.status
            FROM users u
            LIMIT 1
        ";
        
        $result = $db->query($testUsersQuery);
        $user = $result->fetch();
        echo "  ✅ Requête users avec name OK\n";
        
        if ($user) {
            echo "    📝 Utilisateur test: {$user['name']} ({$user['email']})\n";
        }
    } catch (Exception $e) {
        echo "  ❌ Erreur requête users: " . $e->getMessage() . "\n";
    }
    
    // Résumé final
    echo "\n📊 Résumé des corrections:\n";
    
    $productCount = $db->query("SELECT COUNT(*) FROM products WHERE store_id = 3")->fetchColumn();
    $imageCount = $db->query("SELECT COUNT(*) FROM product_images")->fetchColumn();
    $userCount = $db->query("SELECT COUNT(*) FROM users")->fetchColumn();
    $orderCount = $db->query("SELECT COUNT(*) FROM orders WHERE store_id = 3")->fetchColumn();
    
    echo "  - Produits: $productCount\n";
    echo "  - Images produits: $imageCount\n";
    echo "  - Utilisateurs: $userCount\n";
    echo "  - Commandes: $orderCount\n";
    
    echo "\n✅ Correction de la structure terminée!\n";
    echo "🎉 Les APIs devraient maintenant fonctionner sans erreur.\n";
    
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
    echo "📍 Fichier: " . $e->getFile() . " ligne " . $e->getLine() . "\n";
}
?>
