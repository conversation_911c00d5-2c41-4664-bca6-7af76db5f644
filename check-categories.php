<?php
require_once 'php/config/database.php';

try {
    echo "🔍 Vérification des tables de catégories...\n";
    
    $database = new Database();
    $db = $database->getConnection();
    
    // Lister toutes les tables contenant "categor"
    $tablesQuery = "SHOW TABLES LIKE '%categor%'";
    $tablesResult = $db->query($tablesQuery);
    $tables = $tablesResult->fetchAll(PDO::FETCH_COLUMN);
    
    echo "📋 Tables trouvées: " . implode(', ', $tables) . "\n\n";
    
    foreach ($tables as $table) {
        echo "📊 Structure de la table '$table':\n";
        
        $columnsQuery = "DESCRIBE $table";
        $columnsResult = $db->query($columnsQuery);
        $columns = $columnsResult->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($columns as $column) {
            echo "  - {$column['Field']} ({$column['Type']})\n";
        }
        
        // Compter les enregistrements
        $countQuery = "SELECT COUNT(*) as count FROM $table";
        $count = $db->query($countQuery)->fetch()['count'];
        echo "  📊 Nombre d'enregistrements: $count\n";
        
        // Afficher quelques exemples
        if ($count > 0) {
            echo "  📝 Exemples:\n";
            $examplesQuery = "SELECT * FROM $table LIMIT 3";
            $examples = $db->query($examplesQuery)->fetchAll(PDO::FETCH_ASSOC);
            
            foreach ($examples as $example) {
                $name = $example['name_ar'] ?? $example['name'] ?? $example['id'] ?? 'N/A';
                echo "    - ID: {$example['id']}, Nom: $name\n";
            }
        }
        
        echo "\n";
    }
    
    // Vérifier la table products pour voir quelle colonne category elle utilise
    echo "🛍️ Vérification de la table products:\n";
    $productColumnsQuery = "DESCRIBE products";
    $productColumns = $db->query($productColumnsQuery)->fetchAll(PDO::FETCH_ASSOC);
    
    $categoryColumns = array_filter($productColumns, function($col) {
        return strpos(strtolower($col['Field']), 'categor') !== false;
    });
    
    if ($categoryColumns) {
        echo "  📋 Colonnes de catégorie trouvées:\n";
        foreach ($categoryColumns as $col) {
            echo "    - {$col['Field']} ({$col['Type']})\n";
        }
    } else {
        echo "  ❌ Aucune colonne de catégorie trouvée dans products\n";
    }
    
    // Tester une requête simple
    echo "\n🧪 Test d'une requête simple:\n";
    
    if (in_array('categories', $tables)) {
        $testQuery = "
            SELECT 
                p.id, p.name_ar, p.category_id,
                c.name_ar as category_name
            FROM products p
            LEFT JOIN categories c ON p.category_id = c.id
            WHERE p.store_id = 3
            LIMIT 1
        ";
        
        try {
            $result = $db->query($testQuery);
            $product = $result->fetch();
            
            if ($product) {
                echo "  ✅ Requête réussie\n";
                echo "  📝 Produit: {$product['name_ar']}, Catégorie: {$product['category_name']}\n";
            } else {
                echo "  ⚠️ Aucun produit trouvé\n";
            }
        } catch (Exception $e) {
            echo "  ❌ Erreur: " . $e->getMessage() . "\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
}
?>
