{"payment_methods": {"ccp": {"id": "ccp", "name": {"ar": "حساب جاري بريدي (CCP)", "fr": "<PERSON><PERSON><PERSON> (CCP)", "en": "Postal Checking Account (CCP)"}, "description": {"ar": "الدفع عبر الحساب الجاري البريدي مع رفع إيصال الدفع", "fr": "Paiement via compte chèques postaux avec téléchargement du reçu", "en": "Payment via postal checking account with receipt upload"}, "icon": "💳", "enabled": true, "requires_proof": true, "fields": [{"name": "ccp_number", "type": "text", "label": {"ar": "رقم الحساب الجاري البريدي", "fr": "Numéro CCP", "en": "CCP Number"}, "placeholder": {"ar": "أدخل رقم CCP الخاص بك", "fr": "Entrez votre numéro CCP", "en": "Enter your CCP number"}, "required": true, "validation": {"pattern": "^[0-9]{10,12}$", "message": {"ar": "رقم CCP يجب أن يكون بين 10-12 رقم", "fr": "Le numéro CCP doit contenir 10-12 chiffres", "en": "CCP number must be 10-12 digits"}}}, {"name": "ccp_key", "type": "text", "label": {"ar": "مفتاح RIP", "fr": "Clé RIP", "en": "RIP Key"}, "placeholder": {"ar": "أد<PERSON>ل مفتاح RIP", "fr": "Entrez la clé RIP", "en": "Enter RIP key"}, "required": true, "validation": {"pattern": "^[0-9]{2}$", "message": {"ar": "مف<PERSON>اح RIP يجب أن يكون رقمين", "fr": "La clé RIP doit contenir 2 chiffres", "en": "RIP key must be 2 digits"}}}, {"name": "payment_receipt", "type": "file", "label": {"ar": "إيصال الدفع", "fr": "Reçu de paiement", "en": "Payment Receipt"}, "required": true, "accept": "image/*,.pdf", "max_size": "5MB"}], "instructions": {"ar": ["قم بتحويل المبلغ إلى الحساب الجاري البريدي: {{ccp_account_number}}", "مفتاح RIP: {{ccp_rip_number}}", "اسم صاحب الحساب: {{ccp_account_name}}", "قم برفع صورة واضحة من إيصال الدفع", "تأكد من ظهور رقم الطلب في الإيصال"], "fr": ["<PERSON><PERSON>z le virement vers le CCP: {{ccp_account_number}}", "Clé RIP: {{ccp_rip_number}}", "Nom du titulaire: {{ccp_account_name}}", "Téléchargez une photo claire du reçu de paiement", "Assurez-vous que le numéro de commande apparaît sur le reçu"], "en": ["Transfer the amount to CCP: {{ccp_account_number}}", "RIP Key: {{ccp_rip_number}}", "Account holder: {{ccp_account_name}}", "Upload a clear photo of the payment receipt", "Make sure the order number appears on the receipt"]}}, "baridimob": {"id": "barid<PERSON><PERSON>", "name": {"ar": "بريدي موب", "fr": "<PERSON><PERSON><PERSON><PERSON>", "en": "<PERSON><PERSON><PERSON><PERSON>"}, "description": {"ar": "الدفع عبر تطبيق بريدي موب مع رقم المعاملة", "fr": "Paiement via l'application BaridiMob avec ID de transaction", "en": "Payment via BaridiMob app with transaction ID"}, "icon": "📱", "enabled": true, "requires_proof": true, "fields": [{"name": "transaction_id", "type": "text", "label": {"ar": "رقم المعاملة", "fr": "ID de transaction", "en": "Transaction ID"}, "placeholder": {"ar": "أدخل رقم المعاملة من بريدي موب", "fr": "Entrez l'ID de transaction BaridiMob", "en": "Enter BaridiMob transaction ID"}, "required": true, "validation": {"pattern": "^[A-Z0-9]{8,20}$", "message": {"ar": "رقم المعاملة غير صحيح", "fr": "ID de transaction invalide", "en": "Invalid transaction ID"}}}, {"name": "sender_phone", "type": "tel", "label": {"ar": "رقم هاتف المرسل", "fr": "Numéro de téléphone expéditeur", "en": "Sender phone number"}, "placeholder": {"ar": "+213xxxxxxxxx", "fr": "+213xxxxxxxxx", "en": "+213xxxxxxxxx"}, "required": true, "validation": {"pattern": "^\\+213[5-7][0-9]{8}$", "message": {"ar": "رقم الهاتف يجب أن يبدأ بـ +213", "fr": "Le numéro doit commencer par +213", "en": "Phone number must start with +213"}}}, {"name": "payment_screenshot", "type": "file", "label": {"ar": "لقطة شاشة من التطبيق", "fr": "Capture d'écran de l'app", "en": "App screenshot"}, "required": true, "accept": "image/*", "max_size": "3MB"}], "instructions": {"ar": ["افتح تطبيق بريدي موب", "قم بتحويل المبلغ إلى: {{baridimob_phone}}", "اسم المستفيد: {{baridimob_account_name}}", "احفظ رقم المعاملة", "التقط لقطة شاشة تظهر نجاح العملية"], "fr": ["Ouvrez l'application BaridiMob", "<PERSON><PERSON><PERSON><PERSON> le montant vers: {{baridimob_phone}}", "Nom du bénéficiaire: {{baridimob_account_name}}", "Sauvegardez l'ID de transaction", "Prenez une capture d'écran confirmant le succès"], "en": ["Open BaridiMob application", "Transfer amount to: {{baridimob_phone}}", "Beneficiary name: {{baridimob_account_name}}", "Save the transaction ID", "Take a screenshot showing successful transaction"]}}, "bank_transfer": {"id": "bank_transfer", "name": {"ar": "تحويل بنكي", "fr": "Virement bancaire", "en": "Bank Transfer"}, "description": {"ar": "التحويل المباشر إلى الحساب البنكي مع إثبات التحويل", "fr": "Virement direct vers compte bancaire avec preuve de transfert", "en": "Direct transfer to bank account with transfer proof"}, "icon": "🏦", "enabled": true, "requires_proof": true, "fields": [{"name": "transfer_reference", "type": "text", "label": {"ar": "مرجع التحويل", "fr": "Référence de virement", "en": "Transfer Reference"}, "placeholder": {"ar": "أدخل مرجع التحويل البنكي", "fr": "Entrez la référence de virement", "en": "Enter bank transfer reference"}, "required": true}, {"name": "sender_bank", "type": "select", "label": {"ar": "البنك المرسل", "fr": "Banque expéditrice", "en": "Sender Bank"}, "required": true, "options": [{"value": "bea", "label": {"ar": "بنك الجزائر الخارجي", "fr": "BEA", "en": "BEA"}}, {"value": "badr", "label": {"ar": "بنك الفلاحة والتنمية الريفية", "fr": "BADR", "en": "BADR"}}, {"value": "cnep", "label": {"ar": "الصندوق الوطني للتوفير والاحتياط", "fr": "CNEP", "en": "CNEP"}}, {"value": "cpa", "label": {"ar": "القرض الشعبي الجزائري", "fr": "CPA", "en": "CPA"}}, {"value": "bna", "label": {"ar": "البنك الوطني الجزائري", "fr": "BNA", "en": "BNA"}}, {"value": "other", "label": {"ar": "أ<PERSON><PERSON><PERSON>", "fr": "<PERSON><PERSON>", "en": "Other"}}]}, {"name": "transfer_proof", "type": "file", "label": {"ar": "إثبات التحويل", "fr": "Preuve de virement", "en": "Transfer Proof"}, "required": true, "accept": "image/*,.pdf", "max_size": "5MB"}], "instructions": {"ar": ["قم بالتحويل إلى الحساب البنكي التالي:", "البنك: {{bank_name}}", "رقم الحساب: {{bank_account_number}}", "اسم صاحب الحساب: {{bank_account_name}}", "IBAN: {{bank_iban}}", "قم برفع إثبات التحويل من البنك"], "fr": ["<PERSON><PERSON>z le virement vers le compte bancaire suivant:", "Banque: {{bank_name}}", "Numéro de compte: {{bank_account_number}}", "Titulaire: {{bank_account_name}}", "IBAN: {{bank_iban}}", "Téléchargez la preuve de virement de la banque"], "en": ["Transfer to the following bank account:", "Bank: {{bank_name}}", "Account number: {{bank_account_number}}", "Account holder: {{bank_account_name}}", "IBAN: {{bank_iban}}", "Upload bank transfer proof"]}}, "cod": {"id": "cod", "name": {"ar": "الدفع عند الاستلام", "fr": "Paiement à la livraison", "en": "Cash on Delivery"}, "description": {"ar": "ادفع نقداً عند استلام الطلب", "fr": "Payez en espèces à la réception de votre commande", "en": "Pay cash when you receive your order"}, "icon": "💵", "enabled": true, "requires_proof": false, "fee": 200, "max_amount": 50000, "available_cities": ["الجزائر", "وهران", "قسنطينة", "عنابة", "سطيف", "باتنة", "تلمسان", "<PERSON><PERSON>", "<PERSON><PERSON>", "Constantine", "Annaba", "<PERSON><PERSON><PERSON><PERSON>", "Batna", "Tlemcen"], "fields": [{"name": "delivery_city", "type": "select", "label": {"ar": "مدينة التسليم", "fr": "Ville de livraison", "en": "Delivery City"}, "required": true, "options": [{"value": "algiers", "label": {"ar": "الجزائر", "fr": "<PERSON><PERSON>", "en": "Algiers"}}, {"value": "oran", "label": {"ar": "وهران", "fr": "<PERSON><PERSON>", "en": "<PERSON><PERSON>"}}, {"value": "constantine", "label": {"ar": "قسنطينة", "fr": "Constantine", "en": "Constantine"}}, {"value": "annaba", "label": {"ar": "عنابة", "fr": "Annaba", "en": "Annaba"}}, {"value": "setif", "label": {"ar": "سطيف", "fr": "<PERSON><PERSON><PERSON><PERSON>", "en": "<PERSON><PERSON>"}}, {"value": "batna", "label": {"ar": "باتنة", "fr": "Batna", "en": "Batna"}}, {"value": "tlemcen", "label": {"ar": "تلمسان", "fr": "Tlemcen", "en": "Tlemcen"}}]}, {"name": "delivery_address", "type": "textarea", "label": {"ar": "عنوان التسليم التفصيلي", "fr": "<PERSON><PERSON><PERSON> <PERSON> l<PERSON>", "en": "Detailed delivery address"}, "placeholder": {"ar": "أد<PERSON>ل العنوان الكامل مع رقم الهاتف", "fr": "Entrez l'adresse complète avec numéro de téléphone", "en": "Enter complete address with phone number"}, "required": true, "rows": 3}, {"name": "preferred_time", "type": "select", "label": {"ar": "الوقت المفضل للتسليم", "fr": "<PERSON><PERSON> pré<PERSON><PERSON><PERSON><PERSON> liv<PERSON>", "en": "Preferred delivery time"}, "required": false, "options": [{"value": "morning", "label": {"ar": "صباحاً (8:00 - 12:00)", "fr": "<PERSON><PERSON> (8:00 - 12:00)", "en": "Morning (8:00 - 12:00)"}}, {"value": "afternoon", "label": {"ar": "بع<PERSON> الظهر (12:00 - 17:00)", "fr": "Après-midi (12:00 - 17:00)", "en": "Afternoon (12:00 - 17:00)"}}, {"value": "evening", "label": {"ar": "مساءً (17:00 - 20:00)", "fr": "Soir (17:00 - 20:00)", "en": "Evening (17:00 - 20:00)"}}]}], "instructions": {"ar": ["سيتم تسليم الطلب خلال 2-5 أيام عمل", "رسوم التسليم: {{cod_fee}} دج", "الحد الأقصى للمبلغ: {{cod_max_amount}} دج", "يرجى التأكد من توفر المبلغ نقداً عند التسليم", "سيتم الاتصال بك قبل التسليم"], "fr": ["Livraison sous 2-5 jours ouvrables", "Frais de livraison: {{cod_fee}} DA", "Montant maximum: {{cod_max_amount}} DA", "Assurez-vous d'avoir le montant en espèces lors de la livraison", "Nous vous appellerons avant la livraison"], "en": ["Delivery within 2-5 business days", "Delivery fee: {{cod_fee}} DA", "Maximum amount: {{cod_max_amount}} DA", "Make sure to have cash ready upon delivery", "We will call you before delivery"]}}}, "validation_rules": {"file_upload": {"max_size": 5242880, "allowed_types": ["image/jpeg", "image/png", "image/gif", "application/pdf"], "required_fields": ["payment_receipt", "payment_screenshot", "transfer_proof"]}, "phone_validation": {"algeria_pattern": "^\\+213[5-7][0-9]{8}$", "international_pattern": "^\\+[1-9]\\d{1,14}$"}, "amount_limits": {"min_order": 500, "max_order": 1000000, "cod_max": 50000}}, "ui_settings": {"rtl_support": true, "default_language": "ar", "theme": {"primary_color": "#667eea", "success_color": "#4caf50", "warning_color": "#ff9800", "error_color": "#f44336"}, "animations": {"form_transition": "0.3s ease", "field_focus": "0.2s ease", "button_hover": "0.2s ease"}}}