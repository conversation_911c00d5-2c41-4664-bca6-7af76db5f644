<?php
/**
 * Script web pour mettre à jour la base de données
 */

header('Content-Type: text/html; charset=utf-8');

// Configuration de la base de données
$host = 'localhost';
$port = 3307;
$dbname = 'landingpage_new';
$username = 'root';
$password = '';

echo "<h1>Mise à jour de la base de données</h1>";
echo "<pre>";

try {
    // Connexion à la base de données
    $pdo = new PDO(
        "mysql:host={$host};port={$port};dbname={$dbname};charset=utf8mb4",
        $username,
        $password,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]
    );

    echo "✓ Connexion à la base de données réussie.\n";

    // 1. Vérifier si la colonne domain existe, sinon l'ajouter
    $checkDomain = $pdo->query("SHOW COLUMNS FROM stores LIKE 'domain'");
    if ($checkDomain->rowCount() == 0) {
        echo "→ Ajout de la colonne domain à la table stores...\n";
        $pdo->exec("ALTER TABLE stores ADD COLUMN domain VARCHAR(255)");
        echo "✓ Colonne domain ajoutée avec succès.\n";
    } else {
        echo "✓ La colonne domain existe déjà.\n";
    }

    // 2. Vérifier si la colonne subdomain existe
    $checkColumn = $pdo->query("SHOW COLUMNS FROM stores LIKE 'subdomain'");
    if ($checkColumn->rowCount() == 0) {
        echo "→ Ajout de la colonne subdomain à la table stores...\n";
        $pdo->exec("ALTER TABLE stores ADD COLUMN subdomain VARCHAR(100)");
        echo "✓ Colonne subdomain ajoutée avec succès.\n";
    } else {
        echo "✓ La colonne subdomain existe déjà.\n";
    }

    // 3. Créer un index sur subdomain
    try {
        $pdo->exec("CREATE INDEX idx_stores_subdomain ON stores(subdomain)");
        echo "✓ Index sur subdomain créé.\n";
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
            echo "✓ L'index sur subdomain existe déjà.\n";
        } else {
            throw $e;
        }
    }

    // 4. Mettre à jour les stores existants avec des subdomains
    echo "→ Mise à jour des subdomains pour les stores existants...\n";
    $stores = $pdo->query("SELECT id, store_name, store_name_en, store_name_ar, subdomain FROM stores")->fetchAll();
    
    $updated = 0;
    foreach ($stores as $store) {
        if (empty($store['subdomain'])) {
            // Utiliser le nom du store pour générer le subdomain
            $storeName = $store['store_name'] ?? $store['store_name_en'] ?? $store['store_name_ar'] ?? 'store-' . $store['id'];
            
            // Nettoyer le nom pour créer un subdomain valide
            $subdomain = strtolower(trim($storeName));
            $subdomain = preg_replace('/[^a-z0-9\-]/', '-', $subdomain);
            $subdomain = preg_replace('/-+/', '-', $subdomain);
            $subdomain = trim($subdomain, '-');
            
            // Assurer l'unicité
            $originalSubdomain = $subdomain;
            $counter = 1;
            while (true) {
                $checkQuery = $pdo->prepare("SELECT COUNT(*) FROM stores WHERE subdomain = ? AND id != ?");
                $checkQuery->execute([$subdomain, $store['id']]);
                if ($checkQuery->fetchColumn() == 0) {
                    break;
                }
                $subdomain = $originalSubdomain . '-' . $counter++;
            }
            
            // Mettre à jour le store
            $updateQuery = $pdo->prepare("UPDATE stores SET subdomain = ? WHERE id = ?");
            $updateQuery->execute([$subdomain, $store['id']]);
            
            echo "  ✓ Store ID {$store['id']}: subdomain défini à '{$subdomain}'\n";
            $updated++;
        }
    }

    if ($updated == 0) {
        echo "✓ Tous les stores ont déjà un subdomain.\n";
    } else {
        echo "✓ {$updated} stores mis à jour.\n";
    }

    echo "\n🎉 Mise à jour terminée avec succès!\n";
    echo "\nMaintenant vous pouvez accéder aux stores via:\n";
    echo "- Format store: http://localhost:8000/store/{store_name}\n";
    echo "- Format subdomain: http://localhost:8000/{subdomain}\n";

} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
}

echo "</pre>";
?>
