<?php
try {
    $pdo = new PDO("mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "Ajout de données d'usage IA réelles...\n";
    
    // Vider la table d'usage existante
    $pdo->exec("DELETE FROM ai_usage");
    echo "Table ai_usage vidée\n";
    
    // Insérer des données d'usage réalistes basées sur l'image fournie
    $usageData = [
        // GPT-4 (modèle cher)
        ['user_id' => 'firebase_uid_1', 'model_id' => 1, 'tokens' => 32150, 'cost' => 8.04],
        ['user_id' => 'firebase_uid_2', 'model_id' => 1, 'tokens' => 8950, 'cost' => 26.85],
        ['user_id' => 'firebase_uid_1', 'model_id' => 1, 'tokens' => 28900, 'cost' => 4.34],
        ['user_id' => 'firebase_uid_2', 'model_id' => 1, 'tokens' => 12100, 'cost' => 18.15],
        
        // GPT-3.5 Turbo (moins cher)
        ['user_id' => 'firebase_uid_1', 'model_id' => 2, 'tokens' => 45200, 'cost' => 2.26],
        ['user_id' => 'firebase_uid_2', 'model_id' => 2, 'tokens' => 25680, 'cost' => 5.14],
        ['user_id' => 'firebase_uid_1', 'model_id' => 2, 'tokens' => 18420, 'cost' => 9.21],
        
        // Claude 3 Opus (cher)
        ['user_id' => 'firebase_uid_2', 'model_id' => 3, 'tokens' => 15240, 'cost' => 45.72],
        ['user_id' => 'firebase_uid_1', 'model_id' => 3, 'tokens' => 12350, 'cost' => 24.70],
    ];
    
    foreach ($usageData as $usage) {
        // Calculer les tokens de prompt et completion (approximation)
        $promptTokens = intval($usage['tokens'] * 0.3);
        $completionTokens = $usage['tokens'] - $promptTokens;
        
        $stmt = $pdo->prepare("
            INSERT INTO ai_usage (user_id, model_id, api_key_id, request_type, prompt_tokens, completion_tokens, total_tokens, cost_usd, response_time_ms, success, created_at) 
            VALUES (?, ?, 1, 'text_generation', ?, ?, ?, ?, ?, 1, NOW() - INTERVAL FLOOR(RAND() * 30) DAY)
        ");
        
        $stmt->execute([
            $usage['user_id'],
            $usage['model_id'],
            $promptTokens,
            $completionTokens,
            $usage['tokens'],
            $usage['cost'],
            rand(800, 3000) // temps de réponse aléatoire
        ]);
    }
    
    echo "✅ Données d'usage insérées\n";
    
    // Vérifier les résultats
    echo "\n📊 Statistiques d'usage par modèle :\n";
    $stmt = $pdo->query("
        SELECT 
            m.display_name,
            COUNT(u.id) as requests,
            SUM(u.total_tokens) as total_tokens,
            SUM(u.cost_usd) as total_cost
        FROM ai_models m
        LEFT JOIN ai_usage u ON m.id = u.model_id
        WHERE u.id IS NOT NULL
        GROUP BY m.id
        ORDER BY total_cost DESC
    ");
    $stats = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($stats as $stat) {
        echo "- {$stat['display_name']}: " . number_format($stat['total_tokens']) . " tokens, \$" . number_format($stat['total_cost'], 2) . " ({$stat['requests']} requêtes)\n";
    }
    
    echo "\n✅ Données d'usage IA créées avec succès !\n";
    
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
}
?>
