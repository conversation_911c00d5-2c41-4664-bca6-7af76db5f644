-- Création des tables pour le système d'abonnements

-- Table des plans d'abonnement
CREATE TABLE IF NOT EXISTS subscriptions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT 'Nom du plan (Basic, Pro, Premium)',
    display_name VARCHAR(100) NOT NULL COMMENT 'Nom affiché',
    description TEXT COMMENT 'Description du plan',
    price DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT 'Prix mensuel',
    currency VARCHAR(3) DEFAULT 'USD' COMMENT 'Devise',
    billing_cycle ENUM('monthly', 'yearly') DEFAULT 'monthly' COMMENT 'Cycle de facturation',
    
    -- Limites du plan
    max_products INT DEFAULT 10 COMMENT 'Nombre max de produits (-1 = illimité)',
    max_landing_pages INT DEFAULT 1 COMMENT 'Nombre max de landing pages (-1 = illimité)',
    max_categories INT DEFAULT 5 COMMENT 'Nombre max de catégories (-1 = illimité)',
    max_subcategories INT DEFAULT 10 COMMENT 'Nombre max de sous-catégories (-1 = illimité)',
    max_storage_mb INT DEFAULT 1000 COMMENT 'Stockage max en MB (-1 = illimité)',
    max_bandwidth_gb INT DEFAULT 10 COMMENT 'Bande passante max en GB (-1 = illimité)',
    
    -- Fonctionnalités IA
    ai_enabled BOOLEAN DEFAULT FALSE COMMENT 'IA activée',
    ai_monthly_tokens INT DEFAULT 0 COMMENT 'Tokens IA par mois (-1 = illimité)',
    ai_models JSON COMMENT 'Modèles IA disponibles',
    
    -- Support et fonctionnalités
    support_level ENUM('basic', 'priority', 'premium') DEFAULT 'basic',
    custom_domain BOOLEAN DEFAULT FALSE COMMENT 'Domaine personnalisé autorisé',
    analytics_advanced BOOLEAN DEFAULT FALSE COMMENT 'Analytics avancées',
    white_label BOOLEAN DEFAULT FALSE COMMENT 'Marque blanche',
    api_access BOOLEAN DEFAULT FALSE COMMENT 'Accès API',
    
    -- Métadonnées
    is_active BOOLEAN DEFAULT TRUE,
    is_featured BOOLEAN DEFAULT FALSE COMMENT 'Plan mis en avant',
    sort_order INT DEFAULT 0 COMMENT 'Ordre d\'affichage',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_active (is_active),
    INDEX idx_featured (is_featured),
    INDEX idx_sort (sort_order)
);

-- Table des abonnements utilisateurs
CREATE TABLE IF NOT EXISTS user_subscriptions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL COMMENT 'ID utilisateur Firebase',
    subscription_id INT NOT NULL COMMENT 'ID du plan d\'abonnement',
    
    -- Statut de l'abonnement
    status ENUM('active', 'cancelled', 'expired', 'suspended') DEFAULT 'active',
    
    -- Dates importantes
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Date de début',
    expires_at TIMESTAMP NULL COMMENT 'Date d\'expiration',
    cancelled_at TIMESTAMP NULL COMMENT 'Date d\'annulation',
    
    -- Informations de paiement
    payment_method VARCHAR(50) COMMENT 'Méthode de paiement',
    payment_id VARCHAR(255) COMMENT 'ID de paiement externe (Stripe, PayPal, etc.)',
    last_payment_at TIMESTAMP NULL COMMENT 'Dernier paiement',
    next_payment_at TIMESTAMP NULL COMMENT 'Prochain paiement',
    
    -- Usage actuel
    current_products INT DEFAULT 0 COMMENT 'Nombre actuel de produits',
    current_landing_pages INT DEFAULT 0 COMMENT 'Nombre actuel de landing pages',
    current_categories INT DEFAULT 0 COMMENT 'Nombre actuel de catégories',
    current_storage_mb INT DEFAULT 0 COMMENT 'Stockage utilisé en MB',
    current_bandwidth_gb INT DEFAULT 0 COMMENT 'Bande passante utilisée ce mois',
    current_ai_tokens INT DEFAULT 0 COMMENT 'Tokens IA utilisés ce mois',
    
    -- Métadonnées
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (subscription_id) REFERENCES subscriptions(id) ON DELETE RESTRICT,
    INDEX idx_user (user_id),
    INDEX idx_status (status),
    INDEX idx_expires (expires_at),
    UNIQUE KEY unique_active_user (user_id, status) -- Un seul abonnement actif par utilisateur
);

-- Table de l'historique des paiements
CREATE TABLE IF NOT EXISTS subscription_payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_subscription_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending',
    payment_method VARCHAR(50),
    payment_id VARCHAR(255) COMMENT 'ID externe du paiement',
    transaction_id VARCHAR(255) COMMENT 'ID de transaction',
    
    -- Détails du paiement
    billing_period_start DATE COMMENT 'Début de la période facturée',
    billing_period_end DATE COMMENT 'Fin de la période facturée',
    
    -- Métadonnées
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_subscription_id) REFERENCES user_subscriptions(id) ON DELETE CASCADE,
    INDEX idx_status (status),
    INDEX idx_payment_id (payment_id),
    INDEX idx_created (created_at)
);

-- Insérer les plans d'abonnement par défaut
INSERT INTO subscriptions (name, display_name, description, price, max_products, max_landing_pages, max_categories, max_subcategories, max_storage_mb, ai_enabled, ai_monthly_tokens, support_level, sort_order) VALUES
('free', 'Gratuit', 'Plan gratuit pour commencer', 0.00, 3, 1, 2, 5, 100, FALSE, 0, 'basic', 1),
('basic', 'Basic', 'Plan de base pour petites entreprises', 9.99, 25, 3, 10, 20, 1000, TRUE, 10000, 'basic', 2),
('pro', 'Pro', 'Plan professionnel pour entreprises en croissance', 29.99, 100, 10, 50, 100, 5000, TRUE, 50000, 'priority', 3),
('premium', 'Premium', 'Plan premium avec toutes les fonctionnalités', 99.99, -1, -1, -1, -1, -1, TRUE, -1, 'premium', 4);

-- Mettre à jour les plans avec les modèles IA disponibles
UPDATE subscriptions SET ai_models = JSON_ARRAY('gpt-3.5-turbo') WHERE name = 'basic';
UPDATE subscriptions SET ai_models = JSON_ARRAY('gpt-3.5-turbo', 'gpt-4o-mini', 'claude-3-haiku') WHERE name = 'pro';
UPDATE subscriptions SET ai_models = JSON_ARRAY('gpt-3.5-turbo', 'gpt-4o-mini', 'gpt-4', 'claude-3', 'claude-3-haiku', 'gemini-pro') WHERE name = 'premium';
