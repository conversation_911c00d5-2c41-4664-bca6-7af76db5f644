-- Ajout de la colonne status à la table categories
ALTER TABLE categories
ADD COLUMN status ENUM('active', 'inactive', 'draft', 'archived') NOT NULL DEFAULT 'active' AFTER category_type;

-- Mise à jour des statuts existants basée sur is_active
UPDATE categories SET status = CASE
    WHEN is_active = 1 THEN 'active'
    ELSE 'inactive'
END;

-- Vérification
SELECT id, name, status, is_active FROM categories LIMIT 5;