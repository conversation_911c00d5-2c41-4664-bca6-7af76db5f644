/**
 * Test automatisé pour la page d'inscription
 * Utilise Puppeteer pour tester les fonctionnalités d'enregistrement
 */

const PageController = require('../page-controller');
const chalk = require('chalk');

class RegisterTest {
    constructor(options = {}) {
        this.controller = new PageController({
            headless: false, // Mode visible pour les tests
            slowMo: 200,
            debug: true,
            ...options
        });
        
        this.testData = {
            validUser: {
                email: '<EMAIL>',
                password: 'MotDePasse123!',
                confirmPassword: 'MotDePasse123!',
                firstName: 'Ahmed',
                lastName: '<PERSON> Ali'
            },
            invalidUser: {
                email: 'email-invalide',
                password: '123',
                confirmPassword: '456',
                firstName: '',
                lastName: ''
            },
            existingUser: {
                email: '<EMAIL>',
                password: 'Test123456!',
                confirmPassword: 'Test123456!'
            }
        };
    }
    
    /**
     * Exécuter tous les tests d'inscription
     */
    async runAllTests() {
        console.log(chalk.blue.bold('🧪 === TESTS D\'INSCRIPTION ==='));
        
        try {
            await this.controller.init();
            
            // Tests principaux
            await this.testPageLoad();
            await this.testLanguageSwitching();
            await this.testFormValidation();
            await this.testPasswordStrength();
            await this.testRegistrationFlow();
            await this.testResponsiveDesign();
            await this.testAccessibility();
            await this.testLoginLink();
            
            console.log(chalk.green.bold('✅ Tous les tests d\'inscription réussis!'));
            
        } catch (error) {
            console.error(chalk.red.bold('❌ Échec des tests:'), error.message);
            throw error;
        } finally {
            await this.controller.close();
        }
    }
    
    /**
     * Test du chargement de la page
     */
    async testPageLoad() {
        console.log(chalk.cyan('\n📄 Test: Chargement de la page d\'inscription'));
        
        await this.controller.navigateTo('register.html');
        
        // Vérifier les éléments essentiels
        await this.controller.waitForElement('#registerForm');
        await this.controller.waitForElement('#email');
        await this.controller.waitForElement('#password');
        await this.controller.waitForElement('#confirmPassword');
        await this.controller.waitForElement('#registerBtn');
        await this.controller.waitForElement('.lang-toggle');
        
        // Vérifier le titre
        const pageInfo = await this.controller.getPageInfo();
        console.log(chalk.green(`✅ Page chargée: ${pageInfo.title}`));
        console.log(chalk.gray(`   Langue: ${pageInfo.language}, Direction: ${pageInfo.direction}`));
        
        await this.controller.takeScreenshot('register_page_loaded');
    }
    
    /**
     * Test du changement de langue
     */
    async testLanguageSwitching() {
        console.log(chalk.cyan('\n🌍 Test: Changement de langue'));
        
        const languages = ['fr', 'en', 'ar'];
        
        for (const lang of languages) {
            await this.controller.changeLanguage(lang);
            await this.controller.wait(1000);
            
            const pageInfo = await this.controller.getPageInfo();
            console.log(chalk.green(`✅ Langue changée vers: ${lang} (${pageInfo.language})`));
            
            await this.controller.takeScreenshot(`register_language_${lang}`);
        }
    }
    
    /**
     * Test de validation du formulaire
     */
    async testFormValidation() {
        console.log(chalk.cyan('\n✅ Test: Validation du formulaire'));
        
        // Test avec champs vides
        await this.controller.clickElement('#registerBtn');
        await this.controller.wait(1000);
        
        // Vérifier les validations HTML5
        const validations = await this.controller.evaluate(() => {
            const fields = ['email', 'password', 'confirmPassword'];
            const results = {};
            
            fields.forEach(fieldId => {
                const field = document.getElementById(fieldId);
                if (field) {
                    results[fieldId] = {
                        valid: field.checkValidity(),
                        validationMessage: field.validationMessage
                    };
                }
            });
            
            return results;
        });
        
        Object.entries(validations).forEach(([field, validation]) => {
            console.log(chalk.green(`✅ Validation ${field}: ${!validation.valid ? 'OK' : 'ÉCHEC'}`));
            if (validation.validationMessage) {
                console.log(chalk.gray(`   Message: ${validation.validationMessage}`));
            }
        });
        
        // Test avec email invalide
        await this.controller.fillForm({
            '#email': this.testData.invalidUser.email,
            '#password': this.testData.invalidUser.password,
            '#confirmPassword': this.testData.invalidUser.confirmPassword
        });
        
        await this.controller.clickElement('#registerBtn');
        await this.controller.wait(1000);
        
        await this.controller.takeScreenshot('register_form_validation');
    }
    
    /**
     * Test de la force du mot de passe
     */
    async testPasswordStrength() {
        console.log(chalk.cyan('\n🔒 Test: Force du mot de passe'));
        
        const passwords = [
            { value: '123', expected: 'faible' },
            { value: 'password', expected: 'faible' },
            { value: 'Password123', expected: 'moyen' },
            { value: 'MotDePasse123!', expected: 'fort' }
        ];
        
        for (const test of passwords) {
            // Nettoyer le champ
            await this.controller.fillForm({ '#password': '' });
            await this.controller.wait(500);
            
            // Saisir le mot de passe
            await this.controller.fillForm({ '#password': test.value });
            await this.controller.wait(1000);
            
            // Vérifier l'indicateur de force
            const strengthInfo = await this.controller.evaluate(() => {
                const indicator = document.querySelector('.password-strength, .strength-indicator');
                const strengthText = document.querySelector('.strength-text');
                
                return {
                    hasIndicator: !!indicator,
                    strengthClass: indicator ? Array.from(indicator.classList).join(' ') : null,
                    strengthText: strengthText ? strengthText.textContent.trim() : null
                };
            });
            
            console.log(chalk.green(`✅ Mot de passe "${test.value}":`));
            console.log(chalk.gray(`   Indicateur présent: ${strengthInfo.hasIndicator}`));
            if (strengthInfo.strengthText) {
                console.log(chalk.gray(`   Force détectée: ${strengthInfo.strengthText}`));
            }
            
            await this.controller.takeScreenshot(`password_strength_${test.expected}`);
        }
    }
    
    /**
     * Test du flux d'inscription
     */
    async testRegistrationFlow() {
        console.log(chalk.cyan('\n📝 Test: Flux d\'inscription'));
        
        // Nettoyer tous les champs
        await this.controller.fillForm({
            '#email': '',
            '#password': '',
            '#confirmPassword': '',
            '#firstName': '',
            '#lastName': ''
        });
        
        // Remplir avec des données valides
        const formData = {};
        Object.entries(this.testData.validUser).forEach(([key, value]) => {
            if (key === 'firstName') formData['#firstName'] = value;
            else if (key === 'lastName') formData['#lastName'] = value;
            else formData[`#${key}`] = value;
        });
        
        await this.controller.fillForm(formData);
        await this.controller.takeScreenshot('register_form_filled');
        
        // Vérifier la correspondance des mots de passe
        const passwordMatch = await this.controller.evaluate(() => {
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            return password === confirmPassword;
        });
        
        console.log(chalk.green(`✅ Correspondance des mots de passe: ${passwordMatch ? 'OK' : 'ÉCHEC'}`));
        
        // Cliquer sur le bouton d'inscription
        await this.controller.clickElement('#registerBtn');
        await this.controller.wait(2000);
        
        // Vérifier l'état du bouton
        const buttonState = await this.controller.evaluate(() => {
            const btn = document.getElementById('registerBtn');
            return {
                disabled: btn.disabled,
                text: btn.textContent.trim(),
                classList: Array.from(btn.classList)
            };
        });
        
        console.log(chalk.green(`✅ État du bouton après clic:`));
        console.log(chalk.gray(`   Désactivé: ${buttonState.disabled}`));
        console.log(chalk.gray(`   Texte: ${buttonState.text}`));
        
        await this.controller.takeScreenshot('register_attempt');
        
        // Attendre la réponse
        await this.controller.wait(3000);
        
        // Vérifier les messages
        const messages = await this.controller.evaluate(() => {
            const errorMsg = document.querySelector('.error-message, .alert-danger');
            const successMsg = document.querySelector('.success-message, .alert-success');
            
            return {
                error: errorMsg ? errorMsg.textContent.trim() : null,
                success: successMsg ? successMsg.textContent.trim() : null
            };
        });
        
        if (messages.error) {
            console.log(chalk.yellow(`⚠️  Message d'erreur: ${messages.error}`));
        }
        
        if (messages.success) {
            console.log(chalk.green(`✅ Message de succès: ${messages.success}`));
        }
        
        await this.controller.takeScreenshot('register_result');
    }
    
    /**
     * Test du design responsive
     */
    async testResponsiveDesign() {
        console.log(chalk.cyan('\n📱 Test: Design responsive'));
        
        const results = await this.controller.testResponsiveness();
        
        results.forEach(result => {
            if (result.success) {
                console.log(chalk.green(`✅ ${result.viewport.name}: OK`));
            } else {
                console.log(chalk.red(`❌ ${result.viewport.name}: ${result.error}`));
            }
        });
        
        return results;
    }
    
    /**
     * Test d'accessibilité
     */
    async testAccessibility() {
        console.log(chalk.cyan('\n♿ Test: Accessibilité'));
        
        const accessibility = await this.controller.evaluate(() => {
            const checks = {
                emailLabel: !!document.querySelector('label[for="email"]'),
                passwordLabel: !!document.querySelector('label[for="password"]'),
                confirmPasswordLabel: !!document.querySelector('label[for="confirmPassword"]'),
                formRole: !!document.querySelector('form[role]'),
                langAttribute: !!document.documentElement.getAttribute('lang'),
                ariaLabels: document.querySelectorAll('[aria-label]').length > 0,
                headingStructure: document.querySelectorAll('h1, h2, h3').length > 0
            };
            
            return checks;
        });
        
        Object.entries(accessibility).forEach(([check, passed]) => {
            console.log(chalk[passed ? 'green' : 'red'](`${passed ? '✅' : '❌'} ${check}: ${passed ? 'OK' : 'MANQUANT'}`));
        });
        
        // Test de navigation au clavier
        console.log(chalk.cyan('🎹 Test de navigation clavier...'));
        
        const tabOrder = [];
        for (let i = 0; i < 6; i++) {
            await this.controller.page.keyboard.press('Tab');
            await this.controller.wait(300);
            
            const activeElement = await this.controller.evaluate(() => {
                const active = document.activeElement;
                return {
                    tagName: active.tagName,
                    id: active.id,
                    type: active.type,
                    className: active.className
                };
            });
            
            tabOrder.push(activeElement);
        }
        
        console.log(chalk.green('✅ Ordre de tabulation:'));
        tabOrder.forEach((element, index) => {
            console.log(chalk.gray(`   ${index + 1}. ${element.tagName}${element.id ? '#' + element.id : ''}${element.type ? '[' + element.type + ']' : ''}`));
        });
        
        await this.controller.takeScreenshot('register_keyboard_navigation');
    }
    
    /**
     * Test du lien vers la connexion
     */
    async testLoginLink() {
        console.log(chalk.cyan('\n🔗 Test: Lien vers la connexion'));
        
        await this.controller.navigateTo('register.html');
        
        const loginLink = await this.controller.evaluate(() => {
            const link = document.querySelector('a[href*="login"]');
            return link ? {
                href: link.href,
                text: link.textContent.trim(),
                exists: true
            } : { exists: false };
        });
        
        if (loginLink.exists) {
            console.log(chalk.green(`✅ Lien de connexion trouvé: ${loginLink.text}`));
            console.log(chalk.gray(`   URL: ${loginLink.href}`));
            
            await this.controller.clickElement('a[href*="login"]', { waitForNavigation: true });
            
            const pageInfo = await this.controller.getPageInfo();
            console.log(chalk.green(`✅ Navigation vers: ${pageInfo.title}`));
            
            await this.controller.takeScreenshot('navigated_to_login');
        } else {
            console.log(chalk.red('❌ Lien de connexion non trouvé'));
        }
    }
    
    /**
     * Test des conditions d'utilisation et politique de confidentialité
     */
    async testTermsAndPrivacy() {
        console.log(chalk.cyan('\n📋 Test: Conditions et confidentialité'));
        
        await this.controller.navigateTo('register.html');
        
        const links = await this.controller.evaluate(() => {
            const termsLink = document.querySelector('a[href*="terms"], a[href*="conditions"]');
            const privacyLink = document.querySelector('a[href*="privacy"], a[href*="confidentialite"]');
            const checkbox = document.querySelector('input[type="checkbox"]');
            
            return {
                terms: termsLink ? {
                    href: termsLink.href,
                    text: termsLink.textContent.trim(),
                    exists: true
                } : { exists: false },
                privacy: privacyLink ? {
                    href: privacyLink.href,
                    text: privacyLink.textContent.trim(),
                    exists: true
                } : { exists: false },
                checkbox: checkbox ? {
                    required: checkbox.required,
                    exists: true
                } : { exists: false }
            };
        });
        
        console.log(chalk.green(`✅ Lien conditions: ${links.terms.exists ? 'Présent' : 'Absent'}`));
        console.log(chalk.green(`✅ Lien confidentialité: ${links.privacy.exists ? 'Présent' : 'Absent'}`));
        console.log(chalk.green(`✅ Case à cocher: ${links.checkbox.exists ? 'Présente' : 'Absente'}`));
        
        if (links.checkbox.exists) {
            console.log(chalk.gray(`   Obligatoire: ${links.checkbox.required}`));
        }
    }
}

// Exporter la classe
module.exports = RegisterTest;

// Exécution directe si le fichier est appelé
if (require.main === module) {
    const test = new RegisterTest();
    test.runAllTests().catch(console.error);
}