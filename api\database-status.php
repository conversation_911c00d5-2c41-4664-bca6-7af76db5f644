<?php
/**
 * API pour vérifier le statut de la base de données
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Configuration de la base de données
$host = 'localhost';
$port = 3307;
$dbname = 'landingpage_new';
$username = 'root';
$password = '';

try {
    // Connexion à la base de données
    $pdo = new PDO(
        "mysql:host={$host};port={$port};dbname={$dbname};charset=utf8mb4",
        $username,
        $password,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]
    );

    // Tables importantes à vérifier
    $tables = ['users', 'stores', 'products', 'orders', 'ai_usage'];
    $tableStatus = [];

    foreach ($tables as $table) {
        try {
            // Vérifier si la table existe et compter les enregistrements
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM `{$table}`");
            $count = (int) $stmt->fetch()['count'];
            
            $status = 'EMPTY';
            if ($count > 0) {
                $status = 'ACTIVE';
            }
            
            $tableStatus[] = [
                'name' => $table,
                'count' => $count,
                'status' => $status
            ];
        } catch (Exception $e) {
            $tableStatus[] = [
                'name' => $table,
                'count' => 0,
                'status' => 'ERROR',
                'error' => $e->getMessage()
            ];
        }
    }

    // Vérifier la connexion générale
    $connectionStatus = [
        'connected' => true,
        'database' => $dbname,
        'host' => $host . ':' . $port,
        'tables' => $tableStatus
    ];

    echo json_encode([
        'success' => true,
        'data' => $connectionStatus
    ]);

} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Erreur de connexion à la base de données: ' . $e->getMessage(),
        'data' => [
            'connected' => false,
            'database' => $dbname,
            'host' => $host . ':' . $port,
            'tables' => []
        ]
    ]);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Erreur: ' . $e->getMessage()
    ]);
}
?>
