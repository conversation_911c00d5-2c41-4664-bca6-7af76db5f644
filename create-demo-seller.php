<?php
/**
 * Script pour créer un utilisateur demo seller
 */

// Configuration de la base de données
$host = 'localhost';
$port = 3307;
$dbname = 'landingpage_new';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;port=$port;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Vérifier si l'utilisateur existe déjà
    $checkQuery = "SELECT id FROM users WHERE email = '<EMAIL>'";
    $checkStmt = $pdo->prepare($checkQuery);
    $checkStmt->execute();
    $existingUser = $checkStmt->fetch();

    if ($existingUser) {
        echo "✅ L'utilisateur <EMAIL> existe déjà (ID: {$existingUser['id']})\n";
        $userId = $existingUser['id'];
    } else {
        // Créer l'utilisateur demo seller
        $insertUserQuery = "
            INSERT INTO users (
                firebase_uid, email, name, role, status, 
                created_at, updated_at, email_verified
            ) VALUES (
                'demo_seller_uid', '<EMAIL>', 'Demo Seller', 'merchant', 'active',
                NOW(), NOW(), 1
            )
        ";
        
        $pdo->prepare($insertUserQuery)->execute();
        $userId = $pdo->lastInsertId();
        echo "✅ Utilisateur demo seller créé avec l'ID: $userId\n";
    }

    // Vérifier si le store existe déjà
    $checkStoreQuery = "SELECT id FROM stores WHERE user_id = ? AND store_name = 'TechStore Algeria'";
    $checkStoreStmt = $pdo->prepare($checkStoreQuery);
    $checkStoreStmt->execute([$userId]);
    $existingStore = $checkStoreStmt->fetch();

    if ($existingStore) {
        echo "✅ Le store TechStore Algeria existe déjà (ID: {$existingStore['id']})\n";
        $storeId = $existingStore['id'];
    } else {
        // Créer le store pour le demo seller
        $insertStoreQuery = "
            INSERT INTO stores (
                user_id, store_name, store_name_ar, store_name_en,
                description, description_ar, description_en,
                subdomain, status, verification_status,
                created_at, updated_at
            ) VALUES (
                ?, 'TechStore Algeria', 'متجر التكنولوجيا الجزائر', 'TechStore Algeria',
                'Votre destination pour les dernières technologies', 
                'وجهتك للتقنيات الحديثة',
                'Your destination for the latest technologies',
                'techstore-algeria', 'active', 'verified',
                NOW(), NOW()
            )
        ";
        
        $pdo->prepare($insertStoreQuery)->execute([$userId]);
        $storeId = $pdo->lastInsertId();
        echo "✅ Store TechStore Algeria créé avec l'ID: $storeId\n";
    }

    // Vérifier combien de produits existent pour ce store
    $countProductsQuery = "SELECT COUNT(*) as count FROM products WHERE store_id = ?";
    $countStmt = $pdo->prepare($countProductsQuery);
    $countStmt->execute([$storeId]);
    $productCount = $countStmt->fetch()['count'];

    if ($productCount >= 10) {
        echo "✅ Le store a déjà $productCount produits\n";
    } else {
        // Créer des produits de démonstration
        $products = [
            ['Smartphone Samsung Galaxy S24', 'Smartphone haut de gamme avec écran AMOLED', 89999, 'https://images.samsung.com/is/image/samsung/p6pim/fr/sm-s921bzadeub/gallery/fr-galaxy-s24-s921-sm-s921bzadeub-539573-1.jpg'],
            ['iPhone 15 Pro', 'iPhone dernière génération avec puce A17 Pro', 129999, 'https://store.storeimages.cdn-apple.com/4982/as-images.apple.com/is/iphone-15-pro-finish-select-202309-6-1inch-naturaltitanium.jpg'],
            ['MacBook Air M2', 'Ordinateur portable ultra-fin avec puce M2', 149999, 'https://store.storeimages.cdn-apple.com/4982/as-images.apple.com/is/macbook-air-midnight-select-20220606.jpg'],
            ['Dell XPS 13', 'Ultrabook professionnel avec écran InfinityEdge', 119999, 'https://i.dell.com/is/image/DellContent/content/dam/ss2/product-images/dell-client-products/notebooks/xps-notebooks/13-9315/media-gallery/notebook-xps-13-9315-nt-blue-gallery-4.psd'],
            ['iPad Pro 12.9"', 'Tablette professionnelle avec écran Liquid Retina', 109999, 'https://store.storeimages.cdn-apple.com/4982/as-images.apple.com/is/ipad-pro-12-select-wifi-spacegray-202210.jpg'],
            ['AirPods Pro 2', 'Écouteurs sans fil avec réduction de bruit active', 29999, 'https://store.storeimages.cdn-apple.com/4982/as-images.apple.com/is/MQD83.jpg'],
            ['Sony WH-1000XM5', 'Casque sans fil avec réduction de bruit', 39999, 'https://www.sony.fr/image/5d02da5df552836db894cead8a68f5f3?fmt=pjpeg&wid=330&bgcolor=FFFFFF&bgc=FFFFFF'],
            ['Nintendo Switch OLED', 'Console de jeu portable avec écran OLED', 34999, 'https://assets.nintendo.com/image/upload/c_fill,w_1200/q_auto:best/f_auto/dpr_2.0/ncom/software/switch/70010000000964/811316ca6c7a5e05b0e9b7b8b8e5b8b5'],
            ['PlayStation 5', 'Console de jeu nouvelle génération', 59999, 'https://gmedia.playstation.com/is/image/SIEPDC/ps5-product-thumbnail-01-en-14sep21'],
            ['Apple Watch Series 9', 'Montre connectée avec GPS et cellular', 44999, 'https://store.storeimages.cdn-apple.com/4982/as-images.apple.com/is/watch-s9-gps-select-202309-41mm-pink-sport-band.jpg']
        ];

        foreach ($products as $index => $product) {
            $insertProductQuery = "
                INSERT INTO products (
                    store_id, name, description, price, image_url,
                    status, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, 'active', NOW(), NOW())
            ";
            
            $pdo->prepare($insertProductQuery)->execute([
                $storeId,
                $product[0],
                $product[1],
                $product[2],
                $product[3]
            ]);
        }
        
        echo "✅ 10 produits de démonstration créés pour le store\n";
    }

    echo "\n🎉 Configuration terminée !\n";
    echo "📧 Email: <EMAIL>\n";
    echo "🔑 Mot de passe: (utiliser Firebase Auth ou créer un système de connexion)\n";
    echo "🏪 Store URL: http://localhost:8000/techstore-algeria\n";
    echo "📊 Produits: $productCount produits disponibles\n";

} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
}
?>
