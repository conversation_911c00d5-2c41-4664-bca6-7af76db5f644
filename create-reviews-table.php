<?php
require_once 'php/config/database.php';

header('Content-Type: text/html; charset=UTF-8');

echo "<h1>📝 Création de la table reviews</h1>";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .success{color:green;} .error{color:red;} .warning{color:orange;}</style>";

try {
    // 1. Vérifier si la table existe déjà
    echo "<h2>🔍 Vérification de l'existence de la table</h2>";
    
    $stmt = $pdo->query("SHOW TABLES LIKE 'reviews'");
    $exists = $stmt->fetch();
    
    if ($exists) {
        echo "<p class='warning'>⚠️ La table reviews existe déjà. Suppression...</p>";
        $pdo->exec("DROP TABLE reviews");
        echo "<p class='success'>✅ Table reviews supprimée</p>";
    }
    
    // 2. Créer la table reviews sans contrainte de clé étrangère d'abord
    echo "<h2>🏗️ Création de la table reviews</h2>";
    
    $sql = "
    CREATE TABLE reviews (
        id INT AUTO_INCREMENT PRIMARY KEY,
        product_id INT NOT NULL,
        user_id VARCHAR(255) NOT NULL,
        rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
        comment TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_product_id (product_id),
        INDEX idx_user_id (user_id),
        INDEX idx_rating (rating)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $pdo->exec($sql);
    echo "<p class='success'>✅ Table reviews créée avec succès</p>";
    
    // 3. Ajouter quelques données de test
    echo "<h2>📊 Ajout de données de test</h2>";
    
    $reviews_data = [
        [1, 'firebase_uid_1', 5, 'Excellent produit, très satisfait de mon achat !'],
        [1, 'firebase_uid_2', 4, 'Bon produit, livraison rapide.'],
        [2, 'firebase_uid_1', 3, 'Produit correct mais pourrait être amélioré.'],
    ];
    
    $stmt = $pdo->prepare("INSERT INTO reviews (product_id, user_id, rating, comment) VALUES (?, ?, ?, ?)");
    
    foreach ($reviews_data as $review) {
        $stmt->execute($review);
    }
    
    echo "<p class='success'>✅ " . count($reviews_data) . " avis de test ajoutés</p>";
    
    // 4. Vérifier la structure de la table
    echo "<h2>🔍 Vérification de la structure</h2>";
    
    $stmt = $pdo->query("DESCRIBE reviews");
    $columns = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse:collapse;'>";
    echo "<tr><th>Colonne</th><th>Type</th><th>Null</th><th>Clé</th><th>Défaut</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 5. Afficher les données de test
    echo "<h2>📋 Données de test ajoutées</h2>";
    
    $stmt = $pdo->query("
        SELECT r.*, p.name as product_name 
        FROM reviews r 
        LEFT JOIN products p ON r.product_id = p.id 
        ORDER BY r.created_at DESC
    ");
    $reviews = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse:collapse;'>";
    echo "<tr><th>ID</th><th>Produit</th><th>User ID</th><th>Note</th><th>Commentaire</th><th>Date</th></tr>";
    foreach ($reviews as $review) {
        echo "<tr>";
        echo "<td>" . $review['id'] . "</td>";
        echo "<td>" . htmlspecialchars($review['product_name']) . "</td>";
        echo "<td>" . htmlspecialchars($review['user_id']) . "</td>";
        echo "<td>" . $review['rating'] . "/5</td>";
        echo "<td>" . htmlspecialchars($review['comment']) . "</td>";
        echo "<td>" . $review['created_at'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 6. Tester la requête de l'API products
    echo "<h2>🧪 Test de la requête API products</h2>";
    
    $product_id = 1;
    $sql = "
        SELECT
            p.*,
            COALESCE(AVG(r.rating), 0) as average_rating,
            COUNT(r.id) as review_count
        FROM products p
        LEFT JOIN reviews r ON p.id = r.product_id
        WHERE p.id = ?
        GROUP BY p.id
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$product_id]);
    $product = $stmt->fetch();
    
    if ($product) {
        echo "<p class='success'>✅ Requête API testée avec succès</p>";
        echo "<p>Produit: " . htmlspecialchars($product['name']) . "</p>";
        echo "<p>Note moyenne: " . round($product['average_rating'], 1) . "/5</p>";
        echo "<p>Nombre d'avis: " . $product['review_count'] . "</p>";
    } else {
        echo "<p class='error'>❌ Erreur lors du test de la requête API</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Erreur: " . $e->getMessage() . "</p>";
}
?>
