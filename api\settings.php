<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once 'config/database.php';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Créer la table settings si elle n'existe pas
    createSettingsTableIfNotExists($db);
    
    $method = $_SERVER['REQUEST_METHOD'];
    $action = isset($_GET['action']) ? $_GET['action'] : '';
    
    switch ($method) {
        case 'GET':
            if ($action === 'all') {
                getAllSettings($db);
            } elseif ($action === 'currencies') {
                getCurrencies($db);
            } else {
                http_response_code(404);
                echo json_encode(['error' => 'Action non supportée']);
            }
            break;
            
        case 'POST':
            $input = json_decode(file_get_contents('php://input'), true);
            if ($action === 'update') {
                updateSettings($db, $input);
            } elseif ($action === 'add-currency') {
                addCurrency($db, $input);
            } else {
                http_response_code(404);
                echo json_encode(['error' => 'Action non supportée']);
            }
            break;
            
        case 'DELETE':
            $id = isset($_GET['id']) ? $_GET['id'] : null;
            if ($action === 'currency' && $id) {
                deleteCurrency($db, $id);
            } else {
                http_response_code(400);
                echo json_encode(['error' => 'ID requis']);
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Méthode non supportée']);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Erreur serveur: ' . $e->getMessage()]);
}

/**
 * Create settings and currencies tables if they don't exist
 */
function createSettingsTableIfNotExists($db)
{
    try {
        // Table des paramètres généraux
        $createSettingsQuery = "
            CREATE TABLE IF NOT EXISTS settings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                setting_key VARCHAR(255) UNIQUE NOT NULL,
                setting_value TEXT NULL,
                setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
                description TEXT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_setting_key (setting_key)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        // Table des devises
        $createCurrenciesQuery = "
            CREATE TABLE IF NOT EXISTS currencies (
                id INT AUTO_INCREMENT PRIMARY KEY,
                code VARCHAR(3) UNIQUE NOT NULL,
                name VARCHAR(100) NOT NULL,
                symbol VARCHAR(10) NOT NULL,
                exchange_rate DECIMAL(10,4) DEFAULT 1.0000,
                is_default TINYINT(1) DEFAULT 0,
                is_active TINYINT(1) DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_code (code),
                INDEX idx_is_default (is_default),
                INDEX idx_is_active (is_active)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        $db->exec($createSettingsQuery);
        $db->exec($createCurrenciesQuery);
        
        // Insérer les devises par défaut
        $defaultCurrencies = [
            ['USD', 'US Dollar', '$', 1.0000, 1],
            ['EUR', 'Euro', '€', 0.85, 0],
            ['GBP', 'British Pound', '£', 0.73, 0],
            ['JPY', 'Japanese Yen', '¥', 110.0, 0],
            ['CAD', 'Canadian Dollar', 'C$', 1.25, 0],
            ['AUD', 'Australian Dollar', 'A$', 1.35, 0],
            ['CHF', 'Swiss Franc', 'CHF', 0.92, 0],
            ['CNY', 'Chinese Yuan', '¥', 6.45, 0],
            ['MAD', 'Moroccan Dirham', 'MAD', 9.0, 0],
            ['TND', 'Tunisian Dinar', 'TND', 2.8, 0]
        ];
        
        $insertCurrencyQuery = "
            INSERT IGNORE INTO currencies (code, name, symbol, exchange_rate, is_default, created_at)
            VALUES (?, ?, ?, ?, ?, NOW())
        ";
        
        $stmt = $db->prepare($insertCurrencyQuery);
        foreach ($defaultCurrencies as $currency) {
            $stmt->execute($currency);
        }
        
        // Insérer les paramètres par défaut
        $defaultSettings = [
            ['default_currency', 'USD', 'string', 'Devise par défaut du système'],
            ['site_name', 'صفحات هبوط للجميع', 'string', 'Nom du site'],
            ['admin_email', '<EMAIL>', 'string', 'Email administrateur'],
            ['max_upload_size', '10', 'number', 'Taille maximale de téléchargement en MB'],
            ['enable_registration', 'true', 'boolean', 'Autoriser les nouvelles inscriptions'],
            ['maintenance_mode', 'false', 'boolean', 'Mode maintenance'],
            ['supported_languages', '["ar", "fr", "en"]', 'json', 'Langues supportées']
        ];
        
        $insertSettingQuery = "
            INSERT IGNORE INTO settings (setting_key, setting_value, setting_type, description, created_at)
            VALUES (?, ?, ?, ?, NOW())
        ";
        
        $stmt = $db->prepare($insertSettingQuery);
        foreach ($defaultSettings as $setting) {
            $stmt->execute($setting);
        }
        
    } catch (Exception $e) {
        error_log("Erreur création tables settings: " . $e->getMessage());
    }
}

/**
 * Get all settings
 */
function getAllSettings($db)
{
    try {
        $settingsQuery = "SELECT * FROM settings ORDER BY setting_key";
        $currenciesQuery = "SELECT * FROM currencies ORDER BY is_default DESC, code ASC";
        
        $settingsStmt = $db->query($settingsQuery);
        $settings = $settingsStmt->fetchAll(PDO::FETCH_ASSOC);
        
        $currenciesStmt = $db->query($currenciesQuery);
        $currencies = $currenciesStmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Convertir les settings en format clé-valeur
        $settingsFormatted = [];
        foreach ($settings as $setting) {
            $value = $setting['setting_value'];
            
            // Convertir selon le type
            switch ($setting['setting_type']) {
                case 'boolean':
                    $value = $value === 'true';
                    break;
                case 'number':
                    $value = is_numeric($value) ? (float)$value : $value;
                    break;
                case 'json':
                    $value = json_decode($value, true);
                    break;
            }
            
            $settingsFormatted[$setting['setting_key']] = [
                'value' => $value,
                'type' => $setting['setting_type'],
                'description' => $setting['description']
            ];
        }
        
        echo json_encode([
            'success' => true,
            'data' => [
                'settings' => $settingsFormatted,
                'currencies' => $currencies
            ]
        ]);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la récupération des paramètres: ' . $e->getMessage()]);
    }
}

/**
 * Get currencies only
 */
function getCurrencies($db)
{
    try {
        $query = "SELECT * FROM currencies WHERE is_active = 1 ORDER BY is_default DESC, code ASC";
        $stmt = $db->query($query);
        $currencies = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode([
            'success' => true,
            'data' => $currencies
        ]);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la récupération des devises: ' . $e->getMessage()]);
    }
}

/**
 * Update settings
 */
function updateSettings($db, $data)
{
    try {
        if (empty($data['settings'])) {
            http_response_code(400);
            echo json_encode(['error' => 'Paramètres requis']);
            return;
        }
        
        $db->beginTransaction();
        
        $updateQuery = "UPDATE settings SET setting_value = ?, updated_at = NOW() WHERE setting_key = ?";
        $stmt = $db->prepare($updateQuery);
        
        foreach ($data['settings'] as $key => $value) {
            // Convertir la valeur selon le type
            if (is_bool($value)) {
                $value = $value ? 'true' : 'false';
            } elseif (is_array($value)) {
                $value = json_encode($value);
            }
            
            $stmt->execute([$value, $key]);
        }
        
        $db->commit();
        
        echo json_encode([
            'success' => true,
            'data' => [
                'message' => 'Paramètres mis à jour avec succès'
            ]
        ]);
        
    } catch (Exception $e) {
        $db->rollBack();
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la mise à jour: ' . $e->getMessage()]);
    }
}

/**
 * Add new currency
 */
function addCurrency($db, $data)
{
    try {
        if (empty($data['code']) || empty($data['name']) || empty($data['symbol'])) {
            http_response_code(400);
            echo json_encode(['error' => 'Code, nom et symbole requis']);
            return;
        }
        
        $query = "
            INSERT INTO currencies (code, name, symbol, exchange_rate, is_active, created_at)
            VALUES (?, ?, ?, ?, 1, NOW())
        ";
        
        $stmt = $db->prepare($query);
        $stmt->execute([
            strtoupper($data['code']),
            $data['name'],
            $data['symbol'],
            $data['exchange_rate'] ?? 1.0
        ]);
        
        $currencyId = $db->lastInsertId();
        
        echo json_encode([
            'success' => true,
            'data' => [
                'id' => $currencyId,
                'message' => 'Devise ajoutée avec succès'
            ]
        ]);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de l\'ajout: ' . $e->getMessage()]);
    }
}

/**
 * Delete currency
 */
function deleteCurrency($db, $id)
{
    try {
        // Vérifier que ce n'est pas la devise par défaut
        $checkQuery = "SELECT is_default FROM currencies WHERE id = ?";
        $stmt = $db->prepare($checkQuery);
        $stmt->execute([$id]);
        $currency = $stmt->fetch();
        
        if (!$currency) {
            http_response_code(404);
            echo json_encode(['error' => 'Devise non trouvée']);
            return;
        }
        
        if ($currency['is_default']) {
            http_response_code(400);
            echo json_encode(['error' => 'Impossible de supprimer la devise par défaut']);
            return;
        }
        
        $deleteQuery = "DELETE FROM currencies WHERE id = ?";
        $stmt = $db->prepare($deleteQuery);
        $stmt->execute([$id]);
        
        echo json_encode([
            'success' => true,
            'data' => [
                'message' => 'Devise supprimée avec succès'
            ]
        ]);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la suppression: ' . $e->getMessage()]);
    }
}
?>
