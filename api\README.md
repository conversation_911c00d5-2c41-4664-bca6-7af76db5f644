# Landing Page SaaS - API Documentation

## 📋 Vue d'ensemble

API RESTful complète pour la plateforme SaaS "صفحات هبوط للجميع" (Landing Pages pour Tous). Cette API gère les produits, commandes, paiements, magasins, utilisateurs, analytics et intégrations IA.

## 🚀 Démarrage rapide

### Prérequis
- PHP 7.4+
- MySQL 5.7+
- Apache avec mod_rewrite
- Extension PHP PDO
- Firebase Admin SDK (pour l'authentification)

### Installation

1. **<PERSON><PERSON><PERSON> le projet**
```bash
git clone <repository-url>
cd LandingPage-New/api
```

2. **Configuration de la base de données**
```bash
# Importer le schéma de base de données
mysql -u root -p landingpage_new < ../database_updates_fixed.sql
```

3. **Configuration**
```php
// Modifier config/database.php avec vos paramètres
define('DB_HOST', 'localhost');
define('DB_PORT', '3307');
define('DB_NAME', 'landingpage_new');
define('DB_USER', 'root');
define('DB_PASS', '');
```

4. **Permissions**
```bash
# Donner les permissions appropriées
chmod 755 api/
chmod 644 api/*.php
chmod 600 api/config/database.php
```

## 🔐 Authentification

L'API utilise Firebase JWT pour l'authentification. Incluez le token dans le header Authorization :

```http
Authorization: Bearer <firebase_jwt_token>
```

### Exemple de requête authentifiée
```javascript
fetch('/api/products', {
    method: 'GET',
    headers: {
        'Authorization': 'Bearer ' + firebaseToken,
        'Content-Type': 'application/json'
    }
})
```

## 📚 Endpoints disponibles

### 🛍️ Produits (`/api/products`)

| Méthode | Endpoint | Description |
|---------|----------|-------------|
| GET | `/api/products` | Liste des produits avec pagination |
| GET | `/api/products/{id}` | Détails d'un produit |
| POST | `/api/products` | Créer un nouveau produit |
| PUT | `/api/products/{id}` | Mettre à jour un produit |
| DELETE | `/api/products/{id}` | Supprimer un produit |

**Filtres disponibles :** `category`, `status`, `price_min`, `price_max`, `search`, `page`, `limit`

### 📦 Commandes (`/api/orders`)

| Méthode | Endpoint | Description |
|---------|----------|-------------|
| GET | `/api/orders` | Liste des commandes |
| GET | `/api/orders/{id}` | Détails d'une commande |
| GET | `/api/orders/stats` | Statistiques des commandes |
| POST | `/api/orders` | Créer une nouvelle commande |
| PUT | `/api/orders/{id}` | Mettre à jour le statut |
| PUT | `/api/orders/{id}/details` | Mettre à jour les détails |
| DELETE | `/api/orders/{id}` | Supprimer une commande |

**Statuts :** `pending`, `confirmed`, `processing`, `shipped`, `delivered`, `cancelled`, `refunded`

### 💳 Paiements (`/api/payments`)

| Méthode | Endpoint | Description |
|---------|----------|-------------|
| GET | `/api/payments` | Liste des paiements |
| GET | `/api/payments/methods` | Méthodes de paiement |
| POST | `/api/payments` | Enregistrer un paiement |
| POST | `/api/payments/methods` | Ajouter une méthode |
| PUT | `/api/payments/methods/{id}` | Modifier une méthode |
| DELETE | `/api/payments/methods/{id}` | Supprimer une méthode |

**Méthodes supportées :** `baridimob`, `ccp`, `bank_transfer`, `cod`, `edahabia`

### 🏪 Magasins (`/api/stores`)

| Méthode | Endpoint | Description |
|---------|----------|-------------|
| GET | `/api/stores` | Informations du magasin |
| GET | `/api/stores/all` | Tous les magasins (admin) |
| POST | `/api/stores` | Créer un magasin |
| PUT | `/api/stores` | Mettre à jour le magasin |
| DELETE | `/api/stores/{id}` | Supprimer un magasin |

### 👥 Utilisateurs (`/api/users`)

| Méthode | Endpoint | Description |
|---------|----------|-------------|
| GET | `/api/users/profile` | Profil utilisateur |
| GET | `/api/users/roles` | Rôles de l'utilisateur |
| GET | `/api/users/all` | Tous les utilisateurs (admin) |
| POST | `/api/users/roles` | Assigner un rôle |
| PUT | `/api/users/profile` | Mettre à jour le profil |
| DELETE | `/api/users/roles/{id}` | Supprimer un rôle |

**Rôles :** `admin`, `seller`, `agent`

### 🤖 IA (`/api/ai`)

| Méthode | Endpoint | Description |
|---------|----------|-------------|
| GET | `/api/ai/keys` | Clés IA configurées |
| GET | `/api/ai/usage` | Statistiques d'utilisation |
| GET | `/api/ai/analytics` | Analytics détaillées |
| POST | `/api/ai/keys` | Ajouter une clé IA |
| PUT | `/api/ai/keys/{id}` | Modifier une clé |
| DELETE | `/api/ai/keys/{id}` | Supprimer une clé |

**Providers :** `openai`, `claude`, `gemini`, `mistral`

### 📊 Analytics (`/api/analytics`)

| Méthode | Endpoint | Description |
|---------|----------|-------------|
| GET | `/api/analytics/dashboard` | Vue d'ensemble |
| GET | `/api/analytics/sales` | Analytics des ventes |
| GET | `/api/analytics/products` | Analytics des produits |
| GET | `/api/analytics/orders` | Analytics des commandes |
| GET | `/api/analytics/customers` | Analytics des clients |

**Périodes :** `today`, `week`, `month`, `quarter`, `year`, `custom`

## 📝 Format des réponses

### Succès
```json
{
    "success": true,
    "data": {
        "id": 1,
        "name": "Produit exemple"
    },
    "message": "Operation successful"
}
```

### Erreur
```json
{
    "success": false,
    "error": "Error message",
    "code": 400
}
```

### Validation
```json
{
    "success": false,
    "error": "Validation failed",
    "code": 422,
    "details": {
        "field": "Field is required"
    }
}
```

## 🔧 Exemples d'utilisation

### Créer un produit
```javascript
const product = {
    name: "Smartphone Samsung",
    name_ar: "هاتف سامسونغ ذكي",
    description: "Smartphone dernière génération",
    price: 45000.00,
    category: "electronics",
    sku: "SAMSUNG-001",
    stock_quantity: 50,
    images: ["samsung1.jpg", "samsung2.jpg"]
};

fetch('/api/products', {
    method: 'POST',
    headers: {
        'Authorization': 'Bearer ' + token,
        'Content-Type': 'application/json'
    },
    body: JSON.stringify(product)
})
.then(response => response.json())
.then(data => console.log(data));
```

### Récupérer les commandes avec filtres
```javascript
const params = new URLSearchParams({
    status: 'pending',
    date_from: '2024-01-01',
    page: 1,
    limit: 20
});

fetch(`/api/orders?${params}`, {
    headers: {
        'Authorization': 'Bearer ' + token
    }
})
.then(response => response.json())
.then(data => {
    console.log('Orders:', data.data.orders);
    console.log('Pagination:', data.data.pagination);
});
```

### Mettre à jour les paramètres du magasin
```javascript
const storeSettings = {
    store_name: "Mon Super Magasin",
    store_name_ar: "متجري الرائع",
    phone: "+213555123456",
    email: "<EMAIL>",
    social_media: {
        facebook: "https://facebook.com/monmagasin",
        instagram: "@monmagasin"
    },
    settings: {
        currency: "DZD",
        language: "ar",
        timezone: "Africa/Algiers"
    }
};

fetch('/api/stores', {
    method: 'PUT',
    headers: {
        'Authorization': 'Bearer ' + token,
        'Content-Type': 'application/json'
    },
    body: JSON.stringify(storeSettings)
})
.then(response => response.json())
.then(data => console.log(data));
```

## 🔒 Sécurité

### Headers de sécurité
- `X-Content-Type-Options: nosniff`
- `X-Frame-Options: DENY`
- `X-XSS-Protection: 1; mode=block`
- `Content-Security-Policy`

### Validation des données
- Sanitisation automatique des entrées
- Validation des types de données
- Protection contre l'injection SQL
- Chiffrement des clés API sensibles

### Rate limiting
- 1000 requêtes/heure pour les utilisateurs authentifiés
- 100 requêtes/heure pour les requêtes non authentifiées

## 📊 Monitoring

### Health Check
```http
GET /api/health
```

Retourne l'état de santé de l'API, la connexion à la base de données et les métriques système.

### Logs
- Logs d'erreur : `logs/api_error.log`
- Logs d'accès : `logs/api_access.log`
- Logs PHP : `logs/php_errors.log`

## 🚀 Performance

### Optimisations
- Compression gzip activée
- Cache des requêtes fréquentes
- Pagination automatique
- Indexation optimisée de la base de données

### Métriques
- Temps de réponse moyen : < 200ms
- Disponibilité : 99.9%
- Throughput : 1000 req/sec

## 🔄 Versioning

L'API suit le versioning sémantique :
- Version actuelle : `1.0.0`
- Endpoint de version : `/api/v1/`
- Rétrocompatibilité maintenue

## 🐛 Debugging

### Mode développement
```php
// Dans config/database.php
define('DEBUG_MODE', true);
define('LOG_LEVEL', 'debug');
```

### Codes d'erreur courants
- `400` : Données invalides
- `401` : Token manquant ou invalide
- `403` : Permissions insuffisantes
- `404` : Ressource non trouvée
- `422` : Erreur de validation
- `500` : Erreur serveur

## 📞 Support

- **Documentation complète :** `/api/docs`
- **Email :** <EMAIL>
- **GitHub :** [Repository](https://github.com/landingpage-saas/api)
- **Status page :** [status.landingpage-saas.com](https://status.landingpage-saas.com)

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

---

**Dernière mise à jour :** Janvier 2024
**Version API :** 1.0.0
**Compatibilité :** PHP 7.4+, MySQL 5.7+