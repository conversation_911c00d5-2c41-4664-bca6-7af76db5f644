-- Ajout de la colonne store_id à la table landing_pages
ALTER TABLE landing_pages
ADD COLUMN store_id INT NOT NULL AFTER id,
ADD CONSTRAINT fk_landing_pages_store
FOREIGN KEY (store_id) REFERENCES stores(id);

-- Mise à jour des landing pages existantes pour le store 3
UPDATE landing_pages SET store_id = 3 WHERE store_id = 0 OR store_id IS NULL;

-- Vérification
SELECT id, store_id, title FROM landing_pages LIMIT 5;