-- Structure de la table `templates`
DROP TABLE IF EXISTS `templates`;
CREATE TABLE IF NOT EXISTS `templates` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` text,
  `category` varchar(50) NOT NULL DEFAULT 'general',
  `preview_image` varchar(255) DEFAULT NULL,
  `html_content` longtext NOT NULL,
  `css_content` longtext,
  `js_content` longtext,
  `features` json DEFAULT NULL,
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Structure de la table `landing_pages`
DROP TABLE IF EXISTS `landing_pages`;
CREATE TABLE IF NOT EXISTS `landing_pages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL,
  `store_id` int(11) NOT NULL,
  `template_id` int(11) NOT NULL,
  `content` longtext,
  `custom_css` longtext,
  `custom_js` longtext,
  `meta_title` varchar(255) DEFAULT NULL,
  `meta_description` text,
  `status` enum('draft','published','archived') NOT NULL DEFAULT 'draft',
  `views` int(11) NOT NULL DEFAULT '0',
  `conversions` int(11) NOT NULL DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `slug` (`slug`),
  KEY `store_id` (`store_id`),
  KEY `template_id` (`template_id`),
  CONSTRAINT `landing_pages_ibfk_1` FOREIGN KEY (`store_id`) REFERENCES `stores` (`id`) ON DELETE CASCADE,
  CONSTRAINT `landing_pages_ibfk_2` FOREIGN KEY (`template_id`) REFERENCES `templates` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insertion des templates par défaut
INSERT INTO `templates` (`name`, `description`, `category`, `html_content`, `css_content`, `js_content`, `features`, `status`) VALUES
('E-commerce Premium', 'Template premium pour boutique en ligne', 'e-commerce', '<div class="container"><!-- Template E-commerce Premium --></div>', 'body { font-family: Arial, sans-serif; }', 'console.log("E-commerce Premium Template");', '{"features": ["Responsive", "SEO optimisé", "Haute conversion"]}', 'active'),
('Promo Flash', 'Template pour promotions limitées', 'promo', '<div class="container"><!-- Template Promo Flash --></div>', 'body { background: #f8f9fa; }', 'console.log("Promo Flash Template");', '{"features": ["Compte à rebours", "Pop-up", "CTA attractif"]}', 'active'),
('Catalogue Produit', 'Template pour catalogue de produits', 'catalogue', '<div class="container"><!-- Template Catalogue Produit --></div>', 'body { color: #333; }', 'console.log("Catalogue Produit Template");', '{"features": ["Grille produits", "Filtres", "Tri"]}', 'active'),
('Landing Page Saisonnière', 'Template pour événements saisonniers', 'seasonal', '<div class="container"><!-- Template Landing Page Saisonnière --></div>', 'body { margin: 0; }', 'console.log("Landing Page Saisonnière Template");', '{"features": ["Thème adaptable", "Animations", "Responsive"]}', 'active'),
('Présentation Produit', 'Template pour mise en avant produit', 'product', '<div class="container"><!-- Template Présentation Produit --></div>', 'body { line-height: 1.6; }', 'console.log("Présentation Produit Template");', '{"features": ["Galerie photos", "Vidéo", "Témoignages"]}', 'active');