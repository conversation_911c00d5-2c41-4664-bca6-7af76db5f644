<?php
require_once 'php/config/database.php';

header('Content-Type: text/html; charset=UTF-8');

echo "<h1>🔍 Debug du système de rôles</h1>";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .success{color:green;} .error{color:red;} .warning{color:orange;}</style>";

try {
    // Vérifier la structure de la table user_roles
    echo "<h2>🔍 Structure de la table user_roles</h2>";
    
    $stmt = $pdo->query("DESCRIBE user_roles");
    $columns = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse:collapse;'>";
    echo "<tr><th>Colonne</th><th>Type</th><th>Null</th><th>Clé</th><th>Défaut</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Supprimer et recréer la table user_roles
    echo "<h2>🔧 Correction de la table user_roles</h2>";
    
    $pdo->exec("DROP TABLE IF EXISTS user_roles");
    echo "<p class='success'>✅ Table user_roles supprimée</p>";
    
    $sql = "
    CREATE TABLE user_roles (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id VARCHAR(255) NOT NULL,
        role_id INT NOT NULL,
        assigned_by VARCHAR(255),
        assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_user_id (user_id),
        INDEX idx_role_id (role_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $pdo->exec($sql);
    echo "<p class='success'>✅ Table user_roles recréée</p>";
    
    // Vérifier la nouvelle structure
    $stmt = $pdo->query("DESCRIBE user_roles");
    $columns = $stmt->fetchAll();
    
    echo "<h3>Nouvelle structure :</h3>";
    echo "<table border='1' style='border-collapse:collapse;'>";
    echo "<tr><th>Colonne</th><th>Type</th><th>Null</th><th>Clé</th><th>Défaut</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Assigner des rôles aux utilisateurs de test
    echo "<h2>👤 Attribution de rôles aux utilisateurs de test</h2>";
    
    $user_roles = [
        ['firebase_uid_1', 'admin'],
        ['firebase_uid_2', 'merchant']
    ];
    
    $stmt = $pdo->prepare("
        INSERT INTO user_roles (user_id, role_id, assigned_by) 
        SELECT ?, r.id, 'system' 
        FROM roles r 
        WHERE r.name = ?
    ");
    
    foreach ($user_roles as $user_role) {
        $stmt->execute($user_role);
        echo "<p class='success'>✅ Rôle " . $user_role[1] . " attribué à " . $user_role[0] . "</p>";
    }
    
    // Afficher les attributions de rôles
    echo "<h2>🔗 Attributions de rôles</h2>";
    
    $stmt = $pdo->query("
        SELECT ur.user_id, r.name as role_name, r.display_name, ur.assigned_at
        FROM user_roles ur
        JOIN roles r ON ur.role_id = r.id
        ORDER BY ur.assigned_at DESC
    ");
    $assignments = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse:collapse;'>";
    echo "<tr><th>User ID</th><th>Rôle</th><th>Nom d'affichage</th><th>Attribué le</th></tr>";
    foreach ($assignments as $assignment) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($assignment['user_id']) . "</td>";
        echo "<td>" . htmlspecialchars($assignment['role_name']) . "</td>";
        echo "<td>" . htmlspecialchars($assignment['display_name']) . "</td>";
        echo "<td>" . $assignment['assigned_at'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Erreur: " . $e->getMessage() . "</p>";
}
?>
