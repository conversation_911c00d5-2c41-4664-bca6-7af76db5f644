<?php
require_once '../api/config/database.php';

// Initialize database connection
$database = new Database();
$db = $database->getConnection();

// Get all available templates
$query = "SELECT id, name, category, description, preview_image FROM templates WHERE status = 'active' ORDER BY category, name";
$stmt = $db->prepare($query);
$stmt->execute();
$templates = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Group templates by category
$grouped_templates = [];
foreach ($templates as $template) {
    $category = $template['category'];
    if (!isset($grouped_templates[$category])) {
        $grouped_templates[$category] = [];
    }
    $grouped_templates[$category][] = $template;
}

// Get all stores
$query = "SELECT id, name FROM stores ORDER BY name";
$stmt = $db->prepare($query);
$stmt->execute();
$stores = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nouvelle Landing Page</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .template-card {
            cursor: pointer;
            transition: all 0.3s ease;
            height: 100%;
        }
        .template-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .template-card.selected {
            border: 2px solid #0d6efd;
        }
        .template-preview {
            height: 200px;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            border-radius: 4px 4px 0 0;
        }
        .category-title {
            text-transform: capitalize;
            margin: 2rem 0 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #e9ecef;
        }
    </style>
</head>
<body>

<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>Nouvelle Landing Page</h2>
        <div>
            <button type="button" class="btn btn-secondary me-2" onclick="window.location.href='landing-pages.php'">
                <i class="bi bi-arrow-left"></i> Retour
            </button>
            <button type="button" class="btn btn-primary" onclick="createPage()">
                <i class="bi bi-check-lg"></i> Créer
            </button>
        </div>
    </div>

    <div class="row">
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Paramètres</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">Titre</label>
                        <input type="text" class="form-control" id="title" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Slug</label>
                        <input type="text" class="form-control" id="slug" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Boutique</label>
                        <select class="form-select" id="store_id" required>
                            <option value="">Sélectionner une boutique</option>
                            <?php foreach ($stores as $store): ?>
                            <option value="<?php echo $store['id']; ?>">
                                <?php echo htmlspecialchars($store['name']); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Statut</label>
                        <select class="form-select" id="status">
                            <option value="draft">Brouillon</option>
                            <option value="published">Publié</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Choisir un Template</h5>
                </div>
                <div class="card-body">
                    <?php foreach ($grouped_templates as $category => $templates): ?>
                    <h3 class="category-title"><?php echo htmlspecialchars($category); ?></h3>
                    <div class="row g-4 mb-4">
                        <?php foreach ($templates as $template): ?>
                        <div class="col-md-6 col-lg-4">
                            <div class="card template-card" onclick="selectTemplate(this, <?php echo $template['id']; ?>)">
                                <div class="template-preview" 
                                     style="background-image: url('<?php echo htmlspecialchars($template['preview_image']); ?>')">
                                </div>
                                <div class="card-body">
                                    <h5 class="card-title"><?php echo htmlspecialchars($template['name']); ?></h5>
                                    <p class="card-text small text-muted"><?php echo htmlspecialchars($template['description']); ?></p>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
let selectedTemplateId = null;

function selectTemplate(card, templateId) {
    // Remove selection from all cards
    document.querySelectorAll('.template-card').forEach(c => c.classList.remove('selected'));
    // Add selection to clicked card
    card.classList.add('selected');
    selectedTemplateId = templateId;
}

function createPage() {
    if (!selectedTemplateId) {
        alert('Veuillez sélectionner un template');
        return;
    }

    const title = document.getElementById('title').value;
    const slug = document.getElementById('slug').value;
    const storeId = document.getElementById('store_id').value;
    const status = document.getElementById('status').value;

    if (!title || !slug || !storeId) {
        alert('Veuillez remplir tous les champs requis');
        return;
    }

    const data = {
        title,
        slug,
        store_id: storeId,
        template_id: selectedTemplateId,
        status
    };

    fetch('/api/landing-pages.php?action=create', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer demo_token'
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            window.location.href = `edit-landing-page.php?id=${data.page_id}`;
        } else {
            alert('Erreur lors de la création : ' + data.error);
        }
    })
    .catch(error => {
        console.error('Erreur:', error);
        alert('Une erreur est survenue lors de la création');
    });
}

// Auto-generate slug from title
document.getElementById('title').addEventListener('input', function() {
    const slug = this.value
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)/g, '');
    document.getElementById('slug').value = slug;
});
</script>

</body>
</html>