<?php

/**
 * Test creating a product
 */

echo "<h2>Create Product Test</h2>\n";
echo "<pre>\n";

// Set up environment for API testing
$_SERVER['REQUEST_METHOD'] = 'POST';
$_SERVER['REQUEST_URI'] = '/api/products';
$_SERVER['HTTP_AUTHORIZATION'] = 'Bearer demo_token';

// Create sample product data
$product_data = [
    'name_ar' => 'هاتف ذكي جديد',
    'name_fr' => 'Nouveau smartphone',
    'name_en' => 'New smartphone',
    'description_ar' => 'وصف المنتج باللغة العربية',
    'description_fr' => 'Description du produit en français',
    'description_en' => 'Product description in English',
    'price' => 25000.00,
    'compare_price' => 30000.00,
    'stock_quantity' => 10,
    'status' => 'active',
    'featured' => 1,
    'tags' => ['smartphone', 'electronics', 'mobile'],
    'images' => ['image1.jpg', 'image2.jpg']
];

// Create a temporary file to simulate php://input
$temp_file = tempnam(sys_get_temp_dir(), 'product_data');
file_put_contents($temp_file, json_encode($product_data));

// Override php://input stream
stream_wrapper_unregister('php');
stream_wrapper_register('php', 'MockPhpStream');

// Capture output
ob_start();

try {
    echo "Creating product with data:\n";
    echo json_encode($product_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";

    // Include the products API
    include 'api/products.php';
} catch (Exception $e) {
    echo "Exception: " . $e->getMessage() . "\n";
} catch (Error $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

$output = ob_get_clean();
echo $output;

echo "</pre>\n";
