<?php
/**
 * Vérifier et créer la table users si nécessaire
 */

require_once __DIR__ . '/api/config/database.php';

try {
    $database = new Database();
    $db = $database->getConnection();

    echo "🔍 Vérification de la table users...\n\n";

    // Vérifier si la table users existe
    $result = $db->query("SHOW TABLES LIKE 'users'");
    $tableExists = $result->rowCount() > 0;

    if (!$tableExists) {
        echo "⚠️  Table 'users' n'existe pas. Création...\n";
        
        $createUsersTable = "
            CREATE TABLE users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                email VARCHAR(255) UNIQUE NOT NULL,
                displayName VARCHAR(255),
                role ENUM('customer', 'merchant', 'admin') DEFAULT 'customer',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_email (email),
                INDEX idx_role (role)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        $db->exec($createUsersTable);
        echo "✅ Table 'users' créée\n";
        
        // Créer l'utilisateur demo
        $insertDemo = $db->prepare("
            INSERT INTO users (email, displayName, role) 
            VALUES (?, ?, ?)
        ");
        $insertDemo->execute(['<EMAIL>', 'Demo Merchant', 'merchant']);
        echo "✅ Utilisateur demo créé\n";
        
    } else {
        echo "✅ Table 'users' existe\n";
        
        // Vérifier si l'utilisateur demo existe
        $checkDemo = $db->prepare("SELECT id FROM users WHERE email = ?");
        $checkDemo->execute(['<EMAIL>']);
        $demoUser = $checkDemo->fetch();
        
        if (!$demoUser) {
            $insertDemo = $db->prepare("
                INSERT INTO users (email, displayName, role) 
                VALUES (?, ?, ?)
            ");
            $insertDemo->execute(['<EMAIL>', 'Demo Merchant', 'merchant']);
            echo "✅ Utilisateur demo créé\n";
        } else {
            echo "✅ Utilisateur demo existe (ID: {$demoUser['id']})\n";
        }
    }

    // Afficher la structure de la table
    echo "\n📋 Structure de la table users:\n";
    $result = $db->query("DESCRIBE users");
    $columns = $result->fetchAll(PDO::FETCH_ASSOC);
    foreach ($columns as $column) {
        echo "- {$column['Field']} ({$column['Type']}) - {$column['Null']} - {$column['Default']}\n";
    }

    // Afficher les utilisateurs existants
    echo "\n👥 Utilisateurs existants:\n";
    $result = $db->query("SELECT id, email, displayName, role FROM users");
    $users = $result->fetchAll(PDO::FETCH_ASSOC);
    foreach ($users as $user) {
        echo "- ID: {$user['id']}, Email: {$user['email']}, Nom: {$user['displayName']}, Rôle: {$user['role']}\n";
    }

    echo "\n🎉 Vérification terminée !\n";

} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
}
?>
