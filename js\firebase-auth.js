/**
 * Firebase Authentication System
 * Système d'authentification Firebase avec support multilingue (AR/FR/EN) et RTL
 */

// Configuration des langues pour l'authentification
const AUTH_TRANSLATIONS = {
    ar: {
        // Boutons et actions
        login_with_google: 'تسجيل الدخول بـ Google',
        login_with_email: 'تسجيل الدخول بالبريد الإلكتروني',
        logout: 'تسجيل الخروج',
        welcome_back: 'مرحباً بعودتك',
        welcome_user: 'مرحباً',

        // Messages
        login_success: 'تم تسجيل الدخول بنجاح',
        login_error: 'خطأ في تسجيل الدخول',
        logout_success: 'تم تسجيل الخروج بنجاح',
        logout_error: 'خطأ في تسجيل الخروج',
        loading: 'جاري التحميل...',

        // Erreurs
        popup_blocked: 'تم حظر النافذة المنبثقة. يرجى السماح بالنوافذ المنبثقة',
        network_error: 'خطأ في الشبكة. يرجى المحاولة مرة أخرى',
        auth_cancelled: 'تم إلغاء تسجيل الدخول',
        unknown_error: 'حدث خطأ غير متوقع',

        // Formulaire email
        email_placeholder: 'البريد الإلكتروني',
        password_placeholder: 'كلمة المرور',
        email_required: 'البريد الإلكتروني مطلوب',
        password_required: 'كلمة المرور مطلوبة',
        invalid_email: 'البريد الإلكتروني غير صحيح',
        weak_password: 'كلمة المرور ضعيفة',

        // Navigation
        dashboard: 'لوحة التحكم',
        profile: 'الملف الشخصي',
        settings: 'الإعدادات'
    },

    fr: {
        // Boutons et actions
        login_with_google: 'Se connecter avec Google',
        login_with_email: 'Se connecter par email',
        logout: 'Se déconnecter',
        welcome_back: 'Bon retour',
        welcome_user: 'Bienvenue',

        // Messages
        login_success: 'Connexion réussie',
        login_error: 'Erreur de connexion',
        logout_success: 'Déconnexion réussie',
        logout_error: 'Erreur de déconnexion',
        loading: 'Chargement...',

        // Erreurs
        popup_blocked: 'Popup bloquée. Veuillez autoriser les popups',
        network_error: 'Erreur réseau. Veuillez réessayer',
        auth_cancelled: 'Connexion annulée',
        unknown_error: 'Une erreur inattendue s\'est produite',

        // Formulaire email
        email_placeholder: 'Adresse email',
        password_placeholder: 'Mot de passe',
        email_required: 'Email requis',
        password_required: 'Mot de passe requis',
        invalid_email: 'Email invalide',
        weak_password: 'Mot de passe trop faible',

        // Navigation
        dashboard: 'Tableau de bord',
        profile: 'Profil',
        settings: 'Paramètres'
    },

    en: {
        // Boutons et actions
        login_with_google: 'Sign in with Google',
        login_with_email: 'Sign in with Email',
        logout: 'Sign out',
        welcome_back: 'Welcome back',
        welcome_user: 'Welcome',

        // Messages
        login_success: 'Successfully signed in',
        login_error: 'Sign in error',
        logout_success: 'Successfully signed out',
        logout_error: 'Sign out error',
        loading: 'Loading...',

        // Erreurs
        popup_blocked: 'Popup blocked. Please allow popups',
        network_error: 'Network error. Please try again',
        auth_cancelled: 'Sign in cancelled',
        unknown_error: 'An unexpected error occurred',

        // Formulaire email
        email_placeholder: 'Email address',
        password_placeholder: 'Password',
        email_required: 'Email is required',
        password_required: 'Password is required',
        invalid_email: 'Invalid email',
        weak_password: 'Password is too weak',

        // Navigation
        dashboard: 'Dashboard',
        profile: 'Profile',
        settings: 'Settings'
    }
};

// Système d'authentification Firebase
window.LandingPageAuth = {
    // Configuration
    config: {
        redirectAfterLogin: '/dashboard.html',
        persistSession: true,
        autoDetectLanguage: true,
        debug: false
    },

    // État
    state: {
        initialized: false,
        user: null,
        loading: false,
        currentLanguage: 'ar'
    },

    // Utilitaires
    utils: {
        // Détecter la langue courante
        detectLanguage: () => {
            // 1. Depuis localStorage
            const storedLang = localStorage.getItem('preferred_language');
            if (storedLang && AUTH_TRANSLATIONS[storedLang]) {
                return storedLang;
            }

            // 2. Depuis l'attribut HTML
            const htmlLang = document.documentElement.getAttribute('lang');
            if (htmlLang && AUTH_TRANSLATIONS[htmlLang]) {
                return htmlLang;
            }

            // 3. Depuis la session (si disponible)
            if (window.AppState && window.AppState.currentLanguage) {
                return window.AppState.currentLanguage;
            }

            // 4. Défaut
            return 'ar';
        },

        // Obtenir une traduction
        t: (key, lang = null) => {
            const currentLang = lang || window.LandingPageAuth.state.currentLanguage;
            return AUTH_TRANSLATIONS[currentLang]?.[key] || AUTH_TRANSLATIONS.en[key] || key;
        },

        // Vérifier si la langue est RTL
        isRTL: (lang = null) => {
            const currentLang = lang || window.LandingPageAuth.state.currentLanguage;
            return currentLang === 'ar';
        },

        // Logger pour debug
        log: (...args) => {
            if (window.LandingPageAuth.config.debug) {
                console.log('[Firebase Auth]', ...args);
            }
        },

        // Afficher une notification
        showNotification: (message, type = 'info') => {
            if (typeof window.showNotification === 'function') {
                window.showNotification(message, type);
            } else if (typeof window.LandingPageApp?.utils?.showNotification === 'function') {
                window.LandingPageApp.utils.showNotification(message, type);
            } else {
                // Fallback simple
                alert(message);
            }
        },

        // Stocker les données utilisateur
        storeUserSession: (user) => {
            if (!window.LandingPageAuth.config.persistSession) return;

            const userData = {
                uid: user.uid,
                email: user.email,
                displayName: user.displayName,
                photoURL: user.photoURL,
                emailVerified: user.emailVerified,
                loginTime: Date.now()
            };

            try {
                localStorage.setItem('lp_user_session', JSON.stringify(userData));
                window.LandingPageAuth.utils.log('Session utilisateur stockée');
            } catch (error) {
                window.LandingPageAuth.utils.log('Erreur stockage session:', error);
            }
        },

        // Récupérer la session utilisateur
        getUserSession: () => {
            try {
                const userData = localStorage.getItem('lp_user_session');
                return userData ? JSON.parse(userData) : null;
            } catch (error) {
                window.LandingPageAuth.utils.log('Erreur récupération session:', error);
                return null;
            }
        },

        // Supprimer la session utilisateur
        clearUserSession: () => {
            try {
                localStorage.removeItem('lp_user_session');
                window.LandingPageAuth.utils.log('Session utilisateur supprimée');
            } catch (error) {
                window.LandingPageAuth.utils.log('Erreur suppression session:', error);
            }
        },

        // Rediriger vers le dashboard approprié selon le rôle
        redirectToDashboard: (user) => {
            // Liste des emails d'administrateurs autorisés
            const adminEmails = [
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>'
            ];

            if (user && adminEmails.includes(user.email)) {
                // Rediriger vers le dashboard admin
                window.location.href = '/dashboard.html';
            } else {
                // Rediriger vers le dashboard utilisateur
                window.location.href = '/user-dashboard.html';
            }
        }
    },

    // Méthodes d'authentification
    auth: {
        // Connexion avec Google
        signInWithGoogle: async () => {
            if (!window.LandingPageFirebase?.isInitialized()) {
                window.LandingPageAuth.utils.showNotification(
                    window.LandingPageAuth.utils.t('unknown_error'),
                    'error'
                );
                return null;
            }

            window.LandingPageAuth.state.loading = true;
            window.LandingPageAuth.ui.updateLoadingState(true);

            try {
                const auth = window.LandingPageFirebase.getAuth();
                const provider = new firebase.auth.GoogleAuthProvider();

                // Configuration du provider
                provider.addScope('email');
                provider.addScope('profile');
                provider.setCustomParameters({
                    prompt: 'select_account'
                });

                const result = await auth.signInWithPopup(provider);
                const user = result.user;

                window.LandingPageAuth.utils.log('Connexion Google réussie:', user);

                // Stocker la session
                window.LandingPageAuth.utils.storeUserSession(user);

                // Mettre à jour l'état
                window.LandingPageAuth.state.user = user;

                // Tracker l'événement
                if (window.LandingPageAnalytics?.trackEvent) {
                    window.LandingPageAnalytics.trackEvent('user_login', {
                        method: 'google',
                        user_id: user.uid
                    });
                }

                // Notification de succès
                window.LandingPageAuth.utils.showNotification(
                    window.LandingPageAuth.utils.t('login_success'),
                    'success'
                );

                // Mettre à jour l'UI
                window.LandingPageAuth.ui.updateAuthState(user);

                // Redirection optionnelle
                setTimeout(() => {
                    window.LandingPageAuth.utils.redirectToDashboard(user);
                }, 1500);

                return user;

            } catch (error) {
                window.LandingPageAuth.utils.log('Erreur connexion Google:', error);

                let errorMessage = window.LandingPageAuth.utils.t('unknown_error');

                switch (error.code) {
                    case 'auth/popup-blocked':
                        errorMessage = window.LandingPageAuth.utils.t('popup_blocked');
                        break;
                    case 'auth/popup-closed-by-user':
                        errorMessage = window.LandingPageAuth.utils.t('auth_cancelled');
                        break;
                    case 'auth/network-request-failed':
                        errorMessage = window.LandingPageAuth.utils.t('network_error');
                        break;
                }

                window.LandingPageAuth.utils.showNotification(errorMessage, 'error');
                return null;

            } finally {
                window.LandingPageAuth.state.loading = false;
                window.LandingPageAuth.ui.updateLoadingState(false);
            }
        },

        // Connexion avec email/mot de passe
        signInWithEmail: async (email, password) => {
            if (!window.LandingPageFirebase?.isInitialized()) {
                window.LandingPageAuth.utils.showNotification(
                    window.LandingPageAuth.utils.t('unknown_error'),
                    'error'
                );
                return null;
            }

            window.LandingPageAuth.state.loading = true;
            window.LandingPageAuth.ui.updateLoadingState(true);

            try {
                const auth = window.LandingPageFirebase.getAuth();
                const result = await auth.signInWithEmailAndPassword(email, password);
                const user = result.user;

                window.LandingPageAuth.utils.log('Connexion email réussie:', user);

                // Stocker la session
                window.LandingPageAuth.utils.storeUserSession(user);

                // Mettre à jour l'état
                window.LandingPageAuth.state.user = user;

                // Tracker l'événement
                if (window.LandingPageAnalytics?.trackEvent) {
                    window.LandingPageAnalytics.trackEvent('user_login', {
                        method: 'email',
                        user_id: user.uid
                    });
                }

                // Notification de succès
                window.LandingPageAuth.utils.showNotification(
                    window.LandingPageAuth.utils.t('login_success'),
                    'success'
                );

                // Mettre à jour l'UI
                window.LandingPageAuth.ui.updateAuthState(user);

                // Redirection optionnelle
                setTimeout(() => {
                    window.LandingPageAuth.utils.redirectToDashboard(user);
                }, 1500);

                return user;

            } catch (error) {
                window.LandingPageAuth.utils.log('Erreur connexion email:', error);

                let errorMessage = window.LandingPageAuth.utils.t('unknown_error');

                switch (error.code) {
                    case 'auth/invalid-email':
                        errorMessage = window.LandingPageAuth.utils.t('invalid_email');
                        break;
                    case 'auth/user-disabled':
                    case 'auth/user-not-found':
                    case 'auth/wrong-password':
                        errorMessage = window.LandingPageAuth.utils.t('login_error');
                        break;
                    case 'auth/network-request-failed':
                        errorMessage = window.LandingPageAuth.utils.t('network_error');
                        break;
                }

                window.LandingPageAuth.utils.showNotification(errorMessage, 'error');
                return null;

            } finally {
                window.LandingPageAuth.state.loading = false;
                window.LandingPageAuth.ui.updateLoadingState(false);
            }
        },

        // Déconnexion
        signOut: async () => {
            if (!window.LandingPageFirebase?.isInitialized()) {
                return;
            }

            try {
                const auth = window.LandingPageFirebase.getAuth();
                await auth.signOut();

                window.LandingPageAuth.utils.log('Déconnexion réussie');

                // Supprimer la session
                window.LandingPageAuth.utils.clearUserSession();

                // Mettre à jour l'état
                window.LandingPageAuth.state.user = null;

                // Tracker l'événement
                if (window.LandingPageAnalytics?.trackEvent) {
                    window.LandingPageAnalytics.trackEvent('user_logout');
                }

                // Notification de succès
                window.LandingPageAuth.utils.showNotification(
                    window.LandingPageAuth.utils.t('logout_success'),
                    'success'
                );

                // Mettre à jour l'UI
                window.LandingPageAuth.ui.updateAuthState(null);

            } catch (error) {
                window.LandingPageAuth.utils.log('Erreur déconnexion:', error);
                window.LandingPageAuth.utils.showNotification(
                    window.LandingPageAuth.utils.t('logout_error'),
                    'error'
                );
            }
        },

        // Vérifier l'état d'authentification
        checkAuthState: () => {
            if (!window.LandingPageFirebase?.isInitialized()) {
                return;
            }

            const auth = window.LandingPageFirebase.getAuth();

            auth.onAuthStateChanged((user) => {
                window.LandingPageAuth.utils.log('État auth changé:', user);

                if (user) {
                    // Utilisateur connecté
                    window.LandingPageAuth.state.user = user;
                    window.LandingPageAuth.utils.storeUserSession(user);
                    window.LandingPageAuth.ui.updateAuthState(user);
                } else {
                    // Utilisateur déconnecté
                    window.LandingPageAuth.state.user = null;
                    window.LandingPageAuth.utils.clearUserSession();
                    window.LandingPageAuth.ui.updateAuthState(null);
                }
            });
        }
    },

    // Interface utilisateur
    ui: {
        // Créer le bouton de connexion Google
        createGoogleSignInButton: (containerId, options = {}) => {
            const container = document.getElementById(containerId);
            if (!container) {
                window.LandingPageAuth.utils.log('Conteneur non trouvé:', containerId);
                return null;
            }

            const isRTL = window.LandingPageAuth.utils.isRTL();
            const buttonText = window.LandingPageAuth.utils.t('login_with_google');

            const button = document.createElement('button');
            button.className = `firebase-auth-btn google-signin-btn ${isRTL ? 'rtl' : 'ltr'}`;
            button.innerHTML = `
                <svg class="google-icon" viewBox="0 0 24 24" width="20" height="20">
                    <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                    <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                    <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                    <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                </svg>
                <span class="button-text">${buttonText}</span>
                <div class="loading-spinner" style="display: none;">
                    <div class="spinner"></div>
                </div>
            `;

            // Styles inline pour le bouton
            button.style.cssText = `
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 12px;
                padding: 12px 24px;
                background: white;
                border: 2px solid #dadce0;
                border-radius: 8px;
                font-size: 16px;
                font-weight: 500;
                color: #3c4043;
                cursor: pointer;
                transition: all 0.2s ease;
                min-height: 48px;
                width: 100%;
                max-width: 300px;
                font-family: inherit;
                direction: ${isRTL ? 'rtl' : 'ltr'};
            `;

            // Événements hover
            button.addEventListener('mouseenter', () => {
                button.style.backgroundColor = '#f8f9fa';
                button.style.borderColor = '#c1c7cd';
                button.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
            });

            button.addEventListener('mouseleave', () => {
                button.style.backgroundColor = 'white';
                button.style.borderColor = '#dadce0';
                button.style.boxShadow = 'none';
            });

            // Événement de clic
            button.addEventListener('click', () => {
                window.LandingPageAuth.auth.signInWithGoogle();
            });

            container.appendChild(button);
            return button;
        },

        // Créer un formulaire de connexion par email
        createEmailSignInForm: (containerId, options = {}) => {
            const container = document.getElementById(containerId);
            if (!container) {
                window.LandingPageAuth.utils.log('Conteneur non trouvé:', containerId);
                return null;
            }

            const isRTL = window.LandingPageAuth.utils.isRTL();

            const form = document.createElement('form');
            form.className = `firebase-auth-form ${isRTL ? 'rtl' : 'ltr'}`;
            form.innerHTML = `
                <div class="form-group">
                    <input
                        type="email"
                        id="auth-email"
                        placeholder="${window.LandingPageAuth.utils.t('email_placeholder')}"
                        required
                        dir="${isRTL ? 'rtl' : 'ltr'}"
                    >
                </div>
                <div class="form-group">
                    <input
                        type="password"
                        id="auth-password"
                        placeholder="${window.LandingPageAuth.utils.t('password_placeholder')}"
                        required
                        dir="${isRTL ? 'rtl' : 'ltr'}"
                    >
                </div>
                <button type="submit" class="firebase-auth-btn email-signin-btn">
                    <span class="button-text">${window.LandingPageAuth.utils.t('login_with_email')}</span>
                    <div class="loading-spinner" style="display: none;">
                        <div class="spinner"></div>
                    </div>
                </button>
            `;

            // Styles pour le formulaire
            form.style.cssText = `
                display: flex;
                flex-direction: column;
                gap: 16px;
                width: 100%;
                max-width: 300px;
                direction: ${isRTL ? 'rtl' : 'ltr'};
            `;

            // Styles pour les champs
            const inputs = form.querySelectorAll('input');
            inputs.forEach(input => {
                input.style.cssText = `
                    padding: 12px 16px;
                    border: 2px solid #dadce0;
                    border-radius: 8px;
                    font-size: 16px;
                    font-family: inherit;
                    transition: border-color 0.2s ease;
                    direction: ${isRTL ? 'rtl' : 'ltr'};
                `;

                input.addEventListener('focus', () => {
                    input.style.borderColor = '#4285f4';
                    input.style.outline = 'none';
                });

                input.addEventListener('blur', () => {
                    input.style.borderColor = '#dadce0';
                });
            });

            // Style pour le bouton
            const submitBtn = form.querySelector('.email-signin-btn');
            submitBtn.style.cssText = `
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 12px;
                padding: 12px 24px;
                background: #4285f4;
                border: none;
                border-radius: 8px;
                font-size: 16px;
                font-weight: 500;
                color: white;
                cursor: pointer;
                transition: all 0.2s ease;
                min-height: 48px;
                font-family: inherit;
            `;

            submitBtn.addEventListener('mouseenter', () => {
                submitBtn.style.backgroundColor = '#3367d6';
            });

            submitBtn.addEventListener('mouseleave', () => {
                submitBtn.style.backgroundColor = '#4285f4';
            });

            // Événement de soumission
            form.addEventListener('submit', (e) => {
                e.preventDefault();

                const email = form.querySelector('#auth-email').value.trim();
                const password = form.querySelector('#auth-password').value;

                // Validation simple
                if (!email) {
                    window.LandingPageAuth.utils.showNotification(
                        window.LandingPageAuth.utils.t('email_required'),
                        'error'
                    );
                    return;
                }

                if (!password) {
                    window.LandingPageAuth.utils.showNotification(
                        window.LandingPageAuth.utils.t('password_required'),
                        'error'
                    );
                    return;
                }

                window.LandingPageAuth.auth.signInWithEmail(email, password);
            });

            container.appendChild(form);
            return form;
        },

        // Mettre à jour l'état de chargement
        updateLoadingState: (loading) => {
            const buttons = document.querySelectorAll('.firebase-auth-btn');

            buttons.forEach(button => {
                const buttonText = button.querySelector('.button-text');
                const spinner = button.querySelector('.loading-spinner');

                if (loading) {
                    button.disabled = true;
                    button.style.opacity = '0.7';
                    if (buttonText) buttonText.style.display = 'none';
                    if (spinner) spinner.style.display = 'block';
                } else {
                    button.disabled = false;
                    button.style.opacity = '1';
                    if (buttonText) buttonText.style.display = 'block';
                    if (spinner) spinner.style.display = 'none';
                }
            });
        },

        // Mettre à jour l'état d'authentification dans l'UI
        updateAuthState: (user) => {
            // Mettre à jour les boutons de connexion/déconnexion
            const loginButtons = document.querySelectorAll('[data-auth="login"]');
            const logoutButtons = document.querySelectorAll('[data-auth="logout"]');
            const userInfo = document.querySelectorAll('[data-auth="user-info"]');

            if (user) {
                // Utilisateur connecté
                loginButtons.forEach(btn => btn.style.display = 'none');
                logoutButtons.forEach(btn => btn.style.display = 'block');

                // Afficher les informations utilisateur
                userInfo.forEach(info => {
                    info.style.display = 'block';
                    info.innerHTML = `
                        <div class="user-avatar">
                            ${user.photoURL ?
                                `<img src="${user.photoURL}" alt="${user.displayName || user.email}" style="width: 32px; height: 32px; border-radius: 50%;">` :
                                `<div style="width: 32px; height: 32px; border-radius: 50%; background: #4285f4; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">${(user.displayName || user.email).charAt(0).toUpperCase()}</div>`
                            }
                        </div>
                        <div class="user-details">
                            <div class="user-name">${user.displayName || window.LandingPageAuth.utils.t('welcome_user')}</div>
                            <div class="user-email" style="font-size: 12px; color: #666;">${user.email}</div>
                        </div>
                    `;
                });
            } else {
                // Utilisateur déconnecté
                loginButtons.forEach(btn => btn.style.display = 'block');
                logoutButtons.forEach(btn => btn.style.display = 'none');
                userInfo.forEach(info => info.style.display = 'none');
            }
        }
    },

    // Initialisation
    init: async (config = {}) => {
        if (window.LandingPageAuth.state.initialized) {
            window.LandingPageAuth.utils.log('Auth déjà initialisé');
            return;
        }

        // Merger la configuration
        Object.assign(window.LandingPageAuth.config, config);

        // Détecter la langue
        window.LandingPageAuth.state.currentLanguage = window.LandingPageAuth.utils.detectLanguage();

        window.LandingPageAuth.utils.log('Initialisation Firebase Auth, langue:', window.LandingPageAuth.state.currentLanguage);

        // Attendre que Firebase soit initialisé
        if (!window.LandingPageFirebase?.isInitialized()) {
            window.LandingPageAuth.utils.log('Attente de l\'initialisation Firebase...');

            // Écouter l'événement d'initialisation Firebase
            window.addEventListener('firebase:initialized', () => {
                window.LandingPageAuth.auth.checkAuthState();
            });
        } else {
            window.LandingPageAuth.auth.checkAuthState();
        }

        // Marquer comme initialisé
        window.LandingPageAuth.state.initialized = true;

        window.LandingPageAuth.utils.log('Firebase Auth initialisé avec succès');
    }
};

// Styles CSS pour les composants d'authentification
const authStyles = document.createElement('style');
authStyles.textContent = `
    .firebase-auth-btn {
        position: relative;
        overflow: hidden;
    }

    .firebase-auth-btn:disabled {
        cursor: not-allowed;
    }

    .loading-spinner {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .spinner {
        width: 20px;
        height: 20px;
        border: 2px solid #f3f3f3;
        border-top: 2px solid #4285f4;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .google-icon {
        flex-shrink: 0;
    }

    .firebase-auth-form .form-group {
        display: flex;
        flex-direction: column;
    }

    .user-avatar {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .user-details {
        display: flex;
        flex-direction: column;
    }

    [data-auth="user-info"] {
        display: flex;
        align-items: center;
        gap: 12px;
    }

    /* RTL Support */
    .rtl {
        direction: rtl;
    }

    .rtl .firebase-auth-btn {
        flex-direction: row-reverse;
    }

    .rtl [data-auth="user-info"] {
        flex-direction: row-reverse;
    }
`;

document.head.appendChild(authStyles);

// Auto-initialisation
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.LandingPageAuth.init();
    });
} else {
    window.LandingPageAuth.init();
}

// Alias global
window.firebaseAuth = window.LandingPageAuth;

console.log('🔐 Firebase Authentication chargé');
