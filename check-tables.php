<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "=== Structure des tables ===\n";

try {
    $pdo = new PDO(
        "mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4",
        'root',
        '',
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    
    $tables = ['products', 'categories', 'landing_pages', 'stores'];
    
    foreach ($tables as $table) {
        echo "\n--- Table: $table ---\n";
        try {
            $stmt = $pdo->query("DESCRIBE $table");
            $columns = $stmt->fetchAll();
            foreach ($columns as $column) {
                echo "- " . $column['Field'] . " (" . $column['Type'] . ")\n";
            }
        } catch (Exception $e) {
            echo "❌ Erreur: " . $e->getMessage() . "\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Erreur connexion: " . $e->getMessage() . "\n";
}
?>
