<?php

/**
 * Landing Pages API Endpoint
 * Handles landing page management for merchants
 */

require_once 'config/database.php';

// Set headers
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    $database = new Database();
    $db = $database->getConnection();

    // Get request method and path
    $method = $_SERVER['REQUEST_METHOD'];
    $action = isset($_GET['action']) ? $_GET['action'] : '';
    $id = isset($_GET['id']) ? $_GET['id'] : null;

    // Simple authentication check
    $headers = function_exists('getallheaders') ? getallheaders() : [];
    if (empty($headers) && isset($_SERVER['HTTP_AUTHORIZATION'])) {
        $headers['Authorization'] = $_SERVER['HTTP_AUTHORIZATION'];
    }
    $token = isset($headers['Authorization']) ? str_replace('Bearer ', '', $headers['Authorization']) : null;

    // For demo purposes, accept demo_token
    if ($token !== 'demo_token') {
        http_response_code(401);
        echo json_encode(['error' => 'Token d\'authentification requis']);
        exit;
    }

    // Create tables if they don't exist
    createLandingPagesTablesIfNotExist($db);

    switch ($method) {
        case 'GET':
            if ($action === 'all' || $action === '') {
                getAllLandingPages($db, $_GET);
            } elseif ($action === 'get' && $id) {
                getLandingPage($db, $id);
            } elseif ($action === 'templates') {
                getLandingPageTemplates($db);
            } else {
                http_response_code(404);
                echo json_encode(['error' => 'Action non supportée']);
            }
            break;

        case 'POST':
            $input = json_decode(file_get_contents('php://input'), true);
            if ($action === 'create') {
                createLandingPage($db, $input);
            } else {
                http_response_code(404);
                echo json_encode(['error' => 'Action non supportée']);
            }
            break;

        case 'PUT':
            $input = json_decode(file_get_contents('php://input'), true);
            if ($action === 'update' && $id) {
                updateLandingPage($db, $id, $input);
            } else {
                http_response_code(404);
                echo json_encode(['error' => 'Action non supportée']);
            }
            break;

        case 'DELETE':
            if ($action === 'delete' && $id) {
                deleteLandingPage($db, $id);
            } else {
                http_response_code(404);
                echo json_encode(['error' => 'Action non supportée']);
            }
            break;

        default:
            http_response_code(405);
            echo json_encode(['error' => 'Méthode non autorisée']);
            break;
    }

} catch (Exception $e) {
    error_log("Landing Pages API Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Erreur interne du serveur']);
}

/**
 * Create tables if they don't exist
 */
function createLandingPagesTablesIfNotExist($db)
{
    try {
        // Create landing_page_templates table
        $createTemplatesTable = "
            CREATE TABLE IF NOT EXISTS landing_page_templates (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                description TEXT,
                preview_image VARCHAR(500),
                category VARCHAR(100) DEFAULT 'general',
                features JSON,
                template_data JSON,
                status ENUM('active', 'inactive') DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";

        $db->exec($createTemplatesTable);

        // Create landing_pages table
        $createPagesTable = "
            CREATE TABLE IF NOT EXISTS landing_pages (
                id INT AUTO_INCREMENT PRIMARY KEY,
                store_id INT NOT NULL,
                title VARCHAR(255) NOT NULL,
                slug VARCHAR(255) NOT NULL,
                template_id INT,
                content JSON,
                seo_data JSON,
                status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
                views INT DEFAULT 0,
                conversions INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (template_id) REFERENCES landing_page_templates(id) ON DELETE SET NULL,
                UNIQUE KEY unique_store_slug (store_id, slug),
                INDEX idx_store_id (store_id),
                INDEX idx_status (status)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";

        $db->exec($createPagesTable);

        // Insert default templates if none exist
        $checkTemplates = "SELECT COUNT(*) as count FROM landing_page_templates";
        $stmt = $db->prepare($checkTemplates);
        $stmt->execute();
        $templateCount = $stmt->fetch()['count'];

        if ($templateCount == 0) {
            insertDefaultTemplates($db);
        }

    } catch (Exception $e) {
        error_log("Error creating landing pages tables: " . $e->getMessage());
    }
}

/**
 * Insert default templates
 */
function insertDefaultTemplates($db)
{
    $templates = [
        [
            'name' => 'E-commerce Moderne',
            'description' => 'Template moderne pour boutique en ligne avec design épuré',
            'preview_image' => '/images/templates/ecommerce-modern.jpg',
            'category' => 'ecommerce',
            'features' => ['Responsive', 'SEO optimisé', 'Panier intégré', 'Paiement sécurisé']
        ],
        [
            'name' => 'Landing Page Produit',
            'description' => 'Page de vente dédiée à un produit spécifique',
            'preview_image' => '/images/templates/product-landing.jpg',
            'category' => 'product',
            'features' => ['Call-to-action', 'Témoignages', 'Galerie photos', 'Formulaire contact']
        ],
        [
            'name' => 'Portfolio Créatif',
            'description' => 'Showcase créatif pour présenter vos réalisations',
            'preview_image' => '/images/templates/creative-portfolio.jpg',
            'category' => 'portfolio',
            'features' => ['Galerie interactive', 'Animations', 'Contact form', 'Blog intégré']
        ],
        [
            'name' => 'Service Professionnel',
            'description' => 'Template pour présenter vos services professionnels',
            'preview_image' => '/images/templates/professional-service.jpg',
            'category' => 'service',
            'features' => ['Présentation équipe', 'Tarifs', 'Témoignages', 'Réservation en ligne']
        ],
        [
            'name' => 'Restaurant & Food',
            'description' => 'Template spécialisé pour restaurants et food business',
            'preview_image' => '/images/templates/restaurant-food.jpg',
            'category' => 'restaurant',
            'features' => ['Menu interactif', 'Réservation', 'Galerie plats', 'Livraison']
        ]
    ];

    foreach ($templates as $template) {
        $query = "
            INSERT INTO landing_page_templates (name, description, preview_image, category, features)
            VALUES (?, ?, ?, ?, ?)
        ";

        $stmt = $db->prepare($query);
        $stmt->execute([
            $template['name'],
            $template['description'],
            $template['preview_image'],
            $template['category'],
            json_encode($template['features'])
        ]);
    }
}

/**
 * Get all landing pages for a store
 */
function getAllLandingPages($db, $params)
{
    try {
        $storeId = isset($params['store_id']) ? $params['store_id'] : null;

        if (!$storeId) {
            http_response_code(400);
            echo json_encode(['error' => 'store_id requis']);
            return;
        }

        $query = "
            SELECT
                lp.id, lp.store_id, lp.title, lp.slug, lp.template_id, lp.status,
                lp.views, lp.conversions, lp.created_at, lp.updated_at,
                lpt.name as template_name
            FROM landing_pages lp
            LEFT JOIN landing_page_templates lpt ON lp.template_id = lpt.id
            WHERE lp.store_id = ?
            ORDER BY lp.created_at DESC
        ";

        $stmt = $db->prepare($query);
        $stmt->execute([$storeId]);
        $landingPages = $stmt->fetchAll(PDO::FETCH_ASSOC);

        echo json_encode([
            'success' => true,
            'data' => $landingPages
        ]);
    } catch (Exception $e) {
        error_log("Error getting landing pages: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la récupération des landing pages']);
    }
}

/**
 * Get a single landing page
 */
function getLandingPage($db, $id)
{
    try {
        $query = "
            SELECT
                lp.*,
                lpt.name as template_name,
                lpt.preview_image as template_preview
            FROM landing_pages lp
            LEFT JOIN landing_page_templates lpt ON lp.template_id = lpt.id
            WHERE lp.id = ?
        ";

        $stmt = $db->prepare($query);
        $stmt->execute([$id]);
        $landingPage = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$landingPage) {
            http_response_code(404);
            echo json_encode(['error' => 'Landing page non trouvée']);
            return;
        }

        // Decode JSON fields
        if ($landingPage['content']) {
            $landingPage['content'] = json_decode($landingPage['content'], true);
        }
        if ($landingPage['seo_data']) {
            $landingPage['seo_data'] = json_decode($landingPage['seo_data'], true);
        }

        echo json_encode([
            'success' => true,
            'data' => $landingPage
        ]);
    } catch (Exception $e) {
        error_log("Error getting landing page: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la récupération de la landing page']);
    }
}

/**
 * Get available landing page templates
 */
function getLandingPageTemplates($db)
{
    try {
        $query = "
            SELECT
                id, name, description, preview_image, category,
                features, created_at
            FROM landing_page_templates
            WHERE status = 'active'
            ORDER BY category, name
        ";

        $stmt = $db->prepare($query);
        $stmt->execute();
        $templates = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Decode JSON fields
        foreach ($templates as &$template) {
            if ($template['features']) {
                $template['features'] = json_decode($template['features'], true);
            }
        }

        echo json_encode([
            'success' => true,
            'data' => $templates
        ]);
    } catch (Exception $e) {
        error_log("Error getting templates: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la récupération des templates']);
    }
}

/**
 * Create a new landing page
 */
function createLandingPage($db, $data)
{
    try {
        $query = "
            INSERT INTO landing_pages (
                store_id, title, slug, template_id, content,
                seo_data, status, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
        ";

        $stmt = $db->prepare($query);
        $result = $stmt->execute([
            $data['store_id'],
            $data['title'],
            $data['slug'],
            $data['template_id'],
            json_encode($data['content'] ?? []),
            json_encode($data['seo_data'] ?? []),
            $data['status'] ?? 'draft'
        ]);

        if ($result) {
            $landingPageId = $db->lastInsertId();
            echo json_encode([
                'success' => true,
                'data' => ['id' => $landingPageId],
                'message' => 'Landing page créée avec succès'
            ]);
        } else {
            http_response_code(500);
            echo json_encode(['error' => 'Erreur lors de la création de la landing page']);
        }
    } catch (Exception $e) {
        error_log("Error creating landing page: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la création de la landing page']);
    }
}

/**
 * Update a landing page
 */
function updateLandingPage($db, $id, $data)
{
    try {
        $query = "
            UPDATE landing_pages SET
                title = ?, slug = ?, template_id = ?, content = ?,
                seo_data = ?, status = ?, updated_at = NOW()
            WHERE id = ?
        ";

        $stmt = $db->prepare($query);
        $result = $stmt->execute([
            $data['title'],
            $data['slug'],
            $data['template_id'],
            json_encode($data['content'] ?? []),
            json_encode($data['seo_data'] ?? []),
            $data['status'] ?? 'draft',
            $id
        ]);

        if ($result) {
            echo json_encode([
                'success' => true,
                'message' => 'Landing page mise à jour avec succès'
            ]);
        } else {
            http_response_code(500);
            echo json_encode(['error' => 'Erreur lors de la mise à jour de la landing page']);
        }
    } catch (Exception $e) {
        error_log("Error updating landing page: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la mise à jour de la landing page']);
    }
}

/**
 * Delete a landing page
 */
function deleteLandingPage($db, $id)
{
    try {
        $query = "DELETE FROM landing_pages WHERE id = ?";
        $stmt = $db->prepare($query);
        $result = $stmt->execute([$id]);

        if ($result) {
            echo json_encode([
                'success' => true,
                'message' => 'Landing page supprimée avec succès'
            ]);
        } else {
            http_response_code(500);
            echo json_encode(['error' => 'Erreur lors de la suppression de la landing page']);
        }
    } catch (Exception $e) {
        error_log("Error deleting landing page: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la suppression de la landing page']);
    }
}
?>
