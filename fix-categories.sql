-- Correction des catégories pour le store 3
-- D'abord, trouvons le merchant_id du store 3

-- Vérifier le store 3
SELECT id, merchant_id, store_name FROM stores WHERE id = 3;

-- Supposons que le merchant_id est 4 (basé sur seller_id dans seller-login.html)
-- Supprimer les anciennes catégories si elles existent
DELETE FROM categories WHERE user_id = 4;

-- Créer les nouvelles catégories pour le merchant 4
INSERT INTO categories (
    user_id, name, name_ar, name_fr, name_en, slug, description,
    description_ar, description_fr, description_en, color, icon,
    category_type, sort_order, status, created_at, updated_at
) VALUES 
(4, 'Électronique', 'إلكترونيات', 'Électronique', 'Electronics', 'electronique', 
 'Catégorie Électronique', 'فئة إلكترونيات', 'Catégorie Électronique', 'Electronics Category',
 '#007bff', 'fas fa-microchip', 'category', 1, 'active', NOW(), NOW()),

(4, 'Informatique', 'معلوماتية', 'Informatique', 'Computing', 'informatique',
 'Catégorie Informatique', 'فئة معلوماتية', 'Catégorie Informatique', 'Computing Category',
 '#28a745', 'fas fa-laptop', 'category', 2, 'active', NOW(), NOW()),

(4, 'Smartphones', 'هواتف ذكية', 'Smartphones', 'Smartphones', 'smartphones',
 'Catégorie Smartphones', 'فئة هواتف ذكية', 'Catégorie Smartphones', 'Smartphones Category',
 '#17a2b8', 'fas fa-mobile-alt', 'category', 3, 'active', NOW(), NOW()),

(4, 'Audio/Vidéo', 'صوت/فيديو', 'Audio/Vidéo', 'Audio/Video', 'audio-video',
 'Catégorie Audio/Vidéo', 'فئة صوت/فيديو', 'Catégorie Audio/Vidéo', 'Audio/Video Category',
 '#ffc107', 'fas fa-headphones', 'category', 4, 'active', NOW(), NOW()),

(4, 'Gaming', 'ألعاب', 'Gaming', 'Gaming', 'gaming',
 'Catégorie Gaming', 'فئة ألعاب', 'Catégorie Gaming', 'Gaming Category',
 '#dc3545', 'fas fa-gamepad', 'category', 5, 'active', NOW(), NOW()),

(4, 'Accessoires', 'إكسسوارات', 'Accessoires', 'Accessories', 'accessoires',
 'Catégorie Accessoires', 'فئة إكسسوارات', 'Catégorie Accessoires', 'Accessories Category',
 '#6f42c1', 'fas fa-plug', 'category', 6, 'active', NOW(), NOW());

-- Vérifier le résultat
SELECT id, user_id, name, name_ar, slug, category_type FROM categories WHERE user_id = 4;
