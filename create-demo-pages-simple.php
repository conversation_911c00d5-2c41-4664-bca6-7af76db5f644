<?php

/**
 * <PERSON><PERSON>er des landing pages de démonstration via l'API
 */

require_once __DIR__ . '/api/config/database.php';

try {
    $database = new Database();
    $db = $database->getConnection();

    // Vérifier si l'utilisateur demo existe
    $checkUser = $db->prepare("SELECT id FROM users WHERE email = ?");
    $checkUser->execute(['<EMAIL>']);
    $demoUser = $checkUser->fetch();

    if (!$demoUser) {
        // Créer l'utilisateur demo
        $createUser = $db->prepare("INSERT INTO users (email, displayName, role, created_at) VALUES (?, ?, ?, NOW())");
        $createUser->execute(['<EMAIL>', 'Demo Merchant', 'merchant']);
        $demoUserId = $db->lastInsertId();
        echo "✅ Utilisateur demo créé avec ID: $demoUserId\n";
    } else {
        $demoUserId = $demoUser['id'];
        echo "✅ Utilisateur demo trouvé avec ID: $demoUserId\n";
    }

    // Créer directement dans la base de données
    $landingPages = [
        [
            'title' => 'Boutique E-commerce TechStore',
            'slug' => 'boutique-ecommerce-techstore',
            'template_id' => 1, // ID numérique pour ecommerce
            'status' => 'published',
            'content' => '<!-- Template E-commerce -->'
        ],
        [
            'title' => 'Service SaaS LandingCraft Pro',
            'slug' => 'service-saas-landingcraft-pro',
            'template_id' => 2, // ID numérique pour saas
            'status' => 'published',
            'content' => '<!-- Template SaaS -->'
        ],
        [
            'title' => 'Portfolio Créatif Ahmed Design',
            'slug' => 'portfolio-creatif-ahmed-design',
            'template_id' => 3, // ID numérique pour portfolio
            'status' => 'draft',
            'content' => '<!-- Template Portfolio -->'
        ],
        [
            'title' => 'Services Conseil ConseilPro',
            'slug' => 'services-conseil-conseilpro',
            'template_id' => 4, // ID numérique pour service
            'status' => 'published',
            'content' => '<!-- Template Service -->'
        ],
        [
            'title' => 'App Mobile FoodieApp',
            'slug' => 'app-mobile-foodieapp',
            'template_id' => 5, // ID numérique pour app
            'status' => 'published',
            'content' => '<!-- Template App -->'
        ]
    ];

    echo "\n🚀 Création des landing pages...\n\n";

    foreach ($landingPages as $page) {
        // Vérifier si la page existe déjà
        $checkPage = $db->prepare("SELECT id FROM landing_pages WHERE slug = ?");
        $checkPage->execute([$page['slug']]);
        if ($checkPage->fetch()) {
            echo "⚠️  Landing page '{$page['title']}' existe déjà\n";
            continue;
        }

        // Insérer la landing page
        $insertPage = $db->prepare("
            INSERT INTO landing_pages (
                merchant_id, title, slug, template_id, content, status, user_id,
                views_count, conversions_count, created_at, updated_at
            ) VALUES (
                ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW()
            )
        ");

        $views = rand(50, 500);
        $conversions = rand(5, 50);

        $success = $insertPage->execute([
            $demoUserId, // merchant_id
            $page['title'],
            $page['slug'],
            $page['template_id'],
            $page['content'],
            $page['status'],
            $demoUserId, // user_id
            $views,
            $conversions
        ]);

        if ($success) {
            $pageId = $db->lastInsertId();
            echo "✅ Landing page créée: '{$page['title']}' (ID: $pageId)\n";
        } else {
            echo "❌ Erreur lors de la création de '{$page['title']}'\n";
        }
    }

    echo "\n🎉 Création terminée !\n";
    echo "🔗 Testez le dashboard: http://localhost:8000/dashboard.html\n";
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
}
