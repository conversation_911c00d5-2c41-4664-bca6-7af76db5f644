-- Ajouter le champ subdomain à la table stores si il n'existe pas
ALTER TABLE stores 
ADD COLUMN IF NOT EXISTS subdomain VARCHAR(100) UNIQUE AFTER domain;

-- Créer un index sur le subdomain pour améliorer les performances
CREATE INDEX IF NOT EXISTS idx_stores_subdomain ON stores(subdomain);

-- Mettre à jour les stores existants avec des subdomains basés sur leur nom
UPDATE stores 
SET subdomain = LOWER(REPLACE(REPLACE(REPLACE(store_name, ' ', '-'), '_', '-'), '--', '-'))
WHERE subdomain IS NULL OR subdomain = '';

-- Assurer l'unicité des subdomains
UPDATE stores s1
JOIN (
    SELECT 
        id,
        subdomain,
        ROW_NUMBER() OVER (PARTITION BY subdomain ORDER BY id) as rn
    FROM stores 
    WHERE subdomain IS NOT NULL AND subdomain != ''
) s2 ON s1.id = s2.id
SET s1.subdomain = CONCAT(s2.subdomain, '-', s2.rn)
WHERE s2.rn > 1;
