<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>تسجيل الدخول - صفحات هبوط للجميع</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />

    <!-- Styles CSS -->
    <link rel="stylesheet" href="css/main.css" />
    <link rel="stylesheet" href="css/components.css" />

    <style>
      /* Styles spécifiques pour la page de connexion */
      body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20px;
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
      }

      .login-container {
        background: white;
        border-radius: 20px;
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
        padding: 50px;
        max-width: 450px;
        width: 100%;
        text-align: center;
        position: relative;
        overflow: hidden;
      }

      .login-container::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 5px;
        background: linear-gradient(90deg, #4285f4, #34a853, #fbbc05, #ea4335);
      }

      .logo {
        font-size: 3em;
        margin-bottom: 15px;
        background: linear-gradient(45deg, #4285f4, #34a853);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      .page-title {
        font-size: 2.2em;
        color: #333;
        margin-bottom: 10px;
        font-weight: 700;
      }

      .page-subtitle {
        color: #666;
        margin-bottom: 40px;
        font-size: 1.1em;
        line-height: 1.5;
      }

      .auth-methods {
        display: flex;
        flex-direction: column;
        gap: 20px;
      }

      .divider {
        display: flex;
        align-items: center;
        margin: 30px 0;
        color: #999;
        font-size: 0.9em;
      }

      .divider::before,
      .divider::after {
        content: "";
        flex: 1;
        height: 1px;
        background: linear-gradient(90deg, transparent, #ddd, transparent);
      }

      .divider span {
        padding: 0 20px;
        background: white;
      }

      .form-footer {
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid #eee;
      }

      .register-link {
        color: #4285f4;
        text-decoration: none;
        font-weight: 500;
        transition: color 0.2s ease;
      }

      .register-link:hover {
        color: #3367d6;
        text-decoration: underline;
      }

      /* Sélecteur de langue glissant */
      .language-selector {
        position: fixed;
        top: 30px;
        right: 30px;
        z-index: 1000;
      }

      .lang-toggle {
        background: rgba(255, 255, 255, 0.95);
        border: 2px solid #e0e0e0;
        border-radius: 25px;
        padding: 8px 16px;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 0.9em;
        font-weight: 500;
        color: #333;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
      }

      .lang-toggle:hover {
        background: white;
        border-color: #4285f4;
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
      }

      .lang-flag {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
      }

      .lang-dropdown {
        position: absolute;
        top: 100%;
        right: 0;
        background: white;
        border: 2px solid #e0e0e0;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
        padding: 10px 0;
        min-width: 180px;
        opacity: 0;
        visibility: hidden;
        transform: translateY(-10px);
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
      }

      .lang-dropdown.show {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
      }

      .lang-option {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 12px 20px;
        cursor: pointer;
        transition: background 0.2s ease;
        font-size: 0.9em;
      }

      .lang-option:hover {
        background: #f8f9fa;
      }

      .lang-option.active {
        background: #e3f2fd;
        color: #1976d2;
        font-weight: 600;
      }

      .lang-option .lang-flag {
        width: 24px;
        height: 24px;
        font-size: 14px;
      }

      /* Support RTL */
      [dir="rtl"] .language-selector {
        right: auto;
        left: 30px;
      }

      [dir="rtl"] .lang-dropdown {
        right: auto;
        left: 0;
      }

      [dir="ltr"] .divider {
        direction: ltr;
      }

      /* Responsive */
      @media (max-width: 600px) {
        .login-container {
          padding: 30px 25px;
          margin: 10px;
          border-radius: 15px;
        }

        .page-title {
          font-size: 1.8em;
        }

        .logo {
          font-size: 2.5em;
        }

        .language-selector {
          top: 20px;
          right: 20px;
        }

        [dir="rtl"] .language-selector {
          right: auto;
          left: 20px;
        }
      }

      /* Animation d'entrée */
      .login-container {
        animation: slideInUp 0.6s ease-out;
      }

      @keyframes slideInUp {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      /* États de chargement */
      .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
      }

      .loading-overlay.show {
        opacity: 1;
        visibility: visible;
      }

      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #4285f4;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }
    </style>
  </head>
  <body>
    <!-- Overlay de chargement -->
    <div class="loading-overlay" id="loadingOverlay">
      <div class="loading-spinner"></div>
    </div>

    <!-- Sélecteur de langue -->
    <div class="language-selector">
      <div class="lang-toggle" id="langToggle">
        <div class="lang-flag" id="currentFlag">🇩🇿</div>
        <span id="currentLang">العربية</span>
        <svg width="12" height="8" viewBox="0 0 12 8" fill="currentColor">
          <path
            d="M1 1l5 5 5-5"
            stroke="currentColor"
            stroke-width="2"
            fill="none"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </div>
      <div class="lang-dropdown" id="langDropdown">
        <div class="lang-option active" data-lang="ar">
          <div class="lang-flag">🇩🇿</div>
          <span>العربية</span>
        </div>
        <div class="lang-option" data-lang="fr">
          <div class="lang-flag">🇫🇷</div>
          <span>Français</span>
        </div>
        <div class="lang-option" data-lang="en">
          <div class="lang-flag">🇺🇸</div>
          <span>English</span>
        </div>
      </div>
    </div>

    <!-- Conteneur principal -->
    <div class="login-container">
      <!-- Logo et titre -->
      <div class="logo">🔐</div>
      <h1 class="page-title" id="pageTitle">تسجيل الدخول</h1>
      <p class="page-subtitle" id="pageSubtitle">
        مرحباً بك في منصة صفحات الهبوط للجميع
      </p>

      <!-- Méthodes d'authentification -->
      <div class="auth-methods">
        <!-- Connexion Google -->
        <div id="googleSigninContainer"></div>

        <!-- Séparateur -->
        <div class="divider">
          <span id="dividerText">أو</span>
        </div>

        <!-- Connexion Email -->
        <div id="emailSigninContainer"></div>
      </div>

      <!-- Pied de page -->
      <div class="form-footer">
        <p id="registerPrompt">
          ليس لديك حساب؟
          <a href="register.html" class="register-link" id="registerLink"
            >إنشاء حساب جديد</a
          >
        </p>
      </div>
    </div>

    <!-- Scripts -->
    <script src="js/firebase-config.js"></script>
    <script src="js/firebase-auth.js"></script>
    <script src="js/main.js"></script>

    <script>
      // Configuration des langues
      const LANGUAGE_CONFIG = {
        ar: {
          code: "ar",
          name: "العربية",
          flag: "🇩🇿",
          dir: "rtl",
          locale: "ar-DZ",
        },
        fr: {
          code: "fr",
          name: "Français",
          flag: "🇫🇷",
          dir: "ltr",
          locale: "fr-FR",
        },
        en: {
          code: "en",
          name: "English",
          flag: "🇺🇸",
          dir: "ltr",
          locale: "en-US",
        },
      };

      // Traductions pour la page de connexion
      const LOGIN_TRANSLATIONS = {
        ar: {
          pageTitle: "تسجيل الدخول",
          pageSubtitle: "مرحباً بك في منصة صفحات الهبوط للجميع",
          dividerText: "أو",
          registerPrompt: "ليس لديك حساب؟",
          registerLink: "إنشاء حساب جديد",
          documentTitle: "تسجيل الدخول - صفحات هبوط للجميع",
        },
        fr: {
          pageTitle: "Connexion",
          pageSubtitle:
            "Bienvenue sur la plateforme Pages de destination pour tous",
          dividerText: "ou",
          registerPrompt: "Pas encore de compte ?",
          registerLink: "Créer un compte",
          documentTitle: "Connexion - Pages de destination pour tous",
        },
        en: {
          pageTitle: "Sign In",
          pageSubtitle: "Welcome to Landing Pages for Everyone platform",
          dividerText: "or",
          registerPrompt: "Don't have an account?",
          registerLink: "Create account",
          documentTitle: "Sign In - Landing Pages for Everyone",
        },
      };

      // État de l'application
      let currentLanguage = "ar";
      let isDropdownOpen = false;

      // Fonction pour détecter la langue du système
      function detectSystemLanguage() {
        // 1. Langue stockée localement
        const storedLang = localStorage.getItem("preferred_language");
        if (storedLang && LANGUAGE_CONFIG[storedLang]) {
          return storedLang;
        }

        // 2. Langue du navigateur
        const browserLang = navigator.language || navigator.userLanguage;
        const langCode = browserLang.split("-")[0].toLowerCase();

        if (LANGUAGE_CONFIG[langCode]) {
          return langCode;
        }

        // 3. Langues préférées du navigateur
        if (navigator.languages) {
          for (const lang of navigator.languages) {
            const code = lang.split("-")[0].toLowerCase();
            if (LANGUAGE_CONFIG[code]) {
              return code;
            }
          }
        }

        // 4. Défaut
        return "ar";
      }

      // Fonction pour obtenir une traduction
      function t(key, lang = currentLanguage) {
        return (
          LOGIN_TRANSLATIONS[lang]?.[key] || LOGIN_TRANSLATIONS.en[key] || key
        );
      }

      // Fonction pour mettre à jour l'interface
      function updateUI() {
        const config = LANGUAGE_CONFIG[currentLanguage];

        // Mettre à jour les attributs HTML
        document.documentElement.setAttribute("lang", config.code);
        document.documentElement.setAttribute("dir", config.dir);
        document.title = t("documentTitle");

        // Mettre à jour les textes
        document.getElementById("pageTitle").textContent = t("pageTitle");
        document.getElementById("pageSubtitle").textContent = t("pageSubtitle");
        document.getElementById("dividerText").textContent = t("dividerText");
        document.getElementById("registerPrompt").innerHTML =
          t("registerPrompt") +
          ' <a href="register.html" class="register-link" id="registerLink">' +
          t("registerLink") +
          "</a>";

        // Mettre à jour le sélecteur de langue
        document.getElementById("currentFlag").textContent = config.flag;
        document.getElementById("currentLang").textContent = config.name;

        // Mettre à jour les options actives
        document.querySelectorAll(".lang-option").forEach((option) => {
          option.classList.toggle(
            "active",
            option.dataset.lang === currentLanguage
          );
        });

        // Mettre à jour l'état de l'authentification
        if (window.LandingPageAuth) {
          window.LandingPageAuth.state.currentLanguage = currentLanguage;

          // Recréer les composants d'authentification
          recreateAuthComponents();
        }
      }

      // Fonction pour recréer les composants d'authentification
      function recreateAuthComponents() {
        // Vider les conteneurs
        document.getElementById("googleSigninContainer").innerHTML = "";
        document.getElementById("emailSigninContainer").innerHTML = "";

        // Recréer les composants si l'auth est initialisé
        if (
          window.LandingPageAuth &&
          window.LandingPageAuth.state.initialized
        ) {
          window.LandingPageAuth.ui.createGoogleSignInButton(
            "googleSigninContainer"
          );
          window.LandingPageAuth.ui.createEmailSignInForm(
            "emailSigninContainer"
          );
        }
      }

      // Fonction pour changer de langue
      function changeLanguage(lang) {
        if (LANGUAGE_CONFIG[lang] && lang !== currentLanguage) {
          currentLanguage = lang;
          localStorage.setItem("preferred_language", lang);
          updateUI();

          // Fermer le dropdown
          closeDropdown();

          // Notification
          if (window.showNotification) {
            const config = LANGUAGE_CONFIG[lang];
            window.showNotification(
              `تم تغيير اللغة إلى ${config.name}`,
              "success"
            );
          }
        }
      }

      // Fonction pour ouvrir/fermer le dropdown
      function toggleDropdown() {
        const dropdown = document.getElementById("langDropdown");
        isDropdownOpen = !isDropdownOpen;
        dropdown.classList.toggle("show", isDropdownOpen);
      }

      // Fonction pour fermer le dropdown
      function closeDropdown() {
        const dropdown = document.getElementById("langDropdown");
        isDropdownOpen = false;
        dropdown.classList.remove("show");
      }

      // Fonction d'initialisation
      function initializePage() {
        // Afficher l'overlay de chargement
        document.getElementById("loadingOverlay").classList.add("show");

        // Détecter et définir la langue
        currentLanguage = detectSystemLanguage();

        // Mettre à jour l'interface
        updateUI();

        // Attendre que Firebase soit initialisé
        if (
          window.LandingPageFirebase &&
          window.LandingPageFirebase.isInitialized()
        ) {
          initializeAuth();
        } else {
          window.addEventListener("firebase:initialized", initializeAuth);
        }
      }

      // Fonction pour rediriger vers le bon dashboard selon le rôle
      function redirectToAppropriateeDashboard(user) {
        // Liste des emails d'administrateurs autorisés
        const adminEmails = [
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
        ];

        if (adminEmails.includes(user.email)) {
          // Rediriger vers le dashboard admin
          window.location.href = "/dashboard.html";
        } else {
          // Rediriger vers le dashboard utilisateur
          window.location.href = "/user-dashboard.html";
        }
      }

      // Fonction d'initialisation de l'authentification
      function initializeAuth() {
        if (window.LandingPageAuth) {
          window.LandingPageAuth.init({
            redirectAfterLogin: "/user-dashboard.html",
            persistSession: true,
            debug: false,
          }).then(() => {
            // Créer les composants d'authentification
            recreateAuthComponents();

            // Vérifier si l'utilisateur est déjà connecté
            const auth = window.LandingPageFirebase.getAuth();
            if (auth && auth.currentUser) {
              // Rediriger vers le dashboard utilisateur
              redirectToAppropriateeDashboard(auth.currentUser);
              return;
            }

            // Masquer l'overlay de chargement
            setTimeout(() => {
              document
                .getElementById("loadingOverlay")
                .classList.remove("show");
            }, 500);
          });
        } else {
          // Masquer l'overlay en cas d'erreur
          setTimeout(() => {
            document.getElementById("loadingOverlay").classList.remove("show");
          }, 1000);
        }
      }

      // Gestionnaires d'événements
      document.addEventListener("DOMContentLoaded", initializePage);

      // Gestionnaire pour le toggle de langue
      document.getElementById("langToggle").addEventListener("click", (e) => {
        e.stopPropagation();
        toggleDropdown();
      });

      // Gestionnaire pour les options de langue
      document.getElementById("langDropdown").addEventListener("click", (e) => {
        e.stopPropagation();
        const option = e.target.closest(".lang-option");
        if (option) {
          const lang = option.dataset.lang;
          changeLanguage(lang);
        }
      });

      // Fermer le dropdown en cliquant ailleurs
      document.addEventListener("click", () => {
        if (isDropdownOpen) {
          closeDropdown();
        }
      });

      // Fermer le dropdown avec Escape
      document.addEventListener("keydown", (e) => {
        if (e.key === "Escape" && isDropdownOpen) {
          closeDropdown();
        }
      });

      // Fonction de notification personnalisée
      window.showNotification = function (message, type = "info") {
        // Utiliser le système de notification existant ou créer un simple
        if (
          window.LandingPageApp &&
          window.LandingPageApp.utils &&
          window.LandingPageApp.utils.showNotification
        ) {
          window.LandingPageApp.utils.showNotification(message, type);
        } else {
          // Notification simple
          const notification = document.createElement("div");
          notification.className = `notification ${type}`;
          notification.textContent = message;
          notification.style.cssText = `
                    position: fixed;
                    top: 20px;
                    ${currentLanguage === "ar" ? "left" : "right"}: 20px;
                    background: ${
                      type === "success"
                        ? "#28a745"
                        : type === "error"
                        ? "#dc3545"
                        : "#17a2b8"
                    };
                    color: white;
                    padding: 15px 20px;
                    border-radius: 8px;
                    z-index: 10000;
                    transform: translateX(${
                      currentLanguage === "ar" ? "-" : ""
                    }100%);
                    transition: transform 0.3s ease;
                `;

          document.body.appendChild(notification);

          setTimeout(() => {
            notification.style.transform = "translateX(0)";
          }, 100);

          setTimeout(() => {
            notification.style.transform = `translateX(${
              currentLanguage === "ar" ? "-" : ""
            }100%)`;
            setTimeout(() => notification.remove(), 300);
          }, 3000);
        }
      };

      console.log("🔐 Page de connexion initialisée");
    </script>
  </body>
</html>
