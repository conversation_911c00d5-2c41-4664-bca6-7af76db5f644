<?php
/**
 * Script de test pour vérifier les données du vendeur de démonstration
 */

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once __DIR__ . '/config/database.php';

try {
    $database = new Database();
    $pdo = $database->getConnection();
    echo "✅ Connexion à la base de données réussie\n\n";

    // Rechercher le merchant avec l'email <EMAIL>
    $merchantQuery = "SELECT * FROM merchants WHERE email = ?";
    $merchantStmt = $pdo->prepare($merchantQuery);
    $merchantStmt->execute(['<EMAIL>']);
    $merchant = $merchantStmt->fetch();

    if (!$merchant) {
        throw new Exception("❌ Merchant <EMAIL> non trouvé");
    }

    echo "📊 Informations du merchant de démo:\n";
    echo "ID: {$merchant['id']}\n";
    echo "Email: {$merchant['email']}\n";
    echo "Status: {$merchant['status']}\n\n";

    // Récupérer le store associé
    $storeQuery = "SELECT * FROM stores WHERE merchant_id = ?";
    $storeStmt = $pdo->prepare($storeQuery);
    $storeStmt->execute([$merchant['id']]);
    $store = $storeStmt->fetch();

    if (!$store) {
        throw new Exception("❌ Aucun store trouvé pour ce merchant");
    }

    echo "🏪 Informations du store:\n";
    echo "ID: {$store['id']}\n";
    echo "Nom: {$store['store_name']}\n";
    echo "Status: {$store['status']}\n\n";

    // Compter les produits
    $productsQuery = "SELECT COUNT(*) as count FROM products WHERE store_id = ?";
    $productsStmt = $pdo->prepare($productsQuery);
    $productsStmt->execute([$store['id']]);
    $productsCount = $productsStmt->fetch()['count'];

    echo "📦 Nombre de produits: {$productsCount}\n";

    // Compter les catégories
    $categoriesQuery = "SELECT COUNT(*) as count FROM categories WHERE store_id = ?";
    $categoriesStmt = $pdo->prepare($categoriesQuery);
    $categoriesStmt->execute([$store['id']]);
    $categoriesCount = $categoriesStmt->fetch()['count'];

    echo "📑 Nombre de catégories: {$categoriesCount}\n";

    // Compter les landing pages
    $pagesQuery = "SELECT COUNT(*) as count FROM landing_pages WHERE merchant_id = ?";
    $pagesStmt = $pdo->prepare($pagesQuery);
    $pagesStmt->execute([$merchant['id']]);
    $pagesCount = $pagesStmt->fetch()['count'];

    echo "🌐 Nombre de landing pages: {$pagesCount}\n";

} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
}
