-- Mise à jour de la table roles pour ajouter les limites
ALTER TABLE roles ADD COLUMN IF NOT EXISTS max_products INT DEFAULT NULL COMMENT 'Nombre maximum de produits autorisés (-1 = illimité)';
ALTER TABLE roles ADD COLUMN IF NOT EXISTS max_landing_pages INT DEFAULT NULL COMMENT 'Nombre maximum de landing pages autorisées (-1 = illimité)';
ALTER TABLE roles ADD COLUMN IF NOT EXISTS max_categories INT DEFAULT NULL COMMENT 'Nombre maximum de catégories autorisées (-1 = illimité)';
ALTER TABLE roles ADD COLUMN IF NOT EXISTS max_subcategories INT DEFAULT NULL COMMENT 'Nombre maximum de sous-catégories autorisées (-1 = illimité)';

-- Mise à jour des rôles existants avec des limites par défaut
UPDATE roles SET 
    max_products = CASE 
        WHEN name = 'admin' THEN -1 
        WHEN name = 'seller' THEN 100 
        WHEN name = 'agent' THEN 0 
        WHEN name = 'customer' THEN 0 
        ELSE 10 
    END,
    max_landing_pages = CASE 
        WHEN name = 'admin' THEN -1 
        WHEN name = 'seller' THEN 10 
        WHEN name = 'agent' THEN 0 
        WHEN name = 'customer' THEN 0 
        ELSE 1 
    END,
    max_categories = CASE 
        WHEN name = 'admin' THEN -1 
        WHEN name = 'seller' THEN 20 
        WHEN name = 'agent' THEN 0 
        WHEN name = 'customer' THEN 0 
        ELSE 5 
    END,
    max_subcategories = CASE 
        WHEN name = 'admin' THEN -1 
        WHEN name = 'seller' THEN 50 
        WHEN name = 'agent' THEN 0 
        WHEN name = 'customer' THEN 0 
        ELSE 10 
    END
WHERE id IN (SELECT id FROM roles);
