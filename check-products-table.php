<?php
/**
 * Script pour vérifier la structure de la table products
 */

// Configuration de la base de données
$host = 'localhost';
$port = 3307;
$dbname = 'landingpage_new';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;port=$port;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Vérifier si la table products existe
    $result = $pdo->query("SHOW TABLES LIKE 'products'");
    if ($result->rowCount() > 0) {
        echo "✅ Table products existe\n\n";
        
        // Afficher la structure
        echo "Structure de la table products:\n";
        $desc = $pdo->query("DESCRIBE products");
        while ($row = $desc->fetch()) {
            echo "- {$row['Field']} ({$row['Type']}) - {$row['Null']} - {$row['Key']}\n";
        }
        
        echo "\nContenu de la table products:\n";
        $products = $pdo->query("SELECT * FROM products LIMIT 5");
        while ($product = $products->fetch()) {
            echo "ID: {$product['id']}, Name: {$product['name']}\n";
        }
        
    } else {
        echo "❌ Table products n'existe pas\n";
        
        // Créer la table products
        $createProductsTable = "
            CREATE TABLE products (
                id INT AUTO_INCREMENT PRIMARY KEY,
                store_id INT NOT NULL,
                name VARCHAR(255) NOT NULL,
                description TEXT,
                price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
                image_url VARCHAR(500),
                status ENUM('active', 'inactive') DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (store_id) REFERENCES stores(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        $pdo->exec($createProductsTable);
        echo "✅ Table products créée\n";
    }

} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
}
?>
