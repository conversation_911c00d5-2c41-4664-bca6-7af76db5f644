<?php
/**
 * Exemple d'intégration du système de quotas
 * Formulaire de création de produit avec vérification des limites
 */

session_start();

// Simulation d'un utilisateur connecté
$currentUserId = $_SESSION['user_id'] ?? 1;
$currentUserRole = $_SESSION['user_role'] ?? 'seller';
$language = $_GET['lang'] ?? 'ar';

require_once '../config/database.php';
require_once '../php/QuotaManager.php';
require_once '../components/quota-widget.php';

// Traductions
$translations = [
    'ar' => [
        'create_product' => 'إنشاء منتج جديد',
        'product_name' => 'اسم المنتج',
        'product_description' => 'وصف المنتج',
        'product_price' => 'سعر المنتج',
        'product_category' => 'فئة المنتج',
        'create_button' => 'إنشاء المنتج',
        'quota_status' => 'حالة الحصة',
        'quota_warning' => 'تحذير: أنت قريب من حد المنتجات المسموح',
        'quota_exceeded' => 'تم تجاوز حد المنتجات المسموح',
        'upgrade_subscription' => 'ترقية الاشتراك',
        'success_message' => 'تم إنشاء المنتج بنجاح',
        'error_message' => 'حدث خطأ أثناء إنشاء المنتج',
        'select_category' => 'اختر الفئة',
        'required_field' => 'هذا الحقل مطلوب'
    ],
    'fr' => [
        'create_product' => 'Créer un nouveau produit',
        'product_name' => 'Nom du produit',
        'product_description' => 'Description du produit',
        'product_price' => 'Prix du produit',
        'product_category' => 'Catégorie du produit',
        'create_button' => 'Créer le produit',
        'quota_status' => 'État du quota',
        'quota_warning' => 'Attention: Vous approchez de votre limite de produits',
        'quota_exceeded' => 'Limite de produits dépassée',
        'upgrade_subscription' => 'Mettre à niveau l\'abonnement',
        'success_message' => 'Produit créé avec succès',
        'error_message' => 'Erreur lors de la création du produit',
        'select_category' => 'Sélectionner une catégorie',
        'required_field' => 'Ce champ est requis'
    ],
    'en' => [
        'create_product' => 'Create New Product',
        'product_name' => 'Product Name',
        'product_description' => 'Product Description',
        'product_price' => 'Product Price',
        'product_category' => 'Product Category',
        'create_button' => 'Create Product',
        'quota_status' => 'Quota Status',
        'quota_warning' => 'Warning: You are approaching your product limit',
        'quota_exceeded' => 'Product limit exceeded',
        'upgrade_subscription' => 'Upgrade Subscription',
        'success_message' => 'Product created successfully',
        'error_message' => 'Error creating product',
        'select_category' => 'Select Category',
        'required_field' => 'This field is required'
    ]
];

$t = $translations[$language] ?? $translations['ar'];
$direction = $language === 'ar' ? 'rtl' : 'ltr';

// Traitement du formulaire
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_product'])) {
    try {
        $pdo = new PDO(
            "mysql:host=" . DB_HOST . ";port=" . DB_PORT . ";dbname=" . DB_DATABASE . ";charset=" . DB_CHARSET,
            DB_USERNAME,
            DB_PASSWORD,
            [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
        );
        
        $quotaManager = new QuotaManager($pdo);
        
        // Vérifier les quotas avant création
        $canCreate = $quotaManager->canCreate($currentUserId, 'products');
        
        if (!$canCreate['allowed']) {
            $message = $t['quota_exceeded'];
            $messageType = 'danger';
        } else {
            // Valider les données
            $name = trim($_POST['name'] ?? '');
            $description = trim($_POST['description'] ?? '');
            $price = floatval($_POST['price'] ?? 0);
            $categoryId = intval($_POST['category_id'] ?? 0);
            
            if (empty($name)) {
                throw new Exception($t['required_field'] . ': ' . $t['product_name']);
            }
            
            // Créer le slug
            $slug = strtolower(preg_replace('/[^a-zA-Z0-9\u0600-\u06FF]+/', '-', $name));
            $slug = trim($slug, '-');
            
            // Insérer le produit
            $sql = "
                INSERT INTO products (user_id, category_id, name, slug, description, price, status, created_at)
                VALUES (?, ?, ?, ?, ?, ?, 'draft', NOW())
            ";
            
            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute([
                $currentUserId,
                $categoryId > 0 ? $categoryId : null,
                $name,
                $slug,
                $description,
                $price
            ]);
            
            if ($result) {
                // Mettre à jour le cache des quotas
                $quotaManager->updateQuotaCache($currentUserId);
                
                // Log de l'activité
                $quotaManager->logActivity($currentUserId, 'create_product', 'product', $pdo->lastInsertId());
                
                $message = $t['success_message'];
                $messageType = 'success';
                
                // Rediriger pour éviter la resoumission
                header('Location: ' . $_SERVER['PHP_SELF'] . '?lang=' . $language . '&success=1');
                exit();
            }
        }
        
    } catch (Exception $e) {
        $message = $t['error_message'] . ': ' . $e->getMessage();
        $messageType = 'danger';
    }
}

// Message de succès après redirection
if (isset($_GET['success'])) {
    $message = $t['success_message'];
    $messageType = 'success';
}

// Récupérer les catégories de l'utilisateur
$categories = [];
try {
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";port=" . DB_PORT . ";dbname=" . DB_DATABASE . ";charset=" . DB_CHARSET,
        DB_USERNAME,
        DB_PASSWORD,
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    $sql = "SELECT id, name FROM categories WHERE user_id = ? AND is_active = 1 ORDER BY name";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$currentUserId]);
    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    // Ignorer les erreurs de récupération des catégories
}
?>

<!DOCTYPE html>
<html lang="<?php echo $language; ?>" dir="<?php echo $direction; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($t['create_product']); ?></title>
    
    <!-- Bootstrap 5 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background-color: #f8f9fa;
            font-family: <?php echo $language === 'ar' ? 'Cairo, sans-serif' : 'Inter, sans-serif'; ?>;
        }
        
        .form-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 2rem;
        }
        
        .quota-sidebar {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 1.5rem;
            position: sticky;
            top: 2rem;
        }
        
        .form-label {
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .form-control, .form-select {
            border-radius: 8px;
            border: 1px solid #e0e0e0;
            padding: 0.75rem;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
            padding: 0.75rem 2rem;
            font-weight: 600;
        }
        
        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        .alert {
            border-radius: 8px;
            border: none;
        }
        
        .quota-warning {
            background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
            color: #2d3436;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        
        .quota-error {
            background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
            color: white;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <!-- Formulaire principal -->
            <div class="col-lg-8">
                <div class="form-container">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2>
                            <i class="fas fa-plus-circle text-primary me-2"></i>
                            <?php echo htmlspecialchars($t['create_product']); ?>
                        </h2>
                        
                        <!-- Sélecteur de langue -->
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-globe me-1"></i>
                                <?php echo strtoupper($language); ?>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="?lang=ar">العربية</a></li>
                                <li><a class="dropdown-item" href="?lang=fr">Français</a></li>
                                <li><a class="dropdown-item" href="?lang=en">English</a></li>
                            </ul>
                        </div>
                    </div>
                    
                    <?php if ($message): ?>
                    <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                        <i class="fas fa-<?php echo $messageType === 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                        <?php echo htmlspecialchars($message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    <?php endif; ?>
                    
                    <!-- Vérification des quotas en temps réel -->
                    <div id="quota-check-result"></div>
                    
                    <form method="POST" id="productForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">
                                    <?php echo htmlspecialchars($t['product_name']); ?>
                                    <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="name" name="name" required
                                       placeholder="<?php echo htmlspecialchars($t['product_name']); ?>">
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="price" class="form-label">
                                    <?php echo htmlspecialchars($t['product_price']); ?>
                                </label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="price" name="price" 
                                           step="0.01" min="0" placeholder="0.00">
                                    <span class="input-group-text">$</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="category_id" class="form-label">
                                <?php echo htmlspecialchars($t['product_category']); ?>
                            </label>
                            <select class="form-select" id="category_id" name="category_id">
                                <option value=""><?php echo htmlspecialchars($t['select_category']); ?></option>
                                <?php foreach ($categories as $category): ?>
                                <option value="<?php echo $category['id']; ?>">
                                    <?php echo htmlspecialchars($category['name']); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="mb-4">
                            <label for="description" class="form-label">
                                <?php echo htmlspecialchars($t['product_description']); ?>
                            </label>
                            <textarea class="form-control" id="description" name="description" rows="4"
                                      placeholder="<?php echo htmlspecialchars($t['product_description']); ?>"></textarea>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" name="create_product" class="btn btn-primary btn-lg" id="submitBtn">
                                <i class="fas fa-plus me-2"></i>
                                <?php echo htmlspecialchars($t['create_button']); ?>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Sidebar des quotas -->
            <div class="col-lg-4">
                <div class="quota-sidebar">
                    <h5 class="mb-3">
                        <i class="fas fa-chart-pie text-primary me-2"></i>
                        <?php echo htmlspecialchars($t['quota_status']); ?>
                    </h5>
                    
                    <!-- Widget des quotas -->
                    <?php renderQuotaWidget($currentUserId, $language, false); ?>
                    
                    <!-- Informations supplémentaires -->
                    <div class="mt-3 p-3 bg-light rounded">
                        <h6 class="mb-2">
                            <i class="fas fa-info-circle text-info me-2"></i>
                            Conseils
                        </h6>
                        <ul class="list-unstyled mb-0 small text-muted">
                            <li class="mb-1">• Utilisez des noms de produits descriptifs</li>
                            <li class="mb-1">• Organisez vos produits par catégories</li>
                            <li class="mb-1">• Ajoutez des descriptions détaillées</li>
                            <li>• Surveillez vos quotas d'abonnement</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        const userId = <?php echo $currentUserId; ?>;
        const language = '<?php echo $language; ?>';
        const translations = <?php echo json_encode($t, JSON_UNESCAPED_UNICODE); ?>;
        
        // Vérifier les quotas avant soumission
        document.getElementById('productForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const submitBtn = document.getElementById('submitBtn');
            const originalText = submitBtn.innerHTML;
            
            // Afficher le loader
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Vérification...';
            submitBtn.disabled = true;
            
            try {
                // Vérifier les quotas
                const quotaCheck = await checkQuotaBeforeCreate(userId, 'products', language);
                
                if (!quotaCheck.success) {
                    // Afficher l'erreur de quota
                    showQuotaError(quotaCheck.message, quotaCheck.error);
                    
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                    return;
                }
                
                // Si les quotas sont OK, soumettre le formulaire
                this.submit();
                
            } catch (error) {
                console.error('Erreur lors de la vérification des quotas:', error);
                
                // En cas d'erreur réseau, permettre la soumission
                this.submit();
            }
        });
        
        // Afficher les erreurs de quota
        function showQuotaError(message, errorType) {
            const container = document.getElementById('quota-check-result');
            
            let alertClass = 'alert-warning';
            let icon = 'exclamation-triangle';
            
            if (errorType === 'quota_exceeded' || errorType === 'subscription_inactive') {
                alertClass = 'alert-danger';
                icon = 'times-circle';
            }
            
            container.innerHTML = `
                <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                    <i class="fas fa-${icon} me-2"></i>
                    ${message}
                    ${errorType === 'quota_exceeded' ? `
                        <hr>
                        <a href="/upgrade" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-arrow-up me-1"></i>
                            ${translations.upgrade_subscription}
                        </a>
                    ` : ''}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
        }
        
        // Actualiser le widget des quotas après création
        function refreshQuotaWidget() {
            loadQuotaWidget(userId, language, false);
        }
        
        // Vérification en temps réel lors de la saisie
        let quotaCheckTimeout;
        document.getElementById('name').addEventListener('input', function() {
            clearTimeout(quotaCheckTimeout);
            quotaCheckTimeout = setTimeout(async () => {
                if (this.value.trim()) {
                    const quotaCheck = await checkQuotaBeforeCreate(userId, 'products', language);
                    
                    if (!quotaCheck.success) {
                        showQuotaError(quotaCheck.message, quotaCheck.error);
                    } else {
                        document.getElementById('quota-check-result').innerHTML = '';
                    }
                }
            }, 1000);
        });
    </script>
</body>
</html>