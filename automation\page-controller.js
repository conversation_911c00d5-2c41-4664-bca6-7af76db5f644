/**
 * Contrôleur principal Puppeteer pour les pages de destination
 * Système d'automatisation avancé avec support multilingue
 */

const puppeteer = require('puppeteer');
const chalk = require('chalk');
const fs = require('fs-extra');
const path = require('path');

class PageController {
    constructor(options = {}) {
        this.options = {
            headless: options.headless !== false, // Par défaut en mode headless
            slowMo: options.slowMo || 100, // Ralentir les actions
            viewport: options.viewport || { width: 1366, height: 768 },
            baseUrl: options.baseUrl || 'http://localhost:8000',
            timeout: options.timeout || 30000,
            screenshotPath: options.screenshotPath || './screenshots',
            debug: options.debug || false,
            ...options
        };
        
        this.browser = null;
        this.page = null;
        this.currentLanguage = 'ar';
        
        // Configuration des langues
        this.languages = {
            ar: { code: 'ar', name: 'العربية', flag: '🇩🇿', dir: 'rtl' },
            fr: { code: 'fr', name: 'Fran<PERSON>', flag: '🇫🇷', dir: 'ltr' },
            en: { code: 'en', name: 'English', flag: '🇺🇸', dir: 'ltr' }
        };
        
        this.log = this.options.debug ? console.log : () => {};
    }
    
    /**
     * Initialiser le navigateur et la page
     */
    async init() {
        try {
            console.log(chalk.blue('🚀 Initialisation de Puppeteer...'));
            
            this.browser = await puppeteer.launch({
                headless: this.options.headless,
                slowMo: this.options.slowMo,
                args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-web-security',
                    '--allow-running-insecure-content'
                ]
            });
            
            this.page = await this.browser.newPage();
            
            // Configuration de la page
            await this.page.setViewport(this.options.viewport);
            await this.page.setDefaultTimeout(this.options.timeout);
            
            // Intercepter les erreurs JavaScript
            this.page.on('pageerror', error => {
                console.log(chalk.red('❌ Erreur JavaScript:'), error.message);
            });
            
            // Intercepter les erreurs de console
            this.page.on('console', msg => {
                if (msg.type() === 'error') {
                    console.log(chalk.red('🔴 Console Error:'), msg.text());
                } else if (this.options.debug) {
                    console.log(chalk.gray('📝 Console:'), msg.text());
                }
            });
            
            // Créer le dossier de captures d'écran
            await fs.ensureDir(this.options.screenshotPath);
            
            console.log(chalk.green('✅ Puppeteer initialisé avec succès'));
            return this;
            
        } catch (error) {
            console.error(chalk.red('❌ Erreur d\'initialisation:'), error);
            throw error;
        }
    }
    
    /**
     * Naviguer vers une page
     */
    async navigateTo(path = '') {
        const url = `${this.options.baseUrl}/${path}`.replace(/\/+/g, '/').replace(':/', '://');
        
        console.log(chalk.cyan(`🌐 Navigation vers: ${url}`));
        
        try {
            await this.page.goto(url, { 
                waitUntil: 'networkidle2',
                timeout: this.options.timeout 
            });
            
            // Attendre que la page soit complètement chargée
            await this.page.waitForSelector('body', { timeout: 5000 });
            
            console.log(chalk.green(`✅ Page chargée: ${await this.page.title()}`));
            return this;
            
        } catch (error) {
            console.error(chalk.red(`❌ Erreur de navigation vers ${url}:`), error.message);
            throw error;
        }
    }
    
    /**
     * Changer la langue de la page
     */
    async changeLanguage(langCode) {
        if (!this.languages[langCode]) {
            throw new Error(`Langue non supportée: ${langCode}`);
        }
        
        console.log(chalk.yellow(`🌍 Changement de langue vers: ${this.languages[langCode].name}`));
        
        try {
            // Cliquer sur le sélecteur de langue
            await this.page.click('.lang-toggle');
            await this.page.waitForSelector('.lang-dropdown.show', { timeout: 2000 });
            
            // Sélectionner la langue
            await this.page.click(`[data-lang="${langCode}"]`);
            
            // Attendre que le changement soit effectué
            await this.page.waitForFunction(
                (lang) => document.documentElement.lang === lang,
                { timeout: 5000 },
                langCode
            );
            
            this.currentLanguage = langCode;
            console.log(chalk.green(`✅ Langue changée vers: ${this.languages[langCode].name}`));
            
            return this;
            
        } catch (error) {
            console.error(chalk.red('❌ Erreur de changement de langue:'), error.message);
            throw error;
        }
    }
    
    /**
     * Remplir un formulaire
     */
    async fillForm(formData) {
        console.log(chalk.cyan('📝 Remplissage du formulaire...'));
        
        try {
            for (const [selector, value] of Object.entries(formData)) {
                if (value !== null && value !== undefined) {
                    await this.page.waitForSelector(selector, { timeout: 5000 });
                    await this.page.click(selector);
                    await this.page.keyboard.down('Control');
                    await this.page.keyboard.press('KeyA');
                    await this.page.keyboard.up('Control');
                    await this.page.type(selector, String(value), { delay: 50 });
                    
                    this.log(`Champ ${selector} rempli avec: ${value}`);
                }
            }
            
            console.log(chalk.green('✅ Formulaire rempli'));
            return this;
            
        } catch (error) {
            console.error(chalk.red('❌ Erreur de remplissage du formulaire:'), error.message);
            throw error;
        }
    }
    
    /**
     * Cliquer sur un élément avec attente
     */
    async clickElement(selector, options = {}) {
        const { timeout = 5000, waitForNavigation = false } = options;
        
        console.log(chalk.cyan(`👆 Clic sur: ${selector}`));
        
        try {
            await this.page.waitForSelector(selector, { timeout });
            
            if (waitForNavigation) {
                await Promise.all([
                    this.page.waitForNavigation({ waitUntil: 'networkidle2' }),
                    this.page.click(selector)
                ]);
            } else {
                await this.page.click(selector);
            }
            
            console.log(chalk.green(`✅ Clic effectué sur: ${selector}`));
            return this;
            
        } catch (error) {
            console.error(chalk.red(`❌ Erreur de clic sur ${selector}:`), error.message);
            throw error;
        }
    }
    
    /**
     * Attendre un élément
     */
    async waitForElement(selector, options = {}) {
        const { timeout = 5000, visible = true } = options;
        
        console.log(chalk.cyan(`⏳ Attente de l'élément: ${selector}`));
        
        try {
            await this.page.waitForSelector(selector, { timeout, visible });
            console.log(chalk.green(`✅ Élément trouvé: ${selector}`));
            return this;
            
        } catch (error) {
            console.error(chalk.red(`❌ Élément non trouvé ${selector}:`), error.message);
            throw error;
        }
    }
    
    /**
     * Prendre une capture d'écran
     */
    async takeScreenshot(name, options = {}) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `${name}_${timestamp}.png`;
        const filepath = path.join(this.options.screenshotPath, filename);
        
        console.log(chalk.cyan(`📸 Capture d'écran: ${filename}`));
        
        try {
            await this.page.screenshot({
                path: filepath,
                fullPage: options.fullPage || true,
                ...options
            });
            
            console.log(chalk.green(`✅ Capture sauvée: ${filepath}`));
            return filepath;
            
        } catch (error) {
            console.error(chalk.red('❌ Erreur de capture d\'écran:'), error.message);
            throw error;
        }
    }
    
    /**
     * Évaluer du JavaScript sur la page
     */
    async evaluate(fn, ...args) {
        try {
            const result = await this.page.evaluate(fn, ...args);
            this.log('JavaScript évalué avec succès');
            return result;
            
        } catch (error) {
            console.error(chalk.red('❌ Erreur d\'évaluation JavaScript:'), error.message);
            throw error;
        }
    }
    
    /**
     * Obtenir les métriques de performance
     */
    async getPerformanceMetrics() {
        console.log(chalk.cyan('📊 Collecte des métriques de performance...'));
        
        try {
            const metrics = await this.page.metrics();
            const performanceTiming = await this.page.evaluate(() => {
                return JSON.stringify(window.performance.timing);
            });
            
            const result = {
                puppeteerMetrics: metrics,
                performanceTiming: JSON.parse(performanceTiming),
                timestamp: new Date().toISOString()
            };
            
            console.log(chalk.green('✅ Métriques collectées'));
            return result;
            
        } catch (error) {
            console.error(chalk.red('❌ Erreur de collecte des métriques:'), error.message);
            throw error;
        }
    }
    
    /**
     * Tester la responsivité
     */
    async testResponsiveness(viewports = []) {
        const defaultViewports = [
            { name: 'Mobile', width: 375, height: 667 },
            { name: 'Tablet', width: 768, height: 1024 },
            { name: 'Desktop', width: 1366, height: 768 },
            { name: 'Large Desktop', width: 1920, height: 1080 }
        ];
        
        const testViewports = viewports.length > 0 ? viewports : defaultViewports;
        const results = [];
        
        console.log(chalk.cyan('📱 Test de responsivité...'));
        
        for (const viewport of testViewports) {
            try {
                console.log(chalk.yellow(`📐 Test ${viewport.name}: ${viewport.width}x${viewport.height}`));
                
                await this.page.setViewport(viewport);
                await this.page.waitForTimeout(1000); // Attendre le redimensionnement
                
                const screenshot = await this.takeScreenshot(`responsive_${viewport.name.toLowerCase().replace(' ', '_')}`);
                
                results.push({
                    viewport,
                    screenshot,
                    success: true
                });
                
                console.log(chalk.green(`✅ ${viewport.name} testé`));
                
            } catch (error) {
                console.error(chalk.red(`❌ Erreur ${viewport.name}:`), error.message);
                results.push({
                    viewport,
                    error: error.message,
                    success: false
                });
            }
        }
        
        // Restaurer la taille originale
        await this.page.setViewport(this.options.viewport);
        
        console.log(chalk.green('✅ Test de responsivité terminé'));
        return results;
    }
    
    /**
     * Attendre et fermer le navigateur
     */
    async close() {
        if (this.browser) {
            console.log(chalk.blue('🔒 Fermeture du navigateur...'));
            await this.browser.close();
            console.log(chalk.green('✅ Navigateur fermé'));
        }
    }
    
    /**
     * Méthode utilitaire pour attendre
     */
    async wait(ms) {
        console.log(chalk.gray(`⏱️  Attente de ${ms}ms...`));
        await this.page.waitForTimeout(ms);
        return this;
    }
    
    /**
     * Obtenir des informations sur la page actuelle
     */
    async getPageInfo() {
        try {
            const info = await this.page.evaluate(() => {
                return {
                    title: document.title,
                    url: window.location.href,
                    language: document.documentElement.lang,
                    direction: document.documentElement.dir,
                    readyState: document.readyState
                };
            });
            
            return info;
            
        } catch (error) {
            console.error(chalk.red('❌ Erreur d\'obtention des infos de page:'), error.message);
            throw error;
        }
    }
}

module.exports = PageController;