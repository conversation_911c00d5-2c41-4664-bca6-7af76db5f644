/**
 * Test automatisé pour la page de connexion
 * Utilise Puppeteer pour tester les fonctionnalités de login
 */

const PageController = require('../page-controller');
const chalk = require('chalk');

class LoginTest {
    constructor(options = {}) {
        this.controller = new PageController({
            headless: false, // Mode visible pour les tests
            slowMo: 200,
            debug: true,
            ...options
        });
        
        this.testData = {
            validUser: {
                email: '<EMAIL>',
                password: 'Test123456!'
            },
            invalidUser: {
                email: '<EMAIL>',
                password: 'wrongpassword'
            }
        };
    }
    
    /**
     * Exécuter tous les tests de connexion
     */
    async runAllTests() {
        console.log(chalk.blue.bold('🧪 === TESTS DE CONNEXION ==='));
        
        try {
            await this.controller.init();
            
            // Tests principaux
            await this.testPageLoad();
            await this.testLanguageSwitching();
            await this.testFormValidation();
            await this.testLoginFlow();
            await this.testResponsiveDesign();
            await this.testAccessibility();
            
            console.log(chalk.green.bold('✅ Tous les tests de connexion réussis!'));
            
        } catch (error) {
            console.error(chalk.red.bold('❌ Échec des tests:'), error.message);
            throw error;
        } finally {
            await this.controller.close();
        }
    }
    
    /**
     * Test du chargement de la page
     */
    async testPageLoad() {
        console.log(chalk.cyan('\n📄 Test: Chargement de la page de connexion'));
        
        await this.controller.navigateTo('login.html');
        
        // Vérifier les éléments essentiels
        await this.controller.waitForElement('#loginForm');
        await this.controller.waitForElement('#email');
        await this.controller.waitForElement('#password');
        await this.controller.waitForElement('#loginBtn');
        await this.controller.waitForElement('.lang-toggle');
        
        // Vérifier le titre
        const pageInfo = await this.controller.getPageInfo();
        console.log(chalk.green(`✅ Page chargée: ${pageInfo.title}`));
        console.log(chalk.gray(`   Langue: ${pageInfo.language}, Direction: ${pageInfo.direction}`));
        
        await this.controller.takeScreenshot('login_page_loaded');
    }
    
    /**
     * Test du changement de langue
     */
    async testLanguageSwitching() {
        console.log(chalk.cyan('\n🌍 Test: Changement de langue'));
        
        const languages = ['fr', 'en', 'ar'];
        
        for (const lang of languages) {
            await this.controller.changeLanguage(lang);
            await this.controller.wait(1000);
            
            const pageInfo = await this.controller.getPageInfo();
            console.log(chalk.green(`✅ Langue changée vers: ${lang} (${pageInfo.language})`));
            
            await this.controller.takeScreenshot(`login_language_${lang}`);
        }
    }
    
    /**
     * Test de validation du formulaire
     */
    async testFormValidation() {
        console.log(chalk.cyan('\n✅ Test: Validation du formulaire'));
        
        // Test avec champs vides
        await this.controller.clickElement('#loginBtn');
        await this.controller.wait(1000);
        
        // Vérifier les messages d'erreur
        const hasEmailError = await this.controller.evaluate(() => {
            const emailField = document.getElementById('email');
            return !emailField.checkValidity();
        });
        
        const hasPasswordError = await this.controller.evaluate(() => {
            const passwordField = document.getElementById('password');
            return !passwordField.checkValidity();
        });
        
        console.log(chalk.green(`✅ Validation email vide: ${hasEmailError ? 'OK' : 'ÉCHEC'}`));
        console.log(chalk.green(`✅ Validation mot de passe vide: ${hasPasswordError ? 'OK' : 'ÉCHEC'}`));
        
        // Test avec email invalide
        await this.controller.fillForm({
            '#email': 'email-invalide',
            '#password': 'test123'
        });
        
        await this.controller.clickElement('#loginBtn');
        await this.controller.wait(1000);
        
        const emailValidation = await this.controller.evaluate(() => {
            const emailField = document.getElementById('email');
            return !emailField.checkValidity();
        });
        
        console.log(chalk.green(`✅ Validation email invalide: ${emailValidation ? 'OK' : 'ÉCHEC'}`));
        
        await this.controller.takeScreenshot('login_form_validation');
    }
    
    /**
     * Test du flux de connexion
     */
    async testLoginFlow() {
        console.log(chalk.cyan('\n🔐 Test: Flux de connexion'));
        
        // Nettoyer les champs
        await this.controller.fillForm({
            '#email': '',
            '#password': ''
        });
        
        // Test avec utilisateur valide
        await this.controller.fillForm({
            '#email': this.testData.validUser.email,
            '#password': this.testData.validUser.password
        });
        
        await this.controller.takeScreenshot('login_form_filled');
        
        // Cliquer sur le bouton de connexion
        await this.controller.clickElement('#loginBtn');
        await this.controller.wait(2000);
        
        // Vérifier l'état du bouton (loading)
        const buttonState = await this.controller.evaluate(() => {
            const btn = document.getElementById('loginBtn');
            return {
                disabled: btn.disabled,
                text: btn.textContent.trim(),
                classList: Array.from(btn.classList)
            };
        });
        
        console.log(chalk.green(`✅ État du bouton après clic:`));
        console.log(chalk.gray(`   Désactivé: ${buttonState.disabled}`));
        console.log(chalk.gray(`   Texte: ${buttonState.text}`));
        console.log(chalk.gray(`   Classes: ${buttonState.classList.join(', ')}`));
        
        await this.controller.takeScreenshot('login_attempt');
        
        // Attendre la réponse (succès ou erreur)
        await this.controller.wait(3000);
        
        // Vérifier s'il y a un message d'erreur ou de succès
        const messages = await this.controller.evaluate(() => {
            const errorMsg = document.querySelector('.error-message, .alert-danger');
            const successMsg = document.querySelector('.success-message, .alert-success');
            
            return {
                error: errorMsg ? errorMsg.textContent.trim() : null,
                success: successMsg ? successMsg.textContent.trim() : null
            };
        });
        
        if (messages.error) {
            console.log(chalk.yellow(`⚠️  Message d'erreur: ${messages.error}`));
        }
        
        if (messages.success) {
            console.log(chalk.green(`✅ Message de succès: ${messages.success}`));
        }
        
        await this.controller.takeScreenshot('login_result');
    }
    
    /**
     * Test du design responsive
     */
    async testResponsiveDesign() {
        console.log(chalk.cyan('\n📱 Test: Design responsive'));
        
        const results = await this.controller.testResponsiveness();
        
        results.forEach(result => {
            if (result.success) {
                console.log(chalk.green(`✅ ${result.viewport.name}: OK`));
            } else {
                console.log(chalk.red(`❌ ${result.viewport.name}: ${result.error}`));
            }
        });
        
        return results;
    }
    
    /**
     * Test d'accessibilité
     */
    async testAccessibility() {
        console.log(chalk.cyan('\n♿ Test: Accessibilité'));
        
        // Vérifier les attributs d'accessibilité
        const accessibility = await this.controller.evaluate(() => {
            const checks = {
                emailLabel: !!document.querySelector('label[for="email"]'),
                passwordLabel: !!document.querySelector('label[for="password"]'),
                emailAria: !!document.getElementById('email').getAttribute('aria-label'),
                passwordAria: !!document.getElementById('password').getAttribute('aria-label'),
                formRole: !!document.querySelector('form[role]'),
                langAttribute: !!document.documentElement.getAttribute('lang')
            };
            
            return checks;
        });
        
        Object.entries(accessibility).forEach(([check, passed]) => {
            console.log(chalk[passed ? 'green' : 'red'](`${passed ? '✅' : '❌'} ${check}: ${passed ? 'OK' : 'MANQUANT'}`));
        });
        
        // Test de navigation au clavier
        await this.controller.page.keyboard.press('Tab');
        await this.controller.wait(500);
        await this.controller.page.keyboard.press('Tab');
        await this.controller.wait(500);
        await this.controller.page.keyboard.press('Tab');
        await this.controller.wait(500);
        
        await this.controller.takeScreenshot('login_keyboard_navigation');
        
        console.log(chalk.green('✅ Test de navigation clavier terminé'));
    }
    
    /**
     * Test spécifique pour le lien vers l'inscription
     */
    async testRegisterLink() {
        console.log(chalk.cyan('\n🔗 Test: Lien vers l\'inscription'));
        
        await this.controller.navigateTo('login.html');
        
        // Chercher le lien vers l'inscription
        const registerLink = await this.controller.evaluate(() => {
            const link = document.querySelector('a[href*="register"]');
            return link ? {
                href: link.href,
                text: link.textContent.trim(),
                exists: true
            } : { exists: false };
        });
        
        if (registerLink.exists) {
            console.log(chalk.green(`✅ Lien d'inscription trouvé: ${registerLink.text}`));
            console.log(chalk.gray(`   URL: ${registerLink.href}`));
            
            // Cliquer sur le lien
            await this.controller.clickElement('a[href*="register"]', { waitForNavigation: true });
            
            const pageInfo = await this.controller.getPageInfo();
            console.log(chalk.green(`✅ Navigation vers: ${pageInfo.title}`));
            
            await this.controller.takeScreenshot('navigated_to_register');
        } else {
            console.log(chalk.red('❌ Lien d\'inscription non trouvé'));
        }
    }
}

// Exporter la classe
module.exports = LoginTest;

// Exécution directe si le fichier est appelé
if (require.main === module) {
    const test = new LoginTest();
    test.runAllTests().catch(console.error);
}