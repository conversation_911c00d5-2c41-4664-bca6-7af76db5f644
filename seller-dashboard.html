<!DOCTYPE html>
<html lang="fr">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Dashboard Vendeur - LandingPage</title>
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
      rel="stylesheet"
    />
    <style>
      :root {
        --primary-color: #667eea;
        --secondary-color: #764ba2;
        --sidebar-width: 280px;
      }

      body {
        background: #f8f9fa;
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
      }

      .sidebar {
        position: fixed;
        top: 0;
        left: 0;
        height: 100vh;
        width: var(--sidebar-width);
        background: linear-gradient(
          135deg,
          var(--primary-color) 0%,
          var(--secondary-color) 100%
        );
        color: white;
        z-index: 1000;
        overflow-y: auto;
      }

      .sidebar-header {
        padding: 30px 20px;
        text-align: center;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      }

      .sidebar-nav {
        padding: 20px 0;
      }

      .nav-item {
        margin: 5px 15px;
      }

      .nav-link {
        color: rgba(255, 255, 255, 0.8);
        padding: 15px 20px;
        border-radius: 10px;
        transition: all 0.3s ease;
        text-decoration: none;
        display: flex;
        align-items: center;
      }

      .nav-link:hover,
      .nav-link.active {
        background: rgba(255, 255, 255, 0.1);
        color: white;
        transform: translateX(5px);
      }

      .nav-link i {
        width: 20px;
        margin-right: 15px;
      }

      .main-content {
        margin-left: var(--sidebar-width);
        padding: 30px;
        min-height: 100vh;
      }

      .page-header {
        background: white;
        border-radius: 15px;
        padding: 30px;
        margin-bottom: 30px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
      }

      .stats-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        transition: transform 0.3s ease;
      }

      .stats-card:hover {
        transform: translateY(-5px);
      }

      .stats-icon {
        width: 60px;
        height: 60px;
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: white;
        margin-bottom: 15px;
      }

      .content-section {
        display: none;
      }

      .content-section.active {
        display: block;
      }

      .card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
      }

      .btn-primary {
        background: linear-gradient(
          135deg,
          var(--primary-color) 0%,
          var(--secondary-color) 100%
        );
        border: none;
        border-radius: 10px;
        padding: 10px 20px;
      }

      .language-selector {
        position: absolute;
        top: 20px;
        right: 20px;
      }

      .language-btn {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: white;
        padding: 5px 10px;
        margin: 0 2px;
        border-radius: 5px;
        cursor: pointer;
        transition: all 0.3s ease;
      }

      .language-btn.active,
      .language-btn:hover {
        background: rgba(255, 255, 255, 0.2);
      }

      .product-card {
        border: 1px solid #e9ecef;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 15px;
        transition: all 0.3s ease;
      }

      .product-card:hover {
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
      }

      .product-image {
        width: 80px;
        height: 80px;
        object-fit: cover;
        border-radius: 8px;
      }

      @media (max-width: 768px) {
        .sidebar {
          transform: translateX(-100%);
          transition: transform 0.3s ease;
        }

        .sidebar.show {
          transform: translateX(0);
        }

        .main-content {
          margin-left: 0;
        }
      }
    </style>
  </head>
  <body>
    <!-- Sidebar -->
    <div class="sidebar">
      <div class="language-selector">
        <button class="language-btn active" data-lang="fr">FR</button>
        <button class="language-btn" data-lang="ar">AR</button>
        <button class="language-btn" data-lang="en">EN</button>
      </div>

      <div class="sidebar-header">
        <i class="fas fa-store fa-3x mb-3"></i>
        <h4 id="store-name">TechStore Algeria</h4>
        <p id="seller-email"><EMAIL></p>
      </div>

      <nav class="sidebar-nav">
        <div class="nav-item">
          <a href="#" class="nav-link active" data-section="dashboard">
            <i class="fas fa-tachometer-alt"></i>
            <span data-translate="dashboard">Tableau de bord</span>
          </a>
        </div>
        <div class="nav-item">
          <a href="#" class="nav-link" data-section="products">
            <i class="fas fa-box"></i>
            <span data-translate="products">Produits</span>
          </a>
        </div>
        <div class="nav-item">
          <a href="#" class="nav-link" data-section="categories">
            <i class="fas fa-tags"></i>
            <span data-translate="categories">Catégories</span>
          </a>
        </div>
        <div class="nav-item">
          <a href="#" class="nav-link" data-section="landing-pages">
            <i class="fas fa-file-alt"></i>
            <span data-translate="landing_pages">Landing Pages</span>
          </a>
        </div>
        <div class="nav-item">
          <a href="#" class="nav-link" data-section="orders">
            <i class="fas fa-shopping-cart"></i>
            <span data-translate="orders">Commandes</span>
          </a>
        </div>
        <div class="nav-item">
          <a href="#" class="nav-link" data-section="google-sheets">
            <i class="fab fa-google"></i>
            <span data-translate="google_sheets">Google Sheets</span>
          </a>
        </div>
        <div class="nav-item">
          <a href="#" class="nav-link" data-section="messages">
            <i class="fas fa-envelope"></i>
            <span data-translate="messages">Messages</span>
          </a>
        </div>
        <div class="nav-item">
          <a href="#" class="nav-link" data-section="customization">
            <i class="fas fa-palette"></i>
            <span data-translate="customization">Personnalisation</span>
          </a>
        </div>
        <div class="nav-item">
          <a href="#" class="nav-link" data-section="profile">
            <i class="fas fa-user"></i>
            <span data-translate="profile">Profil</span>
          </a>
        </div>
        <div class="nav-item mt-4">
          <a href="#" class="nav-link" onclick="logout()">
            <i class="fas fa-sign-out-alt"></i>
            <span data-translate="logout">Déconnexion</span>
          </a>
        </div>
      </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
      <!-- Dashboard Section -->
      <div id="dashboard-section" class="content-section active">
        <div class="page-header">
          <h1 data-translate="welcome">Bienvenue dans votre espace vendeur</h1>
          <p data-translate="dashboard_subtitle">
            Gérez votre store et suivez vos performances
          </p>
        </div>

        <div class="row mb-4">
          <div class="col-md-3 mb-3">
            <div class="stats-card">
              <div
                class="stats-icon"
                style="
                  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                "
              >
                <i class="fas fa-box"></i>
              </div>
              <h3 id="total-products">10</h3>
              <p data-translate="total_products">Produits totaux</p>
            </div>
          </div>
          <div class="col-md-3 mb-3">
            <div class="stats-card">
              <div
                class="stats-icon"
                style="
                  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
                "
              >
                <i class="fas fa-shopping-cart"></i>
              </div>
              <h3 id="total-orders">0</h3>
              <p data-translate="total_orders">Commandes</p>
            </div>
          </div>
          <div class="col-md-3 mb-3">
            <div class="stats-card">
              <div
                class="stats-icon"
                style="
                  background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
                "
              >
                <i class="fas fa-eye"></i>
              </div>
              <h3 id="total-views">0</h3>
              <p data-translate="total_views">Vues du store</p>
            </div>
          </div>
          <div class="col-md-3 mb-3">
            <div class="stats-card">
              <div
                class="stats-icon"
                style="
                  background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
                "
              >
                <i class="fas fa-envelope"></i>
              </div>
              <h3 id="total-messages">0</h3>
              <p data-translate="total_messages">Messages</p>
            </div>
          </div>
        </div>

        <div class="row">
          <div class="col-md-8">
            <div class="card">
              <div class="card-header">
                <h5 data-translate="recent_activity">Activité récente</h5>
              </div>
              <div class="card-body">
                <p data-translate="no_recent_activity">
                  Aucune activité récente
                </p>
              </div>
            </div>
          </div>
          <div class="col-md-4">
            <div class="card">
              <div class="card-header">
                <h5 data-translate="quick_actions">Actions rapides</h5>
              </div>
              <div class="card-body">
                <div class="d-grid gap-2">
                  <button
                    class="btn btn-primary"
                    onclick="showSection('products')"
                  >
                    <i class="fas fa-plus me-2"></i
                    ><span data-translate="add_product"
                      >Ajouter un produit</span
                    >
                  </button>
                  <button class="btn btn-outline-primary" onclick="viewStore()">
                    <i class="fas fa-external-link-alt me-2"></i
                    ><span data-translate="view_store">Voir mon store</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Products Section -->
      <div id="products-section" class="content-section">
        <div class="page-header">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <h1 data-translate="products">Produits</h1>
              <p data-translate="products_subtitle">Gérez vos produits</p>
            </div>
            <button class="btn btn-primary" onclick="addProduct()">
              <i class="fas fa-plus me-2"></i
              ><span data-translate="add_product">Ajouter un produit</span>
            </button>
          </div>
        </div>

        <div class="card">
          <div class="card-body">
            <div id="products-list">
              <div class="text-center py-4">
                <div class="spinner-border" role="status"></div>
                <p class="mt-2">Chargement des produits...</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Categories Section -->
      <div id="categories-section" class="content-section">
        <div class="page-header">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <h1 data-translate="categories">Catégories</h1>
              <p data-translate="categories_subtitle">
                Organisez vos produits par catégories
              </p>
            </div>
            <button class="btn btn-primary" onclick="addCategory()">
              <i class="fas fa-plus me-2"></i
              ><span data-translate="add_category">Ajouter une catégorie</span>
            </button>
          </div>
        </div>

        <div class="card">
          <div class="card-body">
            <div id="categories-list">
              <div class="text-center py-4">
                <div class="spinner-border" role="status"></div>
                <p class="mt-2">Chargement des catégories...</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Landing Pages Section -->
      <div id="landing-pages-section" class="content-section">
        <div class="page-header">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <h1 data-translate="landing_pages">Landing Pages</h1>
              <p data-translate="landing_pages_subtitle">
                Créez et gérez vos pages de vente
              </p>
            </div>
            <button class="btn btn-primary" onclick="createLandingPage()">
              <i class="fas fa-plus me-2"></i
              ><span data-translate="create_landing_page"
                >Créer une landing page</span
              >
            </button>
          </div>
        </div>

        <div class="card">
          <div class="card-body">
            <div id="landing-pages-list">
              <div class="text-center py-4">
                <div class="spinner-border" role="status"></div>
                <p class="mt-2">Chargement des landing pages...</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Orders Section -->
      <div id="orders-section" class="content-section">
        <div class="page-header">
          <h1 data-translate="orders">Commandes</h1>
          <p data-translate="orders_subtitle">Suivez vos commandes et ventes</p>
        </div>

        <div class="card">
          <div class="card-body">
            <div class="text-center py-5">
              <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
              <h5>Aucune commande pour le moment</h5>
              <p class="text-muted">
                Les commandes de vos clients apparaîtront ici
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Google Sheets Section -->
      <div id="google-sheets-section" class="content-section">
        <div class="page-header">
          <h1 data-translate="google_sheets">Google Sheets</h1>
          <p data-translate="google_sheets_subtitle">
            Générez et exportez vos données
          </p>
        </div>

        <div class="row">
          <div class="col-md-6">
            <div class="card">
              <div class="card-header">
                <h5><i class="fas fa-table me-2"></i>Export des produits</h5>
              </div>
              <div class="card-body">
                <p>Exportez tous vos produits vers Google Sheets</p>
                <button
                  class="btn btn-success"
                  onclick="exportToGoogleSheets('products')"
                >
                  <i class="fab fa-google me-2"></i>Exporter les produits
                </button>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="card">
              <div class="card-header">
                <h5><i class="fas fa-chart-bar me-2"></i>Rapport de ventes</h5>
              </div>
              <div class="card-body">
                <p>Générez un rapport de vos performances</p>
                <button
                  class="btn btn-success"
                  onclick="exportToGoogleSheets('sales')"
                >
                  <i class="fab fa-google me-2"></i>Générer le rapport
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Messages Section -->
      <div id="messages-section" class="content-section">
        <div class="page-header">
          <h1 data-translate="messages">Messages</h1>
          <p data-translate="messages_subtitle">Messages de vos clients</p>
        </div>

        <div class="card">
          <div class="card-body">
            <div class="text-center py-5">
              <i class="fas fa-envelope fa-3x text-muted mb-3"></i>
              <h5>Aucun message</h5>
              <p class="text-muted">
                Les messages de vos clients apparaîtront ici
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Customization Section -->
      <div id="customization-section" class="content-section">
        <div class="page-header">
          <h1 data-translate="customization">Personnalisation</h1>
          <p data-translate="customization_subtitle">
            Personnalisez l'apparence de votre store
          </p>
        </div>

        <div class="row">
          <div class="col-md-6">
            <div class="card">
              <div class="card-header">
                <h5><i class="fas fa-palette me-2"></i>Couleurs et thème</h5>
              </div>
              <div class="card-body">
                <div class="mb-3">
                  <label class="form-label">Couleur principale</label>
                  <input
                    type="color"
                    class="form-control form-control-color"
                    id="primary-color"
                    value="#667eea"
                  />
                </div>
                <div class="mb-3">
                  <label class="form-label">Couleur secondaire</label>
                  <input
                    type="color"
                    class="form-control form-control-color"
                    id="secondary-color"
                    value="#764ba2"
                  />
                </div>
                <button class="btn btn-primary" onclick="saveColors()">
                  <i class="fas fa-save me-2"></i>Sauvegarder
                </button>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="card">
              <div class="card-header">
                <h5><i class="fas fa-cog me-2"></i>Paramètres d'affichage</h5>
              </div>
              <div class="card-body">
                <div class="form-check mb-3">
                  <input
                    class="form-check-input"
                    type="checkbox"
                    id="show-store-name"
                    checked
                  />
                  <label class="form-check-label" for="show-store-name">
                    Afficher le nom du store
                  </label>
                </div>
                <div class="form-check mb-3">
                  <input
                    class="form-check-input"
                    type="checkbox"
                    id="show-description"
                    checked
                  />
                  <label class="form-check-label" for="show-description">
                    Afficher la description
                  </label>
                </div>
                <div class="form-check mb-3">
                  <input
                    class="form-check-input"
                    type="checkbox"
                    id="show-categories"
                    checked
                  />
                  <label class="form-check-label" for="show-categories">
                    Afficher les filtres de catégories
                  </label>
                </div>
                <button class="btn btn-primary" onclick="saveDisplaySettings()">
                  <i class="fas fa-save me-2"></i>Sauvegarder
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Profile Section -->
      <div id="profile-section" class="content-section">
        <div class="page-header">
          <h1 data-translate="profile">Profil</h1>
          <p data-translate="profile_subtitle">
            Gérez vos informations personnelles
          </p>
        </div>

        <div class="row">
          <div class="col-md-8">
            <div class="card">
              <div class="card-header">
                <h5>
                  <i class="fas fa-user me-2"></i>Informations personnelles
                </h5>
              </div>
              <div class="card-body">
                <form id="profile-form">
                  <div class="row">
                    <div class="col-md-6 mb-3">
                      <label class="form-label">Nom du store</label>
                      <input
                        type="text"
                        class="form-control"
                        id="store-name-input"
                        value="TechStore Algeria"
                      />
                    </div>
                    <div class="col-md-6 mb-3">
                      <label class="form-label">Email</label>
                      <input
                        type="email"
                        class="form-control"
                        id="email-input"
                        value="<EMAIL>"
                        readonly
                      />
                    </div>
                  </div>
                  <div class="mb-3">
                    <label class="form-label">Description du store</label>
                    <textarea
                      class="form-control"
                      id="store-description"
                      rows="3"
                    >
Votre destination pour les dernières technologies</textarea
                    >
                  </div>
                  <div class="row">
                    <div class="col-md-6 mb-3">
                      <label class="form-label">Téléphone</label>
                      <input
                        type="tel"
                        class="form-control"
                        id="phone-input"
                        placeholder="+213 XXX XXX XXX"
                      />
                    </div>
                    <div class="col-md-6 mb-3">
                      <label class="form-label">Adresse</label>
                      <input
                        type="text"
                        class="form-control"
                        id="address-input"
                        placeholder="Votre adresse"
                      />
                    </div>
                  </div>
                  <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-2"></i>Sauvegarder
                  </button>
                </form>
              </div>
            </div>
          </div>
          <div class="col-md-4">
            <div class="card">
              <div class="card-header">
                <h5><i class="fas fa-link me-2"></i>Liens de votre store</h5>
              </div>
              <div class="card-body">
                <div class="mb-3">
                  <label class="form-label">URL principale</label>
                  <div class="input-group">
                    <input
                      type="text"
                      class="form-control"
                      value="http://localhost:8000/techstore-algeria"
                      readonly
                    />
                    <button
                      class="btn btn-outline-secondary"
                      onclick="copyToClipboard('http://localhost:8000/techstore-algeria')"
                    >
                      <i class="fas fa-copy"></i>
                    </button>
                  </div>
                </div>
                <div class="mb-3">
                  <label class="form-label">URL alternative</label>
                  <div class="input-group">
                    <input
                      type="text"
                      class="form-control"
                      value="http://localhost:8000/store/TechStore Algeria"
                      readonly
                    />
                    <button
                      class="btn btn-outline-secondary"
                      onclick="copyToClipboard('http://localhost:8000/store/TechStore Algeria')"
                    >
                      <i class="fas fa-copy"></i>
                    </button>
                  </div>
                </div>
                <button class="btn btn-success w-100" onclick="viewStore()">
                  <i class="fas fa-external-link-alt me-2"></i>Voir mon store
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
      // Vérifier la connexion
      document.addEventListener("DOMContentLoaded", function () {
        if (!sessionStorage.getItem("seller_logged_in")) {
          window.location.href = "seller-login.html";
          return;
        }

        // Initialiser le dashboard
        initializeDashboard();
      });

      // Traductions multilingues
      const translations = {
        fr: {
          dashboard: "Tableau de bord",
          products: "Produits",
          categories: "Catégories",
          landing_pages: "Landing Pages",
          orders: "Commandes",
          google_sheets: "Google Sheets",
          messages: "Messages",
          customization: "Personnalisation",
          profile: "Profil",
          logout: "Déconnexion",
          welcome: "Bienvenue dans votre espace vendeur",
          dashboard_subtitle: "Gérez votre store et suivez vos performances",
          total_products: "Produits totaux",
          total_orders: "Commandes",
          total_views: "Vues du store",
          total_messages: "Messages",
          recent_activity: "Activité récente",
          quick_actions: "Actions rapides",
          add_product: "Ajouter un produit",
          view_store: "Voir mon store",
        },
        ar: {
          dashboard: "لوحة التحكم",
          products: "المنتجات",
          categories: "الفئات",
          landing_pages: "صفحات الهبوط",
          orders: "الطلبات",
          google_sheets: "جداول جوجل",
          messages: "الرسائل",
          customization: "التخصيص",
          profile: "الملف الشخصي",
          logout: "تسجيل الخروج",
          welcome: "مرحباً بك في مساحة البائع",
          dashboard_subtitle: "إدارة متجرك وتتبع أدائك",
          total_products: "إجمالي المنتجات",
          total_orders: "الطلبات",
          total_views: "مشاهدات المتجر",
          total_messages: "الرسائل",
          recent_activity: "النشاط الأخير",
          quick_actions: "إجراءات سريعة",
          add_product: "إضافة منتج",
          view_store: "عرض متجري",
        },
        en: {
          dashboard: "Dashboard",
          products: "Products",
          categories: "Categories",
          landing_pages: "Landing Pages",
          orders: "Orders",
          google_sheets: "Google Sheets",
          messages: "Messages",
          customization: "Customization",
          profile: "Profile",
          logout: "Logout",
          welcome: "Welcome to your seller space",
          dashboard_subtitle: "Manage your store and track your performance",
          total_products: "Total products",
          total_orders: "Orders",
          total_views: "Store views",
          total_messages: "Messages",
          recent_activity: "Recent activity",
          quick_actions: "Quick actions",
          add_product: "Add product",
          view_store: "View my store",
        },
      };

      // Fonctions principales
      function initializeDashboard() {
        loadProducts();
        loadCategories();
        loadLandingPages();
        updateStats();
      }

      function showSection(sectionName) {
        // Masquer toutes les sections
        document.querySelectorAll(".content-section").forEach((section) => {
          section.classList.remove("active");
        });

        // Retirer la classe active de tous les liens
        document.querySelectorAll(".nav-link").forEach((link) => {
          link.classList.remove("active");
        });

        // Afficher la section demandée
        document
          .getElementById(sectionName + "-section")
          .classList.add("active");

        // Activer le lien correspondant
        document
          .querySelector(`[data-section="${sectionName}"]`)
          .classList.add("active");
      }

      // Gestion des langues
      function changeLanguage(lang) {
        document.querySelectorAll(".language-btn").forEach((btn) => {
          btn.classList.remove("active");
        });
        document.querySelector(`[data-lang="${lang}"]`).classList.add("active");

        // Appliquer les traductions
        document.querySelectorAll("[data-translate]").forEach((element) => {
          const key = element.getAttribute("data-translate");
          if (translations[lang] && translations[lang][key]) {
            element.textContent = translations[lang][key];
          }
        });

        // Changer la direction pour l'arabe
        if (lang === "ar") {
          document.body.setAttribute("dir", "rtl");
        } else {
          document.body.setAttribute("dir", "ltr");
        }
      }

      // Fonctions de chargement des données
      async function loadProducts() {
        try {
          const storeId = sessionStorage.getItem("store_id");
          const response = await fetch(
            `/api/products-simple.php?store_id=${storeId}`,
            {
              headers: {
                Authorization: "Bearer demo_token",
              },
            }
          );

          const data = await response.json();
          if (data.success) {
            // L'API retourne soit un tableau directement, soit un objet avec products
            const products = Array.isArray(data.data)
              ? data.data
              : data.data.products || [];
            displayProducts(products);
            document.getElementById("total-products").textContent =
              products.length;
          }
        } catch (error) {
          console.error("Erreur lors du chargement des produits:", error);
          document.getElementById("products-list").innerHTML =
            '<p class="text-danger">Erreur lors du chargement des produits</p>';
        }
      }

      async function loadCategories() {
        try {
          const storeId = sessionStorage.getItem("store_id");
          const response = await fetch(
            `/api/categories.php?store_id=${storeId}`,
            {
              headers: {
                Authorization: "Bearer demo_token",
              },
            }
          );

          const data = await response.json();
          if (data.success) {
            // L'API retourne soit un tableau directement, soit un objet avec categories
            const categories = Array.isArray(data.data)
              ? data.data
              : data.data.categories || [];
            displayCategories(categories);
          }
        } catch (error) {
          console.error("Erreur lors du chargement des catégories:", error);
          document.getElementById("categories-list").innerHTML =
            '<p class="text-danger">Erreur lors du chargement des catégories</p>';
        }
      }

      async function loadLandingPages() {
        try {
          const storeId = sessionStorage.getItem("store_id");
          const response = await fetch(
            `/api/landing-pages.php?store_id=${storeId}`,
            {
              headers: {
                Authorization: "Bearer demo_token",
              },
            }
          );

          const data = await response.json();
          if (data.success) {
            // L'API retourne soit un tableau directement, soit un objet avec pages
            const pages = Array.isArray(data.data) ? data.data : data.data.pages || [];
            displayLandingPages(pages);
          }
        } catch (error) {
          console.error("Erreur lors du chargement des landing pages:", error);
          document.getElementById("landing-pages-list").innerHTML =
            '<p class="text-danger">Erreur lors du chargement des landing pages</p>';
        }
      }

      // Fonctions d'affichage
      function displayProducts(products) {
        const container = document.getElementById("products-list");
        if (!products || products.length === 0) {
          container.innerHTML =
            '<p class="text-center py-4">Aucun produit trouvé</p>';
          return;
        }

        let html = "";
        products.forEach((product) => {
          const image = product.images
            ? JSON.parse(product.images)[0]
            : "https://via.placeholder.com/80x80";
          html += `
                    <div class="product-card d-flex align-items-center">
                        <img src="${image}" alt="${product.name}" class="product-image me-3" onerror="this.src='https://via.placeholder.com/80x80'">
                        <div class="flex-grow-1">
                            <h6 class="mb-1">${product.name}</h6>
                            <p class="text-muted mb-1">${product.price} DA</p>
                            <small class="text-muted">${product.status}</small>
                        </div>
                        <div class="btn-group">
                            <button class="btn btn-sm btn-outline-primary" onclick="editProduct(${product.id})">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteProduct(${product.id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                `;
        });
        container.innerHTML = html;
      }

      function displayCategories(categories) {
        const container = document.getElementById("categories-list");
        if (!categories || categories.length === 0) {
          container.innerHTML =
            '<p class="text-center py-4">Aucune catégorie trouvée</p>';
          return;
        }

        let html = "";
        categories.forEach((category) => {
          html += `
                    <div class="product-card d-flex align-items-center">
                        <div class="stats-icon me-3" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); width: 50px; height: 50px;">
                            <i class="fas fa-tag"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-1">${category.name}</h6>
                            <p class="text-muted mb-0">${
                              category.description || "Aucune description"
                            }</p>
                        </div>
                        <div class="btn-group">
                            <button class="btn btn-sm btn-outline-primary" onclick="editCategory(${
                              category.id
                            })">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteCategory(${
                              category.id
                            })">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                `;
        });
        container.innerHTML = html;
      }

      function displayLandingPages(pages) {
        const container = document.getElementById("landing-pages-list");
        if (!pages || pages.length === 0) {
          container.innerHTML =
            '<p class="text-center py-4">Aucune landing page trouvée</p>';
          return;
        }

        let html = "";
        pages.forEach((page) => {
          html += `
                    <div class="product-card d-flex align-items-center">
                        <div class="stats-icon me-3" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); width: 50px; height: 50px;">
                            <i class="fas fa-file-alt"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-1">${page.title}</h6>
                            <p class="text-muted mb-0">${page.status}</p>
                        </div>
                        <div class="btn-group">
                            <button class="btn btn-sm btn-outline-success" onclick="previewLandingPage(${page.id})">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-primary" onclick="editLandingPage(${page.id})">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteLandingPage(${page.id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                `;
        });
        container.innerHTML = html;
      }

      // Fonctions utilitaires
      function updateStats() {
        // Mettre à jour les statistiques (à implémenter avec de vraies données)
      }

      function viewStore() {
        window.open("http://localhost:8000/techstore-algeria", "_blank");
      }

      function copyToClipboard(text) {
        navigator.clipboard.writeText(text).then(() => {
          alert("URL copiée dans le presse-papiers !");
        });
      }

      function logout() {
        sessionStorage.clear();
        window.location.href = "seller-login.html";
      }

      // Fonctions d'actions (à implémenter)
      function addProduct() {
        alert("Fonction à implémenter : Ajouter un produit");
      }
      function editProduct(id) {
        alert("Fonction à implémenter : Modifier le produit " + id);
      }
      function deleteProduct(id) {
        alert("Fonction à implémenter : Supprimer le produit " + id);
      }
      function addCategory() {
        alert("Fonction à implémenter : Ajouter une catégorie");
      }
      function editCategory(id) {
        alert("Fonction à implémenter : Modifier la catégorie " + id);
      }
      function deleteCategory(id) {
        alert("Fonction à implémenter : Supprimer la catégorie " + id);
      }
      function createLandingPage() {
        alert("Fonction à implémenter : Créer une landing page");
      }
      function editLandingPage(id) {
        alert("Fonction à implémenter : Modifier la landing page " + id);
      }
      function deleteLandingPage(id) {
        alert("Fonction à implémenter : Supprimer la landing page " + id);
      }
      function previewLandingPage(id) {
        alert("Fonction à implémenter : Prévisualiser la landing page " + id);
      }
      function exportToGoogleSheets(type) {
        alert("Fonction à implémenter : Export Google Sheets " + type);
      }
      function saveColors() {
        alert("Fonction à implémenter : Sauvegarder les couleurs");
      }
      function saveDisplaySettings() {
        alert(
          "Fonction à implémenter : Sauvegarder les paramètres d'affichage"
        );
      }

      // Event listeners
      document.querySelectorAll(".nav-link[data-section]").forEach((link) => {
        link.addEventListener("click", function (e) {
          e.preventDefault();
          const section = this.getAttribute("data-section");
          showSection(section);
        });
      });

      document.querySelectorAll(".language-btn").forEach((btn) => {
        btn.addEventListener("click", function () {
          const lang = this.getAttribute("data-lang");
          changeLanguage(lang);
        });
      });
    </script>
  </body>
</html>
