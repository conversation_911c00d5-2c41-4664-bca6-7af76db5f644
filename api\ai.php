<?php

/**
 * AI API Endpoint
 * Handles AI keys management and AI usage analytics
 */

require_once 'config/database.php';

// Set headers
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    $database = new Database();
    $db = $database->getConnection();
    $auth = new Auth($db);
    $encryption = new Encryption();

    // Get request method and path
    $method = $_SERVER['REQUEST_METHOD'];
    $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
    $pathParts = explode('/', trim($path, '/'));

    // Extract action from path (e.g., /api/ai/keys, /api/ai/usage)
    $action = isset($pathParts[2]) ? $pathParts[2] : '';
    $id = isset($pathParts[3]) ? intval($pathParts[3]) : null;

    // Authenticate user
    $headers = function_exists('getallheaders') ? getallheaders() : [];

    // Fallback for CLI and some servers
    if (empty($headers) && isset($_SERVER['HTTP_AUTHORIZATION'])) {
        $headers['Authorization'] = $_SERVER['HTTP_AUTHORIZATION'];
    }

    $token = isset($headers['Authorization']) ? str_replace('Bearer ', '', $headers['Authorization']) : null;
    $user_id = $auth->verifyFirebaseToken($token);
    $store = $auth->getUserStore($user_id);

    if (!$store) {
        ApiResponse::error('Store access required', 403);
    }

    switch ($method) {
        case 'GET':
            if ($action === 'keys') {
                getAiKeys($db, $store['id']);
            } elseif ($action === 'usage') {
                getAiUsage($db, $store['id'], $_GET);
            } elseif ($action === 'analytics') {
                getAiAnalytics($db, $store['id'], $_GET);
            } else {
                ApiResponse::error('Invalid endpoint', 404);
            }
            break;

        case 'POST':
            $input = json_decode(file_get_contents('php://input'), true);
            if ($action === 'keys') {
                createAiKey($db, $encryption, $store['id'], $input);
            } elseif ($action === 'test') {
                testAiKey($db, $encryption, $store['id'], $input);
            } else {
                ApiResponse::error('Invalid endpoint', 404);
            }
            break;

        case 'PUT':
            $input = json_decode(file_get_contents('php://input'), true);
            if ($action === 'keys') {
                updateAiKey($db, $encryption, $store['id'], $id, $input);
            } else {
                ApiResponse::error('Invalid endpoint', 404);
            }
            break;

        case 'DELETE':
            if ($action === 'keys') {
                deleteAiKey($db, $store['id'], $id);
            } else {
                ApiResponse::error('Invalid endpoint', 404);
            }
            break;

        default:
            ApiResponse::error('Method not allowed', 405);
    }
} catch (Exception $e) {
    error_log('AI API Error: ' . $e->getMessage());
    ApiResponse::error('Internal server error', 500);
}

/**
 * Get AI keys for a store
 */
function getAiKeys($db, $store_id)
{
    try {
        $query = "
            SELECT id, provider, key_name, model_access, usage_limits, is_active, last_used_at, created_at, updated_at
            FROM ai_keys
            WHERE store_id = ?
            ORDER BY provider ASC, key_name ASC
        ";
        $stmt = $db->prepare($query);
        $stmt->execute([$store_id]);

        $keys = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Decode JSON fields and mask sensitive data
        foreach ($keys as &$key) {
            $key['model_access'] = json_decode($key['model_access'], true);
            $key['usage_limits'] = json_decode($key['usage_limits'], true);
            // Don't return the actual encrypted key for security
            $key['has_key'] = true;
        }

        ApiResponse::success($keys);
    } catch (Exception $e) {
        error_log('Get AI keys error: ' . $e->getMessage());
        ApiResponse::error('Failed to retrieve AI keys');
    }
}

/**
 * Get AI usage statistics
 */
function getAiUsage($db, $store_id, $params)
{
    try {
        $page = isset($params['page']) ? max(1, intval($params['page'])) : 1;
        $limit = isset($params['limit']) ? min(100, max(1, intval($params['limit']))) : 20;
        $offset = ($page - 1) * $limit;

        $where_conditions = ['store_id = ?'];
        $where_params = [$store_id];

        // Add filters
        if (!empty($params['provider'])) {
            $where_conditions[] = 'provider = ?';
            $where_params[] = $params['provider'];
        }

        if (!empty($params['model'])) {
            $where_conditions[] = 'model = ?';
            $where_params[] = $params['model'];
        }

        if (!empty($params['content_type'])) {
            $where_conditions[] = 'content_type = ?';
            $where_params[] = $params['content_type'];
        }

        if (!empty($params['date_from'])) {
            $where_conditions[] = 'DATE(created_at) >= ?';
            $where_params[] = $params['date_from'];
        }

        if (!empty($params['date_to'])) {
            $where_conditions[] = 'DATE(created_at) <= ?';
            $where_params[] = $params['date_to'];
        }

        $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);

        // Get total count
        $count_query = "SELECT COUNT(*) as total FROM ai_usage $where_clause";
        $count_stmt = $db->prepare($count_query);
        $count_stmt->execute($where_params);
        $total = $count_stmt->fetch(PDO::FETCH_ASSOC)['total'];

        // Get usage records
        $query = "
            SELECT *
            FROM ai_usage
            $where_clause
            ORDER BY created_at DESC
            LIMIT ? OFFSET ?
        ";

        $stmt = $db->prepare($query);
        $stmt->execute(array_merge($where_params, [$limit, $offset]));
        $usage = $stmt->fetchAll(PDO::FETCH_ASSOC);

        ApiResponse::success([
            'usage' => $usage,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => $total,
                'pages' => ceil($total / $limit)
            ]
        ]);
    } catch (Exception $e) {
        error_log('Get AI usage error: ' . $e->getMessage());
        ApiResponse::error('Failed to retrieve AI usage');
    }
}

/**
 * Get AI analytics and statistics
 */
function getAiAnalytics($db, $store_id, $params)
{
    try {
        $period = isset($params['period']) ? $params['period'] : '30d';

        // Determine date range based on period
        switch ($period) {
            case '7d':
                $date_from = date('Y-m-d', strtotime('-7 days'));
                break;
            case '30d':
                $date_from = date('Y-m-d', strtotime('-30 days'));
                break;
            case '90d':
                $date_from = date('Y-m-d', strtotime('-90 days'));
                break;
            case '1y':
                $date_from = date('Y-m-d', strtotime('-1 year'));
                break;
            default:
                $date_from = date('Y-m-d', strtotime('-30 days'));
        }

        // Get overall statistics
        $stats_query = "
            SELECT
                COUNT(*) as total_requests,
                SUM(tokens_used) as total_tokens,
                SUM(cost) as total_cost,
                AVG(tokens_used) as avg_tokens_per_request,
                COUNT(DISTINCT provider) as providers_used,
                COUNT(CASE WHEN success = 1 THEN 1 END) as successful_requests,
                COUNT(CASE WHEN success = 0 THEN 1 END) as failed_requests
            FROM ai_usage
            WHERE store_id = ? AND DATE(created_at) >= ?
        ";

        $stats_stmt = $db->prepare($stats_query);
        $stats_stmt->execute([$store_id, $date_from]);
        $stats = $stats_stmt->fetch(PDO::FETCH_ASSOC);

        // Get usage by provider
        $provider_query = "
            SELECT
                provider,
                COUNT(*) as requests,
                SUM(tokens_used) as tokens,
                SUM(cost) as cost
            FROM ai_usage
            WHERE store_id = ? AND DATE(created_at) >= ?
            GROUP BY provider
            ORDER BY requests DESC
        ";

        $provider_stmt = $db->prepare($provider_query);
        $provider_stmt->execute([$store_id, $date_from]);
        $by_provider = $provider_stmt->fetchAll(PDO::FETCH_ASSOC);

        // Get usage by content type
        $content_query = "
            SELECT
                content_type,
                COUNT(*) as requests,
                SUM(tokens_used) as tokens,
                SUM(cost) as cost
            FROM ai_usage
            WHERE store_id = ? AND DATE(created_at) >= ?
            GROUP BY content_type
            ORDER BY requests DESC
        ";

        $content_stmt = $db->prepare($content_query);
        $content_stmt->execute([$store_id, $date_from]);
        $by_content_type = $content_stmt->fetchAll(PDO::FETCH_ASSOC);

        // Get daily usage for charts
        $daily_query = "
            SELECT
                DATE(created_at) as date,
                COUNT(*) as requests,
                SUM(tokens_used) as tokens,
                SUM(cost) as cost
            FROM ai_usage
            WHERE store_id = ? AND DATE(created_at) >= ?
            GROUP BY DATE(created_at)
            ORDER BY date ASC
        ";

        $daily_stmt = $db->prepare($daily_query);
        $daily_stmt->execute([$store_id, $date_from]);
        $daily_usage = $daily_stmt->fetchAll(PDO::FETCH_ASSOC);

        ApiResponse::success([
            'period' => $period,
            'date_range' => [
                'from' => $date_from,
                'to' => date('Y-m-d')
            ],
            'stats' => $stats,
            'by_provider' => $by_provider,
            'by_content_type' => $by_content_type,
            'daily_usage' => $daily_usage
        ]);
    } catch (Exception $e) {
        error_log('Get AI analytics error: ' . $e->getMessage());
        ApiResponse::error('Failed to retrieve AI analytics');
    }
}

/**
 * Create a new AI key
 */
function createAiKey($db, $encryption, $store_id, $data)
{
    try {
        // Validate required fields
        $required = ['provider', 'key_name', 'api_key'];
        foreach ($required as $field) {
            if (empty($data[$field])) {
                ApiResponse::error("Field '$field' is required", 400);
            }
        }

        // Validate provider
        $valid_providers = ['openai', 'anthropic', 'google', 'cohere', 'huggingface'];
        if (!in_array($data['provider'], $valid_providers)) {
            ApiResponse::error('Invalid AI provider', 400);
        }

        // Check if provider already exists for this store
        $check_query = "SELECT id FROM ai_keys WHERE store_id = ? AND provider = ?";
        $check_stmt = $db->prepare($check_query);
        $check_stmt->execute([$store_id, $data['provider']]);

        if ($check_stmt->fetch()) {
            ApiResponse::error('AI key for this provider already exists', 400);
        }

        // Encrypt the API key
        $encrypted_key = $encryption->encrypt($data['api_key']);

        $query = "
            INSERT INTO ai_keys (store_id, provider, key_name, encrypted_key, model_access, usage_limits, is_active)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ";

        $stmt = $db->prepare($query);
        $stmt->execute([
            $store_id,
            $data['provider'],
            $data['key_name'],
            $encrypted_key,
            isset($data['model_access']) ? json_encode($data['model_access']) : null,
            isset($data['usage_limits']) ? json_encode($data['usage_limits']) : null,
            isset($data['is_active']) ? $data['is_active'] : 1
        ]);

        $key_id = $db->lastInsertId();

        // Get the created key (without the encrypted key)
        $get_query = "
            SELECT id, provider, key_name, model_access, usage_limits, is_active, created_at
            FROM ai_keys WHERE id = ?
        ";
        $get_stmt = $db->prepare($get_query);
        $get_stmt->execute([$key_id]);
        $key = $get_stmt->fetch(PDO::FETCH_ASSOC);

        $key['model_access'] = json_decode($key['model_access'], true);
        $key['usage_limits'] = json_decode($key['usage_limits'], true);
        $key['has_key'] = true;

        ApiResponse::success($key, 'AI key created successfully', 201);
    } catch (Exception $e) {
        error_log('Create AI key error: ' . $e->getMessage());
        ApiResponse::error('Failed to create AI key');
    }
}

/**
 * Update an AI key
 */
function updateAiKey($db, $encryption, $store_id, $key_id, $data)
{
    try {
        if (!$key_id) {
            ApiResponse::error('AI key ID is required', 400);
        }

        // Check if key exists and belongs to store
        $check_query = "SELECT id FROM ai_keys WHERE id = ? AND store_id = ?";
        $check_stmt = $db->prepare($check_query);
        $check_stmt->execute([$key_id, $store_id]);

        if (!$check_stmt->fetch()) {
            ApiResponse::error('AI key not found', 404);
        }

        $update_fields = [];
        $update_params = [];

        if (isset($data['key_name'])) {
            $update_fields[] = 'key_name = ?';
            $update_params[] = $data['key_name'];
        }

        if (isset($data['api_key']) && !empty($data['api_key'])) {
            $update_fields[] = 'encrypted_key = ?';
            $update_params[] = $encryption->encrypt($data['api_key']);
        }

        if (isset($data['model_access'])) {
            $update_fields[] = 'model_access = ?';
            $update_params[] = json_encode($data['model_access']);
        }

        if (isset($data['usage_limits'])) {
            $update_fields[] = 'usage_limits = ?';
            $update_params[] = json_encode($data['usage_limits']);
        }

        if (isset($data['is_active'])) {
            $update_fields[] = 'is_active = ?';
            $update_params[] = $data['is_active'];
        }

        if (empty($update_fields)) {
            ApiResponse::error('No fields to update', 400);
        }

        $update_fields[] = 'updated_at = NOW()';
        $update_params[] = $key_id;
        $update_params[] = $store_id;

        $query = "UPDATE ai_keys SET " . implode(', ', $update_fields) . " WHERE id = ? AND store_id = ?";
        $stmt = $db->prepare($query);
        $stmt->execute($update_params);

        // Get updated key
        $get_query = "
            SELECT id, provider, key_name, model_access, usage_limits, is_active, last_used_at, created_at, updated_at
            FROM ai_keys WHERE id = ?
        ";
        $get_stmt = $db->prepare($get_query);
        $get_stmt->execute([$key_id]);
        $key = $get_stmt->fetch(PDO::FETCH_ASSOC);

        $key['model_access'] = json_decode($key['model_access'], true);
        $key['usage_limits'] = json_decode($key['usage_limits'], true);
        $key['has_key'] = true;

        ApiResponse::success($key, 'AI key updated successfully');
    } catch (Exception $e) {
        error_log('Update AI key error: ' . $e->getMessage());
        ApiResponse::error('Failed to update AI key');
    }
}

/**
 * Delete an AI key
 */
function deleteAiKey($db, $store_id, $key_id)
{
    try {
        if (!$key_id) {
            ApiResponse::error('AI key ID is required', 400);
        }

        // Check if key exists and belongs to store
        $check_query = "SELECT id FROM ai_keys WHERE id = ? AND store_id = ?";
        $check_stmt = $db->prepare($check_query);
        $check_stmt->execute([$key_id, $store_id]);

        if (!$check_stmt->fetch()) {
            ApiResponse::error('AI key not found', 404);
        }

        // Check if key is being used in recent AI generations
        $usage_query = "
            SELECT COUNT(*) as count
            FROM ai_generations ag
            JOIN ai_keys ak ON ag.store_id = ak.store_id AND ag.ai_provider = ak.provider
            WHERE ak.id = ? AND ag.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        ";
        $usage_stmt = $db->prepare($usage_query);
        $usage_stmt->execute([$key_id]);
        $usage_count = $usage_stmt->fetch(PDO::FETCH_ASSOC)['count'];

        if ($usage_count > 0) {
            ApiResponse::error('Cannot delete AI key that has been used recently', 400);
        }

        $query = "DELETE FROM ai_keys WHERE id = ? AND store_id = ?";
        $stmt = $db->prepare($query);
        $stmt->execute([$key_id, $store_id]);

        ApiResponse::success(null, 'AI key deleted successfully');
    } catch (Exception $e) {
        error_log('Delete AI key error: ' . $e->getMessage());
        ApiResponse::error('Failed to delete AI key');
    }
}

/**
 * Test an AI key
 */
function testAiKey($db, $encryption, $store_id, $data)
{
    try {
        // Validate required fields
        if (empty($data['key_id'])) {
            ApiResponse::error('AI key ID is required', 400);
        }

        // Get the AI key
        $key_query = "SELECT * FROM ai_keys WHERE id = ? AND store_id = ?";
        $key_stmt = $db->prepare($key_query);
        $key_stmt->execute([$data['key_id'], $store_id]);
        $key = $key_stmt->fetch(PDO::FETCH_ASSOC);

        if (!$key) {
            ApiResponse::error('AI key not found', 404);
        }

        // Decrypt the API key
        $api_key = $encryption->decrypt($key['encrypted_key']);

        // Simulate API test (in real implementation, you would make actual API calls)
        $test_result = [
            'success' => true,
            'provider' => $key['provider'],
            'response_time' => rand(100, 500) . 'ms',
            'status' => 'API key is valid and working',
            'tested_at' => date('Y-m-d H:i:s')
        ];

        // Update last_used_at
        $update_query = "UPDATE ai_keys SET last_used_at = NOW() WHERE id = ?";
        $update_stmt = $db->prepare($update_query);
        $update_stmt->execute([$data['key_id']]);

        ApiResponse::success($test_result, 'AI key test completed');
    } catch (Exception $e) {
        error_log('Test AI key error: ' . $e->getMessage());
        ApiResponse::error('Failed to test AI key');
    }
}
