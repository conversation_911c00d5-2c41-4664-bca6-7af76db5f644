<?php
/**
 * Gestionnaire d'abonnements - Interface d'administration
 * Permet de gérer les abonnements et surveiller l'utilisation
 */

session_start();

// Vérification des permissions admin (à adapter selon votre système)
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    header('Location: /login.html');
    exit();
}

require_once '../config/database.php';
require_once '../php/QuotaManager.php';

try {
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";port=" . DB_PORT . ";dbname=" . DB_DATABASE . ";charset=" . DB_CHARSET,
        DB_USERNAME,
        DB_PASSWORD,
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    $quotaManager = new QuotaManager($pdo);
    $stats = $quotaManager->getAdminStats();
    
} catch (Exception $e) {
    $error = $e->getMessage();
}

$language = $_GET['lang'] ?? 'ar';
$translations = [
    'ar' => [
        'title' => 'إدارة الاشتراكات والحصص',
        'dashboard' => 'لوحة التحكم',
        'subscriptions' => 'الاشتراكات',
        'users' => 'المستخدمين',
        'statistics' => 'الإحصائيات',
        'global_usage' => 'الاستخدام العام',
        'subscription_plans' => 'خطط الاشتراك',
        'active_users' => 'المستخدمين النشطين',
        'total_products' => 'إجمالي المنتجات',
        'total_landing_pages' => 'إجمالي صفحات الهبوط',
        'total_categories' => 'إجمالي الفئات',
        'user_management' => 'إدارة المستخدمين',
        'force_upgrade' => 'فرض الترقية',
        'view_details' => 'عرض التفاصيل',
        'subscription_status' => 'حالة الاشتراك',
        'usage_overview' => 'نظرة عامة على الاستخدام',
        'refresh_data' => 'تحديث البيانات'
    ],
    'fr' => [
        'title' => 'Gestion des Abonnements et Quotas',
        'dashboard' => 'Tableau de bord',
        'subscriptions' => 'Abonnements',
        'users' => 'Utilisateurs',
        'statistics' => 'Statistiques',
        'global_usage' => 'Utilisation globale',
        'subscription_plans' => 'Plans d\'abonnement',
        'active_users' => 'Utilisateurs actifs',
        'total_products' => 'Total produits',
        'total_landing_pages' => 'Total pages de destination',
        'total_categories' => 'Total catégories',
        'user_management' => 'Gestion des utilisateurs',
        'force_upgrade' => 'Forcer la mise à niveau',
        'view_details' => 'Voir les détails',
        'subscription_status' => 'Statut d\'abonnement',
        'usage_overview' => 'Aperçu de l\'utilisation',
        'refresh_data' => 'Actualiser les données'
    ],
    'en' => [
        'title' => 'Subscriptions & Quotas Management',
        'dashboard' => 'Dashboard',
        'subscriptions' => 'Subscriptions',
        'users' => 'Users',
        'statistics' => 'Statistics',
        'global_usage' => 'Global Usage',
        'subscription_plans' => 'Subscription Plans',
        'active_users' => 'Active Users',
        'total_products' => 'Total Products',
        'total_landing_pages' => 'Total Landing Pages',
        'total_categories' => 'Total Categories',
        'user_management' => 'User Management',
        'force_upgrade' => 'Force Upgrade',
        'view_details' => 'View Details',
        'subscription_status' => 'Subscription Status',
        'usage_overview' => 'Usage Overview',
        'refresh_data' => 'Refresh Data'
    ]
];

$t = $translations[$language] ?? $translations['ar'];
$direction = $language === 'ar' ? 'rtl' : 'ltr';
?>

<!DOCTYPE html>
<html lang="<?php echo $language; ?>" dir="<?php echo $direction; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($t['title']); ?></title>
    
    <!-- Bootstrap 5 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        body {
            background-color: #f8f9fa;
            font-family: <?php echo $language === 'ar' ? 'Cairo, sans-serif' : 'Inter, sans-serif'; ?>;
        }
        
        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            border-radius: 8px;
            margin: 0.25rem 0;
            transition: all 0.3s ease;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }
        
        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-2px);
        }
        
        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }
        
        .table-container {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .progress-thin {
            height: 6px;
        }
        
        .badge-status {
            font-size: 0.75rem;
            padding: 0.375rem 0.75rem;
        }
        
        /* RTL Support */
        [dir="rtl"] .sidebar {
            border-radius: 0 12px 12px 0;
        }
        
        [dir="ltr"] .sidebar {
            border-radius: 12px 0 0 12px;
        }
        
        @media (max-width: 768px) {
            .sidebar {
                border-radius: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 px-0">
                <div class="sidebar p-3">
                    <div class="text-center mb-4">
                        <i class="fas fa-crown fa-2x mb-2"></i>
                        <h5><?php echo htmlspecialchars($t['dashboard']); ?></h5>
                    </div>
                    
                    <nav class="nav flex-column">
                        <a class="nav-link active" href="#overview" data-section="overview">
                            <i class="fas fa-chart-pie me-2"></i>
                            <?php echo htmlspecialchars($t['statistics']); ?>
                        </a>
                        <a class="nav-link" href="#subscriptions" data-section="subscriptions">
                            <i class="fas fa-credit-card me-2"></i>
                            <?php echo htmlspecialchars($t['subscriptions']); ?>
                        </a>
                        <a class="nav-link" href="#users" data-section="users">
                            <i class="fas fa-users me-2"></i>
                            <?php echo htmlspecialchars($t['users']); ?>
                        </a>
                    </nav>
                    
                    <!-- Language Selector -->
                    <div class="mt-auto pt-3">
                        <div class="dropdown">
                            <button class="btn btn-outline-light btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-globe me-1"></i>
                                <?php echo strtoupper($language); ?>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="?lang=ar">العربية</a></li>
                                <li><a class="dropdown-item" href="?lang=fr">Français</a></li>
                                <li><a class="dropdown-item" href="?lang=en">English</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10">
                <div class="p-4">
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2><?php echo htmlspecialchars($t['title']); ?></h2>
                        <button class="btn btn-primary" onclick="refreshAllData()">
                            <i class="fas fa-sync-alt me-2"></i>
                            <?php echo htmlspecialchars($t['refresh_data']); ?>
                        </button>
                    </div>
                    
                    <?php if (isset($error)): ?>
                    <div class="alert alert-danger" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                    <?php endif; ?>
                    
                    <!-- Overview Section -->
                    <div id="overview-section" class="section-content">
                        <!-- Statistics Cards -->
                        <div class="row mb-4">
                            <div class="col-md-3 mb-3">
                                <div class="stat-card">
                                    <div class="d-flex align-items-center">
                                        <div class="stat-icon bg-primary me-3">
                                            <i class="fas fa-box"></i>
                                        </div>
                                        <div>
                                            <h3 class="mb-0"><?php echo $stats['global_usage']['total_products'] ?? 0; ?></h3>
                                            <p class="text-muted mb-0"><?php echo htmlspecialchars($t['total_products']); ?></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                <div class="stat-card">
                                    <div class="d-flex align-items-center">
                                        <div class="stat-icon bg-success me-3">
                                            <i class="fas fa-file-alt"></i>
                                        </div>
                                        <div>
                                            <h3 class="mb-0"><?php echo $stats['global_usage']['total_landing_pages'] ?? 0; ?></h3>
                                            <p class="text-muted mb-0"><?php echo htmlspecialchars($t['total_landing_pages']); ?></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                <div class="stat-card">
                                    <div class="d-flex align-items-center">
                                        <div class="stat-icon bg-info me-3">
                                            <i class="fas fa-tags"></i>
                                        </div>
                                        <div>
                                            <h3 class="mb-0"><?php echo $stats['global_usage']['total_categories'] ?? 0; ?></h3>
                                            <p class="text-muted mb-0"><?php echo htmlspecialchars($t['total_categories']); ?></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                <div class="stat-card">
                                    <div class="d-flex align-items-center">
                                        <div class="stat-icon bg-warning me-3">
                                            <i class="fas fa-users"></i>
                                        </div>
                                        <div>
                                            <h3 class="mb-0"><?php echo array_sum($stats['users_by_role'] ?? []); ?></h3>
                                            <p class="text-muted mb-0"><?php echo htmlspecialchars($t['active_users']); ?></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Charts Row -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="table-container">
                                    <h5 class="mb-3"><?php echo htmlspecialchars($t['subscription_plans']); ?></h5>
                                    <canvas id="subscriptionsChart" width="400" height="200"></canvas>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="table-container">
                                    <h5 class="mb-3"><?php echo htmlspecialchars($t['global_usage']); ?></h5>
                                    <canvas id="usageChart" width="400" height="200"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Subscriptions Section -->
                    <div id="subscriptions-section" class="section-content" style="display: none;">
                        <div class="table-container">
                            <h5 class="mb-3"><?php echo htmlspecialchars($t['subscription_plans']); ?></h5>
                            <div id="subscriptions-table">Chargement...</div>
                        </div>
                    </div>
                    
                    <!-- Users Section -->
                    <div id="users-section" class="section-content" style="display: none;">
                        <div class="table-container">
                            <h5 class="mb-3"><?php echo htmlspecialchars($t['user_management']); ?></h5>
                            <div id="users-table">Chargement...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        const language = '<?php echo $language; ?>';
        const translations = <?php echo json_encode($t, JSON_UNESCAPED_UNICODE); ?>;
        
        // Navigation entre sections
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                
                // Mettre à jour les liens actifs
                document.querySelectorAll('.nav-link').forEach(l => l.classList.remove('active'));
                this.classList.add('active');
                
                // Afficher la section correspondante
                const section = this.dataset.section;
                showSection(section);
            });
        });
        
        function showSection(section) {
            // Cacher toutes les sections
            document.querySelectorAll('.section-content').forEach(s => s.style.display = 'none');
            
            // Afficher la section demandée
            const targetSection = document.getElementById(section + '-section');
            if (targetSection) {
                targetSection.style.display = 'block';
                
                // Charger les données si nécessaire
                if (section === 'subscriptions') {
                    loadSubscriptions();
                } else if (section === 'users') {
                    loadUsers();
                }
            }
        }
        
        // Initialiser les graphiques
        function initCharts() {
            // Graphique des abonnements
            const subscriptionsData = <?php echo json_encode($stats['subscriptions'] ?? [], JSON_UNESCAPED_UNICODE); ?>;
            const subscriptionsCtx = document.getElementById('subscriptionsChart').getContext('2d');
            
            new Chart(subscriptionsCtx, {
                type: 'doughnut',
                data: {
                    labels: subscriptionsData.map(s => s.name),
                    datasets: [{
                        data: subscriptionsData.map(s => s.count),
                        backgroundColor: [
                            '#FF6384',
                            '#36A2EB',
                            '#FFCE56',
                            '#4BC0C0',
                            '#9966FF'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
            
            // Graphique d'utilisation
            const usageData = <?php echo json_encode($stats['global_usage'] ?? [], JSON_UNESCAPED_UNICODE); ?>;
            const usageCtx = document.getElementById('usageChart').getContext('2d');
            
            new Chart(usageCtx, {
                type: 'bar',
                data: {
                    labels: [translations.total_products, translations.total_landing_pages, translations.total_categories],
                    datasets: [{
                        label: translations.global_usage,
                        data: [
                            usageData.total_products || 0,
                            usageData.total_landing_pages || 0,
                            usageData.total_categories || 0
                        ],
                        backgroundColor: ['#36A2EB', '#4BC0C0', '#FFCE56']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }
        
        // Charger les abonnements
        async function loadSubscriptions() {
            try {
                const response = await fetch('/api/quota-api.php?action=get_subscriptions');
                const data = await response.json();
                
                if (data.success) {
                    renderSubscriptionsTable(data.data);
                }
            } catch (error) {
                console.error('Erreur lors du chargement des abonnements:', error);
            }
        }
        
        // Charger les utilisateurs
        async function loadUsers() {
            try {
                const response = await fetch('/api/quota-api.php?action=get_all_users_usage');
                const data = await response.json();
                
                if (data.success) {
                    renderUsersTable(data.data.users);
                }
            } catch (error) {
                console.error('Erreur lors du chargement des utilisateurs:', error);
            }
        }
        
        // Afficher le tableau des abonnements
        function renderSubscriptionsTable(subscriptions) {
            let html = `
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Nom</th>
                                <th>Prix</th>
                                <th>Produits</th>
                                <th>Pages</th>
                                <th>Catégories</th>
                                <th>Statut</th>
                            </tr>
                        </thead>
                        <tbody>
            `;
            
            subscriptions.forEach(sub => {
                const name = sub[`name_${language}`] || sub.name;
                html += `
                    <tr>
                        <td><strong>${name}</strong></td>
                        <td>${sub.price} ${sub.currency}</td>
                        <td>${sub.max_products === -1 ? translations.unlimited : sub.max_products}</td>
                        <td>${sub.max_landing_pages === -1 ? translations.unlimited : sub.max_landing_pages}</td>
                        <td>${sub.max_categories === -1 ? translations.unlimited : sub.max_categories}</td>
                        <td>
                            <span class="badge ${sub.is_active ? 'bg-success' : 'bg-secondary'}">
                                ${sub.is_active ? 'Actif' : 'Inactif'}
                            </span>
                        </td>
                    </tr>
                `;
            });
            
            html += '</tbody></table></div>';
            document.getElementById('subscriptions-table').innerHTML = html;
        }
        
        // Afficher le tableau des utilisateurs
        function renderUsersTable(users) {
            let html = `
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Utilisateur</th>
                                <th>Abonnement</th>
                                <th>Produits</th>
                                <th>Pages</th>
                                <th>Catégories</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
            `;
            
            users.forEach(user => {
                const productsPercentage = user.max_products > 0 ? (user.products_count / user.max_products * 100) : 0;
                const pagesPercentage = user.max_landing_pages > 0 ? (user.landing_pages_count / user.max_landing_pages * 100) : 0;
                const categoriesPercentage = user.max_categories > 0 ? (user.categories_count / user.max_categories * 100) : 0;
                
                html += `
                    <tr>
                        <td>
                            <div>
                                <strong>${user.name}</strong><br>
                                <small class="text-muted">${user.email}</small>
                            </div>
                        </td>
                        <td>
                            <span class="badge badge-status ${getStatusBadgeClass(user.subscription_status)}">
                                ${user.subscription_name || 'Aucun'}
                            </span>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <span class="me-2">${user.products_count}/${user.max_products === -1 ? '∞' : user.max_products}</span>
                                ${user.max_products > 0 ? `<div class="progress progress-thin flex-grow-1"><div class="progress-bar" style="width: ${productsPercentage}%"></div></div>` : ''}
                            </div>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <span class="me-2">${user.landing_pages_count}/${user.max_landing_pages === -1 ? '∞' : user.max_landing_pages}</span>
                                ${user.max_landing_pages > 0 ? `<div class="progress progress-thin flex-grow-1"><div class="progress-bar" style="width: ${pagesPercentage}%"></div></div>` : ''}
                            </div>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <span class="me-2">${user.categories_count}/${user.max_categories === -1 ? '∞' : user.max_categories}</span>
                                ${user.max_categories > 0 ? `<div class="progress progress-thin flex-grow-1"><div class="progress-bar" style="width: ${categoriesPercentage}%"></div></div>` : ''}
                            </div>
                        </td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary" onclick="viewUserDetails(${user.id})">
                                <i class="fas fa-eye"></i>
                            </button>
                        </td>
                    </tr>
                `;
            });
            
            html += '</tbody></table></div>';
            document.getElementById('users-table').innerHTML = html;
        }
        
        function getStatusBadgeClass(status) {
            switch (status) {
                case 'active': return 'bg-success';
                case 'trial': return 'bg-info';
                case 'expired': return 'bg-warning';
                case 'cancelled': return 'bg-danger';
                default: return 'bg-secondary';
            }
        }
        
        function viewUserDetails(userId) {
            // Implémenter la vue détaillée de l'utilisateur
            alert(`Détails de l'utilisateur ID: ${userId}`);
        }
        
        function refreshAllData() {
            location.reload();
        }
        
        // Initialiser la page
        document.addEventListener('DOMContentLoaded', function() {
            initCharts();
        });
    </script>
</body>
</html>