<?php
try {
    $pdo = new PDO("mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Connexion réussie!\n";
    
    // Test simple
    $result = $pdo->query("SELECT COUNT(*) as count FROM stores")->fetch();
    echo "Nombre de stores: " . $result['count'] . "\n";
    
    $result = $pdo->query("SELECT COUNT(*) as count FROM categories")->fetch();
    echo "Nombre de catégories: " . $result['count'] . "\n";
    
    $result = $pdo->query("SELECT COUNT(*) as count FROM landing_pages")->fetch();
    echo "Nombre de landing pages: " . $result['count'] . "\n";
    
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
}
?>
