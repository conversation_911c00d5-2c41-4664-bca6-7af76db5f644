<?php

/**
 * AI Keys API Endpoint
 * Handles AI models and API keys management
 */

require_once 'config/database.php';

// Set headers
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    $database = new Database();
    $db = $database->getConnection();

    // Get request method and path
    $method = $_SERVER['REQUEST_METHOD'];
    $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
    $pathParts = explode('/', trim($path, '/'));

    // Extract action from path or query parameters
    $action = isset($pathParts[2]) ? $pathParts[2] : (isset($_GET['action']) ? $_GET['action'] : '');
    $id = isset($pathParts[3]) ? $pathParts[3] : (isset($_GET['id']) ? $_GET['id'] : null);

    // Simple authentication check
    $headers = function_exists('getallheaders') ? getallheaders() : [];
    if (empty($headers) && isset($_SERVER['HTTP_AUTHORIZATION'])) {
        $headers['Authorization'] = $_SERVER['HTTP_AUTHORIZATION'];
    }
    $token = isset($headers['Authorization']) ? str_replace('Bearer ', '', $headers['Authorization']) : null;

    // For demo purposes, accept demo_token
    if ($token !== 'demo_token') {
        ApiResponse::error('Token d\'authentification requis', 401);
    }

    switch ($method) {
        case 'GET':
            if ($action === 'models' || $action === '') {
                getAIModels($db);
            } elseif ($action === 'keys') {
                getAPIKeys($db);
            } elseif ($action === 'usage') {
                getUsageStats($db);
            } elseif ($action === 'dashboard') {
                getDashboardData($db);
            } else {
                ApiResponse::error('Action non supportée', 404);
            }
            break;

        case 'POST':
            $input = json_decode(file_get_contents('php://input'), true);
            if ($action === 'key') {
                createAPIKey($db, $input);
            } elseif ($action === 'model') {
                createAIModel($db, $input);
            } else {
                ApiResponse::error('Action non supportée', 404);
            }
            break;

        case 'PUT':
            $input = json_decode(file_get_contents('php://input'), true);
            if ($action === 'key' && $id) {
                updateAPIKey($db, $id, $input);
            } elseif ($action === 'model' && $id) {
                updateAIModel($db, $id, $input);
            } else {
                ApiResponse::error('Action non supportée', 404);
            }
            break;

        case 'DELETE':
            if ($action === 'key' && $id) {
                deleteAPIKey($db, $id);
            } elseif ($action === 'model' && $id) {
                deleteAIModel($db, $id);
            } else {
                ApiResponse::error('Action non supportée', 404);
            }
            break;

        default:
            ApiResponse::error('Méthode non supportée', 405);
    }
} catch (Exception $e) {
    error_log('AI Keys API Error: ' . $e->getMessage());
    ApiResponse::error('Internal server error', 500);
}

/**
 * Get all AI models
 */
function getAIModels($db)
{
    try {
        $query = "
            SELECT
                m.*,
                COUNT(u.id) as usage_count,
                SUM(u.total_tokens) as total_tokens_used,
                SUM(u.cost_usd) as total_cost
            FROM ai_models m
            LEFT JOIN ai_usage u ON m.id = u.model_id
            WHERE m.is_active = 1
            GROUP BY m.id
            ORDER BY m.provider, m.tier, m.display_name
        ";

        $stmt = $db->prepare($query);
        $stmt->execute();
        $models = $stmt->fetchAll();

        // Process the data
        foreach ($models as &$model) {
            $model['is_active'] = (bool)$model['is_active'];
            $model['is_premium'] = (bool)$model['is_premium'];
            $model['usage_count'] = (int)$model['usage_count'];
            $model['total_tokens_used'] = (int)$model['total_tokens_used'];
            $model['total_cost'] = (float)$model['total_cost'];
            $model['cost_per_token'] = (float)$model['cost_per_token'];
        }

        ApiResponse::success([
            'models' => $models,
            'total' => count($models)
        ]);
    } catch (Exception $e) {
        error_log("Error getting AI models: " . $e->getMessage());
        ApiResponse::error('Erreur lors de la récupération des modèles', 500);
    }
}

/**
 * Get all API keys
 */
function getAPIKeys($db)
{
    try {
        $query = "
            SELECT
                k.*,
                CASE
                    WHEN k.monthly_limit > 0 THEN ROUND((k.current_usage / k.monthly_limit) * 100, 1)
                    ELSE 0
                END as usage_percentage
            FROM ai_api_keys k
            ORDER BY k.provider, k.status, k.name
        ";

        $stmt = $db->prepare($query);
        $stmt->execute();
        $keys = $stmt->fetchAll();

        // Process the data (hide actual API keys for security)
        foreach ($keys as &$key) {
            $key['api_key'] = '***' . substr($key['api_key'], -4); // Show only last 4 chars
            $key['monthly_limit'] = (float)$key['monthly_limit'];
            $key['current_usage'] = (float)$key['current_usage'];
            $key['total_tokens_used'] = (int)$key['total_tokens_used'];
            $key['total_requests'] = (int)$key['total_requests'];
            $key['usage_percentage'] = (float)$key['usage_percentage'];
        }

        ApiResponse::success([
            'keys' => $keys,
            'total' => count($keys)
        ]);
    } catch (Exception $e) {
        error_log("Error getting API keys: " . $e->getMessage());
        ApiResponse::error('Erreur lors de la récupération des clés API', 500);
    }
}

/**
 * Get dashboard data for AI section
 */
function getDashboardData($db)
{
    try {
        // Get models with real usage and cost data
        $modelsQuery = "
            SELECT
                m.display_name as model,
                m.provider,
                m.cost_per_token,
                CASE
                    WHEN m.tier = 'premium' THEN 'Premium'
                    ELSE 'Économique'
                END as tier_label,
                COALESCE(SUM(u.tokens_used), 0) as total_tokens,
                COALESCE(SUM(u.cost), 0) as total_cost,
                CASE
                    WHEN COUNT(u.id) > 0 THEN 'Actif'
                    ELSE 'Inactif'
                END as status
            FROM ai_models m
            LEFT JOIN ai_usage u ON (
                m.display_name = u.model OR
                m.name = u.model OR
                LOWER(m.display_name) = LOWER(u.model)
            )
            WHERE m.is_active = 1
            GROUP BY m.id, m.display_name, m.provider, m.cost_per_token, m.tier
            ORDER BY total_cost DESC, m.provider, m.display_name
        ";

        $stmt = $db->prepare($modelsQuery);
        $stmt->execute();
        $models = $stmt->fetchAll();

        // Format the data to match the expected structure
        $formattedModels = [];

        foreach ($models as $model) {
            $formattedModels[] = [
                'model' => $model['model'],
                'provider' => $model['provider'],
                'usage' => number_format($model['total_tokens']) . ' tokens',
                'cost_total' => number_format($model['total_cost'], 2) . ' $',
                'cost_per_token' => '$' . number_format($model['cost_per_token'], 5),
                'tier' => $model['tier_label'],
                'status' => $model['status']
            ];
        }

        ApiResponse::success([
            'models' => $formattedModels,
            'total' => count($formattedModels)
        ]);
    } catch (Exception $e) {
        error_log("Error getting dashboard data: " . $e->getMessage());
        ApiResponse::error('Erreur lors de la récupération des données', 500);
    }
}

/**
 * Create new API key
 */
function createAPIKey($db, $data)
{
    try {
        // Validate required fields
        if (!isset($data['name']) || !isset($data['provider']) || !isset($data['api_key'])) {
            ApiResponse::error('Nom, fournisseur et clé API requis', 400);
        }

        $query = "
            INSERT INTO ai_api_keys
            (name, provider, api_key, status, monthly_limit)
            VALUES (?, ?, ?, ?, ?)
        ";

        $stmt = $db->prepare($query);
        $stmt->execute([
            $data['name'],
            $data['provider'],
            $data['api_key'], // In production, this should be encrypted
            $data['status'] ?? 'active',
            $data['monthly_limit'] ?? 0.00
        ]);

        $key_id = $db->lastInsertId();

        // Get the created key
        $stmt = $db->prepare("SELECT * FROM ai_api_keys WHERE id = ?");
        $stmt->execute([$key_id]);
        $created_key = $stmt->fetch();

        // Hide the actual API key
        $created_key['api_key'] = '***' . substr($created_key['api_key'], -4);

        ApiResponse::success($created_key, 'Clé API créée avec succès');
    } catch (Exception $e) {
        error_log("Error creating API key: " . $e->getMessage());
        ApiResponse::error('Erreur lors de la création de la clé API', 500);
    }
}
