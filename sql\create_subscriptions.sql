-- Mise à jour des plans d'abonnement existants
UPDATE `subscription_plans` SET
  `name` = 'Gratuit',
  `name_fr` = 'Gratuit',
  `name_en` = 'Free',
  `description` = 'Plan gratuit avec fonctionnalités de base',
  `description_fr` = 'Plan gratuit avec fonctionnalités de base',
  `description_en` = 'Free plan with basic features',
  `price` = 0.00,
  `currency` = 'DZD',
  `billing_cycle` = 'monthly',
  `features` = '{"features": ["1 landing page", "5 produits", "2 catégories", "100 MB stockage"]}',
  `max_products` = 5,
  `max_categories` = 2,
  `max_landing_pages` = 1,
  `max_storage_mb` = 100,
  `status` = 'active'
WHERE id = 1;

UPDATE `subscription_plans` SET
  `name` = 'Standard',
  `name_fr` = 'Standard',
  `name_en` = 'Standard',
  `description` = 'Plan standard pour les petites entreprises',
  `description_fr` = 'Plan standard pour les petites entreprises',
  `description_en` = 'Standard plan for small businesses',
  `price` = 2000.00,
  `currency` = 'DZD',
  `billing_cycle` = 'monthly',
  `features` = '{"features": ["5 landing pages", "50 produits", "10 catégories", "500 MB stockage"]}',
  `max_products` = 50,
  `max_categories` = 10,
  `max_landing_pages` = 5,
  `max_storage_mb` = 500,
  `status` = 'active'
WHERE id = 2;

UPDATE `subscription_plans` SET
  `name` = 'Premium',
  `name_fr` = 'Premium',
  `name_en` = 'Premium',
  `description` = 'Plan premium pour les entreprises en croissance',
  `description_fr` = 'Plan premium pour les entreprises en croissance',
  `description_en` = 'Premium plan for growing businesses',
  `price` = 5000.00,
  `currency` = 'DZD',
  `billing_cycle` = 'monthly',
  `features` = '{"features": ["20 landing pages", "200 produits", "30 catégories", "2 GB stockage"]}',
  `max_products` = 200,
  `max_categories` = 30,
  `max_landing_pages` = 20,
  `max_storage_mb` = 2000,
  `status` = 'active'
WHERE id = 3;