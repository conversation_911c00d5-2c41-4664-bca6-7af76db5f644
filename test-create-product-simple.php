<?php

/**
 * Simple test to create a product directly in database
 */

require_once 'api/config/database.php';

echo "<h2>Create Product Direct Test</h2>\n";
echo "<pre>\n";

try {
    $database = new Database();
    $db = $database->getConnection();

    // Sample product data
    $product_data = [
        'merchant_id' => 1, // Using merchant ID 1
        'store_id' => 1, // Using store ID 1 from our sample data
        'sku' => 'TEST-PRODUCT-001',
        'name' => 'Test smartphone', // Main name field
        'name_ar' => 'هاتف ذكي تجريبي',
        'name_fr' => 'Smartphone de test',
        'name_en' => 'Test smartphone',
        'description_ar' => 'وصف المنتج التجريبي',
        'description_fr' => 'Description du produit de test',
        'description_en' => 'Test product description',
        'price' => 25000.00,
        'compare_price' => 30000.00,
        'stock_quantity' => 10,
        'status' => 'active',
        'featured' => 1
    ];

    echo "Creating product with data:\n";
    foreach ($product_data as $key => $value) {
        echo "$key: $value\n";
    }
    echo "\n";

    // Insert product directly
    $sql = "
        INSERT INTO products (
            merchant_id, store_id, sku, name, name_ar, name_fr, name_en,
            description_ar, description_fr, description_en,
            price, compare_price, stock_quantity,
            status, featured, created_at, updated_at
        ) VALUES (
            ?, ?, ?, ?, ?, ?, ?,
            ?, ?, ?,
            ?, ?, ?,
            ?, ?, NOW(), NOW()
        )
    ";

    $stmt = $db->prepare($sql);
    $result = $stmt->execute([
        $product_data['merchant_id'],
        $product_data['store_id'],
        $product_data['sku'],
        $product_data['name'],
        $product_data['name_ar'],
        $product_data['name_fr'],
        $product_data['name_en'],
        $product_data['description_ar'],
        $product_data['description_fr'],
        $product_data['description_en'],
        $product_data['price'],
        $product_data['compare_price'],
        $product_data['stock_quantity'],
        $product_data['status'],
        $product_data['featured']
    ]);

    if ($result) {
        $product_id = $db->lastInsertId();
        echo "✅ Product created successfully with ID: $product_id\n";

        // Verify the product was created
        $verify_stmt = $db->prepare("SELECT * FROM products WHERE id = ?");
        $verify_stmt->execute([$product_id]);
        $created_product = $verify_stmt->fetch();

        echo "\nCreated product details:\n";
        echo "ID: {$created_product['id']}\n";
        echo "SKU: {$created_product['sku']}\n";
        echo "Name (AR): {$created_product['name_ar']}\n";
        echo "Price: {$created_product['price']} DZD\n";
        echo "Status: {$created_product['status']}\n";
    } else {
        echo "❌ Failed to create product\n";
    }
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "</pre>\n";
