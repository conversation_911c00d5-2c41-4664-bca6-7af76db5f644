<?php
/**
 * Script pour déboguer les produits
 */

// Configuration de la base de données
$host = 'localhost';
$port = 3307;
$dbname = 'landingpage_new';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;port=$port;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Récupérer le store TechStore Algeria
    $storeQuery = "SELECT id FROM stores WHERE store_name = 'TechStore Algeria'";
    $storeStmt = $pdo->prepare($storeQuery);
    $storeStmt->execute();
    $store = $storeStmt->fetch();

    if (!$store) {
        echo "❌ Store TechStore Algeria non trouvé\n";
        exit;
    }

    $storeId = $store['id'];
    echo "✅ Store ID: $storeId\n\n";

    // Vérifier les produits de ce store
    echo "Produits du store $storeId:\n";
    $productsQuery = "SELECT id, name, store_id FROM products WHERE store_id = ?";
    $productsStmt = $pdo->prepare($productsQuery);
    $productsStmt->execute([$storeId]);
    $products = $productsStmt->fetchAll();

    if (empty($products)) {
        echo "❌ Aucun produit trouvé pour ce store\n";
        
        // Vérifier tous les produits
        echo "\nTous les produits:\n";
        $allProductsQuery = "SELECT id, name, store_id, merchant_id FROM products LIMIT 10";
        $allProductsStmt = $pdo->prepare($allProductsQuery);
        $allProductsStmt->execute();
        $allProducts = $allProductsStmt->fetchAll();
        
        foreach ($allProducts as $product) {
            echo "ID: {$product['id']}, Name: {$product['name']}, Store ID: {$product['store_id']}, Merchant ID: {$product['merchant_id']}\n";
        }
    } else {
        foreach ($products as $product) {
            echo "ID: {$product['id']}, Name: {$product['name']}\n";
        }
    }

} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
}
?>
