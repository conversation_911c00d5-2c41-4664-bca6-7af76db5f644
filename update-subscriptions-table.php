<?php
require_once 'api/config/database.php';

try {
    $database = new Database();
    $pdo = $database->getConnection();
    
    echo "Mise à jour de la table subscriptions...\n";
    
    // Ajouter les colonnes manquantes
    $alterQueries = [
        "ALTER TABLE subscriptions ADD COLUMN IF NOT EXISTS display_name VARCHAR(100) AFTER name",
        "ALTER TABLE subscriptions ADD COLUMN IF NOT EXISTS billing_cycle ENUM('monthly', 'yearly') DEFAULT 'monthly' AFTER currency",
        "ALTER TABLE subscriptions ADD COLUMN IF NOT EXISTS max_subcategories INT DEFAULT 10 AFTER max_categories",
        "ALTER TABLE subscriptions ADD COLUMN IF NOT EXISTS max_storage_mb INT DEFAULT 1000 AFTER max_subcategories",
        "ALTER TABLE subscriptions ADD COLUMN IF NOT EXISTS max_bandwidth_gb INT DEFAULT 10 AFTER max_storage_mb",
        "ALTER TABLE subscriptions ADD COLUMN IF NOT EXISTS ai_enabled BOOLEAN DEFAULT FALSE AFTER max_bandwidth_gb",
        "ALTER TABLE subscriptions ADD COLUMN IF NOT EXISTS ai_monthly_tokens INT DEFAULT 0 AFTER ai_enabled",
        "ALTER TABLE subscriptions ADD COLUMN IF NOT EXISTS ai_models JSON AFTER ai_monthly_tokens",
        "ALTER TABLE subscriptions ADD COLUMN IF NOT EXISTS support_level ENUM('basic', 'priority', 'premium') DEFAULT 'basic' AFTER ai_models",
        "ALTER TABLE subscriptions ADD COLUMN IF NOT EXISTS custom_domain BOOLEAN DEFAULT FALSE AFTER support_level",
        "ALTER TABLE subscriptions ADD COLUMN IF NOT EXISTS analytics_advanced BOOLEAN DEFAULT FALSE AFTER custom_domain",
        "ALTER TABLE subscriptions ADD COLUMN IF NOT EXISTS white_label BOOLEAN DEFAULT FALSE AFTER analytics_advanced",
        "ALTER TABLE subscriptions ADD COLUMN IF NOT EXISTS api_access BOOLEAN DEFAULT FALSE AFTER white_label",
        "ALTER TABLE subscriptions ADD COLUMN IF NOT EXISTS is_featured BOOLEAN DEFAULT FALSE AFTER is_active"
    ];
    
    foreach ($alterQueries as $query) {
        try {
            $pdo->exec($query);
            echo "✅ Colonne ajoutée\n";
        } catch (Exception $e) {
            echo "⚠️ " . $e->getMessage() . "\n";
        }
    }
    
    // Mettre à jour les données existantes
    echo "\nMise à jour des données existantes...\n";
    
    // Ajouter display_name si manquant
    $pdo->exec("UPDATE subscriptions SET display_name = name WHERE display_name IS NULL OR display_name = ''");
    
    // Mettre à jour les plans avec des valeurs par défaut
    $updates = [
        "UPDATE subscriptions SET ai_enabled = TRUE, ai_monthly_tokens = 10000, support_level = 'basic' WHERE name = 'Starter'",
        "UPDATE subscriptions SET ai_enabled = TRUE, ai_monthly_tokens = 50000, support_level = 'priority', analytics_advanced = TRUE WHERE name = 'Pro'",
        "UPDATE subscriptions SET ai_enabled = TRUE, ai_monthly_tokens = -1, support_level = 'premium', analytics_advanced = TRUE, custom_domain = TRUE, white_label = TRUE, api_access = TRUE WHERE name = 'Enterprise'"
    ];
    
    foreach ($updates as $update) {
        try {
            $pdo->exec($update);
            echo "✅ Données mises à jour\n";
        } catch (Exception $e) {
            echo "⚠️ " . $e->getMessage() . "\n";
        }
    }
    
    // Créer la table user_subscriptions si elle n'existe pas
    echo "\nCréation de la table user_subscriptions...\n";
    $createUserSubscriptions = "
    CREATE TABLE IF NOT EXISTS user_subscriptions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id VARCHAR(255) NOT NULL,
        subscription_id INT NOT NULL,
        status ENUM('active', 'cancelled', 'expired', 'suspended') DEFAULT 'active',
        started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        expires_at TIMESTAMP NULL,
        cancelled_at TIMESTAMP NULL,
        payment_method VARCHAR(50),
        payment_id VARCHAR(255),
        last_payment_at TIMESTAMP NULL,
        next_payment_at TIMESTAMP NULL,
        current_products INT DEFAULT 0,
        current_landing_pages INT DEFAULT 0,
        current_categories INT DEFAULT 0,
        current_storage_mb INT DEFAULT 0,
        current_bandwidth_gb INT DEFAULT 0,
        current_ai_tokens INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (subscription_id) REFERENCES subscriptions(id) ON DELETE RESTRICT,
        INDEX idx_user (user_id),
        INDEX idx_status (status)
    )";
    
    $pdo->exec($createUserSubscriptions);
    echo "✅ Table user_subscriptions créée\n";
    
    // Vérifier les résultats
    echo "\n📊 Plans d'abonnement actuels :\n";
    $stmt = $pdo->query("SELECT name, display_name, price, max_products, max_landing_pages, ai_enabled, ai_monthly_tokens FROM subscriptions ORDER BY sort_order");
    $subscriptions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($subscriptions as $sub) {
        echo "- {$sub['display_name']} ({$sub['name']}):\n";
        echo "  Prix: {$sub['price']}€/mois\n";
        echo "  Produits: " . ($sub['max_products'] == -1 ? 'Illimité' : $sub['max_products']) . "\n";
        echo "  Landing Pages: " . ($sub['max_landing_pages'] == -1 ? 'Illimité' : $sub['max_landing_pages']) . "\n";
        echo "  IA: " . ($sub['ai_enabled'] ? 'Oui' : 'Non');
        if ($sub['ai_enabled']) {
            echo " (" . ($sub['ai_monthly_tokens'] == -1 ? 'Illimité' : number_format($sub['ai_monthly_tokens'])) . " tokens/mois)";
        }
        echo "\n\n";
    }
    
    echo "✅ Table subscriptions mise à jour avec succès !\n";
    
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
}
?>
