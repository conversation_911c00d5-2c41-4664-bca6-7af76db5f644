<?php

/**
 * API Endpoint pour la gestion des produits
 * Products management API for seller dashboard
 */

require_once 'config/database.php';

// Set headers
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

/**
 * Simple API Response helper class
 */
class ApiResponse
{
    public static function success($data = null, $message = null)
    {
        http_response_code(200);
        $response = ['success' => true];
        if ($data !== null) $response['data'] = $data;
        if ($message !== null) $response['message'] = $message;
        echo json_encode($response);
        exit;
    }

    public static function error($message, $code = 400)
    {
        http_response_code($code);
        echo json_encode(['success' => false, 'error' => $message]);
        exit;
    }

    public static function validateRequired($data, $required_fields)
    {
        foreach ($required_fields as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                self::error("Le champ '$field' est requis", 400);
            }
        }
    }

    public static function sanitizeInput($data)
    {
        if (is_array($data)) {
            return array_map([self::class, 'sanitizeInput'], $data);
        }
        return is_string($data) ? trim(htmlspecialchars($data, ENT_QUOTES, 'UTF-8')) : $data;
    }
}

try {
    // Initialiser la base de données
    $database = new Database();
    $db = $database->getConnection();

    // Create necessary tables if not exist
    createProductsTables($db);

    // Simple authentication check
    $headers = function_exists('getallheaders') ? getallheaders() : [];
    if (empty($headers) && isset($_SERVER['HTTP_AUTHORIZATION'])) {
        $headers['Authorization'] = $_SERVER['HTTP_AUTHORIZATION'];
    }
    $token = isset($headers['Authorization']) ? str_replace('Bearer ', '', $headers['Authorization']) : null;

    // For demo purposes, accept demo_token
    if ($token !== 'demo_token') {
        ApiResponse::error('Token d\'authentification requis', 401);
    }

    $method = $_SERVER['REQUEST_METHOD'];
    $request_uri = $_SERVER['REQUEST_URI'];
    $path_parts = explode('/', trim(parse_url($request_uri, PHP_URL_PATH), '/'));

    // Récupérer le store_id depuis les paramètres (pour le dashboard vendeur)
    $store_id = isset($_GET['store_id']) ? intval($_GET['store_id']) : 1; // Default à 1 pour le demo

    // Gérer les actions spéciales via query parameters
    $action = isset($_GET['action']) ? $_GET['action'] : '';

    switch ($method) {
        case 'GET':
            if (isset($path_parts[2]) && is_numeric($path_parts[2])) {
                // GET /api/products/{id} - Obtenir un produit spécifique
                getProduct($db, $store_id, $path_parts[2]);
            } else {
                // GET /api/products - Obtenir tous les produits
                getProducts($db, $store_id);
            }
            break;

        case 'POST':
            if ($action === 'clone' && isset($_GET['id']) && is_numeric($_GET['id'])) {
                // POST /api/products?action=clone&id={id} - Cloner un produit
                cloneProduct($db, $store_id, $_GET['id']);
            } elseif ($action === 'toggle-status' && isset($_GET['id']) && is_numeric($_GET['id'])) {
                // POST /api/products?action=toggle-status&id={id} - Changer le statut
                toggleProductStatus($db, $store_id, $_GET['id']);
            } else {
                // POST /api/products - Créer un nouveau produit
                createProduct($db, $store_id);
            }
            break;

        case 'PUT':
            if (isset($path_parts[2]) && is_numeric($path_parts[2])) {
                // PUT /api/products/{id} - Mettre à jour un produit
                updateProduct($db, $store_id, $path_parts[2]);
            } else {
                ApiResponse::error('ID du produit requis pour la mise à jour', 400);
            }
            break;

        case 'DELETE':
            if (isset($path_parts[2]) && is_numeric($path_parts[2])) {
                // DELETE /api/products/{id} - Supprimer un produit
                deleteProduct($db, $store_id, $path_parts[2]);
            } else {
                ApiResponse::error('ID du produit requis pour la suppression', 400);
            }
            break;

        default:
            ApiResponse::error('Méthode non autorisée', 405);
    }
    function createProductsTables($db)
    {
        try {
            // Create stores table if not exists
            $storesQuery = "
            CREATE TABLE IF NOT EXISTS stores (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                name VARCHAR(255) NOT NULL,
                slug VARCHAR(255) UNIQUE NOT NULL,
                description TEXT,
                logo_url VARCHAR(255) NULL,
                cover_url VARCHAR(255) NULL,
                status ENUM('active', 'inactive') DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";
            $db->exec($storesQuery);

            // Create product_categories table
            $categoriesQuery = "
            CREATE TABLE IF NOT EXISTS product_categories (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name_ar VARCHAR(255) NOT NULL,
                name_fr VARCHAR(255) NOT NULL,
                name_en VARCHAR(255) NOT NULL,
                slug VARCHAR(255) NOT NULL,
                description TEXT,
                parent_id INT DEFAULT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (parent_id) REFERENCES product_categories(id) ON DELETE SET NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";
            $db->exec($categoriesQuery);

            // Create products table
            $productsQuery = "
             CREATE TABLE IF NOT EXISTS products (
                 id INT AUTO_INCREMENT PRIMARY KEY,
                 store_id INT NOT NULL,
                 category_id INT NULL,
                 name_ar VARCHAR(255) NOT NULL,
                 name_fr VARCHAR(255) NOT NULL,
                 name_en VARCHAR(255) NOT NULL,
                 description_ar TEXT,
                 description_fr TEXT,
                 description_en TEXT,
                 price DECIMAL(10,2) NOT NULL,
                 compare_price DECIMAL(10,2) NULL,
                 cost DECIMAL(10,2) NULL,
                 sku VARCHAR(100) UNIQUE NULL,
                 barcode VARCHAR(100) NULL,
                 stock INT DEFAULT 0,
                 track_stock TINYINT(1) DEFAULT 1,
                 status ENUM('active', 'draft', 'archived') DEFAULT 'draft',
                 images JSON NULL,
                 variants JSON NULL,
                 tags JSON NULL,
                 seo_data JSON NULL,
                 created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                 updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                 FOREIGN KEY (store_id) REFERENCES stores(id) ON DELETE CASCADE,
                 FOREIGN KEY (category_id) REFERENCES product_categories(id) ON DELETE SET NULL
             ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
         ";
            $db->exec($productsQuery);

            // Create customers table if not exists
            $customersQuery = "
             CREATE TABLE IF NOT EXISTS customers (
                 id INT AUTO_INCREMENT PRIMARY KEY,
                 store_id INT NOT NULL,
                 first_name VARCHAR(100) NULL,
                 last_name VARCHAR(100) NULL,
                 email VARCHAR(255) NULL,
                 phone VARCHAR(50) NULL,
                 address TEXT NULL,
                 created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                 updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                 FOREIGN KEY (store_id) REFERENCES stores(id) ON DELETE CASCADE
             ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
         ";
            $db->exec($customersQuery);

            // Create orders table if not exists
            $ordersQuery = "
             CREATE TABLE IF NOT EXISTS orders (
                 id INT AUTO_INCREMENT PRIMARY KEY,
                 store_id INT NOT NULL,
                 customer_id INT NULL,
                 status ENUM('pending', 'processing', 'shipped', 'delivered', 'cancelled') DEFAULT 'pending',
                 total DECIMAL(10,2) NOT NULL,
                 created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                 updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                 FOREIGN KEY (store_id) REFERENCES stores(id) ON DELETE CASCADE,
                 FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL
             ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
         ";
            $db->exec($ordersQuery);

            // Create order_items table if not exists
            $orderItemsQuery = "
              CREATE TABLE IF NOT EXISTS order_items (
                  id INT AUTO_INCREMENT PRIMARY KEY,
                  order_id INT NOT NULL,
                  product_id INT NOT NULL,
                  quantity INT NOT NULL,
                  price DECIMAL(10,2) NOT NULL,
                  subtotal DECIMAL(10,2) NOT NULL,
                  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                  FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
                  FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
              ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
          ";
            $db->exec($orderItemsQuery);

            // Create reviews table if not exists
            $reviewsQuery = "
             CREATE TABLE IF NOT EXISTS reviews (
                 id INT AUTO_INCREMENT PRIMARY KEY,
                 product_id INT NOT NULL,
                 customer_id INT NULL,
                 rating TINYINT NOT NULL,
                 comment TEXT,
                 status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
                 created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                 FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
                 FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL
             ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
         ";
            $db->exec($reviewsQuery);
        } catch (Exception $e) {
            error_log("Error creating products tables: " . $e->getMessage());
        }
    }
} catch (Exception $e) {
    error_log("Products API Error: " . $e->getMessage());
    ApiResponse::error('Erreur interne du serveur', 500);
}

/**
 * Obtenir tous les produits du magasin
 */
function getProducts($db, $store_id)
{
    try {
        $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
        $limit = isset($_GET['limit']) ? min(100, max(1, intval($_GET['limit']))) : 20;
        $offset = ($page - 1) * $limit;

        $search = isset($_GET['search']) ? trim($_GET['search']) : '';
        $category = isset($_GET['category']) ? trim($_GET['category']) : '';
        $status = isset($_GET['status']) ? trim($_GET['status']) : '';
        $sort = isset($_GET['sort']) ? trim($_GET['sort']) : 'created_at';
        $order = isset($_GET['order']) && strtolower($_GET['order']) === 'asc' ? 'ASC' : 'DESC';

        // Construire la requête avec filtres
        $where_conditions = ['p.store_id = ?'];
        $params = [$store_id];

        if (!empty($search)) {
            $where_conditions[] = '(p.name_ar LIKE ? OR p.name_fr LIKE ? OR p.name_en LIKE ? OR p.sku LIKE ?)';
            $search_param = '%' . $search . '%';
            $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param]);
        }

        if (!empty($category)) {
            $where_conditions[] = 'p.category_id = ?';
            $params[] = $category;
        }

        if (!empty($status)) {
            $where_conditions[] = 'p.status = ?';
            $params[] = $status;
        }

        $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);

        // Requête pour compter le total
        $count_sql = "SELECT COUNT(*) as total FROM products p $where_clause";
        $count_stmt = $db->prepare($count_sql);
        $count_stmt->execute($params);
        $total = $count_stmt->fetch()['total'];

        // Requête principale avec pagination
        $sql = "
            SELECT
                p.*,
                pc.name_ar as category_name_ar,
                pc.name_fr as category_name_fr,
                pc.name_en as category_name_en,
                COALESCE(SUM(oi.quantity), 0) as total_sold
            FROM products p
            LEFT JOIN product_categories pc ON p.category_id = pc.id
            LEFT JOIN order_items oi ON p.id = oi.product_id
            $where_clause
            GROUP BY p.id
            ORDER BY p.$sort $order
            LIMIT $limit OFFSET $offset
        ";

        $stmt = $db->prepare($sql);
        $stmt->execute($params);
        $products = $stmt->fetchAll();

        // Formater les données
        foreach ($products as &$product) {
            $product['images'] = json_decode($product['images'], true) ?: [];
            $product['tags'] = json_decode($product['tags'], true) ?: [];
            $product['variants'] = json_decode($product['variants'], true) ?: [];
            $product['seo_data'] = json_decode($product['seo_data'], true) ?: [];
            $product['total_sold'] = intval($product['total_sold']);
        }

        ApiResponse::success([
            'products' => $products,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $limit,
                'total' => intval($total),
                'total_pages' => ceil($total / $limit)
            ]
        ]);
    } catch (Exception $e) {
        error_log("Error getting products: " . $e->getMessage());
        ApiResponse::error('Erreur lors de la récupération des produits', 500);
    }
}

/**
 * Obtenir un produit spécifique
 */
function getProduct($db, $store_id, $product_id)
{
    try {
        $sql = "
            SELECT
                p.*,
                pc.name_ar as category_name_ar,
                pc.name_fr as category_name_fr,
                pc.name_en as category_name_en,
                COALESCE(SUM(oi.quantity), 0) as total_sold,
                COALESCE(AVG(pr.rating), 0) as average_rating,
                COUNT(pr.id) as review_count
            FROM products p
            LEFT JOIN product_categories pc ON p.category_id = pc.id
            LEFT JOIN order_items oi ON p.id = oi.product_id
            LEFT JOIN reviews pr ON p.id = pr.product_id
            WHERE p.id = ? AND p.store_id = ?
            GROUP BY p.id
        ";

        $stmt = $db->prepare($sql);
        $stmt->execute([$product_id, $store_id]);
        $product = $stmt->fetch();

        if (!$product) {
            ApiResponse::error('Produit non trouvé', 404);
        }

        // Formater les données
        $product['images'] = json_decode($product['images'], true) ?: [];
        $product['tags'] = json_decode($product['tags'], true) ?: [];
        $product['variants'] = json_decode($product['variants'], true) ?: [];
        $product['seo_data'] = json_decode($product['seo_data'], true) ?: [];
        $product['total_sold'] = intval($product['total_sold']);
        $product['average_rating'] = floatval($product['average_rating']);
        $product['review_count'] = intval($product['review_count']);

        ApiResponse::success($product);
    } catch (Exception $e) {
        error_log("Error getting product: " . $e->getMessage());
        ApiResponse::error('Erreur lors de la récupération du produit', 500);
    }
}

/**
 * Créer un nouveau produit
 */
function createProduct($db, $store_id)
{
    try {
        $input = json_decode(file_get_contents('php://input'), true);

        // Validation des champs requis
        ApiResponse::validateRequired($input, ['name_ar', 'price']);

        // Nettoyer les données
        $data = ApiResponse::sanitizeInput($input);

        // Générer un SKU unique si non fourni
        if (empty($data['sku'])) {
            $data['sku'] = 'PRD-' . strtoupper(uniqid());
        }

        // Vérifier l'unicité du SKU
        $sku_check = $db->prepare("SELECT id FROM products WHERE sku = ? AND store_id = ?");
        $sku_check->execute([$data['sku'], $store_id]);
        if ($sku_check->fetch()) {
            ApiResponse::error('SKU déjà existant', 400);
        }

        $sql = "
            INSERT INTO products (
                merchant_id, store_id, sku, name, name_ar, name_fr, name_en,
                description_ar, description_fr, description_en,
                price, compare_price, cost_price, currency,
                stock_quantity, weight, dimensions,
                category_id, tags, images,
                status, featured,
                created_at, updated_at
            ) VALUES (
                ?, ?, ?, ?, ?, ?, ?,
                ?, ?, ?,
                ?, ?, ?, ?,
                ?, ?, ?,
                ?, ?, ?,
                ?, ?,
                NOW(), NOW()
            )
        ";

        $stmt = $db->prepare($sql);
        $result = $stmt->execute([
            $store['merchant_id'] ?? 1, // Get merchant_id from store data
            $store_id,
            $data['sku'],
            $data['name'] ?? $data['name_ar'], // Use name_ar as fallback for name
            $data['name_ar'],
            $data['name_fr'] ?? null,
            $data['name_en'] ?? null,
            $data['description_ar'] ?? null,
            $data['description_fr'] ?? null,
            $data['description_en'] ?? null,
            $data['price'],
            $data['compare_price'] ?? null,
            $data['cost_price'] ?? null,
            $data['currency'] ?? 'DZD',
            $data['stock_quantity'] ?? 0,
            $data['weight'] ?? null,
            isset($data['dimensions']) ? json_encode($data['dimensions']) : null,
            $data['category_id'] ?? null,
            isset($data['tags']) ? json_encode($data['tags']) : null,
            isset($data['images']) ? json_encode($data['images']) : null,
            $data['status'] ?? 'active',
            isset($data['featured']) ? intval($data['featured']) : 0
        ]);

        if ($result) {
            $product_id = $db->lastInsertId();
            ApiResponse::success(['id' => $product_id], 'Produit créé avec succès', 201);
        } else {
            ApiResponse::error('Erreur lors de la création du produit', 500);
        }
    } catch (Exception $e) {
        error_log("Error creating product: " . $e->getMessage());
        ApiResponse::error('Erreur lors de la création du produit', 500);
    }
}

/**
 * Mettre à jour un produit
 */
function updateProduct($db, $store_id, $product_id)
{
    try {
        $input = json_decode(file_get_contents('php://input'), true);

        // Vérifier que le produit existe et appartient au magasin
        $check_stmt = $db->prepare("SELECT id FROM products WHERE id = ? AND store_id = ?");
        $check_stmt->execute([$product_id, $store_id]);
        if (!$check_stmt->fetch()) {
            ApiResponse::error('Produit non trouvé', 404);
        }

        // Nettoyer les données
        $data = ApiResponse::sanitizeInput($input);

        // Construire la requête de mise à jour dynamiquement
        $update_fields = [];
        $params = [];

        $allowed_fields = [
            'sku',
            'name',
            'name_ar',
            'name_fr',
            'name_en',
            'description_ar',
            'description_fr',
            'description_en',
            'price',
            'compare_price',
            'cost_price',
            'currency',
            'stock_quantity',

            'weight',
            'category_id',
            'status',

            'featured'
        ];

        foreach ($allowed_fields as $field) {
            if (isset($data[$field])) {
                $update_fields[] = "$field = ?";
                $params[] = $data[$field];
            }
        }

        // Champs JSON
        $json_fields = ['dimensions', 'tags', 'images', 'variants', 'seo_data'];
        foreach ($json_fields as $field) {
            if (isset($data[$field])) {
                $update_fields[] = "$field = ?";
                $params[] = json_encode($data[$field]);
            }
        }

        if (empty($update_fields)) {
            ApiResponse::error('Aucune donnée à mettre à jour', 400);
        }

        // Vérifier l'unicité du SKU si modifié
        if (isset($data['sku'])) {
            $sku_check = $db->prepare("SELECT id FROM products WHERE sku = ? AND store_id = ? AND id != ?");
            $sku_check->execute([$data['sku'], $store_id, $product_id]);
            if ($sku_check->fetch()) {
                ApiResponse::error('SKU déjà existant', 400);
            }
        }

        $update_fields[] = "updated_at = NOW()";
        $params[] = $product_id;
        $params[] = $store_id;

        $sql = "UPDATE products SET " . implode(', ', $update_fields) . " WHERE id = ? AND store_id = ?";

        $stmt = $db->prepare($sql);
        $result = $stmt->execute($params);

        if ($result) {
            ApiResponse::success(null, 'Produit mis à jour avec succès');
        } else {
            ApiResponse::error('Erreur lors de la mise à jour du produit', 500);
        }
    } catch (Exception $e) {
        error_log("Error updating product: " . $e->getMessage());
        ApiResponse::error('Erreur lors de la mise à jour du produit', 500);
    }
}

/**
 * Supprimer un produit
 */
function deleteProduct($db, $store_id, $product_id)
{
    try {
        // Vérifier que le produit existe et appartient au magasin
        $check_stmt = $db->prepare("SELECT id FROM products WHERE id = ? AND store_id = ?");
        $check_stmt->execute([$product_id, $store_id]);
        if (!$check_stmt->fetch()) {
            ApiResponse::error('Produit non trouvé', 404);
        }

        // Vérifier s'il y a des commandes liées
        $order_check = $db->prepare("SELECT COUNT(*) as count FROM order_items WHERE product_id = ?");
        $order_check->execute([$product_id]);
        $order_count = $order_check->fetch()['count'];

        if ($order_count > 0) {
            // Ne pas supprimer, juste désactiver
            $stmt = $db->prepare("UPDATE products SET status = 'deleted', updated_at = NOW() WHERE id = ? AND store_id = ?");
            $result = $stmt->execute([$product_id, $store_id]);
            $message = 'Produit désactivé (commandes existantes)';
        } else {
            // Supprimer définitivement
            $stmt = $db->prepare("DELETE FROM products WHERE id = ? AND store_id = ?");
            $result = $stmt->execute([$product_id, $store_id]);
            $message = 'Produit supprimé définitivement';
        }

        if ($result) {
            ApiResponse::success(null, $message);
        } else {
            ApiResponse::error('Erreur lors de la suppression du produit', 500);
        }
    } catch (Exception $e) {
        error_log("Error deleting product: " . $e->getMessage());
        ApiResponse::error('Erreur lors de la suppression du produit', 500);
    }
}

/**
 * Cloner un produit existant
 */
function cloneProduct($db, $store_id, $product_id)
{
    try {
        // Vérifier que le produit existe et appartient au store
        $check_stmt = $db->prepare("SELECT * FROM products WHERE id = ? AND store_id = ?");
        $check_stmt->execute([$product_id, $store_id]);
        $original_product = $check_stmt->fetch();

        if (!$original_product) {
            ApiResponse::error('Produit non trouvé', 404);
            return;
        }

        // Créer une copie du produit avec un nouveau nom
        $new_name_ar = $original_product['name_ar'] . ' (Copie)';
        $new_name_fr = $original_product['name_fr'] . ' (Copie)';
        $new_name_en = $original_product['name_en'] . ' (Copy)';
        $new_sku = $original_product['sku'] . '_COPY_' . time();

        $stmt = $db->prepare("
            INSERT INTO products (
                store_id, name_ar, name_fr, name_en, description_ar, description_fr, description_en,
                sku, price, compare_price, cost_price, stock_quantity, manage_stock,
                weight, dimensions, category_id, tags, images, status, seo_title, seo_description,
                created_at, updated_at
            ) VALUES (
                ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'draft', ?, ?, NOW(), NOW()
            )
        ");

        $result = $stmt->execute([
            $store_id,
            $new_name_ar,
            $new_name_fr,
            $new_name_en,
            $original_product['description_ar'],
            $original_product['description_fr'],
            $original_product['description_en'],
            $new_sku,
            $original_product['price'],
            $original_product['compare_price'],
            $original_product['cost_price'],
            0, // Stock à 0 pour la copie
            $original_product['manage_stock'],
            $original_product['weight'],
            $original_product['dimensions'],
            $original_product['category_id'],
            $original_product['tags'],
            $original_product['images'],
            $original_product['seo_title'] . ' (Copie)',
            $original_product['seo_description']
        ]);

        if ($result) {
            $new_product_id = $db->lastInsertId();
            ApiResponse::success([
                'id' => $new_product_id,
                'message' => 'Produit cloné avec succès'
            ], 'Produit cloné avec succès');
        } else {
            ApiResponse::error('Erreur lors du clonage du produit', 500);
        }
    } catch (Exception $e) {
        error_log("Error cloning product: " . $e->getMessage());
        ApiResponse::error('Erreur lors du clonage du produit', 500);
    }
}

/**
 * Changer le statut d'un produit (activer/désactiver)
 */
function toggleProductStatus($db, $store_id, $product_id)
{
    try {
        // Vérifier que le produit existe et appartient au store
        $check_stmt = $db->prepare("SELECT status FROM products WHERE id = ? AND store_id = ?");
        $check_stmt->execute([$product_id, $store_id]);
        $product = $check_stmt->fetch();

        if (!$product) {
            ApiResponse::error('Produit non trouvé', 404);
            return;
        }

        // Déterminer le nouveau statut
        $current_status = $product['status'];
        $new_status = ($current_status === 'active') ? 'inactive' : 'active';

        // Mettre à jour le statut
        $stmt = $db->prepare("UPDATE products SET status = ?, updated_at = NOW() WHERE id = ? AND store_id = ?");
        $result = $stmt->execute([$new_status, $product_id, $store_id]);

        if ($result) {
            $action_text = ($new_status === 'active') ? 'activé' : 'désactivé';
            ApiResponse::success([
                'id' => $product_id,
                'old_status' => $current_status,
                'new_status' => $new_status,
                'message' => "Produit {$action_text} avec succès"
            ], "Produit {$action_text} avec succès");
        } else {
            ApiResponse::error('Erreur lors du changement de statut', 500);
        }
    } catch (Exception $e) {
        error_log("Error toggling product status: " . $e->getMessage());
        ApiResponse::error('Erreur lors du changement de statut', 500);
    }
}
