<?php
/**
 * Test de l'API landing pages
 */

echo "🧪 Test de l'API landing pages...\n\n";

// Simuler les headers et variables d'environnement
$_SERVER['REQUEST_METHOD'] = 'GET';
$_GET['action'] = 'all';
$_SERVER['HTTP_AUTHORIZATION'] = 'Bearer demo_token';

// Capturer la sortie
ob_start();

try {
    // Inclure l'API
    include __DIR__ . '/api/landing-pages.php';
    
    $output = ob_get_contents();
    ob_end_clean();
    
    echo "✅ API Response:\n";
    echo $output . "\n";
    
    // Vérifier si c'est du JSON valide
    $json = json_decode($output, true);
    if ($json) {
        echo "\n✅ JSON valide\n";
        if (isset($json['success']) && $json['success']) {
            echo "✅ Succès: " . (isset($json['data']) ? count($json['data']) : 0) . " landing pages trouvées\n";
        } else {
            echo "❌ Erreur dans la réponse: " . ($json['error'] ?? 'Erreur inconnue') . "\n";
        }
    } else {
        echo "❌ Réponse non-JSON ou invalide\n";
    }
    
} catch (Exception $e) {
    ob_end_clean();
    echo "❌ Erreur: " . $e->getMessage() . "\n";
}
?>
