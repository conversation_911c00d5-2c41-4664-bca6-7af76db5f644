# Changelog

## [1.2.0] - 2025-07-31

### 🎯 Nouvelles fonctionnalités
  - Section IA : Utilisation de données réelles d'usage et de coûts depuis `ai_usage`
  - Section Commandes : Création de commandes de test avec données réalistes
  - Section Abonnements : Abonnements utilisateurs actifs avec données réelles
  - Section Utilisateurs : CRUD complet fonctionnel avec gestion des rôles

### 🐛 Corrections de bugs majeures
- 🔧 **Section IA** : Correction de l'erreur `html is not defined`
  - Template literal correctement fermé
    Suppression de la référence incorrecte à la variable `html`
  - Template literal correctement fermé

-
 - ✅ Erreur (MethodN Allwed): Ajo d méthoatesPUT*oriDELETEsà  cAPI
  - ✅ Emplète404 sdr aetio  `get`r: Implsm nRatiDn  40la f sctiou `g tUaer()`
c ti✅``d IpteUltriit not drfined``undefined` la f CctioocJ vaSceipt manquan
  ✅✅IDs utitisatiusIa`u: Afintd` :uCorratsion `(`e .u`d`l→(`.e_d`
  - ✅ FoncPI aquane: Ajo de `updUer()`e-`d ra eUser()`, statuts, méthodes de paiement

##🛒 📊 Base deCoema de DonnéCorrlcedl'errer"Aucuné rouvée"
  - 9 entrées bs5rstmmaideqsdu etavec tatt vaé
  - Cl1r4c,200 pa l'API drdero foncotsl2.5.7otl l_obigor
  - Dinné : réaOpsAI, :hmontpie, Mstttutmthddpaiement

###📊Basdonné-Dné réls- 🛒 **Table orders** : Création de commandes de test
  - 5 cTablm an_usage** :sajoutvd tatuts s d'uvageariés (p
  -d9n,rorécsebaséss surhlpspstdtieliques fvureies)
  - Ttkesalui8àé : 32,150 àh45,200 dap : CCPmaire
  - Coûrs sé çi :o$2.26tes$45.72slmoè
Pvir:

s🛒i**Tibln:Ardors** : 2réteurs de v tSanarteePm'cgt
l-n5 iemmasvecsttusvaié(*ending, APocI aikg,p hia  d,oneesvercd)kées vers requêtes SQL réelles
  - Mnnraat `réasasgee`:o€89.99qàs€459.99  - Calcul automatique du statut Actif/Inactif basé sur l'usage
 i- Méthocosûta*pIi** :  localeEen CCP, Bat dsm b,,virELETEbancir
  -Adrssraçaises mpèe

- 👤 **Table estion des erreurs**amélioréeif
  - 2 n Clura euastavoc tbonnlisateStrtretPro
## [St.tutactfdaed'xpion
Ld' conf goréeu

###e🔧 Amélotict hnvqneses réelles** : Refonte complète de la gestion IA
###�🐛 API oi-keyr.prpns de bga dedés ockévrsAICoêtrtiSQL re 5l00 de la création de rôles
Jonture ave `i_usa`pour
### Cel uleautnmatsquu-**atnteActif/Inmctsf b`sésiur l'uptio
s - Tr  par coût tota` décroussenrbscriptions`, `subscription_payments`
- 🗄️ **Tables IA** : `ai_models`, `ai_api_keys`, `ai_usage`
-� oen**Extensionèedfctioalié
- ✨ Suuport des dcteonceGET,`PUT, DELETE
  - domain` (var rrturr a élln éeduct_count` (int, default 0) à la table `stores`
Fonctns CRUD compètpu la gsutiliat1573🎯Nvsfionnié**StiAbmn**:Implmentclèysèm'bnnmes🤖**SoIAvnnéées**:RfoèeIA
###🐛rrecionsbg�**APIRl**Crecion'errer500lacrôle📊Bonnée🗄️**TabsAonen**:`sscps`,`se_sbscps`,`subscip_pyets`-🗄️**TlIA**:`_ml`,`ai_pi_ky`,`sg` [1.0.0] -2024-01-17###B✨Ajlolnn`n`(cha)à bl``✨lacolon`prduct_o` (t,defau 0)àlaable`t`
