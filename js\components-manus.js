/**
 * Components JavaScript - Composants réutilisables
 * Gestion des composants UI interactifs avec support RTL
 */

// Namespace pour les composants
window.LandingPageComponents = {
    // Configuration globale
    config: {
        animationDuration: 300,
        breakpoints: {
            mobile: 768,
            tablet: 1024,
            desktop: 1440
        },
        rtl: document.dir === 'rtl' || document.documentElement.dir === 'rtl'
    },

    // Utilitaires
    utils: {
        // Détection de l'appareil
        isMobile: () => window.innerWidth < window.LandingPageComponents.config.breakpoints.mobile,
        isTablet: () => window.innerWidth >= window.LandingPageComponents.config.breakpoints.mobile && window.innerWidth < window.LandingPageComponents.config.breakpoints.tablet,
        isDesktop: () => window.innerWidth >= window.LandingPageComponents.config.breakpoints.desktop,

        // Debounce function
        debounce: (func, wait) => {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },

        // Throttle function
        throttle: (func, limit) => {
            let inThrottle;
            return function() {
                const args = arguments;
                const context = this;
                if (!inThrottle) {
                    func.apply(context, args);
                    inThrottle = true;
                    setTimeout(() => inThrottle = false, limit);
                }
            }
        },

        // Animation avec support des préférences utilisateur
        animate: (element, animation, duration = 300) => {
            if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
                return Promise.resolve();
            }
            
            return new Promise(resolve => {
                element.style.animation = `${animation} ${duration}ms ease-out`;
                element.addEventListener('animationend', () => {
                    element.style.animation = '';
                    resolve();
                }, { once: true });
            });
        },

        // Gestion des événements tactiles
        addTouchSupport: (element, callback) => {
            let startY = 0;
            let startX = 0;
            
            element.addEventListener('touchstart', (e) => {
                startY = e.touches[0].clientY;
                startX = e.touches[0].clientX;
            });
            
            element.addEventListener('touchend', (e) => {
                const endY = e.changedTouches[0].clientY;
                const endX = e.changedTouches[0].clientX;
                const diffY = startY - endY;
                const diffX = startX - endX;
                
                if (Math.abs(diffY) > Math.abs(diffX)) {
                    if (diffY > 50) callback('swipeUp');
                    if (diffY < -50) callback('swipeDown');
                } else {
                    if (diffX > 50) callback('swipeLeft');
                    if (diffX < -50) callback('swipeRight');
                }
            });
        }
    },

    // Composant Modal
    Modal: {
        instances: new Map(),
        
        create: (id, options = {}) => {
            const defaults = {
                closable: true,
                backdrop: true,
                keyboard: true,
                focus: true,
                animation: true
            };
            
            const config = { ...defaults, ...options };
            const modal = document.getElementById(id);
            
            if (!modal) {
                console.error(`Modal with id '${id}' not found`);
                return null;
            }
            
            const instance = {
                element: modal,
                config: config,
                isOpen: false,
                
                open: function() {
                    if (this.isOpen) return;
                    
                    this.isOpen = true;
                    document.body.classList.add('modal-open');
                    this.element.classList.add('active');
                    
                    if (this.config.focus) {
                        this.element.focus();
                    }
                    
                    // Trap focus
                    this.trapFocus();
                    
                    // Événement personnalisé
                    this.element.dispatchEvent(new CustomEvent('modal:open'));
                },
                
                close: function() {
                    if (!this.isOpen) return;
                    
                    this.isOpen = false;
                    document.body.classList.remove('modal-open');
                    this.element.classList.remove('active');
                    
                    // Événement personnalisé
                    this.element.dispatchEvent(new CustomEvent('modal:close'));
                },
                
                toggle: function() {
                    this.isOpen ? this.close() : this.open();
                },
                
                trapFocus: function() {
                    const focusableElements = this.element.querySelectorAll(
                        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
                    );
                    
                    if (focusableElements.length === 0) return;
                    
                    const firstElement = focusableElements[0];
                    const lastElement = focusableElements[focusableElements.length - 1];
                    
                    this.element.addEventListener('keydown', (e) => {
                        if (e.key === 'Tab') {
                            if (e.shiftKey) {
                                if (document.activeElement === firstElement) {
                                    lastElement.focus();
                                    e.preventDefault();
                                }
                            } else {
                                if (document.activeElement === lastElement) {
                                    firstElement.focus();
                                    e.preventDefault();
                                }
                            }
                        }
                    });
                }
            };
            
            // Gestion des événements
            if (config.closable) {
                const closeButtons = modal.querySelectorAll('[data-modal-close]');
                closeButtons.forEach(btn => {
                    btn.addEventListener('click', () => instance.close());
                });
            }
            
            if (config.backdrop) {
                modal.addEventListener('click', (e) => {
                    if (e.target === modal) {
                        instance.close();
                    }
                });
            }
            
            if (config.keyboard) {
                document.addEventListener('keydown', (e) => {
                    if (e.key === 'Escape' && instance.isOpen) {
                        instance.close();
                    }
                });
            }
            
            window.LandingPageComponents.Modal.instances.set(id, instance);
            return instance;
        },
        
        get: (id) => {
            return window.LandingPageComponents.Modal.instances.get(id);
        }
    },

    // Composant Accordion
    Accordion: {
        init: (selector) => {
            const accordions = document.querySelectorAll(selector);
            
            accordions.forEach(accordion => {
                const items = accordion.querySelectorAll('.accordion-item');
                
                items.forEach(item => {
                    const header = item.querySelector('.accordion-header');
                    const content = item.querySelector('.accordion-content');
                    
                    if (!header || !content) return;
                    
                    header.addEventListener('click', () => {
                        const isActive = item.classList.contains('active');
                        
                        // Fermer tous les autres items si accordion simple
                        if (!accordion.hasAttribute('data-multiple')) {
                            items.forEach(otherItem => {
                                if (otherItem !== item) {
                                    otherItem.classList.remove('active');
                                    const otherContent = otherItem.querySelector('.accordion-content');
                                    if (otherContent) {
                                        otherContent.style.maxHeight = '0';
                                    }
                                }
                            });
                        }
                        
                        // Toggle l'item actuel
                        if (isActive) {
                            item.classList.remove('active');
                            content.style.maxHeight = '0';
                        } else {
                            item.classList.add('active');
                            content.style.maxHeight = content.scrollHeight + 'px';
                        }
                    });
                    
                    // Initialiser la hauteur
                    if (item.classList.contains('active')) {
                        content.style.maxHeight = content.scrollHeight + 'px';
                    } else {
                        content.style.maxHeight = '0';
                    }
                });
            });
        }
    },

    // Composant Tabs
    Tabs: {
        init: (selector) => {
            const tabContainers = document.querySelectorAll(selector);
            
            tabContainers.forEach(container => {
                const tabs = container.querySelectorAll('.tab-button');
                const panels = container.querySelectorAll('.tab-panel');
                
                tabs.forEach((tab, index) => {
                    tab.addEventListener('click', () => {
                        // Désactiver tous les tabs
                        tabs.forEach(t => t.classList.remove('active'));
                        panels.forEach(p => p.classList.remove('active'));
                        
                        // Activer le tab cliqué
                        tab.classList.add('active');
                        if (panels[index]) {
                            panels[index].classList.add('active');
                        }
                        
                        // Événement personnalisé
                        container.dispatchEvent(new CustomEvent('tab:change', {
                            detail: { index, tab, panel: panels[index] }
                        }));
                    });
                    
                    // Support clavier
                    tab.addEventListener('keydown', (e) => {
                        if (e.key === 'Enter' || e.key === ' ') {
                            e.preventDefault();
                            tab.click();
                        }
                    });
                });
            });
        }
    },

    // Composant Carousel/Slider
    Carousel: {
        instances: new Map(),
        
        create: (selector, options = {}) => {
            const element = document.querySelector(selector);
            if (!element) return null;
            
            const defaults = {
                autoplay: false,
                autoplayDelay: 3000,
                loop: true,
                navigation: true,
                pagination: true,
                slidesPerView: 1,
                spaceBetween: 20,
                breakpoints: {}
            };
            
            const config = { ...defaults, ...options };
            const slides = element.querySelectorAll('.carousel-slide');
            let currentIndex = 0;
            let autoplayTimer = null;
            
            const instance = {
                element,
                config,
                currentIndex,
                slides,
                
                goTo: function(index) {
                    if (index < 0 || index >= this.slides.length) {
                        if (!this.config.loop) return;
                        index = index < 0 ? this.slides.length - 1 : 0;
                    }
                    
                    this.currentIndex = index;
                    this.updateSlides();
                    this.updatePagination();
                    
                    // Événement personnalisé
                    this.element.dispatchEvent(new CustomEvent('carousel:change', {
                        detail: { index: this.currentIndex }
                    }));
                },
                
                next: function() {
                    this.goTo(this.currentIndex + 1);
                },
                
                prev: function() {
                    this.goTo(this.currentIndex - 1);
                },
                
                updateSlides: function() {
                    const translateX = -this.currentIndex * 100;
                    const slidesContainer = this.element.querySelector('.carousel-slides');
                    if (slidesContainer) {
                        slidesContainer.style.transform = `translateX(${translateX}%)`;
                    }
                },
                
                updatePagination: function() {
                    const dots = this.element.querySelectorAll('.carousel-dot');
                    dots.forEach((dot, index) => {
                        dot.classList.toggle('active', index === this.currentIndex);
                    });
                },
                
                startAutoplay: function() {
                    if (!this.config.autoplay) return;
                    
                    this.stopAutoplay();
                    autoplayTimer = setInterval(() => {
                        this.next();
                    }, this.config.autoplayDelay);
                },
                
                stopAutoplay: function() {
                    if (autoplayTimer) {
                        clearInterval(autoplayTimer);
                        autoplayTimer = null;
                    }
                },
                
                destroy: function() {
                    this.stopAutoplay();
                    window.LandingPageComponents.Carousel.instances.delete(selector);
                }
            };
            
            // Initialisation des contrôles
            if (config.navigation) {
                const prevBtn = element.querySelector('.carousel-prev');
                const nextBtn = element.querySelector('.carousel-next');
                
                if (prevBtn) prevBtn.addEventListener('click', () => instance.prev());
                if (nextBtn) nextBtn.addEventListener('click', () => instance.next());
            }
            
            if (config.pagination) {
                const dots = element.querySelectorAll('.carousel-dot');
                dots.forEach((dot, index) => {
                    dot.addEventListener('click', () => instance.goTo(index));
                });
            }
            
            // Support tactile
            window.LandingPageComponents.utils.addTouchSupport(element, (direction) => {
                if (direction === 'swipeLeft') instance.next();
                if (direction === 'swipeRight') instance.prev();
            });
            
            // Support clavier
            element.addEventListener('keydown', (e) => {
                if (e.key === 'ArrowLeft') instance.prev();
                if (e.key === 'ArrowRight') instance.next();
            });
            
            // Pause autoplay au survol
            element.addEventListener('mouseenter', () => instance.stopAutoplay());
            element.addEventListener('mouseleave', () => instance.startAutoplay());
            
            // Démarrer l'autoplay si activé
            instance.startAutoplay();
            
            window.LandingPageComponents.Carousel.instances.set(selector, instance);
            return instance;
        },
        
        get: (selector) => {
            return window.LandingPageComponents.Carousel.instances.get(selector);
        }
    },

    // Composant Form Validator
    FormValidator: {
        create: (formSelector, options = {}) => {
            const form = document.querySelector(formSelector);
            if (!form) return null;
            
            const defaults = {
                validateOnSubmit: true,
                validateOnBlur: true,
                showErrors: true,
                errorClass: 'is-invalid',
                successClass: 'is-valid'
            };
            
            const config = { ...defaults, ...options };
            
            const instance = {
                form,
                config,
                errors: {},
                
                validate: function(field = null) {
                    if (field) {
                        return this.validateField(field);
                    } else {
                        return this.validateForm();
                    }
                },
                
                validateField: function(field) {
                    const value = field.value.trim();
                    const rules = field.dataset.validate ? field.dataset.validate.split('|') : [];
                    let isValid = true;
                    let errorMessage = '';
                    
                    for (const rule of rules) {
                        const [ruleName, ruleValue] = rule.split(':');
                        
                        switch (ruleName) {
                            case 'required':
                                if (!value) {
                                    isValid = false;
                                    errorMessage = 'Ce champ est requis';
                                }
                                break;
                            case 'email':
                                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                                if (value && !emailRegex.test(value)) {
                                    isValid = false;
                                    errorMessage = 'Adresse email invalide';
                                }
                                break;
                            case 'min':
                                if (value && value.length < parseInt(ruleValue)) {
                                    isValid = false;
                                    errorMessage = `Minimum ${ruleValue} caractères`;
                                }
                                break;
                            case 'max':
                                if (value && value.length > parseInt(ruleValue)) {
                                    isValid = false;
                                    errorMessage = `Maximum ${ruleValue} caractères`;
                                }
                                break;
                        }
                        
                        if (!isValid) break;
                    }
                    
                    this.updateFieldStatus(field, isValid, errorMessage);
                    return isValid;
                },
                
                validateForm: function() {
                    const fields = this.form.querySelectorAll('[data-validate]');
                    let isFormValid = true;
                    
                    fields.forEach(field => {
                        if (!this.validateField(field)) {
                            isFormValid = false;
                        }
                    });
                    
                    return isFormValid;
                },
                
                updateFieldStatus: function(field, isValid, errorMessage) {
                    field.classList.remove(this.config.errorClass, this.config.successClass);
                    
                    if (isValid) {
                        field.classList.add(this.config.successClass);
                        this.hideError(field);
                    } else {
                        field.classList.add(this.config.errorClass);
                        if (this.config.showErrors) {
                            this.showError(field, errorMessage);
                        }
                    }
                },
                
                showError: function(field, message) {
                    let errorElement = field.parentNode.querySelector('.error-message');
                    if (!errorElement) {
                        errorElement = document.createElement('div');
                        errorElement.className = 'error-message text-danger small mt-1';
                        field.parentNode.appendChild(errorElement);
                    }
                    errorElement.textContent = message;
                },
                
                hideError: function(field) {
                    const errorElement = field.parentNode.querySelector('.error-message');
                    if (errorElement) {
                        errorElement.remove();
                    }
                }
            };
            
            // Événements
            if (config.validateOnSubmit) {
                form.addEventListener('submit', (e) => {
                    if (!instance.validateForm()) {
                        e.preventDefault();
                    }
                });
            }
            
            if (config.validateOnBlur) {
                const fields = form.querySelectorAll('[data-validate]');
                fields.forEach(field => {
                    field.addEventListener('blur', () => instance.validateField(field));
                });
            }
            
            return instance;
        }
    },

    // Composant Notification
    Notification: {
        show: (message, type = 'info', duration = 5000) => {
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.innerHTML = `
                <div class="notification-content">
                    <span class="notification-message">${message}</span>
                    <button class="notification-close">&times;</button>
                </div>
            `;
            
            // Styles inline pour éviter les dépendances CSS
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                min-width: 300px;
                padding: 15px;
                border-radius: 5px;
                color: white;
                font-family: Arial, sans-serif;
                box-shadow: 0 4px 6px rgba(0,0,0,0.1);
                transform: translateX(100%);
                transition: transform 0.3s ease;
            `;
            
            // Couleurs selon le type
            const colors = {
                success: '#28a745',
                error: '#dc3545',
                warning: '#ffc107',
                info: '#17a2b8'
            };
            notification.style.backgroundColor = colors[type] || colors.info;
            
            document.body.appendChild(notification);
            
            // Animation d'entrée
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 10);
            
            // Fermeture automatique
            const autoClose = setTimeout(() => {
                window.LandingPageComponents.Notification.hide(notification);
            }, duration);
            
            // Fermeture manuelle
            const closeBtn = notification.querySelector('.notification-close');
            closeBtn.addEventListener('click', () => {
                clearTimeout(autoClose);
                window.LandingPageComponents.Notification.hide(notification);
            });
            
            return notification;
        },
        
        hide: (notification) => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }
    },

    // Initialisation automatique
    init: () => {
        // Initialiser les accordéons
        window.LandingPageComponents.Accordion.init('.accordion');
        
        // Initialiser les tabs
        window.LandingPageComponents.Tabs.init('.tabs');
        
        // Initialiser les carousels
        document.querySelectorAll('.carousel').forEach(carousel => {
            const options = {
                autoplay: carousel.dataset.autoplay === 'true',
                autoplayDelay: parseInt(carousel.dataset.autoplayDelay) || 3000,
                loop: carousel.dataset.loop !== 'false'
            };
            window.LandingPageComponents.Carousel.create(`#${carousel.id}`, options);
        });
        
        // Initialiser les formulaires avec validation
        document.querySelectorAll('form[data-validate]').forEach(form => {
            window.LandingPageComponents.FormValidator.create(`#${form.id}`);
        });
    }
};

// Initialisation au chargement du DOM
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', window.LandingPageComponents.init);
} else {
    window.LandingPageComponents.init();
}

// Export pour utilisation en module
if (typeof module !== 'undefined' && module.exports) {
    module.exports = window.LandingPageComponents;
}

