<?php
require_once 'php/config/database.php';

try {
    echo "🔍 Diagnostic et correction de l'API subscriptions...\n\n";
    
    // 1. Vérifier l'existence des tables
    echo "📊 VÉRIFICATION DES TABLES:\n";
    
    $tables = ['subscriptions', 'subscription_plans'];
    $missingTables = [];
    
    foreach ($tables as $table) {
        $query = "SHOW TABLES LIKE '{$table}'";
        $result = $pdo->query($query);
        
        if ($result->rowCount() > 0) {
            echo "  ✅ Table '{$table}' existe\n";
        } else {
            echo "  ❌ Table '{$table}' manquante\n";
            $missingTables[] = $table;
        }
    }
    
    // 2. Créer les tables manquantes
    if (!empty($missingTables)) {
        echo "\n🔧 CRÉATION DES TABLES MANQUANTES:\n";
        
        if (in_array('subscription_plans', $missingTables)) {
            echo "  📝 Création de la table subscription_plans...\n";
            $createPlansTable = "
                CREATE TABLE IF NOT EXISTS subscription_plans (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(255) NOT NULL,
                    name_ar VARCHAR(255),
                    name_fr VARCHAR(255),
                    name_en VARCHAR(255),
                    description TEXT,
                    description_ar TEXT,
                    description_fr TEXT,
                    description_en TEXT,
                    price DECIMAL(10,2) NOT NULL,
                    currency VARCHAR(3) DEFAULT 'DZD',
                    billing_cycle ENUM('monthly', 'yearly') DEFAULT 'monthly',
                    features JSON,
                    max_products INT DEFAULT 10,
                    max_categories INT DEFAULT 5,
                    max_landing_pages INT DEFAULT 3,
                    max_storage_mb INT DEFAULT 1000,
                    status ENUM('active', 'inactive') DEFAULT 'active',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ";
            
            if ($pdo->exec($createPlansTable) !== false) {
                echo "    ✅ Table subscription_plans créée\n";
                
                // Insérer des plans de démonstration
                $insertPlans = "
                    INSERT INTO subscription_plans (name, name_ar, name_fr, name_en, description, price, max_products, max_categories, max_landing_pages, features) VALUES
                    ('Basique', 'أساسي', 'Basique', 'Basic', 'Plan de base pour débuter', 2000.00, 10, 5, 3, '[\"Support email\", \"Tableau de bord\", \"Analytics de base\"]'),
                    ('Pro', 'احترافي', 'Pro', 'Pro', 'Plan professionnel avec plus de fonctionnalités', 5000.00, 50, 20, 10, '[\"Support prioritaire\", \"Analytics avancées\", \"Intégrations\", \"API access\"]'),
                    ('Enterprise', 'مؤسسة', 'Enterprise', 'Enterprise', 'Plan entreprise avec fonctionnalités complètes', 10000.00, -1, -1, -1, '[\"Support 24/7\", \"Manager dédié\", \"Personnalisation\", \"SLA garanti\"]')
                ";
                
                if ($pdo->exec($insertPlans) !== false) {
                    echo "    ✅ Plans de démonstration insérés\n";
                }
            } else {
                echo "    ❌ Erreur lors de la création de subscription_plans\n";
            }
        }
        
        if (in_array('subscriptions', $missingTables)) {
            echo "  📝 Création de la table subscriptions...\n";
            $createSubscriptionsTable = "
                CREATE TABLE IF NOT EXISTS subscriptions (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    plan_id INT NOT NULL,
                    status ENUM('active', 'inactive', 'cancelled', 'expired') DEFAULT 'active',
                    start_date DATE NOT NULL,
                    end_date DATE,
                    next_billing_date DATE,
                    amount DECIMAL(10,2) NOT NULL,
                    currency VARCHAR(3) DEFAULT 'DZD',
                    payment_method VARCHAR(50),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (plan_id) REFERENCES subscription_plans(id) ON DELETE CASCADE,
                    INDEX idx_user_id (user_id),
                    INDEX idx_status (status)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ";
            
            if ($pdo->exec($createSubscriptionsTable) !== false) {
                echo "    ✅ Table subscriptions créée\n";
                
                // Insérer quelques abonnements de démonstration
                $insertSubscriptions = "
                    INSERT INTO subscriptions (user_id, plan_id, status, start_date, end_date, next_billing_date, amount) VALUES
                    (4, 2, 'active', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 1 MONTH), DATE_ADD(CURDATE(), INTERVAL 1 MONTH), 5000.00),
                    (1, 1, 'active', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 1 MONTH), DATE_ADD(CURDATE(), INTERVAL 1 MONTH), 2000.00)
                ";
                
                if ($pdo->exec($insertSubscriptions) !== false) {
                    echo "    ✅ Abonnements de démonstration insérés\n";
                }
            } else {
                echo "    ❌ Erreur lors de la création de subscriptions\n";
            }
        }
    }
    
    // 3. Test de l'API stats
    echo "\n🔗 TEST DE L'API STATS:\n";
    
    try {
        // Test des requêtes de l'API stats
        $stats = [];
        
        // Total active subscriptions
        $query = "SELECT COUNT(*) as total FROM subscriptions WHERE status = 'active'";
        $stmt = $pdo->prepare($query);
        $stmt->execute();
        $stats['total_active'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        echo "  ✅ Total abonnements actifs: {$stats['total_active']}\n";
        
        // Monthly revenue
        $query = "
            SELECT COALESCE(SUM(p.price), 0) as monthly_revenue
            FROM subscriptions s
            JOIN subscription_plans p ON s.plan_id = p.id
            WHERE s.status = 'active'
        ";
        $stmt = $pdo->prepare($query);
        $stmt->execute();
        $stats['monthly_revenue'] = $stmt->fetch(PDO::FETCH_ASSOC)['monthly_revenue'];
        echo "  ✅ Revenus mensuels: {$stats['monthly_revenue']} DA\n";
        
        // New subscriptions this month
        $query = "
            SELECT COUNT(*) as new_this_month
            FROM subscriptions
            WHERE DATE_FORMAT(created_at, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-%m')
        ";
        $stmt = $pdo->prepare($query);
        $stmt->execute();
        $stats['new_this_month'] = $stmt->fetch(PDO::FETCH_ASSOC)['new_this_month'];
        echo "  ✅ Nouveaux abonnements ce mois: {$stats['new_this_month']}\n";
        
        echo "\n📊 STATISTIQUES COMPLÈTES:\n";
        echo json_encode($stats, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
        
    } catch (Exception $e) {
        echo "  ❌ Erreur lors du test: " . $e->getMessage() . "\n";
    }
    
    echo "\n✅ Correction de l'API subscriptions terminée!\n";
    
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
    echo "📍 Fichier: " . $e->getFile() . " ligne " . $e->getLine() . "\n";
}
?>
