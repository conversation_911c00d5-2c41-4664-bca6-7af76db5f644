<?php
// Test direct de l'API products-simple.php
echo "=== Debug API Products ===\n";

// Simuler les paramètres GET
$_GET['store_id'] = '3';
$_SERVER['REQUEST_METHOD'] = 'GET';

// Capturer la sortie
ob_start();

try {
    include 'api/products-simple.php';
    $output = ob_get_contents();
    ob_end_clean();
    
    echo "Sortie de l'API:\n";
    echo $output . "\n";
    
    // Tester si c'est du JSON valide
    $data = json_decode($output, true);
    if ($data) {
        echo "✅ JSON valide\n";
        echo "Success: " . ($data['success'] ? 'true' : 'false') . "\n";
    } else {
        echo "❌ JSON invalide\n";
        echo "Erreur JSON: " . json_last_error_msg() . "\n";
    }
    
} catch (Exception $e) {
    ob_end_clean();
    echo "❌ Exception: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}

echo "\n" . str_repeat("-", 50) . "\n";

// Test de connexion DB
echo "=== Test connexion DB ===\n";

try {
    $pdo = new PDO(
        "mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4",
        'root',
        '',
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    
    echo "✅ Connexion DB réussie\n";
    
    // Test requête simple
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM products");
    $result = $stmt->fetch();
    echo "✅ Nombre total de produits: " . $result['count'] . "\n";
    
    // Test avec store_id = 3
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM products WHERE store_id = ?");
    $stmt->execute([3]);
    $result = $stmt->fetch();
    echo "✅ Produits pour store_id 3: " . $result['count'] . "\n";
    
} catch (Exception $e) {
    echo "❌ Erreur DB: " . $e->getMessage() . "\n";
}
?>
