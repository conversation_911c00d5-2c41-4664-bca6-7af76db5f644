<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{meta_title}}</title>
    <meta name="description" content="{{meta_description}}">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        :root {
            --primary-color: #ff6b6b;
            --secondary-color: #4ecdc4;
            --accent-color: #45b7d1;
            --dark-color: #2c3e50;
            --light-color: #ecf0f1;
        }

        body {
            font-family: 'Poppins', sans-serif;
            line-height: 1.6;
        }

        .hero-section {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="white" opacity="0.1"/><circle cx="80" cy="40" r="1" fill="white" opacity="0.1"/><circle cx="40" cy="80" r="1.5" fill="white" opacity="0.1"/></svg>');
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .profile-image {
            width: 300px;
            height: 300px;
            border-radius: 50%;
            object-fit: cover;
            border: 8px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }

        .portfolio-item {
            position: relative;
            overflow: hidden;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .portfolio-item:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }

        .portfolio-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            opacity: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .portfolio-item:hover .portfolio-overlay {
            opacity: 0.9;
        }

        .skill-bar {
            background: var(--light-color);
            height: 10px;
            border-radius: 5px;
            overflow: hidden;
            margin-bottom: 1rem;
        }

        .skill-progress {
            height: 100%;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border-radius: 5px;
            transition: width 2s ease;
        }

        .service-card {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            text-align: center;
            border: 2px solid transparent;
        }

        .service-card:hover {
            transform: translateY(-10px);
            border-color: var(--primary-color);
        }

        .service-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            margin: 0 auto 1.5rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(255, 107, 107, 0.4);
        }

        .contact-section {
            background: var(--dark-color);
            color: white;
            padding: 80px 0;
        }

        .contact-form {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 2rem;
            backdrop-filter: blur(10px);
        }

        .form-control {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            color: white;
            padding: 12px 15px;
        }

        .form-control:focus {
            background: rgba(255, 255, 255, 0.15);
            border-color: var(--primary-color);
            color: white;
            box-shadow: 0 0 0 0.2rem rgba(255, 107, 107, 0.25);
        }

        .form-control::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }

        @media (max-width: 768px) {
            .hero-section {
                text-align: center;
                padding: 60px 0;
            }
            
            .profile-image {
                width: 200px;
                height: 200px;
                margin-bottom: 2rem;
            }
        }
    </style>
</head>
<body>
    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6 hero-content">
                    <h1 class="display-4 fw-bold mb-4">{{hero_title}}</h1>
                    <p class="lead mb-4">{{hero_subtitle}}</p>
                    <div class="d-flex gap-3 flex-wrap">
                        <a href="#portfolio" class="btn btn-light btn-lg">
                            <i class="fas fa-eye me-2"></i>View My Work
                        </a>
                        <a href="#contact" class="btn btn-outline-light btn-lg">
                            <i class="fas fa-envelope me-2"></i>Get In Touch
                        </a>
                    </div>
                </div>
                <div class="col-lg-6 text-center">
                    <img src="{{profile_image}}" alt="Profile" class="profile-image">
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section class="py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-6">
                    <h2 class="display-5 fw-bold mb-4">{{about_title}}</h2>
                    <p class="lead">{{about_text}}</p>
                    <div class="row mt-4">
                        <div class="col-6">
                            <h4 class="text-primary">{{projects_count}}</h4>
                            <p class="text-muted">Projects Completed</p>
                        </div>
                        <div class="col-6">
                            <h4 class="text-primary">{{experience_years}}</h4>
                            <p class="text-muted">Years Experience</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <h3 class="mb-4">Skills</h3>
                    <div class="skill-item mb-3">
                        <div class="d-flex justify-content-between mb-2">
                            <span>{{skill1_name}}</span>
                            <span>{{skill1_percentage}}%</span>
                        </div>
                        <div class="skill-bar">
                            <div class="skill-progress" style="width: {{skill1_percentage}}%"></div>
                        </div>
                    </div>
                    <div class="skill-item mb-3">
                        <div class="d-flex justify-content-between mb-2">
                            <span>{{skill2_name}}</span>
                            <span>{{skill2_percentage}}%</span>
                        </div>
                        <div class="skill-bar">
                            <div class="skill-progress" style="width: {{skill2_percentage}}%"></div>
                        </div>
                    </div>
                    <div class="skill-item mb-3">
                        <div class="d-flex justify-content-between mb-2">
                            <span>{{skill3_name}}</span>
                            <span>{{skill3_percentage}}%</span>
                        </div>
                        <div class="skill-bar">
                            <div class="skill-progress" style="width: {{skill3_percentage}}%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section class="py-5 bg-light">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="display-5 fw-bold">{{services_title}}</h2>
                <p class="lead text-muted">{{services_subtitle}}</p>
            </div>
            
            <div class="row">
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="service-card">
                        <div class="service-icon">
                            <i class="fas fa-paint-brush"></i>
                        </div>
                        <h4>{{service1_title}}</h4>
                        <p class="text-muted">{{service1_description}}</p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="service-card">
                        <div class="service-icon">
                            <i class="fas fa-code"></i>
                        </div>
                        <h4>{{service2_title}}</h4>
                        <p class="text-muted">{{service2_description}}</p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="service-card">
                        <div class="service-icon">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <h4>{{service3_title}}</h4>
                        <p class="text-muted">{{service3_description}}</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Portfolio Section -->
    <section id="portfolio" class="py-5">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="display-5 fw-bold">{{portfolio_title}}</h2>
                <p class="lead text-muted">{{portfolio_subtitle}}</p>
            </div>
            
            <div class="row">
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="portfolio-item">
                        <img src="{{project1_image}}" alt="Project 1" class="img-fluid">
                        <div class="portfolio-overlay">
                            <div class="text-center">
                                <h4 class="text-white">{{project1_title}}</h4>
                                <p class="text-white">{{project1_category}}</p>
                                <a href="#" class="btn btn-light">View Project</a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="portfolio-item">
                        <img src="{{project2_image}}" alt="Project 2" class="img-fluid">
                        <div class="portfolio-overlay">
                            <div class="text-center">
                                <h4 class="text-white">{{project2_title}}</h4>
                                <p class="text-white">{{project2_category}}</p>
                                <a href="#" class="btn btn-light">View Project</a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="portfolio-item">
                        <img src="{{project3_image}}" alt="Project 3" class="img-fluid">
                        <div class="portfolio-overlay">
                            <div class="text-center">
                                <h4 class="text-white">{{project3_title}}</h4>
                                <p class="text-white">{{project3_category}}</p>
                                <a href="#" class="btn btn-light">View Project</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="contact-section">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="display-5 fw-bold">{{contact_title}}</h2>
                <p class="lead">{{contact_subtitle}}</p>
            </div>
            
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="contact-form">
                        <form>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <input type="text" class="form-control" placeholder="Your Name" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <input type="email" class="form-control" placeholder="Your Email" required>
                                </div>
                            </div>
                            <div class="mb-3">
                                <input type="text" class="form-control" placeholder="Subject" required>
                            </div>
                            <div class="mb-3">
                                <textarea class="form-control" rows="5" placeholder="Your Message" required></textarea>
                            </div>
                            <div class="text-center">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-paper-plane me-2"></i>Send Message
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="py-4 bg-dark text-white text-center">
        <div class="container">
            <p>&copy; 2024 {{name}}. All rights reserved.</p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
