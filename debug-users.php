<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Debug Users API</h1>";

try {
    // Simulate the API call
    $_SERVER['REQUEST_METHOD'] = 'GET';
    $_SERVER['REQUEST_URI'] = '/api/users.php?action=all';
    $_SERVER['HTTP_AUTHORIZATION'] = 'Bearer demo_token';
    $_GET['action'] = 'all';
    
    // Include the API file
    include 'api/users.php';
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
    echo "<br>Trace: " . $e->getTraceAsString();
}
?>
