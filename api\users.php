<?php

/**
 * Users API Endpoint
 * Handles user management, roles, and permissions
 */

require_once 'config/database.php';

// Set headers
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    $database = new Database();
    $db = $database->getConnection();
    $auth = new Auth($db);

    // Get request method and path
    $method = $_SERVER['REQUEST_METHOD'];
    $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
    $pathParts = explode('/', trim($path, '/'));

    // Extract action from path or query parameters
    $action = isset($pathParts[2]) ? $pathParts[2] : (isset($_GET['action']) ? $_GET['action'] : '');
    $id = isset($pathParts[3]) ? $pathParts[3] : (isset($_GET['id']) ? $_GET['id'] : null);

    // Authenticate user
    $headers = function_exists('getallheaders') ? getallheaders() : [];

    // Fallback for CLI and some servers
    if (empty($headers) && isset($_SERVER['HTTP_AUTHORIZATION'])) {
        $headers['Authorization'] = $_SERVER['HTTP_AUTHORIZATION'];
    }

    $token = isset($headers['Authorization']) ? str_replace('Bearer ', '', $headers['Authorization']) : null;
    $user_id = $auth->verifyFirebaseToken($token);
    $store = $auth->getUserStore($user_id);

    switch ($method) {
        case 'GET':
            if ($action === 'profile' || $action === '') {
                getUserProfile($db, $auth, $user_id);
            } elseif ($action === 'roles') {
                getUserRoles($db, $user_id);
            } elseif ($action === 'all' && $store['role'] === 'admin') {
                getAllUsers($db, $_GET);
            } elseif ($action === 'get' && $id && $store['role'] === 'admin') {
                getUserById($db, $id);
            } elseif ($action === 'permissions' && $id) {
                getUserPermissions($db, $auth, $id);
            } else {
                ApiResponse::error('Invalid endpoint', 404);
            }
            break;

        case 'POST':
            $input = json_decode(file_get_contents('php://input'), true);
            if ($action === 'roles') {
                assignUserRole($db, $auth, $user_id, $input);
            } elseif ($action === 'register') {
                registerUser($db, $input);
            } else {
                ApiResponse::error('Invalid endpoint', 404);
            }
            break;

        case 'PUT':
            $input = json_decode(file_get_contents('php://input'), true);
            if ($action === 'profile' || $action === '') {
                updateUserProfile($db, $user_id, $input);
            } elseif ($action === 'roles' && $id) {
                updateUserRole($db, $auth, $user_id, $id, $input);
            } elseif ($action === 'status' && $id && $store['role'] === 'admin') {
                updateUserStatus($db, $id, $input);
            } else {
                ApiResponse::error('Invalid endpoint', 404);
            }
            break;

        case 'DELETE':
            if ($action === 'roles' && $id) {
                removeUserRole($db, $auth, $user_id, $id);
            } elseif ($id && $store['role'] === 'admin') {
                deleteUser($db, $id);
            } else {
                ApiResponse::error('Invalid endpoint', 404);
            }
            break;

        default:
            ApiResponse::error('Method not allowed', 405);
    }
} catch (Exception $e) {
    error_log('Users API Error: ' . $e->getMessage());
    ApiResponse::error('Internal server error', 500);
}

/**
 * Get user profile information
 */
function getUserProfile($db, $auth, $user_id)
{
    try {
        // Get user basic info (from Firebase or local storage)
        $profile = [
            'uid' => $user_id,
            'email' => '', // This would come from Firebase
            'display_name' => '',
            'photo_url' => '',
            'phone_number' => '',
            'created_at' => '',
            'last_login' => ''
        ];

        // Get user roles and permissions
        $roles_query = "
            SELECT
                ur.role,
                ur.store_id,
                ur.is_active,
                ur.created_at as role_assigned_at,
                s.store_name,
                s.status as store_status
            FROM user_roles ur
            LEFT JOIN stores s ON ur.store_id = s.id
            WHERE ur.user_id = ? AND ur.is_active = 1
            ORDER BY ur.created_at DESC
        ";

        $roles_stmt = $db->prepare($roles_query);
        $roles_stmt->execute([$user_id]);
        $roles = $roles_stmt->fetchAll(PDO::FETCH_ASSOC);

        $profile['roles'] = $roles;

        // Get user statistics
        $stats = [];

        // If user is a seller, get store statistics
        $seller_role = array_filter($roles, function ($role) {
            return $role['role'] === 'seller';
        });

        if (!empty($seller_role)) {
            $store_id = reset($seller_role)['store_id'];

            $stats_query = "
                SELECT
                    COUNT(DISTINCT p.id) as total_products,
                    COUNT(DISTINCT o.id) as total_orders,
                    COALESCE(SUM(o.total_amount), 0) as total_revenue,
                    COUNT(DISTINCT o.customer_email) as total_customers,
                    COUNT(DISTINCT au.id) as ai_generations
                FROM stores s
                LEFT JOIN products p ON s.id = p.store_id
                LEFT JOIN orders o ON s.id = o.store_id
                LEFT JOIN ai_usage au ON s.id = au.store_id
                WHERE s.id = ?
            ";

            $stats_stmt = $db->prepare($stats_query);
            $stats_stmt->execute([$store_id]);
            $stats = $stats_stmt->fetch(PDO::FETCH_ASSOC);
        }

        $profile['statistics'] = $stats;

        // Get recent activity
        $activity_query = "
            SELECT
                'order' as type,
                o.id as reference_id,
                o.status,
                o.total_amount,
                o.created_at,
                'Nouvelle commande reçue' as description
            FROM orders o
            JOIN stores s ON o.store_id = s.id
            JOIN user_roles ur ON s.id = ur.store_id
            WHERE ur.user_id = ? AND ur.role = 'seller'

            UNION ALL

            SELECT
                'ai_usage' as type,
                au.id as reference_id,
                'completed' as status,
                NULL as total_amount,
                au.created_at,
                CONCAT('Génération IA: ', au.content_type) as description
            FROM ai_usage au
            JOIN stores s ON au.store_id = s.id
            JOIN user_roles ur ON s.id = ur.store_id
            WHERE ur.user_id = ? AND ur.role = 'seller'

            ORDER BY created_at DESC
            LIMIT 10
        ";

        $activity_stmt = $db->prepare($activity_query);
        $activity_stmt->execute([$user_id, $user_id]);
        $activity = $activity_stmt->fetchAll(PDO::FETCH_ASSOC);

        $profile['recent_activity'] = $activity;

        ApiResponse::success($profile);
    } catch (Exception $e) {
        error_log('Get user profile error: ' . $e->getMessage());
        ApiResponse::error('Failed to retrieve user profile');
    }
}

/**
 * Get user roles
 */
function getUserRoles($db, $user_id)
{
    try {
        $query = "SELECT 
            ur.role,
            ur.permissions,
            ur.store_id,
            ur.is_active,
            ur.assigned_by,
            ur.assigned_at,
            ur.expires_at,
            s.store_name
        FROM user_roles ur
        LEFT JOIN stores s ON ur.store_id = s.id
        WHERE ur.user_id = ?";

        $stmt = $db->prepare($query);
        $stmt->execute([$user_id]);
        $roles = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Transform JSON permissions to array
        foreach ($roles as &$role) {
            $role['permissions'] = json_decode($role['permissions'], true) ?? [];
        }

        ApiResponse::success(['roles' => $roles]);
    } catch (Exception $e) {
        error_log('Error getting user roles: ' . $e->getMessage());
        ApiResponse::error('Erreur lors de la récupération des rôles', 500);
    }
}

/**
 * Get all users (admin only)
 */
function getAllUsers($db, $params)
{
    try {
        $page = isset($params['page']) ? max(1, intval($params['page'])) : 1;
        $limit = isset($params['limit']) ? min(100, max(1, intval($params['limit']))) : 20;
        $offset = ($page - 1) * $limit;

        $where_conditions = [];
        $where_params = [];

        // Add filters
        if (!empty($params['role'])) {
            $where_conditions[] = 'ur.role = ?';
            $where_params[] = $params['role'];
        }

        if (!empty($params['status'])) {
            $where_conditions[] = 'ur.is_active = ?';
            $where_params[] = $params['status'] === 'active' ? 1 : 0;
        }

        if (!empty($params['store_id'])) {
            $where_conditions[] = 'ur.store_id = ?';
            $where_params[] = $params['store_id'];
        }

        $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

        // Get total count
        $count_query = "
            SELECT COUNT(DISTINCT ur.user_id) as total
            FROM user_roles ur
            LEFT JOIN stores s ON ur.store_id = s.id
            $where_clause
        ";
        $count_stmt = $db->prepare($count_query);
        $count_stmt->execute($where_params);
        $total = $count_stmt->fetch(PDO::FETCH_ASSOC)['total'];

        // Get unique users first
        $user_query = "
            SELECT DISTINCT ur.user_id
            FROM user_roles ur
            LEFT JOIN stores s ON ur.store_id = s.id
            $where_clause
            ORDER BY ur.created_at DESC
            LIMIT ? OFFSET ?
        ";
        
        $user_stmt = $db->prepare($user_query);
        $user_stmt->execute(array_merge($where_params, [$limit, $offset]));
        $user_ids = $user_stmt->fetchAll(PDO::FETCH_COLUMN);
        
        $users = [];
        
        // For each user, get their complete profile
        foreach ($user_ids as $user_id) {
            // Get user roles
            $roles_query = "
                SELECT
                    ur.role,
                    ur.store_id,
                    ur.is_active,
                    ur.created_at as role_assigned_at,
                    s.store_name,
                    s.status as store_status
                FROM user_roles ur
                LEFT JOIN stores s ON ur.store_id = s.id
                WHERE ur.user_id = ?
                ORDER BY ur.created_at DESC
            ";
            
            $roles_stmt = $db->prepare($roles_query);
            $roles_stmt->execute([$user_id]);
            $roles = $roles_stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Get user statistics
             $stats_query = "
                 SELECT
                     COUNT(DISTINCT p.id) as total_products,
                     COUNT(DISTINCT o.id) as total_orders,
                     COALESCE(SUM(o.total_amount), 0) as total_revenue,
                     0 as total_customers,
                     0 as ai_generations
                 FROM user_roles ur
                 LEFT JOIN stores s ON ur.store_id = s.id
                 LEFT JOIN products p ON s.id = p.store_id
                 LEFT JOIN orders o ON s.id = o.store_id
                 WHERE ur.user_id = ?
             ";
            
            $stats_stmt = $db->prepare($stats_query);
            $stats_stmt->execute([$user_id]);
            $stats = $stats_stmt->fetch(PDO::FETCH_ASSOC);
            
            $users[] = [
                'uid' => $user_id,
                'email' => '',
                'display_name' => '',
                'photo_url' => '',
                'phone_number' => '',
                'created_at' => '',
                'last_login' => '',
                'roles' => $roles,
                'statistics' => $stats,
                'recent_activity' => []
            ];
        }

        // Users array is already built above

        ApiResponse::success([
            'users' => $users,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => $total,
                'pages' => ceil($total / $limit)
            ]
        ]);
    } catch (Exception $e) {
        error_log('Get all users error: ' . $e->getMessage());
        ApiResponse::error('Failed to retrieve users');
    }
}

/**
 * Get user permissions
 */
function getUserPermissions($db, $auth, $target_user_id)
{
    try {
        $permissions = [];

        // Get user roles
        $roles_query = "SELECT role, store_id, is_active FROM user_roles WHERE user_id = ?";
        $roles_stmt = $db->prepare($roles_query);
        $roles_stmt->execute([$target_user_id]);
        $roles = $roles_stmt->fetchAll(PDO::FETCH_ASSOC);

        foreach ($roles as $role) {
            if (!$role['is_active']) continue;

            switch ($role['role']) {
                case 'admin':
                    $permissions = array_merge($permissions, [
                        'manage_all_stores',
                        'manage_all_users',
                        'view_all_analytics',
                        'manage_system_settings'
                    ]);
                    break;

                case 'seller':
                    $permissions = array_merge($permissions, [
                        'manage_own_store',
                        'manage_own_products',
                        'manage_own_orders',
                        'view_own_analytics',
                        'manage_own_settings'
                    ]);
                    break;

                case 'agent':
                    $permissions = array_merge($permissions, [
                        'view_assigned_stores',
                        'manage_assigned_orders',
                        'view_assigned_analytics'
                    ]);
                    break;
            }
        }

        ApiResponse::success([
            'user_id' => $target_user_id,
            'roles' => $roles,
            'permissions' => array_unique($permissions)
        ]);
    } catch (Exception $e) {
        error_log('Get user permissions error: ' . $e->getMessage());
        ApiResponse::error('Failed to retrieve user permissions');
    }
}

/**
 * Assign role to user
 */
function assignUserRole($db, $auth, $current_user_id, $data)
{
    try {
        // Validate required fields
        $required = ['user_id', 'role'];
        foreach ($required as $field) {
            if (empty($data[$field])) {
                ApiResponse::validation("Field '$field' is required");
            }
        }

        // Check permissions
        if (!$auth->hasPermission($current_user_id, 'admin')) {
            ApiResponse::error('Admin permission required', 403);
        }

        // Validate role
        $valid_roles = ['admin', 'seller', 'agent'];
        if (!in_array($data['role'], $valid_roles)) {
            ApiResponse::validation('Invalid role');
        }

        // For seller role, store_id is required
        if ($data['role'] === 'seller' && empty($data['store_id'])) {
            ApiResponse::validation('Store ID is required for seller role');
        }

        // Check if role already exists
        $existing_query = "SELECT id FROM user_roles WHERE user_id = ? AND role = ? AND store_id = ?";
        $existing_stmt = $db->prepare($existing_query);
        $existing_stmt->execute([
            $data['user_id'],
            $data['role'],
            isset($data['store_id']) ? $data['store_id'] : null
        ]);

        if ($existing_stmt->fetch()) {
            ApiResponse::error('User already has this role', 400);
        }

        // Create role
        $query = "INSERT INTO user_roles (user_id, role, store_id, is_active) VALUES (?, ?, ?, 1)";
        $stmt = $db->prepare($query);
        $stmt->execute([
            $data['user_id'],
            $data['role'],
            isset($data['store_id']) ? $data['store_id'] : null
        ]);

        $role_id = $db->lastInsertId();

        // Get created role
        $get_query = "
            SELECT
                ur.*,
                s.store_name
            FROM user_roles ur
            LEFT JOIN stores s ON ur.store_id = s.id
            WHERE ur.id = ?
        ";
        $get_stmt = $db->prepare($get_query);
        $get_stmt->execute([$role_id]);
        $role = $get_stmt->fetch(PDO::FETCH_ASSOC);

        ApiResponse::success($role, 'Role assigned successfully', 201);
    } catch (Exception $e) {
        error_log('Assign user role error: ' . $e->getMessage());
        ApiResponse::error('Failed to assign role');
    }
}

/**
 * Update user profile
 */
function updateUserProfile($db, $user_id, $data)
{
    try {
        // This would typically update Firebase user data
        // For now, we'll just return success as profile data is managed by Firebase

        ApiResponse::success(null, 'Profile updated successfully');
    } catch (Exception $e) {
        error_log('Update user profile error: ' . $e->getMessage());
        ApiResponse::error('Failed to update profile');
    }
}

/**
 * Update user role
 */
function updateUserRole($db, $auth, $current_user_id, $role_id, $data)
{
    try {
        // Check permissions
        if (!$auth->hasPermission($current_user_id, 'admin')) {
            ApiResponse::error('Admin permission required', 403);
        }

        // Check if role exists
        $check_query = "SELECT * FROM user_roles WHERE id = ?";
        $check_stmt = $db->prepare($check_query);
        $check_stmt->execute([$role_id]);
        $role = $check_stmt->fetch(PDO::FETCH_ASSOC);

        if (!$role) {
            ApiResponse::error('Role not found', 404);
        }

        $update_fields = [];
        $update_params = [];

        if (isset($data['is_active'])) {
            $update_fields[] = 'is_active = ?';
            $update_params[] = $data['is_active'] ? 1 : 0;
        }

        if (isset($data['store_id'])) {
            $update_fields[] = 'store_id = ?';
            $update_params[] = $data['store_id'];
        }

        if (empty($update_fields)) {
            ApiResponse::validation('No fields to update');
        }

        $update_params[] = $role_id;

        $query = "UPDATE user_roles SET " . implode(', ', $update_fields) . " WHERE id = ?";
        $stmt = $db->prepare($query);
        $stmt->execute($update_params);

        // Get updated role
        $get_query = "
            SELECT
                ur.*,
                s.store_name
            FROM user_roles ur
            LEFT JOIN stores s ON ur.store_id = s.id
            WHERE ur.id = ?
        ";
        $get_stmt = $db->prepare($get_query);
        $get_stmt->execute([$role_id]);
        $updated_role = $get_stmt->fetch(PDO::FETCH_ASSOC);

        ApiResponse::success($updated_role, 'Role updated successfully');
    } catch (Exception $e) {
        error_log('Update user role error: ' . $e->getMessage());
        ApiResponse::error('Failed to update role');
    }
}

/**
 * Update user status (admin only)
 */
function updateUserStatus($db, $user_id, $data)
{
    try {
        // This would typically update Firebase user status
        // For now, we'll update the is_active status in user_roles

        if (!isset($data['is_active'])) {
            ApiResponse::validation('is_active field is required');
        }

        $query = "UPDATE user_roles SET is_active = ? WHERE user_id = ?";
        $stmt = $db->prepare($query);
        $stmt->execute([$data['is_active'] ? 1 : 0, $user_id]);

        ApiResponse::success(null, 'User status updated successfully');
    } catch (Exception $e) {
        error_log('Update user status error: ' . $e->getMessage());
        ApiResponse::error('Failed to update user status');
    }
}

/**
 * Remove user role
 */
function removeUserRole($db, $auth, $current_user_id, $role_id)
{
    try {
        // Check permissions
        if (!$auth->hasPermission($current_user_id, 'admin')) {
            ApiResponse::error('Admin permission required', 403);
        }

        // Check if role exists
        $check_query = "SELECT * FROM user_roles WHERE id = ?";
        $check_stmt = $db->prepare($check_query);
        $check_stmt->execute([$role_id]);
        $role = $check_stmt->fetch(PDO::FETCH_ASSOC);

        if (!$role) {
            ApiResponse::error('Role not found', 404);
        }

        // Delete role
        $query = "DELETE FROM user_roles WHERE id = ?";
        $stmt = $db->prepare($query);
        $stmt->execute([$role_id]);

        ApiResponse::success(null, 'Role removed successfully');
    } catch (Exception $e) {
        error_log('Remove user role error: ' . $e->getMessage());
        ApiResponse::error('Failed to remove role');
    }
}

/**
 * Get user by ID (admin only)
 */
function getUserById($db, $user_id)
{
    try {
        $query = "
            SELECT 
                u.uid,
                u.email,
                u.display_name,
                u.role,
                u.status,
                u.created_at,
                u.last_login,
                s.name as subscription_name,
                s.max_products,
                s.max_landing_pages,
                s.max_categories
            FROM users u
            LEFT JOIN subscriptions s ON u.subscription_id = s.id
            WHERE u.uid = ?
        ";
        
        $stmt = $db->prepare($query);
        $stmt->execute([$user_id]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$user) {
            ApiResponse::error('User not found', 404);
            return;
        }
        
        // Format the response
        $userData = [
            'uid' => $user['uid'],
            'email' => $user['email'],
            'display_name' => $user['display_name'],
            'role' => $user['role'],
            'status' => $user['status'],
            'created_at' => $user['created_at'],
            'last_login' => $user['last_login'],
            'subscription' => [
                'name' => $user['subscription_name'],
                'max_products' => $user['max_products'],
                'max_landing_pages' => $user['max_landing_pages'],
                'max_categories' => $user['max_categories']
            ]
        ];
        
        ApiResponse::success($userData, 'User retrieved successfully');
    } catch (Exception $e) {
        error_log('Get user by ID error: ' . $e->getMessage());
        ApiResponse::error('Failed to retrieve user');
    }
}

/**
 * Delete user (admin only)
 */
function deleteUser($db, $user_id)
{
    try {
        // This would typically delete the user from Firebase
        // For now, we'll just deactivate all their roles

        $query = "UPDATE user_roles SET is_active = 0 WHERE user_id = ?";
        $stmt = $db->prepare($query);
        $stmt->execute([$user_id]);

        ApiResponse::success(null, 'User deactivated successfully');
    } catch (Exception $e) {
        error_log('Delete user error: ' . $e->getMessage());
        ApiResponse::error('Failed to delete user');
    }
}

/**
 * Register new user
 */
function registerUser($db, $data)
{
    try {
        // This would typically create a user in Firebase
        // For now, we'll just return success

        ApiResponse::success(null, 'User registration initiated', 201);
    } catch (Exception $e) {
        error_log('Register user error: ' . $e->getMessage());
        ApiResponse::error('Failed to register user');
    }
}
