<?php

/**
 * Users API Endpoint
 * Handles user management, roles, and permissions
 */

require_once 'config/database.php';

// Set headers
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    $database = new Database();
    $db = $database->getConnection();

    // Get request method and path
    $method = $_SERVER['REQUEST_METHOD'];
    $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
    $pathParts = explode('/', trim($path, '/'));

    // Extract action from path or query parameters
    $action = isset($pathParts[2]) ? $pathParts[2] : (isset($_GET['action']) ? $_GET['action'] : '');
    $id = isset($pathParts[3]) ? $pathParts[3] : (isset($_GET['id']) ? $_GET['id'] : null);

    // Simple authentication check
    $headers = function_exists('getallheaders') ? getallheaders() : [];
    if (empty($headers) && isset($_SERVER['HTTP_AUTHORIZATION'])) {
        $headers['Authorization'] = $_SERVER['HTTP_AUTHORIZATION'];
    }
    $token = isset($headers['Authorization']) ? str_replace('Bearer ', '', $headers['Authorization']) : null;

    // For demo purposes, accept demo_token
    if ($token !== 'demo_token') {
        ApiResponse::error('Token d\'authentification requis', 401);
    }

    switch ($method) {
        case 'GET':
            if ($action === 'all') {
                getAllUsers($db, $_GET);
            } elseif ($action === 'get' && $id) {
                getUser($db, $id);
            } else {
                ApiResponse::error('Action non supportée', 404);
            }
            break;

        case 'PUT':
            $input = json_decode(file_get_contents('php://input'), true);
            if ($action === 'update' && $id) {
                updateUser($db, $id, $input);
            } else {
                ApiResponse::error('Action non supportée', 404);
            }
            break;

        case 'DELETE':
            if ($action === 'delete' && $id) {
                deleteUser($db, $id);
            } else {
                ApiResponse::error('Action non supportée', 404);
            }
            break;

        default:
            ApiResponse::error('Méthode non supportée', 405);
    }
} catch (Exception $e) {
    error_log('Users API Error: ' . $e->getMessage());
    ApiResponse::error('Internal server error', 500);
}

/**
 * Get all users (admin only)
 */
function getAllUsers($db, $params)
{
    try {
        // Get users from user_roles table with their roles
        $query = "
            SELECT
                ur.user_id,
                r.name as role,
                r.display_name as role_display_name,
                ur.assigned_at,
                'active' as status
            FROM user_roles ur
            JOIN roles r ON ur.role_id = r.id
            ORDER BY ur.assigned_at DESC
        ";

        $stmt = $db->prepare($query);
        $stmt->execute();
        $user_roles = $stmt->fetchAll();

        // Group by user_id and create user objects
        $users_data = [];
        foreach ($user_roles as $ur) {
            $user_id = $ur['user_id'];
            if (!isset($users_data[$user_id])) {
                $users_data[$user_id] = [
                    'id' => $user_id,
                    'user_id' => $user_id,
                    'email' => $user_id . '@example.com', // Mock email
                    'display_name' => 'User ' . substr($user_id, -4),
                    'first_name' => 'User',
                    'last_name' => substr($user_id, -4),
                    'status' => 'active',
                    'role' => $ur['role'],
                    'role_display_name' => $ur['role_display_name'],
                    'created_at' => $ur['assigned_at'],
                    'roles' => []
                ];
            }
            $users_data[$user_id]['roles'][] = [
                'role' => $ur['role'],
                'display_name' => $ur['role_display_name'],
                'assigned_at' => $ur['assigned_at']
            ];
        }

        $users = array_values($users_data);

        ApiResponse::success([
            'users' => $users,
            'total' => count($users),
            'page' => 1,
            'limit' => count($users)
        ]);
    } catch (Exception $e) {
        error_log("Error getting all users: " . $e->getMessage());
        ApiResponse::error('Erreur lors de la récupération des utilisateurs', 500);
    }
}

/**
 * Get a single user by ID
 */
function getUser($db, $user_id)
{
    try {
        // D'abord, récupérer les informations de base de l'utilisateur
        $userQuery = "
            SELECT
                id, firebase_uid, email, name, phone, status,
                created_at, updated_at
            FROM users
            WHERE id = ?
        ";

        $userStmt = $db->prepare($userQuery);
        $userStmt->execute([$user_id]);
        $userData = $userStmt->fetch(PDO::FETCH_ASSOC);

        if (!$userData) {
            ApiResponse::error('Utilisateur non trouvé', 404);
            return;
        }

        // Ensuite, récupérer les rôles de l'utilisateur
        $rolesQuery = "
            SELECT
                r.name as role,
                r.display_name as role_display_name,
                ur.assigned_at
            FROM user_roles ur
            JOIN roles r ON ur.role_id = r.id
            WHERE ur.user_id = ?
            ORDER BY ur.assigned_at DESC
        ";

        $rolesStmt = $db->prepare($rolesQuery);
        $rolesStmt->execute([$user_id]);
        $user_roles = $rolesStmt->fetchAll(PDO::FETCH_ASSOC);

        // Récupérer l'abonnement actuel si c'est un merchant
        $subscription = null;
        if (!empty($user_roles) && $user_roles[0]['role'] === 'merchant') {
            $subQuery = "
                SELECT
                    s.id, s.plan_id, s.status, s.start_date, s.end_date,
                    sp.name as plan_name, sp.price
                FROM subscriptions s
                JOIN subscription_plans sp ON s.plan_id = sp.id
                WHERE s.user_id = ? AND s.status = 'active'
                ORDER BY s.created_at DESC
                LIMIT 1
            ";

            $subStmt = $db->prepare($subQuery);
            $subStmt->execute([$user_id]);
            $subscription = $subStmt->fetch(PDO::FETCH_ASSOC);
        }

        // Format user data
        $user = [
            'id' => $userData['id'],
            'firebase_uid' => $userData['firebase_uid'],
            'email' => $userData['email'],
            'name' => $userData['name'],
            'phone' => $userData['phone'],
            'status' => $userData['status'],
            'created_at' => $userData['created_at'],
            'updated_at' => $userData['updated_at'],
            'roles' => [],
            'current_role' => null,
            'subscription' => $subscription
        ];

        // Ajouter les rôles
        foreach ($user_roles as $ur) {
            $user['roles'][] = [
                'role' => $ur['role'],
                'display_name' => $ur['role_display_name'],
                'assigned_at' => $ur['assigned_at']
            ];
        }

        // Définir le rôle principal (le plus récent)
        if (!empty($user_roles)) {
            $user['current_role'] = $user_roles[0]['role'];
            $user['role'] = $user_roles[0]['role'];
            $user['role_display_name'] = $user_roles[0]['role_display_name'];
        }

        ApiResponse::success($user);
    } catch (Exception $e) {
        error_log("Error getting user: " . $e->getMessage());
        ApiResponse::error('Erreur lors de la récupération de l\'utilisateur', 500);
    }
}

/**
 * Update user status or role
 */
function updateUser($db, $user_id, $data)
{
    try {
        // For now, we'll just simulate an update
        // In a real application, you would update the user's status or role

        if (isset($data['status'])) {
            // Update user status (this would typically be in a users table)
            // For now, we'll just return success
        }

        if (isset($data['role_id'])) {
            // Update user role
            $stmt = $db->prepare("UPDATE user_roles SET role_id = ? WHERE user_id = ?");
            $stmt->execute([$data['role_id'], $user_id]);
        }

        ApiResponse::success(['message' => 'Utilisateur mis à jour avec succès']);
    } catch (Exception $e) {
        error_log("Error updating user: " . $e->getMessage());
        ApiResponse::error('Erreur lors de la mise à jour de l\'utilisateur', 500);
    }
}

/**
 * Delete user
 */
function deleteUser($db, $user_id)
{
    try {
        // Delete user roles
        $stmt = $db->prepare("DELETE FROM user_roles WHERE user_id = ?");
        $stmt->execute([$user_id]);

        // In a real application, you would also delete from users table
        // and handle related data cleanup

        ApiResponse::success(['message' => 'Utilisateur supprimé avec succès']);
    } catch (Exception $e) {
        error_log("Error deleting user: " . $e->getMessage());
        ApiResponse::error('Erreur lors de la suppression de l\'utilisateur', 500);
    }
}
