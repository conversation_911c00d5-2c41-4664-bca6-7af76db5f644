<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once 'config/database.php';

try {
    // Vérification de l'authentification
    $headers = function_exists('getallheaders') ? getallheaders() : [];
    if (empty($headers) && isset($_SERVER['HTTP_AUTHORIZATION'])) {
        $headers['Authorization'] = $_SERVER['HTTP_AUTHORIZATION'];
    }
    $token = isset($headers['Authorization']) ? str_replace('Bearer ', '', $headers['Authorization']) : null;

    if ($token !== 'demo_token') {
        ApiResponse::error('Token d\'authentification requis', 401);
    }

    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        ApiResponse::error('Méthode non autorisée', 405);
    }

    // Vérifier si un fichier a été uploadé
    if (!isset($_FILES['image']) || $_FILES['image']['error'] !== UPLOAD_ERR_OK) {
        ApiResponse::error('Aucun fichier uploadé ou erreur d\'upload', 400);
    }

    $file = $_FILES['image'];
    
    // Vérifications de sécurité
    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    $maxSize = 5 * 1024 * 1024; // 5MB
    
    if (!in_array($file['type'], $allowedTypes)) {
        ApiResponse::error('Type de fichier non autorisé. Utilisez JPG, PNG, GIF ou WebP', 400);
    }
    
    if ($file['size'] > $maxSize) {
        ApiResponse::error('Fichier trop volumineux. Taille maximale: 5MB', 400);
    }
    
    // Vérifier que c'est vraiment une image
    $imageInfo = getimagesize($file['tmp_name']);
    if ($imageInfo === false) {
        ApiResponse::error('Le fichier n\'est pas une image valide', 400);
    }
    
    // Créer le dossier uploads s'il n'existe pas
    $uploadDir = '../uploads/products/';
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }
    
    // Générer un nom de fichier unique
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = uniqid('product_') . '_' . time() . '.' . $extension;
    $filepath = $uploadDir . $filename;
    
    // Déplacer le fichier uploadé
    if (!move_uploaded_file($file['tmp_name'], $filepath)) {
        ApiResponse::error('Erreur lors de la sauvegarde du fichier', 500);
    }
    
    // Générer l'URL publique
    $publicUrl = '/uploads/products/' . $filename;
    
    // Optionnel: Redimensionner l'image pour optimiser
    try {
        resizeImage($filepath, 800, 600);
    } catch (Exception $e) {
        // Si le redimensionnement échoue, on continue quand même
        error_log("Erreur redimensionnement: " . $e->getMessage());
    }
    
    ApiResponse::success([
        'url' => $publicUrl,
        'filename' => $filename,
        'size' => filesize($filepath),
        'dimensions' => [
            'width' => $imageInfo[0],
            'height' => $imageInfo[1]
        ]
    ], 'Image uploadée avec succès');

} catch (Exception $e) {
    error_log("Upload error: " . $e->getMessage());
    ApiResponse::error('Erreur interne du serveur', 500);
}

/**
 * Redimensionner une image en conservant les proportions
 */
function resizeImage($filepath, $maxWidth, $maxHeight) {
    $imageInfo = getimagesize($filepath);
    if (!$imageInfo) return false;
    
    $width = $imageInfo[0];
    $height = $imageInfo[1];
    $type = $imageInfo[2];
    
    // Calculer les nouvelles dimensions
    $ratio = min($maxWidth / $width, $maxHeight / $height);
    if ($ratio >= 1) return true; // Pas besoin de redimensionner
    
    $newWidth = round($width * $ratio);
    $newHeight = round($height * $ratio);
    
    // Créer l'image source
    switch ($type) {
        case IMAGETYPE_JPEG:
            $source = imagecreatefromjpeg($filepath);
            break;
        case IMAGETYPE_PNG:
            $source = imagecreatefrompng($filepath);
            break;
        case IMAGETYPE_GIF:
            $source = imagecreatefromgif($filepath);
            break;
        case IMAGETYPE_WEBP:
            $source = imagecreatefromwebp($filepath);
            break;
        default:
            return false;
    }
    
    if (!$source) return false;
    
    // Créer l'image de destination
    $destination = imagecreatetruecolor($newWidth, $newHeight);
    
    // Préserver la transparence pour PNG et GIF
    if ($type == IMAGETYPE_PNG || $type == IMAGETYPE_GIF) {
        imagealphablending($destination, false);
        imagesavealpha($destination, true);
        $transparent = imagecolorallocatealpha($destination, 255, 255, 255, 127);
        imagefilledrectangle($destination, 0, 0, $newWidth, $newHeight, $transparent);
    }
    
    // Redimensionner
    imagecopyresampled($destination, $source, 0, 0, 0, 0, $newWidth, $newHeight, $width, $height);
    
    // Sauvegarder
    $result = false;
    switch ($type) {
        case IMAGETYPE_JPEG:
            $result = imagejpeg($destination, $filepath, 85);
            break;
        case IMAGETYPE_PNG:
            $result = imagepng($destination, $filepath, 6);
            break;
        case IMAGETYPE_GIF:
            $result = imagegif($destination, $filepath);
            break;
        case IMAGETYPE_WEBP:
            $result = imagewebp($destination, $filepath, 85);
            break;
    }
    
    // Nettoyer la mémoire
    imagedestroy($source);
    imagedestroy($destination);
    
    return $result;
}
?>
