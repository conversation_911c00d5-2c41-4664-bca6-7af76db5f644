-- Suppression des tables existantes
DROP TABLE IF EXISTS subscriptions;
DROP TABLE IF EXISTS subscription_plans;

-- Création de la table subscription_plans
CREATE TABLE subscription_plans (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    name_ar VARCHAR(255),
    name_fr VARCHAR(255),
    name_en VARCHAR(255),
    description TEXT,
    description_ar TEXT,
    description_fr TEXT,
    description_en TEXT,
    price DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'DZD',
    billing_cycle ENUM('monthly', 'yearly') DEFAULT 'monthly',
    features JSO<PERSON>,
    max_products INT DEFAULT 10,
    max_categories INT DEFAULT 5,
    max_landing_pages INT DEFAULT 3,
    max_storage_mb INT DEFAULT 1000,
    status ENUM('active', 'inactive', 'draft', 'archived') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Création de la table subscriptions
CREATE TABLE subscriptions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    plan_id INT NOT NULL,
    status ENUM('active', 'inactive', 'cancelled', 'expired', 'draft', 'archived') DEFAULT 'active',
    start_date DATE NOT NULL,
    end_date DATE,
    next_billing_date DATE,
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'DZD',
    payment_method VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (plan_id) REFERENCES subscription_plans(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insertion des plans de démonstration
INSERT INTO subscription_plans (name, name_ar, name_fr, name_en, description, price, max_products, max_categories, max_landing_pages, features) VALUES
('Basique', 'أساسي', 'Basique', 'Basic', 'Plan de base pour débuter', 2000.00, 10, 5, 3, '["Support email", "Tableau de bord", "Analytics de base"]'),
('Pro', 'احترافي', 'Pro', 'Pro', 'Plan professionnel avec plus de fonctionnalités', 5000.00, 50, 20, 10, '["Support prioritaire", "Analytics avancées", "Intégrations", "API access"]'),
('Enterprise', 'مؤسسة', 'Enterprise', 'Enterprise', 'Plan entreprise avec fonctionnalités complètes', 10000.00, -1, -1, -1, '["Support 24/7", "Manager dédié", "Personnalisation", "SLA garanti"]');

-- Insertion des abonnements de démonstration
INSERT INTO subscriptions (user_id, plan_id, status, start_date, end_date, next_billing_date, amount) 
SELECT 4, id, 'active', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 1 MONTH), DATE_ADD(CURDATE(), INTERVAL 1 MONTH), price
FROM subscription_plans WHERE name = 'Pro';

-- Vérification
SELECT id, name, status FROM subscription_plans;
SELECT id, user_id, status FROM subscriptions;