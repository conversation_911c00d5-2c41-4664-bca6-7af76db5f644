<?php
/**
 * Insérer des landing pages de démonstration directement
 */

require_once __DIR__ . '/api/config/database.php';

try {
    $database = new Database();
    $db = $database->getConnection();

    echo "🚀 Insertion de landing pages de démonstration...\n\n";

    // Vérifier l'utilisateur demo
    $checkUser = $db->prepare("SELECT id FROM users WHERE email = ?");
    $checkUser->execute(['<EMAIL>']);
    $demoUser = $checkUser->fetch();

    if (!$demoUser) {
        echo "❌ Utilisateur demo non trouvé. Création...\n";
        $createUser = $db->prepare("INSERT INTO users (email, first_name, last_name, role) VALUES (?, ?, ?, ?)");
        $createUser->execute(['<EMAIL>', 'Demo', 'Merchant', 'merchant']);
        $demoUserId = $db->lastInsertId();
        echo "✅ Utilisateur demo créé avec ID: $demoUserId\n";
    } else {
        $demoUserId = $demoUser['id'];
        echo "✅ Utilisateur demo trouvé avec ID: $demoUserId\n";
    }

    // Landing pages à créer
    $pages = [
        [
            'merchant_id' => $demoUserId,
            'title' => 'Boutique E-commerce TechStore',
            'slug' => 'boutique-ecommerce-techstore-' . time(),
            'template_id' => 1,
            'content' => json_encode([
                'html' => '<h1>TechStore</h1><p>Votre boutique high-tech de confiance</p>',
                'sections' => [
                    ['type' => 'header', 'content' => '<h1>TechStore</h1>'],
                    ['type' => 'text', 'content' => '<p>Votre boutique high-tech de confiance</p>']
                ]
            ]),
            'status' => 'published',
            'user_id' => $demoUserId,
            'views_count' => 245,
            'conversions_count' => 12
        ],
        [
            'merchant_id' => $demoUserId,
            'title' => 'Service SaaS LandingCraft Pro',
            'slug' => 'service-saas-landingcraft-' . time(),
            'template_id' => 2,
            'content' => json_encode([
                'html' => '<h1>LandingCraft Pro</h1><p>Créez des landing pages professionnelles</p>',
                'sections' => [
                    ['type' => 'header', 'content' => '<h1>LandingCraft Pro</h1>'],
                    ['type' => 'text', 'content' => '<p>Créez des landing pages professionnelles</p>']
                ]
            ]),
            'status' => 'published',
            'user_id' => $demoUserId,
            'views_count' => 189,
            'conversions_count' => 23
        ],
        [
            'merchant_id' => $demoUserId,
            'title' => 'Portfolio Ahmed Design',
            'slug' => 'portfolio-ahmed-design-' . time(),
            'template_id' => 3,
            'content' => json_encode([
                'html' => '<h1>Ahmed Design</h1><p>Portfolio créatif et moderne</p>',
                'sections' => [
                    ['type' => 'header', 'content' => '<h1>Ahmed Design</h1>'],
                    ['type' => 'text', 'content' => '<p>Portfolio créatif et moderne</p>']
                ]
            ]),
            'status' => 'draft',
            'user_id' => $demoUserId,
            'views_count' => 67,
            'conversions_count' => 5
        ]
    ];

    foreach ($pages as $page) {
        $sql = "INSERT INTO landing_pages (
            merchant_id, title, slug, template_id, content, status, user_id,
            views_count, conversions_count, created_at, updated_at
        ) VALUES (
            :merchant_id, :title, :slug, :template_id, :content, :status, :user_id,
            :views_count, :conversions_count, NOW(), NOW()
        )";

        $stmt = $db->prepare($sql);
        $success = $stmt->execute($page);

        if ($success) {
            $pageId = $db->lastInsertId();
            echo "✅ Landing page créée: '{$page['title']}' (ID: $pageId)\n";
        } else {
            echo "❌ Erreur lors de la création de '{$page['title']}'\n";
            print_r($stmt->errorInfo());
        }
    }

    echo "\n🎉 Insertion terminée !\n";
    echo "🔗 Vérifiez le dashboard: http://localhost:8000/dashboard.html\n";

} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
?>
