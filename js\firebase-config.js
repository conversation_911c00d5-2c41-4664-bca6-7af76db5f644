/**
 * Firebase Configuration - Version compatible navigateur
 * Utilise les CDN Firebase au lieu des modules ES6
 */

// Configuration Firebase
const firebaseConfig = {
  apiKey: "AIzaSyAHYP4efj_6z7lodL56YF2_vZfLVRnraBs",
  authDomain: "landingpage-a7491.firebaseapp.com",
  projectId: "landingpage-a7491",
  storageBucket: "landingpage-a7491.firebasestorage.app",
  messagingSenderId: "538587228680",
  appId: "1:538587228680:web:662bc194bf9894634b3fbd",
  measurementId: "G-NXQWCWG5YD"
};

// Namespace global pour Firebase
window.LandingPageFirebase = {
  config: firebaseConfig,
  app: null,
  analytics: null,
  db: null,
  auth: null,
  storage: null,
  initialized: false,

  // Initialiser Firebase
  init: async () => {
    if (window.LandingPageFirebase.initialized) {
      console.log('🔥 Firebase déjà initialisé');
      return;
    }

    try {
      // Vérifier si Firebase est chargé
      if (typeof firebase === 'undefined') {
        console.warn('⚠️ Firebase SDK non chargé - chargement depuis CDN...');
        await window.LandingPageFirebase.loadFirebaseSDK();
      }

      // Initialiser l'app Firebase
      window.LandingPageFirebase.app = firebase.initializeApp(firebaseConfig);
      
      // Initialiser les services si disponibles
      if (firebase.analytics) {
        window.LandingPageFirebase.analytics = firebase.analytics(window.LandingPageFirebase.app);
      }
      
      if (firebase.firestore) {
        window.LandingPageFirebase.db = firebase.firestore(window.LandingPageFirebase.app);
      }
      
      if (firebase.auth) {
        window.LandingPageFirebase.auth = firebase.auth(window.LandingPageFirebase.app);
      }
      
      if (firebase.storage) {
        window.LandingPageFirebase.storage = firebase.storage(window.LandingPageFirebase.app);
      }

      window.LandingPageFirebase.initialized = true;
      console.log('🔥 Firebase initialisé avec succès');
      
      // Événement personnalisé
      window.dispatchEvent(new CustomEvent('firebase:initialized'));
      
    } catch (error) {
      console.error('❌ Erreur initialisation Firebase:', error);
    }
  },

  // Charger le SDK Firebase depuis CDN
  loadFirebaseSDK: () => {
    return new Promise((resolve, reject) => {
      // Firebase App (core)
      const appScript = document.createElement('script');
      appScript.src = 'https://www.gstatic.com/firebasejs/9.23.0/firebase-app-compat.js';
      appScript.onload = () => {
        // Firebase Analytics
        const analyticsScript = document.createElement('script');
        analyticsScript.src = 'https://www.gstatic.com/firebasejs/9.23.0/firebase-analytics-compat.js';
        analyticsScript.onload = () => {
          // Firebase Firestore
          const firestoreScript = document.createElement('script');
          firestoreScript.src = 'https://www.gstatic.com/firebasejs/9.23.0/firebase-firestore-compat.js';
          firestoreScript.onload = () => {
            // Firebase Auth
            const authScript = document.createElement('script');
            authScript.src = 'https://www.gstatic.com/firebasejs/9.23.0/firebase-auth-compat.js';
            authScript.onload = () => {
              // Firebase Storage
              const storageScript = document.createElement('script');
              storageScript.src = 'https://www.gstatic.com/firebasejs/9.23.0/firebase-storage-compat.js';
              storageScript.onload = resolve;
              storageScript.onerror = reject;
              document.head.appendChild(storageScript);
            };
            authScript.onerror = reject;
            document.head.appendChild(authScript);
          };
          firestoreScript.onerror = reject;
          document.head.appendChild(firestoreScript);
        };
        analyticsScript.onerror = reject;
        document.head.appendChild(analyticsScript);
      };
      appScript.onerror = reject;
      document.head.appendChild(appScript);
    });
  },

  // Méthodes utilitaires
  isInitialized: () => window.LandingPageFirebase.initialized,
  
  getApp: () => window.LandingPageFirebase.app,
  getAnalytics: () => window.LandingPageFirebase.analytics,
  getFirestore: () => window.LandingPageFirebase.db,
  getAuth: () => window.LandingPageFirebase.auth,
  getStorage: () => window.LandingPageFirebase.storage
};

// Auto-initialisation
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', window.LandingPageFirebase.init);
} else {
  window.LandingPageFirebase.init();
}

// Alias global pour compatibilité
window.firebaseApp = window.LandingPageFirebase;

console.log('🔥 Configuration Firebase chargée');