<?php

/**
 * API simple pour les produits (sans authentification pour les tests)
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Configuration de la base de données
$host = 'localhost';
$port = 3307;
$dbname = 'landingpage_new';
$username = 'root';
$password = '';

try {
    // Connexion à la base de données
    $pdo = new PDO(
        "mysql:host={$host};port={$port};dbname={$dbname};charset=utf8mb4",
        $username,
        $password,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]
    );

    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // Vérifier s'il y a un ID dans les paramètres GET ou dans l'URL
        $productId = $_GET['id'] ?? null;
        
        // Si pas d'ID dans GET, chercher dans l'URL
        if (!$productId) {
            $pathParts = explode('/', trim($_SERVER['REQUEST_URI'], '/'));
            foreach ($pathParts as $part) {
                if (is_numeric($part)) {
                    $productId = $part;
                    break;
                }
            }
        }

        if ($productId) {
            // Récupérer un produit spécifique
            $query = "SELECT p.* FROM products p WHERE p.id = ?";
            $stmt = $pdo->prepare($query);
            $stmt->execute([$productId]);
            $product = $stmt->fetch();

            if (!$product) {
                throw new Exception('Produit non trouvé');
            }

            $formattedProduct = [
                'id' => (int)$product['id'],
                'merchant_id' => (int)$product['merchant_id'],
                'store_id' => (int)$product['store_id'],
                'sku' => $product['sku'] ?? '',
                'name' => $product['name'] ?? '',
                'name_ar' => $product['name_ar'] ?? '',
                'name_en' => $product['name_en'] ?? '',
                'name_fr' => $product['name_fr'] ?? '',
                'description' => $product['description'] ?? '',
                'description_ar' => $product['description_ar'] ?? '',
                'description_en' => $product['description_en'] ?? '',
                'description_fr' => $product['description_fr'] ?? '',
                'price' => (float)$product['price'],
                'compare_price' => $product['compare_price'] ? (float)$product['compare_price'] : null,
                'currency' => $product['currency'] ?? 'DZD',
                'stock_quantity' => (int)$product['stock_quantity'],
                'status' => $product['status'] ?? 'draft',
                'featured' => (bool)$product['featured'],
                'created_at' => $product['created_at'] ?? date('Y-m-d H:i:s'),
                'updated_at' => $product['updated_at'] ?? date('Y-m-d H:i:s')
            ];

            echo json_encode([
                'success' => true,
                'data' => $formattedProduct
            ]);
        } else {
            // Récupérer tous les produits
            $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
            $limit = isset($_GET['limit']) ? min(100, max(1, intval($_GET['limit']))) : 20;
            $offset = ($page - 1) * $limit;

            // Compter le total
            $countQuery = "SELECT COUNT(*) as total FROM products";
            $countStmt = $pdo->query($countQuery);
            $total = $countStmt->fetch()['total'];

            // Récupérer les produits avec pagination
            $query = "SELECT p.* FROM products p ORDER BY p.created_at DESC LIMIT $limit OFFSET $offset";
            $stmt = $pdo->query($query);
            $products = $stmt->fetchAll();

            // Formater les données pour le frontend
            $formattedProducts = array_map(function ($product) {
                return [
                    'id' => (int)$product['id'],
                    'merchant_id' => (int)$product['merchant_id'],
                    'store_id' => (int)$product['store_id'],
                    'sku' => $product['sku'] ?? '',
                    'name' => $product['name'] ?? '',
                    'name_ar' => $product['name_ar'] ?? '',
                    'name_en' => $product['name_en'] ?? '',
                    'name_fr' => $product['name_fr'] ?? '',
                    'description' => $product['description'] ?? '',
                    'price' => (float)$product['price'],
                    'compare_price' => $product['compare_price'] ? (float)$product['compare_price'] : null,
                    'currency' => $product['currency'] ?? 'DZD',
                    'stock_quantity' => (int)$product['stock_quantity'],
                    'status' => $product['status'] ?? 'draft',
                    'featured' => (bool)$product['featured'],
                    'created_at' => $product['created_at'] ?? date('Y-m-d H:i:s'),
                    'updated_at' => $product['updated_at'] ?? date('Y-m-d H:i:s')
                ];
            }, $products);

            echo json_encode([
                'success' => true,
                'data' => [
                    'products' => $formattedProducts,
                    'pagination' => [
                        'current_page' => $page,
                        'per_page' => $limit,
                        'total' => (int)$total,
                        'total_pages' => ceil($total / $limit)
                    ]
                ]
            ]);
        }
    } elseif ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Créer un nouveau produit
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            throw new Exception('Données JSON invalides');
        }

        $name = $input['name'] ?? '';
        $price = $input['price'] ?? 0;
        $merchant_id = $input['merchant_id'] ?? 1;
        $store_id = $input['store_id'] ?? 1;

        if (empty($name)) {
            throw new Exception('Le nom du produit est requis');
        }

        if ($price <= 0) {
            throw new Exception('Le prix doit être supérieur à 0');
        }

        // Générer un SKU automatique
        $sku = 'PRD-' . strtoupper(uniqid());

        // Insérer le nouveau produit
        $stmt = $pdo->prepare("
            INSERT INTO products (
                merchant_id, store_id, sku, name, name_ar, price, 
                stock_quantity, status, featured, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, 'active', 0, NOW(), NOW())
        ");
        
        $stmt->execute([
            $merchant_id,
            $store_id,
            $sku,
            $name,
            $name, // Utiliser le même nom pour l'arabe par défaut
            $price,
            $input['stock_quantity'] ?? 0
        ]);

        $newId = $pdo->lastInsertId();

        echo json_encode([
            'success' => true,
            'message' => 'Produit créé avec succès',
            'data' => ['id' => (int)$newId]
        ]);
    } else {
        throw new Exception('Méthode non supportée');
    }
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Erreur de base de données: ' . $e->getMessage()
    ]);
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
