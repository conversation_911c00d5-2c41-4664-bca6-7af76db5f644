<?php
require_once 'php/config/database.php';

try {
    echo "🔍 Vérification des données stores...\n\n";
    
    // Vérifier tous les stores
    $storeQuery = "SELECT id, merchant_id, store_name, store_name_en, store_name_ar, subdomain, status FROM stores";
    $stores = $pdo->query($storeQuery)->fetchAll(PDO::FETCH_ASSOC);
    
    echo "📊 STORES EXISTANTS:\n";
    if (empty($stores)) {
        echo "  ❌ Aucun store trouvé\n";
    } else {
        foreach ($stores as $store) {
            echo "  - ID: {$store['id']}, Merchant: {$store['merchant_id']}, Nom: {$store['store_name']}, Status: {$store['status']}\n";
            if ($store['subdomain']) {
                echo "    Subdomain: {$store['subdomain']}\n";
            }
        }
    }
    
    // Vérifier les merchants
    echo "\n👥 MERCHANTS EXISTANTS:\n";
    $merchantQuery = "SELECT id, email, first_name, last_name, role FROM users WHERE role = 'merchant'";
    $merchants = $pdo->query($merchantQuery)->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($merchants)) {
        echo "  ❌ Aucun merchant trouvé\n";
    } else {
        foreach ($merchants as $merchant) {
            echo "  - ID: {$merchant['id']}, Email: {$merchant['email']}, Nom: {$merchant['first_name']} {$merchant['last_name']}\n";
        }
    }
    
    // Vérifier les landing pages
    echo "\n📄 LANDING PAGES EXISTANTES:\n";
    $lpQuery = "SELECT id, merchant_id, title, slug, status FROM landing_pages";
    $landingPages = $pdo->query($lpQuery)->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($landingPages)) {
        echo "  ❌ Aucune landing page trouvée\n";
    } else {
        foreach ($landingPages as $lp) {
            echo "  - ID: {$lp['id']}, Merchant: {$lp['merchant_id']}, Titre: {$lp['title']}, Slug: {$lp['slug']}, Status: {$lp['status']}\n";
        }
    }
    
    // Si on a des stores, utiliser le premier pour créer des landing pages
    if (!empty($stores)) {
        $store = $stores[0];
        echo "\n🔧 Utilisation du store: {$store['store_name']} (Merchant: {$store['merchant_id']})\n";
        
        // Créer une landing page de test
        $checkQuery = "SELECT id FROM landing_pages WHERE merchant_id = ? AND slug = 'home'";
        $checkStmt = $pdo->prepare($checkQuery);
        $checkStmt->execute([$store['merchant_id']]);
        
        if (!$checkStmt->fetch()) {
            $insertQuery = "
                INSERT INTO landing_pages (
                    merchant_id, title, title_ar, title_fr, title_en, slug, description,
                    status, template_id, created_at, updated_at
                ) VALUES (
                    ?, ?, ?, ?, ?, 'home', ?, 'published', 1, NOW(), NOW()
                )
            ";
            
            $title = $store['store_name'] . ' - Accueil';
            $insertStmt = $pdo->prepare($insertQuery);
            $result = $insertStmt->execute([
                $store['merchant_id'],
                $title,
                $title,
                $title,
                $title,
                'Page d\'accueil de ' . $store['store_name']
            ]);
            
            if ($result) {
                echo "✅ Landing page d'accueil créée\n";
            }
        }
        
        // Afficher les URLs de test
        echo "\n🔗 URLs de test:\n";
        $storeName = urlencode($store['store_name']);
        echo "  - Store principal: http://localhost:8000/store/{$storeName}\n";
        echo "  - Page d'accueil: http://localhost:8000/store/{$storeName}/home\n";
        
        if (!empty($store['subdomain'])) {
            echo "  - Subdomain: http://localhost:8000/{$store['subdomain']}\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
}
?>
