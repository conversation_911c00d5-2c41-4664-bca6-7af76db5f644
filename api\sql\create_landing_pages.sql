CREATE TABLE IF NOT EXISTS landing_pages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VA<PERSON>HAR(255) NOT NULL,
    template_id VARCHAR(50) NOT NULL,
    status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
    content LONGTEXT,
    metadata JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    store_id INT,
    INDEX idx_status (status),
    INDEX idx_store (store_id),
    INDEX idx_template (template_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;