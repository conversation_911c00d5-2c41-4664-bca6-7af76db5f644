<?php
/**
 * Test Stores API
 */

echo "<h2>Stores API Test</h2>\n";
echo "<pre>\n";

// Set up environment for API testing
$_SERVER['REQUEST_METHOD'] = 'GET';
$_SERVER['REQUEST_URI'] = '/api/stores';
$_SERVER['HTTP_AUTHORIZATION'] = 'Bearer demo_token';

// Capture output
ob_start();

try {
    echo "Testing Stores API...\n";
    
    // Include the stores API
    include 'api/stores.php';
    
} catch (Exception $e) {
    echo "Exception: " . $e->getMessage() . "\n";
} catch (Error $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

$output = ob_get_clean();
echo $output;

echo "</pre>\n";
?>
