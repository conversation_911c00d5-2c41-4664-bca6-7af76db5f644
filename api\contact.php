<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once 'config/database.php';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Créer la table contact_messages si elle n'existe pas
    createContactTableIfNotExists($db);
    
    $method = $_SERVER['REQUEST_METHOD'];
    $action = isset($_GET['action']) ? $_GET['action'] : '';
    
    switch ($method) {
        case 'GET':
            if ($action === 'all') {
                getAllMessages($db);
            } elseif ($action === 'stats') {
                getContactStats($db);
            } else {
                http_response_code(404);
                echo json_encode(['error' => 'Action non supportée']);
            }
            break;
            
        case 'POST':
            $input = json_decode(file_get_contents('php://input'), true);
            if ($action === 'mark-read') {
                markAsRead($db, $input);
            } else {
                createMessage($db, $input);
            }
            break;
            
        case 'DELETE':
            $id = isset($_GET['id']) ? $_GET['id'] : null;
            if ($id) {
                deleteMessage($db, $id);
            } else {
                http_response_code(400);
                echo json_encode(['error' => 'ID requis']);
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Méthode non supportée']);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Erreur serveur: ' . $e->getMessage()]);
}

/**
 * Create contact_messages table if it doesn't exist
 */
function createContactTableIfNotExists($db)
{
    try {
        $createTableQuery = "
            CREATE TABLE IF NOT EXISTS contact_messages (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                email VARCHAR(255) NOT NULL,
                subject VARCHAR(500) NOT NULL,
                message TEXT NOT NULL,
                is_read TINYINT(1) DEFAULT 0,
                ip_address VARCHAR(45) NULL,
                user_agent TEXT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_email (email),
                INDEX idx_is_read (is_read),
                INDEX idx_created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        $db->exec($createTableQuery);
        
    } catch (Exception $e) {
        error_log("Erreur création table contact_messages: " . $e->getMessage());
    }
}

/**
 * Get all contact messages
 */
function getAllMessages($db)
{
    try {
        $query = "
            SELECT 
                id,
                name,
                email,
                subject,
                message,
                is_read,
                created_at,
                updated_at
            FROM contact_messages 
            ORDER BY created_at DESC
        ";
        
        $stmt = $db->query($query);
        $messages = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Compter les statistiques
        $stats = [
            'total' => count($messages),
            'unread' => 0,
            'today' => 0
        ];
        
        $today = date('Y-m-d');
        foreach ($messages as $message) {
            if ($message['is_read'] == 0) {
                $stats['unread']++;
            }
            if (date('Y-m-d', strtotime($message['created_at'])) === $today) {
                $stats['today']++;
            }
        }
        
        echo json_encode([
            'success' => true,
            'data' => [
                'messages' => $messages,
                'stats' => $stats
            ]
        ]);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la récupération des messages: ' . $e->getMessage()]);
    }
}

/**
 * Get contact statistics
 */
function getContactStats($db)
{
    try {
        $statsQuery = "
            SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN is_read = 0 THEN 1 ELSE 0 END) as unread,
                SUM(CASE WHEN DATE(created_at) = CURDATE() THEN 1 ELSE 0 END) as today,
                SUM(CASE WHEN DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) THEN 1 ELSE 0 END) as this_week,
                SUM(CASE WHEN DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as this_month
            FROM contact_messages
        ";
        
        $stmt = $db->query($statsQuery);
        $stats = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo json_encode([
            'success' => true,
            'data' => $stats
        ]);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la récupération des statistiques: ' . $e->getMessage()]);
    }
}

/**
 * Create new contact message
 */
function createMessage($db, $data)
{
    try {
        // Validation des données
        if (empty($data['name']) || empty($data['email']) || empty($data['subject']) || empty($data['message'])) {
            http_response_code(400);
            echo json_encode(['error' => 'Tous les champs sont requis']);
            return;
        }
        
        // Validation de l'email
        if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            http_response_code(400);
            echo json_encode(['error' => 'Email invalide']);
            return;
        }
        
        $query = "
            INSERT INTO contact_messages (
                name, email, subject, message, ip_address, user_agent, created_at
            ) VALUES (
                ?, ?, ?, ?, ?, ?, NOW()
            )
        ";
        
        $stmt = $db->prepare($query);
        $stmt->execute([
            $data['name'],
            $data['email'],
            $data['subject'],
            $data['message'],
            $_SERVER['REMOTE_ADDR'] ?? null,
            $_SERVER['HTTP_USER_AGENT'] ?? null
        ]);
        
        $messageId = $db->lastInsertId();
        
        echo json_encode([
            'success' => true,
            'data' => [
                'id' => $messageId,
                'message' => 'Message envoyé avec succès'
            ]
        ]);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de l\'envoi du message: ' . $e->getMessage()]);
    }
}

/**
 * Mark message as read
 */
function markAsRead($db, $data)
{
    try {
        if (empty($data['id'])) {
            http_response_code(400);
            echo json_encode(['error' => 'ID du message requis']);
            return;
        }
        
        $query = "UPDATE contact_messages SET is_read = 1, updated_at = NOW() WHERE id = ?";
        $stmt = $db->prepare($query);
        $stmt->execute([$data['id']]);
        
        if ($stmt->rowCount() === 0) {
            http_response_code(404);
            echo json_encode(['error' => 'Message non trouvé']);
            return;
        }
        
        echo json_encode([
            'success' => true,
            'data' => [
                'message' => 'Message marqué comme lu'
            ]
        ]);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la mise à jour: ' . $e->getMessage()]);
    }
}

/**
 * Delete message
 */
function deleteMessage($db, $id)
{
    try {
        $query = "DELETE FROM contact_messages WHERE id = ?";
        $stmt = $db->prepare($query);
        $stmt->execute([$id]);
        
        if ($stmt->rowCount() === 0) {
            http_response_code(404);
            echo json_encode(['error' => 'Message non trouvé']);
            return;
        }
        
        echo json_encode([
            'success' => true,
            'data' => [
                'message' => 'Message supprimé avec succès'
            ]
        ]);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la suppression: ' . $e->getMessage()]);
    }
}
?>
