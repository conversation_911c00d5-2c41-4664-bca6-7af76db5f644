<?php
/**
 * Script de configuration de la base de données
 */

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once __DIR__ . '/config/database.php';

try {
    $database = new Database();
    $pdo = $database->getConnection();
    echo "✅ Connexion à la base de données réussie\n\n";

    // Suppression des tables existantes dans l'ordre inverse des dépendances
    $pdo->exec("DROP TABLE IF EXISTS landing_pages");
    $pdo->exec("DROP TABLE IF EXISTS product_categories");
    $pdo->exec("DROP TABLE IF EXISTS products");
    $pdo->exec("DROP TABLE IF EXISTS categories");
    $pdo->exec("DROP TABLE IF EXISTS templates");
    $pdo->exec("DROP TABLE IF EXISTS stores");
    $pdo->exec("DROP TABLE IF EXISTS merchants");
    echo "✅ Tables existantes supprimées\n";

    // Création de la table merchants
    $pdo->exec("CREATE TABLE merchants (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255),
        email VARCHAR(255) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        phone VARCHAR(50),
        address TEXT,
        status ENUM('active', 'inactive', 'pending') DEFAULT 'pending',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "✅ Table merchants créée\n";

    // Création de la table stores
    $pdo->exec("CREATE TABLE stores (
        id INT AUTO_INCREMENT PRIMARY KEY,
        merchant_id INT NOT NULL,
        store_name VARCHAR(255) NOT NULL,
        store_name_ar VARCHAR(255),
        store_name_fr VARCHAR(255),
        store_name_en VARCHAR(255),
        description TEXT,
        description_ar TEXT,
        description_fr TEXT,
        description_en TEXT,
        logo VARCHAR(255),
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (merchant_id) REFERENCES merchants(id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "✅ Table stores créée\n";

    // Création de la table categories
    $pdo->exec("CREATE TABLE categories (
        id INT AUTO_INCREMENT PRIMARY KEY,
        store_id INT NOT NULL,
        name VARCHAR(255) NOT NULL,
        name_ar VARCHAR(255),
        name_fr VARCHAR(255),
        name_en VARCHAR(255),
        slug VARCHAR(255) NOT NULL,
        description TEXT,
        image VARCHAR(255),
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (store_id) REFERENCES stores(id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "✅ Table categories créée\n";

    // Création de la table products
    $pdo->exec("CREATE TABLE products (
        id INT AUTO_INCREMENT PRIMARY KEY,
        store_id INT NOT NULL,
        name VARCHAR(255) NOT NULL,
        name_ar VARCHAR(255),
        name_fr VARCHAR(255),
        name_en VARCHAR(255),
        slug VARCHAR(255) NOT NULL,
        description TEXT,
        description_ar TEXT,
        description_fr TEXT,
        description_en TEXT,
        price DECIMAL(10,2) NOT NULL,
        compare_price DECIMAL(10,2),
        sku VARCHAR(100),
        stock INT DEFAULT 0,
        images TEXT,
        status ENUM('active', 'inactive', 'draft') DEFAULT 'draft',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (store_id) REFERENCES stores(id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "✅ Table products créée\n";

    // Création de la table product_categories
    $pdo->exec("CREATE TABLE product_categories (
        product_id INT NOT NULL,
        category_id INT NOT NULL,
        PRIMARY KEY (product_id, category_id),
        FOREIGN KEY (product_id) REFERENCES products(id),
        FOREIGN KEY (category_id) REFERENCES categories(id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "✅ Table product_categories créée\n";

    // Création de la table templates
    $pdo->exec("CREATE TABLE templates (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        preview_image VARCHAR(255),
        html_content TEXT,
        css_content TEXT,
        js_content TEXT,
        category VARCHAR(50),
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "✅ Table templates créée\n";

    // Création de la table landing_pages
    $pdo->exec("CREATE TABLE landing_pages (
        id INT AUTO_INCREMENT PRIMARY KEY,
        merchant_id INT NOT NULL,
        template_id INT NOT NULL,
        title VARCHAR(255) NOT NULL,
        slug VARCHAR(255) NOT NULL,
        description TEXT,
        content TEXT,
        custom_css TEXT,
        custom_js TEXT,
        status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
        views INT DEFAULT 0,
        conversions INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (merchant_id) REFERENCES merchants(id),
        FOREIGN KEY (template_id) REFERENCES templates(id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "✅ Table landing_pages créée\n";

    // Insertion du merchant de démo
    $merchantStmt = $pdo->prepare("INSERT INTO merchants (name, email, password, status) VALUES (?, ?, ?, ?)");
    $merchantStmt->execute(['Demo Merchant', '<EMAIL>', password_hash('demo123', PASSWORD_DEFAULT), 'active']);
    $merchantId = $pdo->lastInsertId();
    echo "✅ Merchant de démo créé (ID: $merchantId)\n";

    // Insertion du store de démo
    $storeStmt = $pdo->prepare("INSERT INTO stores (merchant_id, store_name, store_name_ar, store_name_fr, store_name_en) VALUES (?, ?, ?, ?, ?)");
    $storeStmt->execute([$merchantId, 'TechStore Algeria', 'متجر التكنولوجيا الجزائر', 'TechStore Algérie', 'TechStore Algeria']);
    $storeId = $pdo->lastInsertId();
    echo "✅ Store de démo créé (ID: $storeId)\n";

    echo "\n✨ Configuration de la base de données terminée avec succès!\n";

} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
}
