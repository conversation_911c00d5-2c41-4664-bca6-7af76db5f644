<?php
/**
 * API Router and Documentation
 * Central entry point for all API endpoints
 */

require_once 'config/database.php';

// Set headers
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
    $pathParts = explode('/', trim($path, '/'));
    
    // Extract endpoint from path
    $endpoint = isset($pathParts[1]) ? $pathParts[1] : '';
    
    // Route to appropriate endpoint
    switch ($endpoint) {
        case 'products':
            require_once 'products.php';
            break;
            
        case 'orders':
            require_once 'orders.php';
            break;
            
        case 'payments':
            require_once 'payments.php';
            break;
            
        case 'stores':
            require_once 'stores.php';
            break;
            
        case 'users':
            require_once 'users.php';
            break;
            
        case 'ai':
            require_once 'ai.php';
            break;
            
        case 'analytics':
            require_once 'analytics.php';
            break;
            
        case 'docs':
        case 'documentation':
        case '':
            showApiDocumentation();
            break;
            
        case 'health':
            healthCheck();
            break;
            
        default:
            ApiResponse::error('Endpoint not found', 404);
    }
    
} catch (Exception $e) {
    error_log('API Router Error: ' . $e->getMessage());
    ApiResponse::error('Internal server error', 500);
}

/**
 * Show API documentation
 */
function showApiDocumentation() {
    $documentation = [
        'title' => 'Landing Page SaaS API',
        'version' => '1.0.0',
        'description' => 'API pour la gestion des landing pages, commandes, produits et analytics',
        'base_url' => 'https://' . $_SERVER['HTTP_HOST'] . '/api',
        'authentication' => [
            'type' => 'Bearer Token',
            'description' => 'Utilisez un token Firebase JWT dans le header Authorization',
            'header' => 'Authorization: Bearer <firebase_jwt_token>'
        ],
        'endpoints' => [
            'products' => [
                'description' => 'Gestion des produits',
                'methods' => [
                    'GET /api/products' => 'Liste des produits avec pagination et filtres',
                    'GET /api/products/{id}' => 'Détails d\'un produit',
                    'POST /api/products' => 'Créer un nouveau produit',
                    'PUT /api/products/{id}' => 'Mettre à jour un produit',
                    'DELETE /api/products/{id}' => 'Supprimer un produit'
                ],
                'filters' => ['category', 'status', 'price_min', 'price_max', 'search'],
                'pagination' => ['page', 'limit']
            ],
            'orders' => [
                'description' => 'Gestion des commandes',
                'methods' => [
                    'GET /api/orders' => 'Liste des commandes avec pagination et filtres',
                    'GET /api/orders/{id}' => 'Détails d\'une commande',
                    'GET /api/orders/stats' => 'Statistiques des commandes',
                    'POST /api/orders' => 'Créer une nouvelle commande',
                    'PUT /api/orders/{id}' => 'Mettre à jour le statut d\'une commande',
                    'PUT /api/orders/{id}/details' => 'Mettre à jour les détails d\'une commande',
                    'DELETE /api/orders/{id}' => 'Supprimer une commande'
                ],
                'filters' => ['status', 'date_from', 'date_to', 'customer_email', 'payment_method'],
                'statuses' => ['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded']
            ],
            'payments' => [
                'description' => 'Gestion des paiements et méthodes de paiement',
                'methods' => [
                    'GET /api/payments' => 'Liste des paiements',
                    'GET /api/payments/methods' => 'Liste des méthodes de paiement',
                    'POST /api/payments' => 'Enregistrer un nouveau paiement',
                    'POST /api/payments/methods' => 'Ajouter une méthode de paiement',
                    'PUT /api/payments/methods/{id}' => 'Mettre à jour une méthode de paiement',
                    'DELETE /api/payments/methods/{id}' => 'Supprimer une méthode de paiement'
                ],
                'payment_methods' => ['baridimob', 'ccp', 'bank_transfer', 'cod', 'edahabia']
            ],
            'stores' => [
                'description' => 'Gestion des magasins',
                'methods' => [
                    'GET /api/stores' => 'Informations du magasin de l\'utilisateur',
                    'GET /api/stores/all' => 'Liste de tous les magasins (admin)',
                    'POST /api/stores' => 'Créer un nouveau magasin',
                    'PUT /api/stores' => 'Mettre à jour les informations du magasin',
                    'DELETE /api/stores/{id}' => 'Supprimer un magasin (admin)'
                ],
                'fields' => ['store_name', 'description', 'phone', 'email', 'address', 'logo_url', 'social_media']
            ],
            'users' => [
                'description' => 'Gestion des utilisateurs et rôles',
                'methods' => [
                    'GET /api/users/profile' => 'Profil de l\'utilisateur connecté',
                    'GET /api/users/roles' => 'Rôles de l\'utilisateur',
                    'GET /api/users/all' => 'Liste de tous les utilisateurs (admin)',
                    'POST /api/users/roles' => 'Assigner un rôle à un utilisateur',
                    'PUT /api/users/profile' => 'Mettre à jour le profil',
                    'DELETE /api/users/roles/{id}' => 'Supprimer un rôle'
                ],
                'roles' => ['admin', 'seller', 'agent']
            ],
            'ai' => [
                'description' => 'Gestion des clés IA et analytics d\'utilisation',
                'methods' => [
                    'GET /api/ai/keys' => 'Liste des clés IA',
                    'GET /api/ai/usage' => 'Statistiques d\'utilisation IA',
                    'GET /api/ai/analytics' => 'Analytics détaillées IA',
                    'POST /api/ai/keys' => 'Ajouter une clé IA',
                    'PUT /api/ai/keys/{id}' => 'Mettre à jour une clé IA',
                    'DELETE /api/ai/keys/{id}' => 'Supprimer une clé IA'
                ],
                'providers' => ['openai', 'claude', 'gemini', 'mistral']
            ],
            'analytics' => [
                'description' => 'Analytics et statistiques du dashboard',
                'methods' => [
                    'GET /api/analytics/dashboard' => 'Vue d\'ensemble du dashboard',
                    'GET /api/analytics/sales' => 'Analytics des ventes',
                    'GET /api/analytics/products' => 'Analytics des produits',
                    'GET /api/analytics/orders' => 'Analytics des commandes',
                    'GET /api/analytics/customers' => 'Analytics des clients'
                ],
                'periods' => ['today', 'week', 'month', 'quarter', 'year', 'custom']
            ]
        ],
        'response_format' => [
            'success' => [
                'success' => true,
                'data' => 'object|array',
                'message' => 'string (optional)'
            ],
            'error' => [
                'success' => false,
                'error' => 'string',
                'code' => 'integer'
            ],
            'validation' => [
                'success' => false,
                'error' => 'string',
                'code' => 422,
                'details' => 'array (optional)'
            ]
        ],
        'status_codes' => [
            200 => 'OK - Requête réussie',
            201 => 'Created - Ressource créée avec succès',
            400 => 'Bad Request - Données invalides',
            401 => 'Unauthorized - Authentication requise',
            403 => 'Forbidden - Permissions insuffisantes',
            404 => 'Not Found - Ressource non trouvée',
            422 => 'Unprocessable Entity - Erreur de validation',
            500 => 'Internal Server Error - Erreur serveur'
        ],
        'examples' => [
            'authentication' => [
                'description' => 'Exemple d\'authentification',
                'request' => [
                    'method' => 'GET',
                    'url' => '/api/products',
                    'headers' => [
                        'Authorization' => 'Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...'
                    ]
                ]
            ],
            'create_product' => [
                'description' => 'Exemple de création de produit',
                'request' => [
                    'method' => 'POST',
                    'url' => '/api/products',
                    'headers' => [
                        'Content-Type' => 'application/json',
                        'Authorization' => 'Bearer <token>'
                    ],
                    'body' => [
                        'name' => 'Produit Test',
                        'name_ar' => 'منتج تجريبي',
                        'description' => 'Description du produit',
                        'price' => 2500.00,
                        'category' => 'electronics',
                        'sku' => 'PROD-001',
                        'stock_quantity' => 100,
                        'images' => ['image1.jpg', 'image2.jpg']
                    ]
                ],
                'response' => [
                    'success' => true,
                    'data' => [
                        'id' => 1,
                        'name' => 'Produit Test',
                        'price' => 2500.00,
                        'created_at' => '2024-01-15 10:30:00'
                    ],
                    'message' => 'Product created successfully'
                ]
            ]
        ],
        'rate_limiting' => [
            'description' => 'Limites de taux d\'API',
            'limits' => [
                'authenticated' => '1000 requêtes par heure',
                'unauthenticated' => '100 requêtes par heure'
            ]
        ],
        'support' => [
            'documentation' => 'https://docs.landingpage-saas.com',
            'contact' => '<EMAIL>',
            'github' => 'https://github.com/landingpage-saas/api'
        ]
    ];
    
    ApiResponse::success($documentation);
}

/**
 * Health check endpoint
 */
function healthCheck() {
    try {
        $database = new Database();
        $db = $database->getConnection();
        
        // Test database connection
        $stmt = $db->query('SELECT 1');
        $db_status = $stmt ? 'connected' : 'disconnected';
        
        // Get system info
        $health = [
            'status' => 'healthy',
            'timestamp' => date('Y-m-d H:i:s'),
            'version' => '1.0.0',
            'environment' => 'production',
            'services' => [
                'database' => [
                    'status' => $db_status,
                    'type' => 'MySQL',
                    'version' => $db->getAttribute(PDO::ATTR_SERVER_VERSION)
                ],
                'php' => [
                    'status' => 'running',
                    'version' => PHP_VERSION
                ],
                'memory' => [
                    'usage' => memory_get_usage(true),
                    'peak' => memory_get_peak_usage(true),
                    'limit' => ini_get('memory_limit')
                ]
            ],
            'uptime' => [
                'server' => $_SERVER['REQUEST_TIME'],
                'formatted' => date('Y-m-d H:i:s', $_SERVER['REQUEST_TIME'])
            ]
        ];
        
        ApiResponse::success($health);
        
    } catch (Exception $e) {
        ApiResponse::error('Health check failed: ' . $e->getMessage(), 503);
    }
}
?>