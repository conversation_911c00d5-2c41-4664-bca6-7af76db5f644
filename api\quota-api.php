<?php
/**
 * API des quotas et abonnements
 * Endpoints pour gérer les limites et l'utilisation
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// Gérer les requêtes OPTIONS (CORS preflight)
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once '../config/database.php';
require_once '../php/QuotaManager.php';

try {
    // Connexion à la base de données
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";port=" . DB_PORT . ";dbname=" . DB_DATABASE . ";charset=" . DB_CHARSET,
        DB_USERNAME,
        DB_PASSWORD,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]
    );
    
    $quotaManager = new QuotaManager($pdo);
    
    // Récupérer l'action et les paramètres
    $action = $_GET['action'] ?? $_POST['action'] ?? '';
    $userId = $_GET['user_id'] ?? $_POST['user_id'] ?? null;
    $language = $_GET['lang'] ?? $_POST['lang'] ?? 'ar';
    
    // Vérification de base
    if (!$action) {
        throw new Exception('Action non spécifiée');
    }
    
    switch ($action) {
        
        // =====================================================
        // ENDPOINTS UTILISATEUR
        // =====================================================
        
        case 'get_quota_usage':
            if (!$userId) {
                throw new Exception('ID utilisateur requis');
            }
            
            $usage = $quotaManager->getQuotaUsage($userId, $language);
            if (!$usage) {
                throw new Exception('Utilisateur non trouvé');
            }
            
            echo json_encode([
                'success' => true,
                'data' => $usage
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'check_can_create':
            $type = $_GET['type'] ?? $_POST['type'] ?? '';
            
            if (!$userId || !$type) {
                throw new Exception('ID utilisateur et type requis');
            }
            
            $result = $quotaManager->canCreate($userId, $type);
            
            echo json_encode([
                'success' => true,
                'data' => $result
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'get_subscription_status':
            if (!$userId) {
                throw new Exception('ID utilisateur requis');
            }
            
            $user = $quotaManager->getUserWithSubscription($userId);
            $isActive = $quotaManager->isSubscriptionActive($userId);
            
            echo json_encode([
                'success' => true,
                'data' => [
                    'user' => $user,
                    'is_active' => $isActive,
                    'subscription_status' => $user['subscription_status'] ?? null,
                    'subscription_end_date' => $user['subscription_end_date'] ?? null
                ]
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'update_quota_cache':
            if (!$userId) {
                throw new Exception('ID utilisateur requis');
            }
            
            $quotaManager->updateQuotaCache($userId);
            
            echo json_encode([
                'success' => true,
                'message' => 'Cache des quotas mis à jour'
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        // =====================================================
        // ENDPOINTS ADMIN
        // =====================================================
        
        case 'get_admin_stats':
            // Vérifier les permissions admin (à implémenter selon votre système d'auth)
            $stats = $quotaManager->getAdminStats();
            
            echo json_encode([
                'success' => true,
                'data' => $stats
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'get_all_users_usage':
            $page = (int) ($_GET['page'] ?? 1);
            $limit = (int) ($_GET['limit'] ?? 20);
            $offset = ($page - 1) * $limit;
            
            // Récupérer tous les utilisateurs avec leur utilisation
            $sql = "
                SELECT u.id, u.name, u.email, u.role, u.subscription_status,
                       s.name as subscription_name, s.max_products, s.max_landing_pages, s.max_categories,
                       (
                           SELECT COUNT(*) FROM products p 
                           WHERE p.user_id = u.id AND p.status != 'archived'
                       ) as products_count,
                       (
                           SELECT COUNT(*) FROM landing_pages lp 
                           WHERE lp.user_id = u.id AND lp.status != 'archived'
                       ) as landing_pages_count,
                       (
                           SELECT COUNT(*) FROM categories c 
                           WHERE c.user_id = u.id AND c.is_active = 1
                       ) as categories_count
                FROM users u
                LEFT JOIN subscriptions s ON u.subscription_id = s.id
                WHERE u.is_active = 1 AND u.role IN ('seller', 'agent')
                ORDER BY u.created_at DESC
                LIMIT ? OFFSET ?
            ";
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$limit, $offset]);
            $users = $stmt->fetchAll();
            
            // Compter le total
            $countSql = "SELECT COUNT(*) FROM users WHERE is_active = 1 AND role IN ('seller', 'agent')";
            $total = $pdo->query($countSql)->fetchColumn();
            
            echo json_encode([
                'success' => true,
                'data' => [
                    'users' => $users,
                    'pagination' => [
                        'page' => $page,
                        'limit' => $limit,
                        'total' => (int) $total,
                        'pages' => ceil($total / $limit)
                    ]
                ]
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'force_upgrade':
            $targetUserId = $_POST['target_user_id'] ?? null;
            $subscriptionId = $_POST['subscription_id'] ?? null;
            $adminId = $_POST['admin_id'] ?? null;
            
            if (!$targetUserId || !$subscriptionId || !$adminId) {
                throw new Exception('Paramètres manquants pour la mise à niveau forcée');
            }
            
            $result = $quotaManager->forceUpgrade($targetUserId, $subscriptionId, $adminId);
            
            echo json_encode([
                'success' => $result,
                'message' => $result ? 'Mise à niveau effectuée avec succès' : 'Erreur lors de la mise à niveau'
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'get_subscriptions':
            $sql = "
                SELECT id, name, name_ar, name_fr, name_en, price, currency,
                       max_products, max_landing_pages, max_categories,
                       description, description_ar, description_fr, description_en,
                       features, is_active
                FROM subscriptions 
                WHERE is_active = 1 
                ORDER BY sort_order, price
            ";
            
            $stmt = $pdo->query($sql);
            $subscriptions = $stmt->fetchAll();
            
            // Décoder les features JSON
            foreach ($subscriptions as &$subscription) {
                $subscription['features'] = json_decode($subscription['features'], true) ?? [];
            }
            
            echo json_encode([
                'success' => true,
                'data' => $subscriptions
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        // =====================================================
        // ENDPOINTS DE VALIDATION
        // =====================================================
        
        case 'validate_before_create':
            $type = $_POST['type'] ?? '';
            
            if (!$userId || !$type) {
                throw new Exception('Paramètres manquants');
            }
            
            // Vérifier l'abonnement actif
            if (!$quotaManager->isSubscriptionActive($userId)) {
                echo json_encode([
                    'success' => false,
                    'error' => 'subscription_inactive',
                    'message' => $quotaManager->translate('upgrade_required', $language)
                ], JSON_UNESCAPED_UNICODE);
                break;
            }
            
            // Vérifier les quotas
            $canCreate = $quotaManager->canCreate($userId, $type);
            
            if (!$canCreate['allowed']) {
                $limitKey = $type . '_limit';
                echo json_encode([
                    'success' => false,
                    'error' => 'quota_exceeded',
                    'message' => $quotaManager->translate('quota_exceeded', $language),
                    'details' => [
                        'type' => $type,
                        'current' => $canCreate['current'] ?? 0,
                        'limit' => $canCreate['limit'] ?? 0,
                        'limit_name' => $quotaManager->translate($limitKey, $language)
                    ]
                ], JSON_UNESCAPED_UNICODE);
                break;
            }
            
            echo json_encode([
                'success' => true,
                'message' => 'Création autorisée',
                'quota_info' => $canCreate
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        // =====================================================
        // ENDPOINTS DE STATISTIQUES
        // =====================================================
        
        case 'get_usage_trends':
            $days = (int) ($_GET['days'] ?? 30);
            
            $sql = "
                SELECT DATE(created_at) as date,
                       COUNT(CASE WHEN action = 'create_product' THEN 1 END) as products_created,
                       COUNT(CASE WHEN action = 'create_landing_page' THEN 1 END) as landing_pages_created,
                       COUNT(CASE WHEN action = 'create_category' THEN 1 END) as categories_created
                FROM activity_logs 
                WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
                  AND action IN ('create_product', 'create_landing_page', 'create_category')
                GROUP BY DATE(created_at)
                ORDER BY date DESC
            ";
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$days]);
            $trends = $stmt->fetchAll();
            
            echo json_encode([
                'success' => true,
                'data' => $trends
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        default:
            throw new Exception('Action non reconnue: ' . $action);
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Erreur de base de données: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}

?>