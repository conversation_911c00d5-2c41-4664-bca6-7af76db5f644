<?php
header('Content-Type: application/json');
require_once '../config/database.php';

// Vérification de l'authentification
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || $_SERVER['HTTP_AUTHORIZATION'] !== 'Bearer demo_token') {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Non autorisé']);
    exit;
}

// Vérification du store_id
$store_id = isset($_GET['store_id']) ? intval($_GET['store_id']) : null;
if (!$store_id) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'store_id manquant']);
    exit;
}

// Connexion à la base de données
try {
    $pdo = new PDO(
        "mysql:host={$_ENV['DB_HOST']};port={$_ENV['DB_PORT']};dbname={$_ENV['DB_DATABASE']};charset={$_ENV['DB_CHARSET']}",
        $_ENV['DB_USERNAME'],
        $_ENV['DB_PASSWORD']
    );
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Erreur de connexion à la base de données']);
    exit;
}

// Gestion des actions
$action = $_GET['action'] ?? '';

switch ($action) {
    case 'export_products':
        exportProducts($pdo, $store_id);
        break;

    case 'export_sales':
        exportSales($pdo, $store_id);
        break;

    case 'generate_script':
        generateScript($store_id);
        break;

    default:
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Action non valide']);
        break;
}

// Fonction d'export des produits
function exportProducts($pdo, $store_id) {
    try {
        $stmt = $pdo->prepare(
            "SELECT p.*, c.name as category_name
            FROM products p
            LEFT JOIN categories c ON p.category_id = c.id
            WHERE p.store_id = ?"
        );
        $stmt->execute([$store_id]);
        $products = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Simulation de création d'un Google Sheet
        $sheet_url = "https://docs.google.com/spreadsheets/d/example-" . uniqid();

        echo json_encode([
            'success' => true,
            'message' => 'Produits exportés avec succès',
            'sheet_url' => $sheet_url,
            'products_count' => count($products)
        ]);
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Erreur lors de l\'export des produits']);
    }
}

// Fonction d'export des ventes
function exportSales($pdo, $store_id) {
    try {
        // Requête pour obtenir les statistiques de vente
        $stmt = $pdo->prepare(
            "SELECT
                COUNT(*) as total_orders,
                SUM(total_amount) as total_revenue,
                DATE(created_at) as order_date
            FROM orders
            WHERE store_id = ?
            GROUP BY DATE(created_at)
            ORDER BY order_date DESC"
        );
        $stmt->execute([$store_id]);
        $sales = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Simulation de création d'un Google Sheet
        $sheet_url = "https://docs.google.com/spreadsheets/d/example-" . uniqid();

        echo json_encode([
            'success' => true,
            'message' => 'Rapport de ventes généré avec succès',
            'sheet_url' => $sheet_url,
            'sales_count' => count($sales)
        ]);
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Erreur lors de la génération du rapport']);
    }
}

// Fonction de génération de script
function generateScript($store_id) {
    // Récupération des événements sélectionnés
    $data = json_decode(file_get_contents('php://input'), true);
    $events = [
        'orders' => $data['orders'] ?? false,
        'inventory' => $data['inventory'] ?? false,
        'customers' => $data['customers'] ?? false
    ];

    // Génération du script Google Apps
    $script = generateGoogleAppsScript($store_id, $events);

    echo json_encode([
        'success' => true,
        'message' => 'Script généré avec succès',
        'script_content' => $script
    ]);
}

// Fonction de génération du script Google Apps
function generateGoogleAppsScript($store_id, $events) {
    $api_url = "https://" . $_SERVER['HTTP_HOST'];

    $script = <<<EOT
// Configuration
const STORE_ID = '$store_id';
const API_URL = '$api_url';
const API_TOKEN = 'demo_token';

// Fonction principale
function synchronizeData() {
  const sheet = SpreadsheetApp.getActiveSpreadsheet();

  // Création des onglets nécessaires
  if ({$events['orders']}) {
    syncOrders(sheet);
  }
  if ({$events['inventory']}) {
    syncInventory(sheet);
  }
  if ({$events['customers']}) {
    syncCustomers(sheet);
  }
}

// Synchronisation des commandes
function syncOrders(sheet) {
  const ordersSheet = getOrCreateSheet(sheet, 'Commandes');
  const response = fetchAPI('/api/orders.php?store_id=' + STORE_ID);
  if (response && response.success) {
    writeDataToSheet(ordersSheet, response.orders);
  }
}

// Synchronisation de l'inventaire
function syncInventory(sheet) {
  const inventorySheet = getOrCreateSheet(sheet, 'Inventaire');
  const response = fetchAPI('/api/products.php?store_id=' + STORE_ID);
  if (response && response.success) {
    writeDataToSheet(inventorySheet, response.products);
  }
}

// Synchronisation des clients
function syncCustomers(sheet) {
  const customersSheet = getOrCreateSheet(sheet, 'Clients');
  const response = fetchAPI('/api/customers.php?store_id=' + STORE_ID);
  if (response && response.success) {
    writeDataToSheet(customersSheet, response.customers);
  }
}

// Utilitaires
function fetchAPI(endpoint) {
  const options = {
    'method': 'get',
    'headers': {
      'Authorization': 'Bearer ' + API_TOKEN
    }
  };
  try {
    const response = UrlFetchApp.fetch(API_URL + endpoint, options);
    return JSON.parse(response.getContentText());
  } catch (error) {
    Logger.log('Erreur API: ' + error);
    return null;
  }
}

function getOrCreateSheet(spreadsheet, sheetName) {
  let sheet = spreadsheet.getSheetByName(sheetName);
  if (!sheet) {
    sheet = spreadsheet.insertSheet(sheetName);
  }
  return sheet;
}

function writeDataToSheet(sheet, data) {
  if (!data || !data.length) return;

  // En-têtes
  const headers = Object.keys(data[0]);
  sheet.getRange(1, 1, 1, headers.length).setValues([headers]);

  // Données
  const values = data.map(row => headers.map(header => row[header]));
  sheet.getRange(2, 1, values.length, headers.length).setValues(values);

  // Formatage
  sheet.autoResizeColumns(1, headers.length);
}

// Création du menu personnalisé
function onOpen() {
  const ui = SpreadsheetApp.getUi();
  ui.createMenu('Synchronisation')
    .addItem('Synchroniser les données', 'synchronizeData')
    .addToUi();
}
EOT;

    return $script;
}
