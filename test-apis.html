<!DOCTYPE html>
<html lang="fr" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Interface de test des APIs du dashboard vendeur pour la plateforme de landing pages">
    <title>Test APIs - Dashboard Vendeur</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #4a90e2;
            --success-color: #28a745;
            --error-color: #dc3545;
            --warning-color: #ffc107;
            --text-color: #333;
            --border-radius: 8px;
        }

        body {
            font-family: 'Segoe UI', system-ui, sans-serif;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
            color: var(--text-color);
            background-color: #f8f9fa;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 15px;
        }

        h1 {
            color: var(--primary-color);
            text-align: center;
            margin-bottom: 2rem;
        }

        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: var(--border-radius);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .test-section:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        .success {
            border-left: 4px solid var(--success-color);
        }

        .error {
            border-left: 4px solid var(--error-color);
        }

        .loading {
            border-left: 4px solid var(--warning-color);
        }

        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: var(--border-radius);
            overflow-x: auto;
            font-size: 14px;
            border: 1px solid #e9ecef;
        }

        button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-weight: 500;
            transition: background-color 0.2s;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        button:hover {
            background-color: #357abd;
        }

        button:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.3);
        }

        button[disabled] {
            background-color: #ccc;
            cursor: not-allowed;
        }

        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
        }

        .status-badge.success {
            background-color: #d4edda;
            color: var(--success-color);
        }

        .status-badge.error {
            background-color: #f8d7da;
            color: var(--error-color);
        }

        details {
            margin-top: 15px;
        }

        summary {
            cursor: pointer;
            color: var(--primary-color);
            font-weight: 500;
        }

        summary:focus {
            outline: none;
        }

        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .test-section {
                padding: 15px;
            }

            button {
                width: 100%;
                margin: 5px 0;
            }
        }
    </style>
</head>
<body>
    <div class="container" role="main">
        <h1>Test des APIs du Dashboard Vendeur</h1>
        
        <div class="test-section" role="region" aria-label="Configuration">
            <h2><i class="fas fa-cog"></i> Configuration</h2>
            <p>Store ID: <strong aria-label="Identifiant du magasin">3</strong></p>
            <p>Base URL: <strong aria-label="URL de base">http://localhost:8000</strong></p>
            <button onclick="testAll()" aria-label="Lancer tous les tests d'API">
                <i class="fas fa-play"></i> Tester toutes les APIs
            </button>
        </div>

        <div id="products-test" class="test-section" role="region" aria-label="Test API Produits">
            <h2><i class="fas fa-box"></i> API Products</h2>
            <button onclick="testProducts()" aria-label="Tester l'API des produits">
                <i class="fas fa-vial"></i> Tester Products
            </button>
            <div id="products-result" aria-live="polite"></div>
        </div>

        <div id="categories-test" class="test-section" role="region" aria-label="Test API Catégories">
            <h2><i class="fas fa-tags"></i> API Categories</h2>
            <button onclick="testCategories()" aria-label="Tester l'API des catégories">
                <i class="fas fa-vial"></i> Tester Categories
            </button>
            <div id="categories-result" aria-live="polite"></div>
        </div>

        <div id="landing-pages-test" class="test-section" role="region" aria-label="Test API Landing Pages">
            <h2><i class="fas fa-file-alt"></i> API Landing Pages</h2>
            <button onclick="testLandingPages()" aria-label="Tester l'API des landing pages">
                <i class="fas fa-vial"></i> Tester Landing Pages
            </button>
            <div id="landing-pages-result" aria-live="polite"></div>
        </div>
    </div>

    <script>
        const BASE_URL = 'http://localhost:8000';
        const STORE_ID = 3;
        const TOKEN = 'demo_token';

        async function testAPI(url, name, resultElementId) {
            const resultElement = document.getElementById(resultElementId);
            resultElement.innerHTML = `
                    <div class="status-badge loading">
                        <i class="fas fa-spinner fa-spin"></i> Test en cours...
                    </div>
                `;
            resultElement.className = 'loading';

            try {
                console.log(`Testing ${name}: ${url}`);
                
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${TOKEN}`,
                        'Content-Type': 'application/json'
                    }
                });

                console.log(`${name} Response status:`, response.status);
                console.log(`${name} Response headers:`, response.headers);

                const responseText = await response.text();
                console.log(`${name} Response text:`, responseText);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${responseText}`);
                }

                let data;
                try {
                    data = JSON.parse(responseText);
                } catch (e) {
                    throw new Error(`JSON Parse Error: ${e.message}\nResponse: ${responseText}`);
                }

                resultElement.className = 'success';
                resultElement.innerHTML = `
                    <div class="status-badge success">
                        <i class="fas fa-check-circle"></i> <strong>${name}</strong> - Succès
                    </div>
                    <p><i class="fas fa-info-circle"></i> Status: <code>${response.status}</code></p>
                    <p><i class="fas fa-check"></i> Success: <code>${data.success ? 'true' : 'false'}</code></p>
                    <p><i class="fas fa-list"></i> Data count: <code>${data.data ? data.data.length : 'N/A'}</code></p>
                    <details>
                        <summary><i class="fas fa-code"></i> Voir la réponse complète</summary>
                        <pre><code>${JSON.stringify(data, null, 2)}</code></pre>
                    </details>
                `;

            } catch (error) {
                console.error(`${name} Error:`, error);
                resultElement.className = 'error';
                resultElement.innerHTML = `
                    <div class="status-badge error">
                        <i class="fas fa-exclamation-circle"></i> <strong>${name}</strong> - Erreur
                    </div>
                    <p><i class="fas fa-exclamation-triangle"></i> Message: <code>${error.message}</code></p>
                    <details>
                        <summary><i class="fas fa-bug"></i> Détails de l'erreur</summary>
                        <pre><code>${error.stack || error.toString()}</code></pre>
                    </details>
                `;
            }
        }

        async function testProducts() {
            const url = `${BASE_URL}/api/products-simple.php?store_id=${STORE_ID}`;
            await testAPI(url, 'Products API', 'products-result');
        }

        async function testCategories() {
            const url = `${BASE_URL}/api/categories.php?store_id=${STORE_ID}`;
            await testAPI(url, 'Categories API', 'categories-result');
        }

        async function testLandingPages() {
            const url = `${BASE_URL}/api/landing-pages.php?store_id=${STORE_ID}`;
            await testAPI(url, 'Landing Pages API', 'landing-pages-result');
        }

        async function testAll() {
            console.log('=== Testing all APIs ===');
            await testProducts();
            await testCategories();
            await testLandingPages();
            console.log('=== All tests completed ===');
        }

        // Test automatique au chargement avec notification
        window.addEventListener('load', () => {
            console.log('Page loaded, starting automatic tests...');
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                bottom: 20px;
                right: 20px;
                background: var(--primary-color);
                color: white;
                padding: 15px 25px;
                border-radius: var(--border-radius);
                box-shadow: 0 2px 10px rgba(0,0,0,0.2);
                z-index: 1000;
                display: flex;
                align-items: center;
                gap: 10px;
                animation: slideIn 0.3s ease;
            `;
            notification.innerHTML = '<i class="fas fa-info-circle"></i> Démarrage automatique des tests dans 1 seconde...';
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'fadeOut 0.3s ease forwards';
                setTimeout(() => notification.remove(), 300);
            }, 3000);

            setTimeout(testAll, 1000);
        });

        // Animations CSS
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }

            @keyframes fadeOut {
                from { opacity: 1; transform: translateY(0); }
                to { opacity: 0; transform: translateY(20px); }
            }

            .fas.fa-spinner {
                animation: spin 1s linear infinite;
            }

            @keyframes spin {
                100% { transform: rotate(360deg); }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
