-- Structure de la table `subscription_templates`
CREATE TABLE IF NOT EXISTS `subscription_templates` (
  `subscription_plan_id` int(11) NOT NULL,
  `template_id` int(11) NOT NULL,
  PRIMARY KEY (`subscription_plan_id`, `template_id`),
  <PERSON>EY `template_id` (`template_id`),
  CONSTRAINT `subscription_templates_ibfk_1` FOREIGN KEY (`subscription_plan_id`) REFERENCES `subscription_plans` (`id`) ON DELETE CASCADE,
  CONSTRAINT `subscription_templates_ibfk_2` FOREIGN KEY (`template_id`) REFERENCES `templates` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Attribution des templates aux plans d'abonnement
-- Plan Basique: accès aux templates Modern Shop et Service Pro
INSERT INTO `subscription_templates` (`subscription_plan_id`, `template_id`) VALUES
(1, 1), -- Basique -> Modern Shop
(1, 4), -- Basique -> Service Pro

-- Plan Pro: accès à tous les templates sauf App Launch
(2, 1), -- Pro -> Modern Shop
(2, 2), -- Pro -> SaaS Landing
(2, 3), -- Pro -> Creative Portfolio
(2, 4), -- Pro -> Service Pro

-- Plan Enterprise: accès à tous les templates
(3, 1), -- Enterprise -> Modern Shop
(3, 2), -- Enterprise -> SaaS Landing
(3, 3), -- Enterprise -> Creative Portfolio
(3, 4), -- Enterprise -> Service Pro
(3, 5); -- Enterprise -> App Launch