<?php
// Test de l'API products pour diagnostiquer l'erreur 500
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "🔍 Test de l'API products...\n\n";

// Simuler les variables d'environnement
$_SERVER['REQUEST_METHOD'] = 'GET';
$_SERVER['REQUEST_URI'] = '/api/products/15?store_id=3';
$_SERVER['HTTP_AUTHORIZATION'] = 'Bearer demo_token';

// Capturer la sortie
ob_start();

try {
    echo "📂 Inclusion du fichier API...\n";
    include 'api/products.php';
} catch (Exception $e) {
    echo "❌ Erreur lors de l'inclusion: " . $e->getMessage() . "\n";
    echo "📍 Fichier: " . $e->getFile() . " ligne " . $e->getLine() . "\n";
} catch (Error $e) {
    echo "❌ Erreur fatale: " . $e->getMessage() . "\n";
    echo "📍 Fichier: " . $e->getFile() . " ligne " . $e->getLine() . "\n";
}

$output = ob_get_clean();
echo "📤 Sortie de l'API:\n";
echo $output;

// Test direct de la base de données
echo "\n🔍 Test direct de la base de données...\n";

try {
    require_once 'api/config/database.php';
    
    $database = new Database();
    $db = $database->getConnection();
    
    echo "✅ Connexion à la base de données réussie\n";
    
    // Test de récupération d'un produit
    $stmt = $db->prepare("SELECT * FROM products WHERE id = ? LIMIT 1");
    $stmt->execute([15]);
    $product = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($product) {
        echo "✅ Produit ID 15 trouvé: {$product['name_ar']}\n";
        echo "  - Store ID: {$product['store_id']}\n";
        echo "  - SKU: {$product['sku']}\n";
        echo "  - Prix: {$product['price']}\n";
    } else {
        echo "❌ Produit ID 15 non trouvé\n";
    }
    
    // Vérifier les stores
    $storeStmt = $db->prepare("SELECT id, store_name FROM stores WHERE id = ?");
    $storeStmt->execute([3]);
    $store = $storeStmt->fetch(PDO::FETCH_ASSOC);
    
    if ($store) {
        echo "✅ Store ID 3 trouvé: {$store['store_name']}\n";
    } else {
        echo "❌ Store ID 3 non trouvé\n";
    }
    
} catch (Exception $e) {
    echo "❌ Erreur base de données: " . $e->getMessage() . "\n";
}
?>
